import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TutorEducation } from '../../database/entities/tutor-education.entity';
import { CreateTutorEducationDto, UpdateTutorEducationDto, TutorEducationResponseDto } from './dto/tutor-education.dto';
import { User, UserType } from '../../database/entities/user.entity';
import { formatToYYYYMMDD } from '../../common/utils/date-utils';

@Injectable()
export class TutorEducationService {
  constructor(
    @InjectRepository(TutorEducation)
    private tutorEducationRepository: Repository<TutorEducation>,
    @InjectRepository(User)
    private userRepository: Repository<User>
  ) {}

  /**
   * Create a new education entry for a tutor
   * @param tutorId The ID of the tutor
   * @param createDto The data for the new education entry
   * @param currentUserId The ID of the current user (for authorization)
   * @returns The created education entry
   */
  async create(tutorId: string, createDto: CreateTutorEducationDto, currentUserId: string): Promise<TutorEducationResponseDto> {
    // Check if the tutor exists and is a tutor
    const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
    if (!tutor) {
      throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
    }
    if (tutor.type !== UserType.TUTOR) {
      throw new ForbiddenException(`User with ID ${tutorId} is not a tutor`);
    }

    // Check if the current user is the tutor or an admin
    if (currentUserId !== tutorId) {
      const currentUser = await this.userRepository.findOne({
        where: { id: currentUserId },
        relations: ['userRoles', 'userRoles.role']
      });

      if (!currentUser || (currentUser.type !== UserType.ADMIN && !currentUser.userRoles.some(ur => ur.role.name === 'admin'))) {
        throw new ForbiddenException('You are not authorized to create education entries for this tutor');
      }
    }

    // Create the education entry
    const education = this.tutorEducationRepository.create({
      tutorId,
      degree: createDto.degree,
      institution: createDto.institution,
      fieldOfStudy: createDto.fieldOfStudy,
      startDate: this.parseDate(createDto.startDate),
      endDate: createDto.endDate ? this.parseDate(createDto.endDate) : null,
      isCurrent: createDto.isCurrent || false,
      description: createDto.description,
      location: createDto.location,
      grade: createDto.grade,
      activities: createDto.activities,
      createdBy: currentUserId,
      updatedBy: currentUserId
    });

    const savedEducation = await this.tutorEducationRepository.save(education);
    return this.mapToResponseDto(savedEducation);
  }

  /**
   * Get all education entries for a tutor
   * @param tutorId The ID of the tutor
   * @returns An array of education entries
   */
  async findAllForTutor(tutorId: string): Promise<TutorEducationResponseDto[]> {
    const educations = await this.tutorEducationRepository.find({
      where: { tutorId },
      order: { startDate: 'DESC' }
    });

    return educations.map(education => this.mapToResponseDto(education));
  }

  /**
   * Get a specific education entry by ID
   * @param id The ID of the education entry
   * @returns The education entry
   */
  async findOne(id: string): Promise<TutorEducationResponseDto> {
    const education = await this.tutorEducationRepository.findOne({ where: { id } });
    if (!education) {
      throw new NotFoundException(`Education entry with ID ${id} not found`);
    }

    return this.mapToResponseDto(education);
  }

  /**
   * Update an education entry
   * @param id The ID of the education entry
   * @param updateDto The data to update
   * @param currentUserId The ID of the current user (for authorization)
   * @returns The updated education entry
   */
  async update(id: string, updateDto: UpdateTutorEducationDto, currentUserId: string): Promise<TutorEducationResponseDto> {
    const education = await this.tutorEducationRepository.findOne({ where: { id } });
    if (!education) {
      throw new NotFoundException(`Education entry with ID ${id} not found`);
    }

    // Check if the current user is the tutor or an admin
    if (currentUserId !== education.tutorId) {
      const currentUser = await this.userRepository.findOne({
        where: { id: currentUserId },
        relations: ['userRoles', 'userRoles.role']
      });

      if (!currentUser || (currentUser.type !== UserType.ADMIN && !currentUser.userRoles.some(ur => ur.role.name === 'admin'))) {
        throw new ForbiddenException('You are not authorized to update this education entry');
      }
    }

    // Update the education entry
    if (updateDto.degree) education.degree = updateDto.degree;
    if (updateDto.institution) education.institution = updateDto.institution;
    if (updateDto.fieldOfStudy) education.fieldOfStudy = updateDto.fieldOfStudy;
    if (updateDto.startDate) education.startDate = this.parseDate(updateDto.startDate);
    if (updateDto.endDate !== undefined) education.endDate = updateDto.endDate ? this.parseDate(updateDto.endDate) : null;
    if (updateDto.isCurrent !== undefined) education.isCurrent = updateDto.isCurrent;
    if (updateDto.description !== undefined) education.description = updateDto.description;
    if (updateDto.location !== undefined) education.location = updateDto.location;
    if (updateDto.grade !== undefined) education.grade = updateDto.grade;
    if (updateDto.activities !== undefined) education.activities = updateDto.activities;
    education.updatedBy = currentUserId;

    const savedEducation = await this.tutorEducationRepository.save(education);
    return this.mapToResponseDto(savedEducation);
  }

  /**
   * Delete an education entry
   * @param id The ID of the education entry
   * @param currentUserId The ID of the current user (for authorization)
   */
  async remove(id: string, currentUserId: string): Promise<void> {
    const education = await this.tutorEducationRepository.findOne({ where: { id } });
    if (!education) {
      throw new NotFoundException(`Education entry with ID ${id} not found`);
    }

    // Check if the current user is the tutor or an admin
    if (currentUserId !== education.tutorId) {
      const currentUser = await this.userRepository.findOne({
        where: { id: currentUserId },
        relations: ['userRoles', 'userRoles.role']
      });

      if (!currentUser || (currentUser.type !== UserType.ADMIN && !currentUser.userRoles.some(ur => ur.role.name === 'admin'))) {
        throw new ForbiddenException('You are not authorized to delete this education entry');
      }
    }

    await this.tutorEducationRepository.remove(education);
  }

  /**
   * Parse a date string to a Date object
   * @param dateString The date string to parse (expected format: YYYY-MM-DD)
   * @returns A Date object
   */
  private parseDate(dateString: string): Date {
    try {
      // Check if the string is in YYYY-MM-DD format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (dateRegex.test(dateString)) {
        // Parse the date string (YYYY-MM-DD)
        const [year, month, day] = dateString.split('-').map(Number);
        // Create a UTC date (month is 0-indexed in JavaScript Date)
        return new Date(Date.UTC(year, month - 1, day));
      } else {
        // Try to parse as a regular date string
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          console.warn(`Invalid date string format: ${dateString}, using current date`);
          return new Date(); // Return current date as fallback
        }
        return date;
      }
    } catch (error) {
      console.error(`Error parsing date: ${dateString}`, error);
      return new Date(); // Return current date as fallback
    }
  }

  /**
   * Map a TutorEducation entity to a TutorEducationResponseDto
   * @param education The education entity
   * @returns The response DTO
   */
  private mapToResponseDto(education: TutorEducation): TutorEducationResponseDto {
    return {
      id: education.id,
      tutorId: education.tutorId,
      degree: education.degree,
      institution: education.institution,
      fieldOfStudy: education.fieldOfStudy,
      startDate: formatToYYYYMMDD(education.startDate) || '', // Format as YYYY-MM-DD using the utility
      endDate: education.endDate ? formatToYYYYMMDD(education.endDate) || null : null,
      isCurrent: education.isCurrent,
      description: education.description,
      location: education.location,
      grade: education.grade,
      activities: education.activities,
      createdAt: education.createdAt,
      updatedAt: education.updatedAt
    };
  }
}
