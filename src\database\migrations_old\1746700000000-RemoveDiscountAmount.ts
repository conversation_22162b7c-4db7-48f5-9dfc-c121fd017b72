import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveDiscountAmount1746700000000 implements MigrationInterface {
    name = 'RemoveDiscountAmount1746700000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // Drop the discount_amount column
            await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN "discount_amount"`);
            
            console.log('Successfully removed discount_amount column');
        } catch (error) {
            console.error('Migration error:', error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        try {
            // Add back the discount_amount column
            await queryRunner.query(`ALTER TABLE "promotion" ADD "discount_amount" decimal(10,2)`);
            
            console.log('Successfully added back discount_amount column');
        } catch (error) {
            console.error('Migration rollback error:', error);
            throw error;
        }
    }
}
