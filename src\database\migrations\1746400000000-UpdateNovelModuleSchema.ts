import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateNovelModuleSchema1746400000000 implements MigrationInterface {
  name = 'UpdateNovelModuleSchema1746400000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if novel_topic table exists before adding title column
    const novelTopicTableExists = await queryRunner.hasTable('novel_topic');
    if (novelTopicTableExists) {
      // Check if title column already exists
      const titleColumnExists = await queryRunner.hasColumn('novel_topic', 'title');
      if (!titleColumnExists) {
        // Add title column to novel_topic table
        await queryRunner.query(`
          ALTER TABLE "novel_topic" 
          ADD COLUMN "title" character varying NOT NULL DEFAULT 'Untitled Topic'
        `);
        
        // Remove the default value after adding the column
        await queryRunner.query(`
          ALTER TABLE "novel_topic" 
          ALTER COLUMN "title" DROP DEFAULT
        `);
      }
    }

    // Check if novel_suggestion table exists before removing title column
    const novelSuggestionTableExists = await queryRunner.hasTable('novel_suggestion');
    if (novelSuggestionTableExists) {
      // Check if title column exists before trying to drop it
      const titleColumnExists = await queryRunner.hasColumn('novel_suggestion', 'title');
      if (titleColumnExists) {
        // Remove title column from novel_suggestion table
        await queryRunner.query(`
          ALTER TABLE "novel_suggestion" 
          DROP COLUMN "title"
        `);
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if novel_suggestion table exists before adding title column back
    const novelSuggestionTableExists = await queryRunner.hasTable('novel_suggestion');
    if (novelSuggestionTableExists) {
      // Check if title column doesn't exist before adding it back
      const titleColumnExists = await queryRunner.hasColumn('novel_suggestion', 'title');
      if (!titleColumnExists) {
        // Add title column back to novel_suggestion table
        await queryRunner.query(`
          ALTER TABLE "novel_suggestion" 
          ADD COLUMN "title" character varying NOT NULL DEFAULT 'Untitled Suggestion'
        `);
        
        // Remove the default value after adding the column
        await queryRunner.query(`
          ALTER TABLE "novel_suggestion" 
          ALTER COLUMN "title" DROP DEFAULT
        `);
      }
    }

    // Check if novel_topic table exists before removing title column
    const novelTopicTableExists = await queryRunner.hasTable('novel_topic');
    if (novelTopicTableExists) {
      // Check if title column exists before trying to drop it
      const titleColumnExists = await queryRunner.hasColumn('novel_topic', 'title');
      if (titleColumnExists) {
        // Remove title column from novel_topic table
        await queryRunner.query(`
          ALTER TABLE "novel_topic" 
          DROP COLUMN "title"
        `);
      }
    }
  }
}
