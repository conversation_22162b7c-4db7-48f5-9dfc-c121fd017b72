# HEC Database Schema

## Overview

This document outlines the database schema for the HEC (Higher Education Companion) system. The schema is designed to support all the core functionalities of the platform, including user management, tutor-student relationships, educational content, and communication.

## Entity Relationship Diagram

```
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│      User       │       │      Plan       │       │  Subscription   │
├─────────────────┤       ├─────────────────┤       ├─────────────────┤
│ id              │       │ id              │       │ id              │
│ email           │       │ name            │       │ userId          │
│ password        │       │ description     │       │ planId          │
│ role            │◄──────┤ price           │◄──────┤ startDate       │
│ name            │       │ duration        │       │ endDate         │
│ phone           │       │ features        │       │ status          │
│ gender          │       │ isActive        │       │ paymentMethod   │
│ profilePicture  │       │ createdAt       │       │ createdAt       │
│ isVerified      │       │ updatedAt       │       │ updatedAt       │
└─────────────────┘       └─────────────────┘       └─────────────────┘
        ▲                                                   ▲
        │                                                   │
        │                                                   │
┌───────┴───────┐                               ┌───────────┴─────────┐
│  StudentProfile│                               │    PlanFeatureMap    │
├───────────────┤                               ├─────────────────────┤
│ id            │                               │ id                  │
│ userId        │                               │ planId              │
│ age           │                               │ featureId           │
│ grade         │                               │ isActive            │
│ school        │                               │ createdAt           │
│ bio           │                               │ updatedAt           │
│ createdAt     │                               └─────────────────────┘
│ updatedAt     │                                         ▲
└───────────────┘                                         │
        ▲                                                 │
        │                                                 │
        │                                         ┌───────┴───────┐
┌───────┴───────┐       ┌─────────────────┐       │   Feature     │
│  TutorProfile │       │ TutorAssignment │       ├───────────────┤
├───────────────┤       ├─────────────────┤       │ id            │
│ id            │       │ id              │       │ name          │
│ userId        │◄──────┤ tutorId         │       │ description   │
│ education     │       │ studentId       │       │ moduleId      │
│ experience    │       │ featureId       │       │ createdAt     │
│ specialties   │       │ assignedAt      │       │ updatedAt     │
│ bio           │       │ status          │       └───────────────┘
│ hourlyRate    │       │ createdAt       │
│ isApproved    │       │ updatedAt       │
│ createdAt     │       └─────────────────┘
│ updatedAt     │
└───────────────┘
        ▲
        │
        │
┌───────┴───────┐       ┌─────────────────┐       ┌─────────────────┐
│  DiaryEntry   │       │  DiaryComment   │       │   DiarySkin     │
├───────────────┤       ├─────────────────┤       ├─────────────────┤
│ id            │       │ id              │       │ id              │
│ studentId     │◄──────┤ diaryEntryId    │       │ name            │
│ title         │       │ tutorId         │       │ description     │
│ content       │       │ content         │       │ cssProperties   │
│ skinId        │◄──────┤ createdAt       │       │ isDefault       │
│ status        │       │ updatedAt       │       │ createdAt       │
│ submittedAt   │       └─────────────────┘       │ updatedAt       │
│ reviewedAt    │                                 └─────────────────┘
│ createdAt     │
│ updatedAt     │
└───────────────┘

┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│     Essay       │       │  EssayFeedback  │       │  EssayComment   │
├─────────────────┤       ├─────────────────┤       ├─────────────────┤
│ id              │       │ id              │       │ id              │
│ studentId       │◄──────┤ essayId         │◄──────┤ essayId         │
│ tutorId         │       │ tutorId         │       │ tutorId         │
│ title           │       │ overallFeedback │       │ startIndex      │
│ content         │       │ score           │       │ endIndex        │
│ prompt          │       │ grammarFeedback │       │ comment         │
│ wordCount       │       │ structureFeedback│       │ type           │
│ status          │       │ contentFeedback │       │ createdAt       │
│ submittedAt     │       │ suggestions     │       │ updatedAt       │
│ reviewedAt      │       │ createdAt       │       └─────────────────┘
│ createdAt       │       │ updatedAt       │
│ updatedAt       │       └─────────────────┘
└─────────────────┘

┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│  Conversation   │       │    Message      │       │  Notification    │
├─────────────────┤       ├─────────────────┤       ├─────────────────┤
│ id              │       │ id              │       │ id              │
│ participantIds  │◄──────┤ conversationId  │       │ userId          │
│ type            │       │ senderId        │       │ type            │
│ lastMessageAt   │       │ content         │       │ title           │
│ createdAt       │       │ attachments     │       │ message         │
│ updatedAt       │       │ readBy          │       │ data            │
└─────────────────┘       │ createdAt       │       │ read            │
                          │ updatedAt       │       │ channels        │
                          └─────────────────┘       │ createdAt       │
                                                    │ updatedAt       │
                                                    └─────────────────┘

┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│    ShopItem     │       │   Promotion     │       │    FileRegistry │
├─────────────────┤       ├─────────────────┤       ├─────────────────┤
│ id              │       │ id              │       │ id              │
│ title           │       │ code            │       │ entityType      │
│ description     │       │ description     │       │ entityId        │
│ category        │       │ discountType    │       │ originalName    │
│ type            │       │ discountValue   │       │ mimeType        │
│ price           │       │ minPurchase     │       │ size            │
│ discountPrice   │       │ maxDiscount     │       │ path            │
│ isFree          │       │ startDate       │       │ url             │
│ fileId          │       │ endDate         │       │ thumbnailUrl    │
│ createdAt       │       │ isActive        │       │ createdAt       │
│ updatedAt       │       │ createdAt       │       │ updatedAt       │
└─────────────────┘       │ updatedAt       │       └─────────────────┘
                          └─────────────────┘
```

## Tables

### User

Stores information about all users in the system.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| email | varchar(255) | User's email address (unique) |
| password | varchar(255) | Hashed password |
| role | enum | User role (ADMIN, TUTOR, STUDENT) |
| name | varchar(255) | User's full name |
| phone | varchar(20) | User's phone number |
| gender | enum | User's gender (MALE, FEMALE, OTHER) |
| profile_picture | varchar(255) | URL to profile picture |
| is_verified | boolean | Whether email is verified |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### StudentProfile

Additional information specific to students.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| user_id | uuid | Foreign key to User |
| age | integer | Student's age |
| grade | varchar(50) | Student's grade/year |
| school | varchar(255) | Student's school |
| bio | text | Student's biography |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### TutorProfile

Additional information specific to tutors.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| user_id | uuid | Foreign key to User |
| education | text | Tutor's educational background |
| experience | text | Tutor's teaching experience |
| specialties | text[] | Tutor's areas of expertise |
| bio | text | Tutor's biography |
| hourly_rate | decimal | Tutor's hourly rate |
| is_approved | boolean | Whether tutor is approved |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### Plan

Subscription plans available to students.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| name | varchar(255) | Plan name |
| description | text | Plan description |
| price | decimal | Plan price |
| duration | integer | Duration in days |
| features | text[] | Features included (deprecated) |
| is_active | boolean | Whether plan is active |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### Feature

Features that can be included in plans.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| name | varchar(255) | Feature name |
| description | text | Feature description |
| module_id | varchar(50) | Module identifier |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### PlanFeatureMap

Maps features to plans.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| plan_id | uuid | Foreign key to Plan |
| feature_id | uuid | Foreign key to Feature |
| is_active | boolean | Whether feature is active in plan |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### Subscription

Student subscriptions to plans.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| user_id | uuid | Foreign key to User (student) |
| plan_id | uuid | Foreign key to Plan |
| start_date | timestamp | Subscription start date |
| end_date | timestamp | Subscription end date |
| status | enum | Status (ACTIVE, EXPIRED, CANCELLED) |
| payment_method | varchar(50) | Payment method used |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### TutorAssignment

Assignments of tutors to students for specific features.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| tutor_id | uuid | Foreign key to User (tutor) |
| student_id | uuid | Foreign key to User (student) |
| feature_id | uuid | Foreign key to Feature |
| assigned_at | timestamp | Assignment timestamp |
| status | enum | Status (ACTIVE, INACTIVE) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### DiaryEntry

Student diary entries.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| student_id | uuid | Foreign key to User (student) |
| title | varchar(255) | Diary entry title |
| content | text | Diary entry content |
| skin_id | uuid | Foreign key to DiarySkin |
| status | enum | Status (NEW, SUBMIT, REVIEWED, CONFIRM) |
| submitted_at | timestamp | Submission timestamp |
| reviewed_at | timestamp | Review timestamp |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### DiaryComment

Tutor comments on diary entries.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| diary_entry_id | uuid | Foreign key to DiaryEntry |
| tutor_id | uuid | Foreign key to User (tutor) |
| content | text | Comment content |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### DiarySkin

Visual themes for diary entries.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| name | varchar(255) | Skin name |
| description | text | Skin description |
| css_properties | jsonb | CSS properties for the skin |
| is_default | boolean | Whether this is the default skin |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### Essay

Student essays.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| student_id | uuid | Foreign key to User (student) |
| tutor_id | uuid | Foreign key to User (tutor) |
| title | varchar(255) | Essay title |
| content | text | Essay content |
| prompt | text | Essay prompt/question |
| word_count | integer | Number of words |
| status | enum | Status (DRAFT, SUBMITTED, UNDER_REVIEW, REVIEWED) |
| submitted_at | timestamp | Submission timestamp |
| reviewed_at | timestamp | Review timestamp |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### EssayFeedback

Tutor feedback on essays.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| essay_id | uuid | Foreign key to Essay |
| tutor_id | uuid | Foreign key to User (tutor) |
| overall_feedback | text | General feedback |
| score | integer | Numerical score |
| grammar_feedback | text | Grammar feedback |
| structure_feedback | text | Structure feedback |
| content_feedback | text | Content feedback |
| suggestions | text | Improvement suggestions |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### EssayComment

Inline comments on essays.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| essay_id | uuid | Foreign key to Essay |
| tutor_id | uuid | Foreign key to User (tutor) |
| start_index | integer | Starting character index |
| end_index | integer | Ending character index |
| comment | text | Comment text |
| type | enum | Comment type (GRAMMAR, STRUCTURE, CONTENT, etc.) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### Conversation

Chat conversations between users.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| participant_ids | uuid[] | Array of User IDs participating |
| type | enum | Type (ONE_TO_ONE, GROUP) |
| last_message_at | timestamp | Last message timestamp |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### Message

Individual chat messages.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| conversation_id | uuid | Foreign key to Conversation |
| sender_id | uuid | Foreign key to User (sender) |
| content | text | Message content |
| attachments | jsonb | File attachments |
| read_by | uuid[] | Array of User IDs who read the message |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### Notification

User notifications.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| user_id | uuid | Foreign key to User (recipient) |
| type | enum | Notification type |
| title | varchar(255) | Notification title |
| message | text | Notification message |
| data | jsonb | Additional data |
| read | boolean | Whether notification is read |
| channels | text[] | Delivery channels (email, push, in-app) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### ShopItem

Items available in the shop.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| title | varchar(255) | Item title |
| description | text | Item description |
| category | varchar(50) | Item category |
| type | enum | Item type |
| price | decimal | Item price |
| discount_price | decimal | Discounted price |
| is_free | boolean | Whether item is free |
| file_id | uuid | Foreign key to FileRegistry |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### Promotion

Promotional discounts.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| code | varchar(50) | Promotion code |
| description | text | Promotion description |
| discount_type | enum | Type (PERCENTAGE, FIXED) |
| discount_value | decimal | Discount amount |
| min_purchase | decimal | Minimum purchase amount |
| max_discount | decimal | Maximum discount amount |
| start_date | timestamp | Start date |
| end_date | timestamp | End date |
| is_active | boolean | Whether promotion is active |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

### FileRegistry

Registry of uploaded files.

| Column | Type | Description |
|--------|------|-------------|
| id | uuid | Primary key |
| entity_type | varchar(50) | Type of entity the file belongs to |
| entity_id | uuid | ID of the entity the file belongs to |
| original_name | varchar(255) | Original filename |
| mime_type | varchar(100) | File MIME type |
| size | integer | File size in bytes |
| path | varchar(255) | File path on disk |
| url | varchar(255) | File URL |
| thumbnail_url | varchar(255) | Thumbnail URL (for images) |
| created_at | timestamp | Creation timestamp |
| updated_at | timestamp | Last update timestamp |
| created_by | uuid | User who created this record |
| updated_by | uuid | User who last updated this record |

## Indexes

### Primary Keys
- All tables have a primary key on the `id` column

### Foreign Keys
- `student_profile.user_id` → `user.id`
- `tutor_profile.user_id` → `user.id`
- `plan_feature_map.plan_id` → `plan.id`
- `plan_feature_map.feature_id` → `feature.id`
- `subscription.user_id` → `user.id`
- `subscription.plan_id` → `plan.id`
- `tutor_assignment.tutor_id` → `user.id`
- `tutor_assignment.student_id` → `user.id`
- `tutor_assignment.feature_id` → `feature.id`
- `diary_entry.student_id` → `user.id`
- `diary_entry.skin_id` → `diary_skin.id`
- `diary_comment.diary_entry_id` → `diary_entry.id`
- `diary_comment.tutor_id` → `user.id`
- `essay.student_id` → `user.id`
- `essay.tutor_id` → `user.id`
- `essay_feedback.essay_id` → `essay.id`
- `essay_feedback.tutor_id` → `user.id`
- `essay_comment.essay_id` → `essay.id`
- `essay_comment.tutor_id` → `user.id`
- `message.conversation_id` → `conversation.id`
- `message.sender_id` → `user.id`
- `notification.user_id` → `user.id`

### Additional Indexes
- `user.email` (unique)
- `subscription.user_id` and `subscription.status` (for finding active subscriptions)
- `tutor_assignment.student_id` and `tutor_assignment.feature_id` (for finding tutors assigned to a student for a feature)
- `diary_entry.student_id` and `diary_entry.status` (for filtering diary entries by status)
- `essay.student_id` and `essay.status` (for filtering essays by status)
- `essay.tutor_id` and `essay.status` (for finding essays assigned to a tutor)
- `conversation.participant_ids` (GIN index for array searching)
- `message.conversation_id` and `message.created_at` (for retrieving messages in chronological order)
- `notification.user_id` and `notification.read` (for finding unread notifications)

## Constraints

- `user.email` must be unique
- `user.role` must be one of ADMIN, TUTOR, STUDENT
- `subscription.status` must be one of ACTIVE, EXPIRED, CANCELLED
- `diary_entry.status` must be one of NEW, SUBMIT, REVIEWED, CONFIRM
- `essay.status` must be one of DRAFT, SUBMITTED, UNDER_REVIEW, REVIEWED
- `essay_comment.type` must be one of GRAMMAR, STRUCTURE, CONTENT, SUGGESTION, GENERAL
- `conversation.type` must be one of ONE_TO_ONE, GROUP

## Audit Fields

All tables include the following audit fields:
- `created_at`: Timestamp when the record was created
- `updated_at`: Timestamp when the record was last updated
- `created_by`: ID of the user who created the record
- `updated_by`: ID of the user who last updated the record

These fields are automatically managed by the application layer.

## Migrations

Database migrations are managed using TypeORM migrations. Each migration is versioned and can be applied or reverted as needed.

Migration files are stored in the `src/migrations` directory and follow the naming convention `YYYYMMDDHHMMSS-MigrationName.ts`.

## Conclusion

This database schema provides a comprehensive foundation for the HEC system, supporting all the core functionalities while maintaining data integrity and performance. The schema is designed to be flexible and extensible to accommodate future requirements.
