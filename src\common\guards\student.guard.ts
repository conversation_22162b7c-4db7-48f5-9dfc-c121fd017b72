import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Inject } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserType } from '../../database/entities/user.entity';
import { Messages } from '../../constants/messages';
import { JwtPayload } from '../../modules/auth/interfaces/jwt-payload.interface';
import LoggerService from '../services/logger.service';
import { STUDENT_ONLY_KEY } from '../decorators/student-only.decorator';
import { STRICT_STUDENT_ONLY_KEY } from '../decorators/strict-student-only.decorator';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class StudentGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    @Inject(LoggerService) private logger: LoggerService
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const isPublic = this.reflector.getAllAndOverride<boolean>(
      IS_PUBLIC_KEY,
      [context.getHandler(), context.getClass()]
    );

    if (isPublic) {
      return true;
    }

    const isStudentOnly = this.reflector.getAllAndOverride<boolean>(
      STUDENT_ONLY_KEY,
      [context.getHandler(), context.getClass()]
    );

    const isStrictStudentOnly = this.reflector.getAllAndOverride<boolean>(
      STRICT_STUDENT_ONLY_KEY,
      [context.getHandler(), context.getClass()]
    );

    // If the endpoint is not marked as student-only or strict-student-only, allow access
    if (!isStudentOnly && !isStrictStudentOnly) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user as JwtPayload;

    if (!user) {
      this.logger.warn(`Student access check failed: No user in request`);
      throw new ForbiddenException(Messages.UNAUTHORIZED);
    }

    // Check if user is a student
    const isStudent = user.type === UserType.STUDENT ||
      (user.roles && Array.isArray(user.roles) && user.roles.includes('student'));

    // For strict student-only endpoints, only allow students
    if (isStrictStudentOnly) {
      if (!isStudent) {
        this.logger.warn(`Strict student access check failed: User ${user.id} with role ${user.type} is not a student`);
        throw new ForbiddenException('This resource is only accessible to students');
      }

      // Check if student has an active subscription
      if (!user.activePlan) {
        this.logger.warn(`Student access check failed: User ${user.id} has no active plan`);
        throw new ForbiddenException('You need an active subscription to access this resource');
      }

      // Check if student's subscription is active
      if (user.planActive === false) {
        this.logger.warn(`Student access check failed: User ${user.id}'s subscription has expired`);
        throw new ForbiddenException('Your subscription has expired. Please renew your plan to access this resource');
      }

      return true;
    }

    // For regular student-only endpoints, allow admin and tutors as well
    const isAdmin = user.type === UserType.ADMIN ||
      (user.roles && Array.isArray(user.roles) && user.roles.includes('admin'));
    const isTutor = user.type === UserType.TUTOR ||
      (user.roles && Array.isArray(user.roles) && user.roles.includes('tutor'));

    // Check if student has an active subscription
    if (isStudent && !user.activePlan) {
      this.logger.warn(`Student access check failed: User ${user.id} has no active plan`);
      throw new ForbiddenException('You need an active subscription to access this resource');
    }

    // Check if student's subscription is active
    if (isStudent && user.planActive === false) {
      this.logger.warn(`Student access check failed: User ${user.id}'s subscription has expired`);
      throw new ForbiddenException('Your subscription has expired. Please renew your plan to access this resource');
    }

    if (isStudent || isAdmin || isTutor) {
      return true;
    }

    this.logger.warn(`Student access check failed: User ${user.id} with role ${user.type} is not authorized`);
    throw new ForbiddenException(Messages.FORBIDDEN);
  }
}
