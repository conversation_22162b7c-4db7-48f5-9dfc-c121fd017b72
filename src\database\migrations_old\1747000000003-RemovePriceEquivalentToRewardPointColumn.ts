import { MigrationInterface, QueryRunner } from "typeorm";

export class RemovePriceEquivalentToRewardPointColumn1747000000003 implements MigrationInterface {
    name = 'RemovePriceEquivalentToRewardPointColumn1747000000003'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Remove the price_equivalent_to_reward_point column
        await queryRunner.query(`ALTER TABLE "shop_item" DROP COLUMN "price_equivalent_to_reward_point"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add back the price_equivalent_to_reward_point column
        await queryRunner.query(`ALTER TABLE "shop_item" ADD "price_equivalent_to_reward_point" decimal(10,2) NOT NULL DEFAULT 0`);
        
        // Update existing records to set price_equivalent_to_reward_point based on is_purchasable_in_rewardpoint
        await queryRunner.query(`
            UPDATE "shop_item" 
            SET "price_equivalent_to_reward_point" = "price" * 100 
            WHERE "is_purchasable_in_rewardpoint" = true
        `);
    }
}
