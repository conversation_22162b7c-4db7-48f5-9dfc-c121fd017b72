import { Injectable, NotFoundException, ForbiddenException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { Diary } from '../../database/entities/diary.entity';
import { DiaryShare } from '../../database/entities/diary-share.entity';
import { DiaryQrRegistry } from '../../database/entities/diary-qr-registry.entity';
import { User } from '../../database/entities/user.entity';
import { v4 as uuidv4 } from 'uuid';
import { getCurrentUTCDate, toUTCDate } from '../../common/utils/date-utils';
import { ShareDiaryEntryDto, DiaryShareResponseDto, DiaryEntryResponseDto } from '../../database/models/diary.dto';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { QrCodeService } from '../../common/services/qr-code.service';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { DiaryVisibility } from '../../common/enums/diary-visibility.enum';

@Injectable()
export class DiaryShareService {
  private readonly logger = new Logger(DiaryShareService.name);

  constructor(
    @InjectRepository(DiaryEntry)
    private diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(Diary)
    private diaryRepository: Repository<Diary>,
    @InjectRepository(DiaryShare)
    private diaryShareRepository: Repository<DiaryShare>,
    @InjectRepository(DiaryQrRegistry)
    private diaryQrRegistryRepository: Repository<DiaryQrRegistry>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly deeplinkService: DeeplinkService,
    private readonly qrCodeService: QrCodeService,
    private readonly fileRegistryService: FileRegistryService
  ) {}

  /**
   * Share a diary entry
   * @param entryId The ID of the diary entry
   * @param userId The ID of the user sharing the entry
   * @param shareDiaryEntryDto Data for sharing the diary entry
   * @returns The share details
   */
  async shareDiaryEntry(entryId: string, userId: string, shareDiaryEntryDto: ShareDiaryEntryDto): Promise<DiaryShareResponseDto> {
    this.logger.log(`User ${userId} is sharing diary entry ${entryId}`);
    // Check if the user is a student
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['userRoles', 'userRoles.role'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Check if the user has the student role
    const isStudent = user.userRoles.some(userRole => userRole.role.name === 'student');
    if (!isStudent) {
      throw new ForbiddenException('Only students can share diary entries');
    }

    const entry = await this.diaryEntryRepository.findOne({
      where: { id: entryId },
      relations: ['diary'],
    });

    if (!entry) {
      throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
    }

    // Check if the user is the owner of the diary
    const diary = await this.diaryRepository.findOne({
      where: { id: entry.diaryId },
    });

    if (diary.userId !== userId) {
      throw new ForbiddenException('You do not have permission to share this diary entry');
    }

    // Make the entry public
    entry.isPrivate = false;
    entry.visibility = DiaryVisibility.PUBLIC;
    await this.diaryEntryRepository.save(entry);

    // Generate a unique token
    const shareToken = uuidv4();

    // Create share record
    const share = this.diaryShareRepository.create({
      diaryEntryId: entryId,
      shareToken: shareToken,
      expiryDate: shareDiaryEntryDto.expiryDate ? toUTCDate(new Date(shareDiaryEntryDto.expiryDate)) : null,
      isActive: true,
    });

    const savedShare = await this.diaryShareRepository.save(share);

    // Generate share URL using DeeplinkService
    const shareUrl = this.deeplinkService.getWebLink(DeeplinkType.DIARY_SHARE, {
      token: shareToken
    });

    // Generate QR code for the share URL
    const qrBuffer = await this.qrCodeService.generateQrCode(shareUrl);

    // Generate a unique filename for the QR code
    const filename = `diary-qr-${entryId}-${Date.now()}.png`;

    // Save the QR code to the file system
    const qrResult = await this.qrCodeService.generateAndSaveQrCode(shareUrl, filename);

    // Register the QR code in the file registry
    const qrRegistry = await this.fileRegistryService.registerFile(
      FileEntityType.DIARY_QR,
      entryId,
      qrResult.filePath,
      filename,
      'image/png',
      qrResult.fileSize,
      userId,
      shareUrl
    );

    // Get the QR code URL
    const qrCodeUrl = await this.fileRegistryService.getFileUrl(FileEntityType.DIARY_QR, entryId);

    return {
      id: savedShare.id,
      shareToken: savedShare.shareToken,
      shareUrl,
      qrCodeUrl,
      expiryDate: savedShare.expiryDate,
      isActive: savedShare.isActive,
      createdAt: savedShare.createdAt,
    };
  }
  /**
   * Get all publicly shared diary entries
   * @param paginationDto Pagination details
   * @param mapEntryToResponseDto Function to map the entry to a response DTO
   * @returns A paginated list of publicly shared diary entries
   */
  async getPubliclySharedEntries(
    paginationDto: { page: number; limit: number },
    mapEntryToResponseDto: (entry: DiaryEntry) => Promise<DiaryEntryResponseDto>
  ): Promise<{ data: DiaryEntryResponseDto[]; total: number; page: number; limit: number }> {
    this.logger.log('Fetching all publicly shared diary entries with pagination');

    const { page = 1, limit = 10 } = paginationDto;    const queryBuilder = this.diaryEntryRepository.createQueryBuilder('entry')
      .leftJoinAndSelect('entry.diary', 'diary')
      .leftJoinAndSelect('diary.user', 'user')
      .leftJoinAndSelect('entry.skin', 'skin')
      .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
      .leftJoinAndSelect('feedbacks.tutor', 'tutor')
      .leftJoinAndSelect('entry.likes', 'likes')
      .where('entry.isPrivate = :isPrivate', { isPrivate: false })
      .andWhere('entry.visibility = :visibility', { visibility: DiaryVisibility.PUBLIC })
      .andWhere('entry.diaryId IS NOT NULL')
      .skip((page - 1) * limit)
      .take(limit);

    const [entries, total] = await queryBuilder.getManyAndCount();

    // Map each entry to the response DTO
    const data = await Promise.all(entries.map(entry => mapEntryToResponseDto(entry)));

    return {
      data,
      total,
      page,
      limit,
    };
  }

  /**
   * Get a shared diary entry by token
   * @param shareToken The share token
   * @param mapEntryToResponseDto Function to map the entry to a response DTO
   * @returns The shared diary entry
   */
  async getSharedDiaryEntry(
    shareToken: string,
    mapEntryToResponseDto: (entry: DiaryEntry) => Promise<DiaryEntryResponseDto>
  ): Promise<DiaryEntryResponseDto> {
    this.logger.log(`Accessing shared diary entry with token ${shareToken}`);
    const share = await this.diaryShareRepository.findOne({
      where: { shareToken: shareToken, isActive: true },
      relations: ['diaryEntry', 'diaryEntry.skin', 'diaryEntry.feedbacks', 'diaryEntry.feedbacks.tutor', 'diaryEntry.diary', 'diaryEntry.diary.user'],
    });

    if (!share) {
      throw new NotFoundException('Shared diary entry not found or link has expired');
    }

    // Check if the share has expired
    const now = getCurrentUTCDate();
    if (share.expiryDate && share.expiryDate < now) {
      throw new NotFoundException('Shared diary entry link has expired');
    }

    const entry = share.diaryEntry;

    // Use the provided mapping function to include diary details
    return await mapEntryToResponseDto(entry);
  }

  /**
   * Deactivate a shared diary entry
   * @param shareId The ID of the share to deactivate
   * @param userId The ID of the user deactivating the share
   */
  async deactivateShare(shareId: string, userId: string): Promise<void> {
    const share = await this.diaryShareRepository.findOne({
      where: { id: shareId },
      relations: ['diaryEntry', 'diaryEntry.diary'],
    });

    if (!share) {
      throw new NotFoundException(`Share with ID ${shareId} not found`);
    }

    // Check if the user is the owner of the diary
    if (share.diaryEntry.diary.userId !== userId) {
      throw new ForbiddenException('You do not have permission to deactivate this share');
    }

    // Deactivate the share
    share.isActive = false;
    await this.diaryShareRepository.save(share);
  }

  /**
   * Make a diary entry private
   * @param entryId The ID of the diary entry
   * @param userId The ID of the user making the entry private
   */
  async makeEntryPrivate(entryId: string, userId: string): Promise<void> {
    this.logger.log(`User ${userId} is making diary entry ${entryId} private`);

    const entry = await this.diaryEntryRepository.findOne({
      where: { id: entryId },
      relations: ['diary'],
    });

    if (!entry) {
      throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
    }

    // Check if the user is the owner of the diary
    if (entry.diary.userId !== userId) {
      throw new ForbiddenException('You do not have permission to modify this diary entry');
    }

    // Make the entry private
    entry.isPrivate = true;
    entry.visibility = DiaryVisibility.PRIVATE;
    await this.diaryEntryRepository.save(entry);

    // Deactivate all shares for this entry
    const shares = await this.diaryShareRepository.find({
      where: { diaryEntryId: entryId },
    });

    for (const share of shares) {
      share.isActive = false;
      await this.diaryShareRepository.save(share);
    }

    this.logger.log(`Diary entry ${entryId} has been made private and all shares deactivated`);
  }

  /**
   * Get all shares for a diary entry
   * @param entryId The ID of the diary entry
   * @param userId The ID of the user requesting the shares
   * @returns The shares for the diary entry
   */
  async getSharesForEntry(entryId: string, userId: string): Promise<DiaryShareResponseDto[]> {
    const entry = await this.diaryEntryRepository.findOne({
      where: { id: entryId },
      relations: ['diary'],
    });

    if (!entry) {
      throw new NotFoundException(`Diary entry with ID ${entryId} not found`);
    }

    // Check if the user is the owner of the diary
    if (entry.diary.userId !== userId) {
      throw new ForbiddenException('You do not have permission to view shares for this diary entry');
    }

    // Get all shares for the entry
    const shares = await this.diaryShareRepository.find({
      where: { diaryEntryId: entryId },
      order: { createdAt: 'DESC' },
    });

    // Generate share URLs using DeeplinkService
    return shares.map(share => ({
      id: share.id,
      shareToken: share.shareToken,
      shareUrl: this.deeplinkService.getWebLink(DeeplinkType.DIARY_SHARE, {
        token: share.shareToken
      }),
      expiryDate: share.expiryDate,
      isActive: share.isActive,
      createdAt: share.createdAt,
    }));
  }
}
