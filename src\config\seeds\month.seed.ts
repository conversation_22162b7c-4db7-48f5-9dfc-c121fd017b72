import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QAMissionMonth as Month } from '../../database/entities/qa-mission-month.entity';

@Injectable()
export class MonthSeed {
  private readonly logger = new Logger(MonthSeed.name);

  constructor(
    @InjectRepository(Month)
    private readonly monthRepository: Repository<Month>,
  ) {}

  async seed(): Promise<void> {
    this.logger.log('Seeding months...');

    const currentYear = new Date().getFullYear();

    const months = [
      { title: 'January', display: 'January', sequence: 1 },
      { title: 'February', display: 'February', sequence: 2 },
      { title: 'March', display: 'March', sequence: 3 },
      { title: 'April', display: 'April', sequence: 4 },
      { title: 'May', display: 'May', sequence: 5 },
      { title: 'June', display: 'June', sequence: 6 },
      { title: 'July', display: 'July', sequence: 7 },
      { title: 'August', display: 'August', sequence: 8 },
      { title: 'September', display: 'September', sequence: 9 },
      { title: 'October', display: 'October', sequence: 10 },
      { title: 'November', display: 'November', sequence: 11 },
      { title: 'December', display: 'December', sequence: 12 }
    ];

    for (const monthData of months) {
      // Check if month already exists for current year
      const existingMonth = await this.monthRepository.findOne({
        where: {
          title: monthData.title,
          year: currentYear
        }
      });

      if (!existingMonth) {
        const month = this.monthRepository.create({
          ...monthData,
          year: currentYear
        });
        await this.monthRepository.save(month);
        this.logger.log(`Created month: ${month.title} ${month.year}`);
      } else {
        this.logger.log(`Month already exists: ${existingMonth.title} ${existingMonth.year}`);
      }
    }

    this.logger.log('Month seeding completed');
  }
}