import { MigrationInterface, QueryRunner } from "typeorm";

export class QAMissionSequenceType1747126410535 implements MigrationInterface {
    name = 'QAMissionSequenceType1747126410535'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_monthly_mission_tasks" DROP COLUMN "deadline"`);
        await queryRunner.query(`ALTER TABLE "qa_monthly_mission_tasks" ADD "deadline" integer`);
        await queryRunner.query(`ALTER TABLE "qa_weekly_mission_tasks" DROP COLUMN "deadline"`);
        await queryRunner.query(`ALTER TABLE "qa_weekly_mission_tasks" ADD "deadline" integer`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_weekly_mission_tasks" DROP COLUMN "deadline"`);
        await queryRunner.query(`ALTER TABLE "qa_weekly_mission_tasks" ADD "deadline" date`);
        await queryRunner.query(`ALTER TABLE "qa_monthly_mission_tasks" DROP COLUMN "deadline"`);
        await queryRunner.query(`ALTER TABLE "qa_monthly_mission_tasks" ADD "deadline" date`);
    }

}
