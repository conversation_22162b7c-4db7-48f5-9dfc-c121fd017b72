import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDiaryEntryFriendShare1704067200000 implements MigrationInterface {
  name = 'AddDiaryEntryFriendShare1704067200000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if table already exists
    const tableExists = await queryRunner.hasTable('diary_entry_friend_share');
    if (tableExists) {
      console.log('Table diary_entry_friend_share already exists, skipping creation');
      return;
    }

    // Create diary_entry_friend_share table
    await queryRunner.query(`
      CREATE TABLE "diary_entry_friend_share" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "diary_entry_id" uuid NOT NULL,
        "shared_by_id" uuid NOT NULL,
        "shared_with_id" uuid NOT NULL,
        "message" text,
        "share_token" character varying(255) NOT NULL,
        "expiry_date" TIMESTAMP,
        "is_active" boolean NOT NULL DEFAULT true,
        "chat_message_id" uuid,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        CONSTRAINT "PK_diary_entry_friend_share" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_diary_entry_friend_share_token" UNIQUE ("share_token")
      )
    `);

    // Create indexes
    await queryRunner.query(`CREATE INDEX "IDX_diary_entry_friend_share_diary_entry_id" ON "diary_entry_friend_share" ("diary_entry_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_diary_entry_friend_share_shared_by_id" ON "diary_entry_friend_share" ("shared_by_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_diary_entry_friend_share_shared_with_id" ON "diary_entry_friend_share" ("shared_with_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_diary_entry_friend_share_share_token" ON "diary_entry_friend_share" ("share_token")`);
    await queryRunner.query(`CREATE INDEX "IDX_diary_entry_friend_share_is_active" ON "diary_entry_friend_share" ("is_active")`);
    await queryRunner.query(`CREATE INDEX "IDX_diary_entry_friend_share_unique_active" ON "diary_entry_friend_share" ("diary_entry_id", "shared_by_id", "shared_with_id", "is_active")`);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "diary_entry_friend_share"
      ADD CONSTRAINT "FK_diary_entry_friend_share_diary_entry"
      FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "diary_entry_friend_share"
      ADD CONSTRAINT "FK_diary_entry_friend_share_shared_by"
      FOREIGN KEY ("shared_by_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "diary_entry_friend_share"
      ADD CONSTRAINT "FK_diary_entry_friend_share_shared_with"
      FOREIGN KEY ("shared_with_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    console.log('Created diary_entry_friend_share table with indexes and foreign keys');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_entry_friend_share_unique_active"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_entry_friend_share_is_active"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_entry_friend_share_share_token"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_entry_friend_share_shared_with_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_entry_friend_share_shared_by_id"`);
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_diary_entry_friend_share_diary_entry_id"`);

    // Drop foreign key constraints
    await queryRunner.query(`ALTER TABLE "diary_entry_friend_share" DROP CONSTRAINT IF EXISTS "FK_diary_entry_friend_share_shared_with"`);
    await queryRunner.query(`ALTER TABLE "diary_entry_friend_share" DROP CONSTRAINT IF EXISTS "FK_diary_entry_friend_share_shared_by"`);
    await queryRunner.query(`ALTER TABLE "diary_entry_friend_share" DROP CONSTRAINT IF EXISTS "FK_diary_entry_friend_share_diary_entry"`);

    // Drop table
    await queryRunner.query(`DROP TABLE IF EXISTS "diary_entry_friend_share"`);
    console.log('Dropped diary_entry_friend_share table');
  }
}
