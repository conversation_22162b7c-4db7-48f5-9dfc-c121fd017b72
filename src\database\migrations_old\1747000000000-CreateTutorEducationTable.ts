import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTutorEducationTable1747000000000 implements MigrationInterface {
    name = 'CreateTutorEducationTable1747000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "tutor_education" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "created_by" uuid,
                "updated_by" uuid,
                "tutor_id" uuid NOT NULL,
                "degree" character varying(100) NOT NULL,
                "institution" character varying(255) NOT NULL,
                "field_of_study" character varying(255) NOT NULL,
                "start_date" date NOT NULL,
                "end_date" date,
                "is_current" boolean NOT NULL DEFAULT false,
                "description" text,
                "location" character varying(255),
                "grade" character varying(50),
                "activities" text,
                CONSTRAINT "PK_tutor_education" PRIMARY KEY ("id")
            )
        `);
        
        await queryRunner.query(`
            ALTER TABLE "tutor_education" 
            ADD CONSTRAINT "FK_tutor_education_tutor" 
            FOREIGN KEY ("tutor_id") REFERENCES "user"("id") 
            ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "tutor_education" 
            DROP CONSTRAINT "FK_tutor_education_tutor"
        `);
        
        await queryRunner.query(`
            DROP TABLE "tutor_education"
        `);
    }
}
