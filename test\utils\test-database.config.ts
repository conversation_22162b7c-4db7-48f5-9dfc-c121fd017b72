import { DataSourceOptions } from 'typeorm';
import { SnakeNamingStrategy } from '../../src/common/strategies/snake-naming.strategy';

/**
 * Test database configuration
 * Uses a dedicated test database to avoid polluting production data
 */
export const getTestDatabaseConfig = (): DataSourceOptions => ({
  type: 'postgres',
  host: process.env.DATABASE_HOST || '**************',
  port: parseInt(process.env.DATABASE_PORT || '5432'),
  username: process.env.DATABASE_USER || 'hec_user',
  password: process.env.DATABASE_PASSWORD || '123456_Az',
  database: process.env.DATABASE_NAME || 'hec_db_test', // Dedicated test database
  entities: [__dirname + '/../../src/database/entities/*.entity{.ts,.js}'],
  synchronize: false, // Don't auto-sync in tests
  logging: false,
  namingStrategy: new SnakeNamingStrategy(),
  ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false,
});

/**
 * Alternative PostgreSQL test database configuration
 * Use this if you need PostgreSQL-specific features in tests
 */
export const getPostgresTestDatabaseConfig = (): DataSourceOptions => ({
  type: 'postgres',
  host: process.env.TEST_DATABASE_HOST || 'localhost',
  port: parseInt(process.env.TEST_DATABASE_PORT || '5432'),
  username: process.env.TEST_DATABASE_USER || 'test',
  password: process.env.TEST_DATABASE_PASSWORD || 'test',
  database: process.env.TEST_DATABASE_NAME || 'hec_test',
  entities: [__dirname + '/../../src/database/entities/*.entity{.ts,.js}'],
  synchronize: true,
  dropSchema: true,
  logging: false,
  namingStrategy: new SnakeNamingStrategy(),
  ssl: false,
});
