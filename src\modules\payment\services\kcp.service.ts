import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KcpConfigService } from './kcp-config.service';
import {
  KcpTradeRegRequest,
  KcpTradeRegResponse,
  KcpPaymentRequest,
  KcpPaymentResponse,
  PaymentInitiationRequest,
  PaymentInitiationResponse
} from '../interfaces/kcp.interface';

@Injectable()
export class KcpService {
  private readonly logger = new Logger(KcpService.name);

  constructor(
    private readonly kcpConfig: KcpConfigService,
    private readonly configService: ConfigService
  ) {}

  /**
   * Register trade with KCP
   */
  async registerTrade(request: PaymentInitiationRequest): Promise<KcpTradeRegResponse> {
    try {
      this.logger.log(`Registering trade for order: ${request.orderId}`);

      const tradeRegRequest: KcpTradeRegRequest = {
        site_cd: this.kcpConfig.getSiteCd(),
        kcp_cert_info: this.kcpConfig.getKcpCertInfo(),
        ordr_idxx: request.orderId,
        good_name: request.productName,
        good_mny: request.amount,
        buyr_name: request.buyerName,
        buyr_mail: request.buyerEmail,
        buyr_tel1: request.buyerPhone,
        currency: request.currency || 'KRW',
        shop_user_id: request.userId,
        pay_method: this.getPayMethodCode(request.paymentMethod),
        user_agent: 'HEC-Backend/1.0',
        remote_addr: '127.0.0.1' // This should be the actual client IP
      };

      // Simulate KCP API call for now
      // In production, this would make actual HTTP request to KCP
      const response = {
        data: {
          res_cd: '0000',
          res_msg: 'SUCCESS',
          tno: `TXN-${Date.now()}`,
          amount: request.amount.toString(),
          pnt_issue: '0',
          trace_no: `TRACE-${Date.now()}`,
          PayUrl: this.kcpConfig.getPaymentUrl(),
          ordr_chk: this.kcpConfig.generateOrderCheck(request.orderId, request.amount),
          kcp_sign_data: `SIGN-${Date.now()}`
        } as KcpTradeRegResponse
      };

      if (response.data.res_cd !== '0000') {
        throw new BadRequestException(`KCP Trade Registration failed: ${response.data.res_msg}`);
      }

      this.logger.log(`Trade registered successfully: ${response.data.tno}`);
      return response.data;

    } catch (error) {
      this.logger.error(`Trade registration failed: ${error.message}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to register trade with KCP');
    }
  }

  /**
   * Process payment with KCP
   */
  async processPayment(
    tradeRegResponse: KcpTradeRegResponse,
    request: PaymentInitiationRequest
  ): Promise<KcpPaymentResponse> {
    try {
      this.logger.log(`Processing payment for transaction: ${tradeRegResponse.tno}`);

      const paymentRequest: KcpPaymentRequest = {
        site_cd: this.kcpConfig.getSiteCd(),
        kcp_cert_info: this.kcpConfig.getKcpCertInfo(),
        tran_cd: '00100000',
        ordr_idxx: request.orderId,
        good_name: request.productName,
        good_mny: request.amount,
        buyr_name: request.buyerName,
        buyr_mail: request.buyerEmail,
        buyr_tel1: request.buyerPhone,
        pay_method: this.getPayMethodCode(request.paymentMethod),
        Ret_URL: request.returnUrl,
        currency: request.currency || 'KRW',
        shop_user_id: request.userId,
        user_agent: 'HEC-Backend/1.0',
        remote_addr: '127.0.0.1',
        ordr_chk: tradeRegResponse.ordr_chk,
        kcp_sign_data: tradeRegResponse.kcp_sign_data
      };

      // Simulate KCP payment processing for now
      // In production, this would make actual HTTP request to KCP
      const response = {
        data: {
          res_cd: '0000',
          res_msg: 'SUCCESS',
          tno: tradeRegResponse.tno,
          amount: request.amount.toString(),
          pnt_issue: '0',
          trace_no: `TRACE-${Date.now()}`,
          app_time: new Date().toISOString(),
          app_no: `APP-${Date.now()}`,
          card_cd: '01',
          card_name: 'Test Card'
        } as KcpPaymentResponse
      };

      if (response.data.res_cd !== '0000') {
        throw new BadRequestException(`KCP Payment failed: ${response.data.res_msg}`);
      }

      this.logger.log(`Payment processed successfully: ${response.data.tno}`);
      return response.data;

    } catch (error) {
      this.logger.error(`Payment processing failed: ${error.message}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to process payment with KCP');
    }
  }

  /**
   * Initiate payment process
   */
  async initiatePayment(request: PaymentInitiationRequest): Promise<PaymentInitiationResponse> {
    try {
      this.logger.log(`Initiating payment for order: ${request.orderId}`);

      // Step 1: Register trade
      const tradeRegResponse = await this.registerTrade(request);

      // Step 2: Generate payment URL
      const paymentUrl = this.generatePaymentUrl(tradeRegResponse, request);

      // Calculate expiration time (30 minutes from now)
      const expiresAt = new Date(Date.now() + 30 * 60 * 1000);

      return {
        success: true,
        transactionId: tradeRegResponse.tno,
        paymentUrl: paymentUrl,
        redirectUrl: paymentUrl,
        message: 'Payment initiated successfully',
        expiresAt: expiresAt
      };

    } catch (error) {
      this.logger.error(`Payment initiation failed: ${error.message}`, error.stack);
      return {
        success: false,
        transactionId: '',
        message: error.message || 'Payment initiation failed',
        errorCode: error.code || 'PAYMENT_INIT_ERROR'
      };
    }
  }

  /**
   * Verify payment result
   */
  async verifyPayment(kcpResponse: any): Promise<boolean> {
    try {
      // Implement payment verification logic
      // This would typically involve checking the signature and status
      if (kcpResponse.res_cd === '0000') {
        this.logger.log(`Payment verified successfully: ${kcpResponse.tno}`);
        return true;
      } else {
        this.logger.warn(`Payment verification failed: ${kcpResponse.res_msg}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`Payment verification error: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Cancel payment
   */
  async cancelPayment(transactionId: string, reason: string): Promise<boolean> {
    try {
      this.logger.log(`Cancelling payment: ${transactionId}`);

      // Implement KCP payment cancellation logic
      // This would involve calling KCP's cancellation API

      this.logger.log(`Payment cancelled successfully: ${transactionId}`);
      return true;
    } catch (error) {
      this.logger.error(`Payment cancellation failed: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Process refund
   */
  async processRefund(transactionId: string, amount: number, reason: string): Promise<any> {
    try {
      this.logger.log(`Processing refund for transaction: ${transactionId}`);

      // Implement KCP refund logic
      // This would involve calling KCP's refund API

      this.logger.log(`Refund processed successfully: ${transactionId}`);
      return {
        success: true,
        refundId: `REF-${Date.now()}`,
        amount: amount,
        message: 'Refund processed successfully'
      };
    } catch (error) {
      this.logger.error(`Refund processing failed: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to process refund');
    }
  }

  /**
   * Get payment method code for KCP
   */
  private getPayMethodCode(paymentMethod: string): string {
    const payMethodMap = {
      'card': '************',    // Credit card
      'bank': '************',    // Bank transfer
      'mobile': '************',  // Mobile payment
      'vacct': '************'    // Virtual account
    };

    return payMethodMap[paymentMethod] || '************';
  }

  /**
   * Generate payment URL
   */
  private generatePaymentUrl(tradeRegResponse: KcpTradeRegResponse, request: PaymentInitiationRequest): string {
    const baseUrl = this.configService.get<string>('API_URL', 'http://localhost:3012');
    const params = new URLSearchParams({
      tno: tradeRegResponse.tno,
      ordr_idxx: request.orderId,
      amount: request.amount.toString(),
      pay_method: this.getPayMethodCode(request.paymentMethod),
      ordr_chk: tradeRegResponse.ordr_chk,
      kcp_sign_data: tradeRegResponse.kcp_sign_data
    });

    return `${baseUrl}/payment/kcp/redirect?${params.toString()}`;
  }

  /**
   * Validate webhook signature
   */
  validateWebhookSignature(payload: string, signature: string): boolean {
    return this.kcpConfig.validateWebhookSignature(payload, signature);
  }
}
