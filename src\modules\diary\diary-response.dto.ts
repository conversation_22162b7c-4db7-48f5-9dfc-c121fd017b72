import { ApiProperty } from '@nestjs/swagger';
import {
  DiaryEntryResponseDto,
  DiarySkinResponseDto,
  DiaryShareResponseDto,
  StudentTutorListResponseDto,
  DiaryAwardsResponseDto,
  DiaryPeriodAwardsResponseDto
} from '../../database/models/diary.dto';

/**
 * Base API response wrapper
 */
export class BaseApiResponse {
  @ApiProperty({ example: true, description: 'Indicates if the request was successful' })
  success: boolean;

  @ApiProperty({ description: 'Response message', example: 'Operation completed successfully' })
  message: string;

  @ApiProperty({ description: 'Timestamp of the response', example: '2023-07-25T12:34:56.789Z' })
  timestamp: string;
}

/**
 * API response for diary skins list
 */
export class DiarySkinListResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'List of diary skins',
    type: [DiarySkinResponseDto]
  })
  data: DiarySkinResponseDto[];
}

/**
 * API response for a single diary entry
 */
export class DiaryEntryResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'Diary entry details',
    type: DiaryEntryResponseDto
  })
  data: DiaryEntryResponseDto;
}

/**
 * API response for a list of diary entries
 */
export class DiaryEntryListResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'List of diary entries',
    type: [DiaryEntryResponseDto]
  })
  data: DiaryEntryResponseDto[];
}

/**
 * API response for diary share
 */
export class DiaryShareResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'Diary share details',
    type: DiaryShareResponseDto
  })
  data: DiaryShareResponseDto;
}

/**
 * API response for student tutors list
 */
export class StudentTutorListResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'List of tutors who have reviewed the student\'s diary entries',
    type: StudentTutorListResponseDto
  })
  data: StudentTutorListResponseDto;
}

/**
 * API response for student awards
 */
export class DiaryAwardsResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'Summary of awards received by the student',
    type: DiaryAwardsResponseDto
  })
  data: DiaryAwardsResponseDto;
}

/**
 * API response for student period awards
 */
export class DiaryPeriodAwardsResponse extends BaseApiResponse {
  @ApiProperty({
    description: 'Awards received by the student for top scores in weekly/monthly periods',
    type: DiaryPeriodAwardsResponseDto
  })
  data: DiaryPeriodAwardsResponseDto;
}

/**
 * Error response for validation errors
 */
export class ValidationErrorResponse extends BaseApiResponse {
  @ApiProperty({ example: false, description: 'Indicates that the request failed' })
  success: boolean;

  @ApiProperty({
    example: {
      title: ['title must not be empty'],
      content: ['content must not be empty']
    },
    description: 'Validation errors by field'
  })
  error: Record<string, string[]>;
}

/**
 * Error response for general errors
 */
export class GeneralErrorResponse extends BaseApiResponse {
  @ApiProperty({ example: false, description: 'Indicates that the request failed' })
  success: boolean;

  @ApiProperty({ example: 'An error occurred', description: 'Error message' })
  message: string;

  @ApiProperty({ example: 'Internal server error', description: 'Error details', required: false })
  error?: string;
}
