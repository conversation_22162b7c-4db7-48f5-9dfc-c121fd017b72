import { Controller, Post, Get, Query, UseGuards, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { SimplifiedAwardScheduler } from './simplified-award.scheduler';
import { AdminGuard } from '../../common/guards/admin.guard';

@ApiTags('Award Scheduler')
@Controller('award-scheduler')
@UseGuards(AdminGuard)
@ApiBearerAuth('JWT-auth')
export class SimplifiedAwardSchedulerController {
  constructor(
    private readonly awardScheduler: SimplifiedAwardScheduler,
  ) {}

  /**
   * Get scheduler status and next run information
   */
  @Get('status')
  @ApiOperation({
    summary: 'Get award scheduler status (Admin only)',
    description: 'Returns the current status of the award scheduler including processing flags and schedule information. Requires admin authentication.'
  })
  @ApiResponse({
    status: 200,
    description: 'Scheduler status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        monthlyProcessing: { type: 'boolean', example: false },
        annualProcessing: { type: 'boolean', example: false },
        nextMonthlyRun: { type: 'string', example: 'Every 1st of month at 02:00 UTC (All modules)' },
        nextAnnualRun: { type: 'string', example: 'January 1st at 03:00 UTC (All modules)' },
        lastMonthlyPeriod: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
            month: { type: 'number' },
            year: { type: 'number' }
          }
        },
        lastAnnualPeriod: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
            year: { type: 'number' }
          }
        }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Admin access required' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin role required' })
  getSchedulerStatus() {
    // Return the status directly without extra wrapping
    return this.awardScheduler.getStatus();
  }

  /**
   * Manually trigger monthly award generation
   */
  @Post('trigger/monthly')
  @ApiOperation({ summary: 'Manually trigger monthly award generation' })
  @ApiQuery({ name: 'year', required: false, description: 'Year (defaults to current year)' })
  @ApiQuery({ name: 'month', required: false, description: 'Month 1-12 (defaults to previous month)' })
  @ApiResponse({ status: 200, description: 'Monthly awards generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Error generating awards' })
  async triggerMonthlyAwards(
    @Query('year', new ParseIntPipe({ optional: true })) year?: number,
    @Query('month', new ParseIntPipe({ optional: true })) month?: number,
  ) {
    await this.awardScheduler.triggerMonthlyAwards(year, month);
    return {
      message: `Monthly awards generated successfully for ${month || 'previous month'}/${year || 'current year'}`,
      period: {
        year: year || new Date().getFullYear(),
        month: month || (new Date().getMonth() === 0 ? 12 : new Date().getMonth())
      }
    };
  }

  /**
   * Manually trigger annual award generation
   */
  @Post('trigger/annual')
  @ApiOperation({ summary: 'Manually trigger annual award generation' })
  @ApiQuery({ name: 'year', required: false, description: 'Year (defaults to previous year)' })
  @ApiResponse({ status: 200, description: 'Annual awards generated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 500, description: 'Error generating awards' })
  async triggerAnnualAwards(
    @Query('year', new ParseIntPipe({ optional: true })) year?: number,
  ) {
    await this.awardScheduler.triggerAnnualAwards(year);
    return {
      message: `Annual awards generated successfully for ${year || 'previous year'}`,
      period: {
        year: year || (new Date().getFullYear() - 1)
      }
    };
  }
}
