import { ApiProperty } from '@nestjs/swagger';
import { UserType } from '../../../database/entities/user.entity';

/**
 * Base profile view DTO with common properties for all user types
 */
export class BaseProfileViewDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000', description: 'User ID' })
  id: string;

  @ApiProperty({ example: '<PERSON>', description: 'User name' })
  name: string;

  @ApiProperty({ example: 'john123', description: 'User ID for login' })
  userId: string;

  @ApiProperty({ example: '<EMAIL>', description: 'User email' })
  email: string;

  @ApiProperty({ example: 'admin', description: 'User type', enum: UserType })
  type: UserType;

  @ApiProperty({ example: true, description: 'Whether the user is active' })
  isActive: boolean;

  @ApiProperty({ example: true, description: 'Whether the user is confirmed' })
  isConfirmed: boolean;

  @ApiProperty({ example: 'http://example.com/profile-picture.jpg', description: 'Profile picture URL', required: false })
  profilePictureUrl?: string;

  @ApiProperty({ example: 'profile-pictures/user123-abc123.jpg', description: 'Profile picture path', required: false })
  profilePicture?: string;

  @ApiProperty({ example: '+1234567890', description: 'User phone number', required: false })
  phoneNumber?: string;

  @ApiProperty({ example: '123 Main St', description: 'User address', required: false })
  address?: string;

  @ApiProperty({ example: 'New York', description: 'User city', required: false })
  city?: string;

  @ApiProperty({ example: 'NY', description: 'User state', required: false })
  state?: string;

  @ApiProperty({ example: 'USA', description: 'User country', required: false })
  country?: string;

  @ApiProperty({ example: '10001', description: 'User postal code', required: false })
  postalCode?: string;

  @ApiProperty({ example: 'male', description: 'User gender', required: false })
  gender?: string;

  @ApiProperty({ example: '1990-01-01', description: 'User date of birth in YYYY-MM-DD format', required: false })
  dateOfBirth?: string;

  @ApiProperty({ example: 33, description: 'User age calculated from date of birth', required: false })
  age?: number;

  @ApiProperty({ example: { linkedin: 'https://linkedin.com/in/johndoe', twitter: 'https://twitter.com/johndoe' }, description: 'User social links', required: false })
  socialLinks?: { [key: string]: string };

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'Date when the user last logged in', required: false })
  lastLoginAt?: Date;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'Date when the user was created', required: false })
  createdAt?: Date;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'Date when the user was last updated', required: false })
  updatedAt?: Date;
}

/**
 * Admin profile view DTO
 */
export class AdminProfileViewDto extends BaseProfileViewDto {
  @ApiProperty({ example: ['admin'], description: 'User roles' })
  roles: string[];

  @ApiProperty({ example: 'I am an administrator with 5 years of experience.', description: 'Admin bio', required: false })
  bio?: string;

  @ApiProperty({ example: [{ id: '123e4567-e89b-12d3-a456-426614174000', name: 'admin' }], description: 'Detailed user roles information' })
  userRoles?: any[];
}

/**
 * Tutor profile view DTO
 */
export class TutorProfileViewDto extends BaseProfileViewDto {
  @ApiProperty({ example: ['tutor'], description: 'User roles' })
  roles: string[];

  @ApiProperty({ example: 'I am an experienced English teacher with 5 years of teaching experience.', description: 'Tutor bio', required: false })
  bio?: string;

  @ApiProperty({ example: true, description: 'Whether the tutor is approved' })
  isApproved: boolean;

  @ApiProperty({ example: 10, description: 'Number of students assigned to this tutor', required: false })
  studentCount?: number;

  @ApiProperty({ example: [{ id: '123e4567-e89b-12d3-a456-426614174000', name: 'tutor' }], description: 'Detailed user roles information' })
  userRoles?: any[];

  @ApiProperty({ example: [{ id: '123e4567-e89b-12d3-a456-426614174000', studentId: '123e4567-e89b-12d3-a456-426614174000', studentName: 'John Doe' }], description: 'Students assigned to this tutor' })
  assignedStudents?: any[];

  @ApiProperty({ example: [{ id: '123e4567-e89b-12d3-a456-426614174000', moduleId: '123e4567-e89b-12d3-a456-426614174000', moduleName: 'Diary' }], description: 'Modules this tutor is assigned to' })
  assignedModules?: any[];

  @ApiProperty({
    example: [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        tutorId: '123e4567-e89b-12d3-a456-426614174000',
        degree: 'Bachelor of Science',
        institution: 'Harvard University',
        fieldOfStudy: 'Computer Science',
        startDate: '2015-09-01',
        endDate: '2019-06-30',
        isCurrent: false,
        description: 'Studied advanced algorithms and data structures',
        location: 'Cambridge, MA',
        grade: '3.8 GPA',
        activities: 'Member of Computer Science Club, Participated in Hackathons',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z'
      }
    ],
    description: 'Education information for this tutor'
  })
  education?: any[];
}

/**
 * Student profile view DTO
 */
export class StudentProfileViewDto extends BaseProfileViewDto {
  @ApiProperty({ example: ['student'], description: 'User roles' })
  roles: string[];

  @ApiProperty({ example: 'I am a student at XYZ University.', description: 'Student bio', required: false })
  bio?: string;

  @ApiProperty({ example: 'Pro', description: 'Active subscription plan', required: false })
  activePlan?: string;

  @ApiProperty({ example: '2023-12-31', description: 'Subscription expiry date', required: false })
  planExpiryDate?: string;

  @ApiProperty({ example: [{ id: '123e4567-e89b-12d3-a456-426614174000', name: 'student' }], description: 'Detailed user roles information' })
  userRoles?: any[];

  @ApiProperty({
    example: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      userId: '123e4567-e89b-12d3-a456-426614174000',
      planId: '123e4567-e89b-12d3-a456-426614174000',
      startDate: '2023-01-01T00:00:00.000Z',
      endDate: '2023-02-01T00:00:00.000Z',
      isActive: true,
      isPaid: true,
      autoRenew: true,
      plan: {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Premium',
        type: 'pro',
        subscriptionType: 'monthly',
        description: 'Premium features',
        price: 29.99,
        features: [
          {
            id: '123e4567-e89b-12d3-a456-426614174000',
            type: 'hec_user_diary',
            name: 'HEC User Diary',
            description: 'Access to the HEC User Diary platform'
          }
        ]
      }
    },
    description: 'Detailed information about the active subscription plan',
    required: false
  })
  activePlanDetails?: any;

  @ApiProperty({ example: [{ id: '123e4567-e89b-12d3-a456-426614174000', tutorId: '123e4567-e89b-12d3-a456-426614174000', tutorName: 'Jane Doe', moduleId: '123e4567-e89b-12d3-a456-426614174000', moduleName: 'Diary' }], description: 'Tutors assigned to this student' })
  assignedTutors?: any[];

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000', description: 'Default skin ID for student users', required: false })
  defaultSkinId?: string;

  @ApiProperty({ example: [{ id: '123e4567-e89b-12d3-a456-426614174000', name: 'My Custom Skin' }], description: 'Skins owned by this student' })
  ownedSkins?: any[];
}
