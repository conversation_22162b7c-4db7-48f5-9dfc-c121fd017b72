import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, Index } from "typeorm";
import { AuditableBaseEntity } from "./base-entity";
import { IsUUID } from "class-validator";
import { DiarySkin } from "./diary-skin.entity";
import { EssayMissionTasks } from "./essay-mission-tasks.entity";

export enum SkinScopeType {
  MODULE_DEFAULT = "module_default",
  TASK_SPECIFIC = "task_specific"
}

@Entity()
@Index(["createdBy", "scopeType"])
@Index(["createdBy", "taskId"])
export class EssayModuleSkinPreference extends AuditableBaseEntity {
  @Column({
    name: "skin_id",
    type: "uuid",
    nullable: false
  })
  @IsUUID()
  skinId: string;

  @ManyToOne(() => DiarySkin)
  @JoinColumn({ name: "skin_id" })
  skin: DiarySkin;

  @Column({
    name: "scope_type",
    type: "enum",
    enum: SkinScopeType,
    nullable: false
  })
  scopeType: SkinScopeType;

  @Column({
    name: "task_id",
    type: "uuid",
    nullable: true
  })
  @IsUUID()
  taskId?: string;

  @ManyToOne(() => EssayMissionTasks, { nullable: true })
  @JoinColumn({ name: "task_id" })
  task?: EssayMissionTasks;

  @Column({
    name: "is_active",
    type: "boolean",
    default: true
  })
  isActive: boolean;
}