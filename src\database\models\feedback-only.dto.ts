import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO for creating feedback on a diary entry (feedback only)
 * @example
 * {
 *   "feedback": "Great work on your diary entry! I particularly liked your analysis of the topic."
 * }
 */
export class FeedbackOnlyDto {
  @ApiProperty({
    example: 'Great work on your diary entry! I particularly liked your analysis of the topic.',
    description: 'Feedback text for the student'
  })
  @IsNotEmpty()
  @IsString()
  feedback: string;
}
