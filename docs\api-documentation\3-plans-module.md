# Plans Module

The Plans Module handles subscription plan management, including creating, updating, and retrieving plans, as well as user subscriptions to plans.

## Epics

1. **Plan Management**
2. **Plan Feature Management**
3. **User Subscription Management**

## APIs

### 1. Get All Plans

**Endpoint:** `GET /plans`

**Description:** Retrieves all active subscription plans available in the system.

**Response:**
```json
{
  "success": true,
  "message": "Plans retrieved successfully",
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Starter",
      "type": "monthly",
      "description": "Basic plan for beginners",
      "price": 9.99,
      "durationDays": 30,
      "autoRenew": false,
      "isActive": true,
      "planFeatures": [
        {
          "id": "123e4567-e89b-12d3-a456-426614174000",
          "name": "Basic Diary",
          "description": "Access to basic diary features",
          "isActive": true
        }
      ],
      "legacyFeatures": [
        {
          "type": "diary",
          "name": "Basic Diary",
          "description": "Access to basic diary features",
          "isActive": true
        }
      ]
    },
    {
      "id": "223e4567-e89b-12d3-a456-426614174000",
      "name": "Pro",
      "type": "monthly",
      "description": "Advanced plan for serious users",
      "price": 19.99,
      "durationDays": 30,
      "autoRenew": true,
      "isActive": true,
      "planFeatures": [
        {
          "id": "123e4567-e89b-12d3-a456-426614174000",
          "name": "Basic Diary",
          "description": "Access to basic diary features",
          "isActive": true
        },
        {
          "id": "223e4567-e89b-12d3-a456-426614174000",
          "name": "Advanced Diary",
          "description": "Access to advanced diary features",
          "isActive": true
        }
      ],
      "legacyFeatures": []
    }
  ],
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Retrieve all active plans from database
2. Include plan features in the response
3. Transform plans to DTOs
4. Return plans list

### 2. Get Plan by ID

**Endpoint:** `GET /plans/:id`

**Description:** Retrieves a specific subscription plan by its ID.

**Path Parameters:**
- `id`: The ID of the plan to retrieve

**Response:**
```json
{
  "success": true,
  "message": "Plan retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Starter",
    "type": "monthly",
    "description": "Basic plan for beginners",
    "price": 9.99,
    "durationDays": 30,
    "autoRenew": false,
    "isActive": true,
    "planFeatures": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Basic Diary",
        "description": "Access to basic diary features",
        "isActive": true
      }
    ],
    "legacyFeatures": [
      {
        "type": "diary",
        "name": "Basic Diary",
        "description": "Access to basic diary features",
        "isActive": true
      }
    ]
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate plan ID
2. Retrieve plan with features
3. Transform plan to DTO
4. Return plan details

### 3. Create Plan

**Endpoint:** `POST /plans`

**Description:** Creates a new subscription plan. Only accessible by admins.

**Request Body:**
```json
{
  "name": "Ultimate",
  "type": "yearly",
  "description": "Ultimate plan with all features",
  "price": 199.99,
  "durationDays": 365,
  "autoRenew": true,
  "isActive": true,
  "legacyFeatures": [
    {
      "type": "diary",
      "name": "Premium Diary",
      "description": "Access to premium diary features",
      "isActive": true
    }
  ],
  "planFeatureIds": [
    "123e4567-e89b-12d3-a456-426614174000",
    "223e4567-e89b-12d3-a456-426614174000",
    "323e4567-e89b-12d3-a456-426614174000"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Plan created successfully",
  "data": {
    "id": "423e4567-e89b-12d3-a456-426614174000",
    "name": "Ultimate",
    "type": "yearly",
    "description": "Ultimate plan with all features",
    "price": 199.99,
    "durationDays": 365,
    "autoRenew": true,
    "isActive": true,
    "planFeatures": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Basic Diary",
        "description": "Access to basic diary features",
        "isActive": true
      },
      {
        "id": "223e4567-e89b-12d3-a456-426614174000",
        "name": "Advanced Diary",
        "description": "Access to advanced diary features",
        "isActive": true
      },
      {
        "id": "323e4567-e89b-12d3-a456-426614174000",
        "name": "Premium Diary",
        "description": "Access to premium diary features",
        "isActive": true
      }
    ],
    "legacyFeatures": [
      {
        "type": "diary",
        "name": "Premium Diary",
        "description": "Access to premium diary features",
        "isActive": true
      }
    ]
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Validate admin access
2. Validate input data
3. Check if plan with same name already exists
4. Create new plan with provided data
5. Associate plan features if provided
6. Save plan to database
7. Return created plan

### 4. Update Plan

**Endpoint:** `PATCH /plans/:id`

**Description:** Updates an existing subscription plan. Only accessible by admins.

**Path Parameters:**
- `id`: The ID of the plan to update

**Request Body:**
```json
{
  "name": "Ultimate Plus",
  "description": "Ultimate plan with all features plus exclusive content",
  "price": 249.99,
  "isActive": true,
  "planFeatureIds": [
    "123e4567-e89b-12d3-a456-426614174000",
    "223e4567-e89b-12d3-a456-426614174000",
    "323e4567-e89b-12d3-a456-426614174000",
    "423e4567-e89b-12d3-a456-426614174000"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Plan updated successfully",
  "data": {
    "id": "423e4567-e89b-12d3-a456-426614174000",
    "name": "Ultimate Plus",
    "type": "yearly",
    "description": "Ultimate plan with all features plus exclusive content",
    "price": 249.99,
    "durationDays": 365,
    "autoRenew": true,
    "isActive": true,
    "planFeatures": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Basic Diary",
        "description": "Access to basic diary features",
        "isActive": true
      },
      {
        "id": "223e4567-e89b-12d3-a456-426614174000",
        "name": "Advanced Diary",
        "description": "Access to advanced diary features",
        "isActive": true
      },
      {
        "id": "323e4567-e89b-12d3-a456-426614174000",
        "name": "Premium Diary",
        "description": "Access to premium diary features",
        "isActive": true
      },
      {
        "id": "423e4567-e89b-12d3-a456-426614174000",
        "name": "Exclusive Content",
        "description": "Access to exclusive content",
        "isActive": true
      }
    ],
    "legacyFeatures": [
      {
        "type": "diary",
        "name": "Premium Diary",
        "description": "Access to premium diary features",
        "isActive": true
      }
    ]
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Validate plan ID
3. Retrieve existing plan
4. Update plan with provided data
5. Update plan features if provided
6. Save updated plan
7. Return updated plan

### 5. Delete Plan

**Endpoint:** `DELETE /plans/:id`

**Description:** Deletes a subscription plan. Only accessible by admins. Cannot delete a plan that is in use.

**Path Parameters:**
- `id`: The ID of the plan to delete

**Response:**
```json
{
  "success": true,
  "message": "Plan removed successfully",
  "data": null,
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Validate plan ID
3. Check if plan is in use by any user
4. Delete plan if not in use
5. Return success response

### 6. Subscribe to Plan

**Endpoint:** `POST /plans/subscribe`

**Description:** Subscribes the current user to a plan. Any existing active subscription will be deactivated.

**Request Body:**
```json
{
  "planId": "123e4567-e89b-12d3-a456-426614174000",
  "autoRenew": true,
  "paymentReference": "payment_123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully subscribed to plan",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "planId": "123e4567-e89b-12d3-a456-426614174000",
    "startDate": "2023-07-25T00:00:00.000Z",
    "endDate": "2023-08-24T00:00:00.000Z",
    "isActive": true,
    "isPaid": true,
    "autoRenew": true,
    "paymentReference": "payment_123456",
    "plan": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Starter",
      "type": "monthly",
      "description": "Basic plan for beginners"
    },
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Validate plan ID
3. Retrieve plan details
4. Deactivate any existing active subscription
5. Create new subscription with start and end dates
6. Generate new JWT token with updated plan info
7. Return subscription details with new token

### 7. Get User Plans

**Endpoint:** `GET /plans/user`

**Description:** Retrieves all subscription plans for the current user.

**Response:**
```json
{
  "success": true,
  "message": "User plans retrieved successfully",
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "userId": "123e4567-e89b-12d3-a456-426614174000",
      "planId": "123e4567-e89b-12d3-a456-426614174000",
      "startDate": "2023-06-25T00:00:00.000Z",
      "endDate": "2023-07-24T00:00:00.000Z",
      "isActive": false,
      "isPaid": true,
      "autoRenew": false,
      "plan": {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Starter",
        "type": "monthly",
        "description": "Basic plan for beginners"
      }
    },
    {
      "id": "223e4567-e89b-12d3-a456-426614174000",
      "userId": "123e4567-e89b-12d3-a456-426614174000",
      "planId": "223e4567-e89b-12d3-a456-426614174000",
      "startDate": "2023-07-25T00:00:00.000Z",
      "endDate": "2023-08-24T00:00:00.000Z",
      "isActive": true,
      "isPaid": true,
      "autoRenew": true,
      "plan": {
        "id": "223e4567-e89b-12d3-a456-426614174000",
        "name": "Pro",
        "type": "monthly",
        "description": "Advanced plan for serious users"
      }
    }
  ],
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Retrieve all user plans with plan details
3. Transform plans to DTOs
4. Return user plans list

### 8. Get All Plan Features

**Endpoint:** `GET /plan-features`

**Description:** Retrieves all available plan features. Only accessible by admins.

**Response:**
```json
{
  "success": true,
  "message": "Plan features retrieved successfully",
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Basic Diary",
      "description": "Access to basic diary features",
      "isActive": true
    },
    {
      "id": "223e4567-e89b-12d3-a456-426614174000",
      "name": "Advanced Diary",
      "description": "Access to advanced diary features",
      "isActive": true
    },
    {
      "id": "323e4567-e89b-12d3-a456-426614174000",
      "name": "Premium Diary",
      "description": "Access to premium diary features",
      "isActive": true
    }
  ],
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Retrieve all plan features
3. Transform features to DTOs
4. Return features list

### 9. Create Plan Feature

**Endpoint:** `POST /plan-features`

**Description:** Creates a new plan feature. Only accessible by admins.

**Request Body:**
```json
{
  "name": "Exclusive Content",
  "description": "Access to exclusive content",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Plan feature created successfully",
  "data": {
    "id": "423e4567-e89b-12d3-a456-426614174000",
    "name": "Exclusive Content",
    "description": "Access to exclusive content",
    "isActive": true
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Validate admin access
2. Validate input data
3. Check if feature with same name already exists
4. Create new feature
5. Save feature to database
6. Return created feature

## Features

1. **Plan Management**
   - Create subscription plans
   - Update subscription plans
   - Delete subscription plans
   - List all plans
   - View plan details

2. **Plan Feature Management**
   - Create plan features
   - List all plan features
   - Associate features with plans

3. **User Subscription Management**
   - Subscribe to plans
   - View user subscriptions
   - Auto-renewal of subscriptions
   - Subscription expiry handling

## Tasks

1. **Implement Plan Management APIs**
   - Create endpoints for CRUD operations on plans
   - Implement plan listing and retrieval
   - Add validation for plan operations

2. **Implement Plan Feature Management APIs**
   - Create endpoints for CRUD operations on plan features
   - Implement feature listing
   - Add association of features with plans

3. **Implement User Subscription Management**
   - Create subscription endpoint
   - Implement subscription listing
   - Add auto-renewal functionality
   - Implement subscription expiry handling
