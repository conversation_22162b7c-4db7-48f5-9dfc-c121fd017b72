# Testing Convention

This document outlines the testing strategy and conventions for the HEC Backend project to ensure API stability and reliability.

## Table of Contents

1. [Testing Philosophy](#testing-philosophy)
2. [Test Types](#test-types)
3. [Test Coverage Requirements](#test-coverage-requirements)
4. [Unit Testing](#unit-testing)
5. [Integration Testing](#integration-testing)
6. [API Testing](#api-testing)
7. [Contract Testing](#contract-testing)
8. [Test Organization](#test-organization)
9. [Test Naming](#test-naming)
10. [Test Data Management](#test-data-management)
11. [Continuous Integration](#continuous-integration)
12. [Test-Driven Development](#test-driven-development)

## Testing Philosophy

Our testing strategy is designed to:

1. **Ensure API Stability**: Prevent unintended changes to stable APIs
2. **Catch Regressions**: Identify issues before they reach production
3. **Document Behavior**: Tests serve as executable documentation
4. **Enable Refactoring**: Allow internal improvements without affecting external behavior
5. **Support Evolution**: Facilitate controlled API evolution when needed

## Test Types

The project employs a comprehensive testing approach with multiple test types:

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test interactions between components
3. **API Tests**: Test API endpoints from an external perspective
4. **Contract Tests**: Verify API contracts are maintained
5. **End-to-End Tests**: Test complete user flows

## Test Coverage Requirements

- **New Features**: Minimum 80% test coverage for new code
- **Critical Paths**: 100% test coverage for authentication, payment, and data integrity flows
- **API Endpoints**: Every endpoint must have at least one test for each response type (success, validation error, business rule error, etc.)
- **Bug Fixes**: Each bug fix must include a regression test

## Unit Testing

Unit tests focus on testing individual components (services, utilities, etc.) in isolation.

### Framework

- Use Jest as the testing framework
- Use TypeORM's testing utilities for database-related tests

### Mocking

- Use Jest's mocking capabilities to isolate the unit under test
- Mock external dependencies and database access
- Use dependency injection to facilitate testing

### Example

```typescript
// auth.service.spec.ts
describe('AuthService', () => {
  let service: AuthService;
  let userRepositoryMock: MockType<Repository<User>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getRepositoryToken(User),
          useFactory: mockRepository,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userRepositoryMock = module.get(getRepositoryToken(User));
  });

  describe('login', () => {
    it('should return a JWT token when credentials are valid', async () => {
      // Arrange
      const loginDto = { userId: 'testuser', password: 'Password123!' };
      const user = new User();
      user.id = '123';
      user.userId = 'testuser';
      user.password = await bcrypt.hash('Password123!', 10);
      user.isActive = true;
      user.isConfirmed = true;
      
      userRepositoryMock.findOne.mockReturnValue(user);
      jest.spyOn(user, 'verifyPassword').mockReturnValue(true);
      
      // Act
      const result = await service.login(loginDto);
      
      // Assert
      expect(result).toHaveProperty('access_token');
      expect(result).toHaveProperty('user');
    });

    it('should throw UnauthorizedException when credentials are invalid', async () => {
      // Arrange
      const loginDto = { userId: 'testuser', password: 'WrongPassword' };
      const user = new User();
      user.userId = 'testuser';
      
      userRepositoryMock.findOne.mockReturnValue(user);
      jest.spyOn(user, 'verifyPassword').mockReturnValue(false);
      
      // Act & Assert
      await expect(service.login(loginDto)).rejects.toThrow(UnauthorizedException);
    });
  });
});
```

## Integration Testing

Integration tests verify that different components work together correctly.

### Framework

- Use NestJS testing utilities
- Use an in-memory database or test database for data persistence

### Scope

- Test interactions between services
- Test database operations
- Test event handling

### Example

```typescript
// plans.integration.spec.ts
describe('Plans Integration', () => {
  let app: INestApplication;
  let plansService: PlansService;
  let usersService: UsersService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [Plan, User, UserPlan],
          synchronize: true,
        }),
        PlansModule,
        UsersModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    plansService = moduleFixture.get<PlansService>(PlansService);
    usersService = moduleFixture.get<UsersService>(UsersService);
  });

  afterAll(async () => {
    await app.close();
  });

  it('should subscribe a user to a plan', async () => {
    // Arrange
    const user = await usersService.create({
      userId: 'testuser',
      email: '<EMAIL>',
      password: 'Password123!',
    });
    
    const plan = await plansService.create({
      name: 'Test Plan',
      type: PlanType.STANDARD,
      price: 9.99,
      subscriptionType: SubscriptionType.MONTHLY,
    });
    
    // Act
    const result = await plansService.subscribeToPlan(user.id, {
      planId: plan.id,
      autoRenew: true,
    });
    
    // Assert
    expect(result).toBeDefined();
    expect(result.userId).toBe(user.id);
    expect(result.planId).toBe(plan.id);
    expect(result.isActive).toBe(true);
  });
});
```

## API Testing

API tests verify that API endpoints behave as expected from an external perspective.

### Framework

- Use Supertest for HTTP requests
- Use Jest for assertions

### Scope

- Test all API endpoints
- Test request validation
- Test response structure and status codes
- Test authentication and authorization

### Example

```typescript
// auth.controller.spec.ts
describe('AuthController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /auth/login', () => {
    it('should return 200 and JWT token when credentials are valid', async () => {
      // Arrange
      const loginDto = {
        userId: 'admin',
        password: '123456_Az',
      };

      // Act
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send(loginDto)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('access_token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('returnUrl');
    });

    it('should return 401 when credentials are invalid', async () => {
      // Arrange
      const loginDto = {
        userId: 'admin',
        password: 'wrong_password',
      };

      // Act & Assert
      const response = await request(app.getHttpServer())
        .post('/auth/login')
        .send(loginDto)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid credentials');
    });
  });
});
```

## Contract Testing

Contract tests verify that API contracts are maintained.

### Framework

- Use Pact.js for consumer-driven contract testing
- Use OpenAPI/Swagger for API specification

### Scope

- Test API request and response schemas
- Verify backward compatibility
- Ensure frontend and backend expectations align

### Example

```typescript
// consumer.pact.spec.ts
describe('Auth API Contract', () => {
  const provider = new Pact({
    consumer: 'Frontend',
    provider: 'Backend',
    port: 8888,
    log: path.resolve(process.cwd(), 'logs', 'pact.log'),
    dir: path.resolve(process.cwd(), 'pacts'),
  });

  beforeAll(() => provider.setup());
  afterAll(() => provider.finalize());

  describe('login', () => {
    it('should return a token when credentials are valid', async () => {
      // Arrange
      const loginRequest = {
        userId: 'testuser',
        password: 'Password123!',
      };

      const loginResponse = {
        success: true,
        message: 'Login successful',
        statusCode: 200,
        data: {
          access_token: like('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'),
          user: {
            id: like('123e4567-e89b-12d3-a456-426614174000'),
            userId: 'testuser',
            email: like('<EMAIL>'),
          },
          returnUrl: like('/dashboard'),
        },
      };

      // Act & Assert
      await provider.addInteraction({
        state: 'a user exists with userId testuser',
        uponReceiving: 'a login request with valid credentials',
        withRequest: {
          method: 'POST',
          path: '/auth/login',
          headers: { 'Content-Type': 'application/json' },
          body: loginRequest,
        },
        willRespondWith: {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
          body: loginResponse,
        },
      });

      const api = new AuthApi(provider.mockService.baseUrl);
      const result = await api.login(loginRequest);
      
      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('access_token');
    });
  });
});
```

## Test Organization

Organize tests to mirror the structure of the source code:

```
src/
├── modules/
│   ├── auth/
│   │   ├── auth.controller.ts
│   │   ├── auth.service.ts
│   │   ├── tests/
│   │   │   ├── auth.controller.spec.ts
│   │   │   ├── auth.service.spec.ts
│   │   │   └── auth.e2e-spec.ts
```

Alternatively, use a dedicated test directory:

```
src/
├── modules/
│   ├── auth/
│   │   ├── auth.controller.ts
│   │   ├── auth.service.ts
test/
├── unit/
│   ├── auth/
│   │   ├── auth.controller.spec.ts
│   │   └── auth.service.spec.ts
├── integration/
│   ├── auth/
│   │   └── auth.integration.spec.ts
├── e2e/
│   ├── auth/
│   │   └── auth.e2e-spec.ts
```

## Test Naming

Use descriptive names for test files and test cases:

- Test files: `[name].[type].spec.ts`
- Test suites: Describe the component or feature being tested
- Test cases: Describe the expected behavior

Example:

```typescript
// auth.service.spec.ts
describe('AuthService', () => {
  describe('login', () => {
    it('should return a JWT token when credentials are valid', async () => {
      // Test implementation
    });

    it('should throw UnauthorizedException when user is not found', async () => {
      // Test implementation
    });

    it('should throw UnauthorizedException when password is incorrect', async () => {
      // Test implementation
    });

    it('should throw UnauthorizedException when user is not confirmed', async () => {
      // Test implementation
    });
  });
});
```

## Test Data Management

### Test Data Principles

- Tests should be independent and isolated
- Tests should not depend on external systems
- Tests should use predictable test data

### Approaches

1. **In-memory Database**: Use an in-memory database for tests
2. **Test Database**: Use a separate database for testing
3. **Database Seeding**: Seed the database with test data before tests
4. **Mocking**: Mock database access for unit tests

### Example

```typescript
// test-utils.ts
export const seedTestDatabase = async (connection: Connection) => {
  // Clear existing data
  await connection.synchronize(true);

  // Seed roles
  const roles = [
    { name: 'admin' },
    { name: 'tutor' },
    { name: 'student' },
  ];
  await connection.getRepository(Role).save(roles);

  // Seed admin user
  const adminUser = connection.getRepository(User).create({
    userId: 'admin',
    email: '<EMAIL>',
    password: await bcrypt.hash('Admin@123', 10),
    isActive: true,
    isConfirmed: true,
    type: UserType.ADMIN,
  });
  await connection.getRepository(User).save(adminUser);

  // Seed plans
  const plans = [
    {
      name: 'Starter',
      type: PlanType.STARTER,
      price: 9.99,
      subscriptionType: SubscriptionType.MONTHLY,
    },
    {
      name: 'Pro',
      type: PlanType.PRO,
      price: 19.99,
      subscriptionType: SubscriptionType.MONTHLY,
    },
  ];
  await connection.getRepository(Plan).save(plans);
};
```

## Continuous Integration

Integrate testing into the CI/CD pipeline:

1. Run unit tests on every commit
2. Run integration tests on pull requests
3. Run API tests on pull requests
4. Run contract tests on pull requests
5. Run end-to-end tests before deployment

### Example CI Configuration

```yaml
# .github/workflows/ci.yml
name: CI

on:
  push:
    branches: [ dev, staging, production ]
  pull_request:
    branches: [ dev ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:12
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '14'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Lint
      run: npm run lint
      
    - name: Unit tests
      run: npm run test
      
    - name: Integration tests
      run: npm run test:integration
      
    - name: API tests
      run: npm run test:api
      
    - name: Contract tests
      run: npm run test:contract
      
    - name: E2E tests
      if: github.event_name == 'pull_request'
      run: npm run test:e2e
```

## Test-Driven Development

Consider adopting Test-Driven Development (TDD) for new features:

1. Write a failing test that defines the expected behavior
2. Implement the minimum code to make the test pass
3. Refactor the code while keeping the tests passing

Benefits of TDD:

- Ensures test coverage
- Focuses development on requirements
- Produces cleaner, more modular code
- Provides immediate feedback

### TDD Workflow

```
┌─────────────┐
│ Write a test│
└──────┬──────┘
       │
       ▼
┌─────────────┐
│ Run the test│
└──────┬──────┘
       │
       ▼
┌─────────────┐     ┌─────────────┐
│  Test fails │ Yes │ Write code  │
│    ?        │────►│ to pass test│
└──────┬──────┘     └──────┬──────┘
       │ No                │
       ▼                   │
┌─────────────┐            │
│  Refactor   │◄───────────┘
└──────┬──────┘
       │
       ▼
┌─────────────┐
│ Next feature│
└─────────────┘
```

---

This testing convention provides a comprehensive framework for ensuring API stability and reliability. By following these guidelines, the team can confidently maintain stable APIs while allowing for controlled evolution when needed.
