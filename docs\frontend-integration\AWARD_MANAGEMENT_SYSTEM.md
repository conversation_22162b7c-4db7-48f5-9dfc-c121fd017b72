# Award Management System Integration Guide

## Overview

The Award Management System handles the automated and manual distribution of awards to users based on various frequencies (daily, weekly, monthly, quarterly, yearly). This document outlines the system architecture, APIs, and integration guidelines.

## System Architecture

```ascii
                                  ┌──────────────────┐
                                  │                  │
                                  │  Frontend UI     │
                                  │                  │
                                  └────────┬─────────┘
                                          │
                                          │ HTTP/REST
                                          ▼
┌──────────────────┐              ┌──────────────────┐
│                  │              │                  │
│  Cron Scheduler  │─────────────▶│  Award API      │
│                  │              │                  │
└──────────────────┘              └────────┬─────────┘
                                          │
                                          │
                    ┌────────────────────┬─┴───────────────────┐
                    │                    │                      │
                    ▼                    ▼                      ▼
            ┌──────────────┐    ┌──────────────┐      ┌──────────────┐
            │              │    │              │      │              │
            │Award Schedule│    │   Awards     │      │Award Winners │
            │              │    │              │      │              │
            └──────────────┘    └──────────────┘      └──────────────┘
```

## Award Frequencies and Scheduling

The system supports multiple award frequencies:

```ascii
┌────────────┬─────────────────┬────────────────────────────┐
│ Frequency  │ Schedule Date   │ Period Coverage           │
├────────────┼─────────────────┼────────────────────────────┤
│ DAILY     │ Next day        │ Current day               │
│ WEEKLY    │ Next Sunday     │ Mon-Sun                   │
│ MONTHLY   │ 1st next month  │ Current month            │
│ QUARTERLY │ 1st next quarter│ Current quarter (3 months)│
│ YEARLY    │ Jan 1st         │ Current year             │
└────────────┴─────────────────┴────────────────────────────┘
```

## API Endpoints

### 1. Award Management

#### Create Award
\`\`\`http
POST /api/v1/awards
\`\`\`

Request body:
\`\`\`json
{
  "name": "Gold Star Diarist",
  "description": "Awarded for exceptional diary entries",
  "module": "DIARY",
  "criteria": ["DIARY_SCORE", "ATTENDANCE"],
  "frequency": "MONTHLY",
  "rewardPoints": 100,
  "criteriaConfig": {
    "minScore": 90,
    "entriesRequired": 5
  }
}
\`\`\`

#### List Awards
\`\`\`http
GET /api/v1/awards?module=DIARY&frequency=MONTHLY&includeInactive=false
\`\`\`

### 2. Award Schedule Management

#### Create Manual Schedule
\`\`\`http
POST /api/v1/award-schedules
\`\`\`

Request body:
\`\`\`json
{
  "module": "DIARY",
  "frequency": "MONTHLY",
  "isActive": true
}
\`\`\`

#### Get Award Schedules
\`\`\`http
GET /api/v1/award-schedules?module=DIARY&status=PENDING&startDate=2025-01-01&endDate=2025-12-31
\`\`\`

#### Update Schedule Status
\`\`\`http
POST /api/v1/award-schedules/:id/active-status
\`\`\`

Request body:
\`\`\`json
{
  "isActive": true
}
\`\`\`

## Schedule Creation Logic

When creating a new award schedule:

1. The system calculates remaining schedule dates for the year based on frequency
2. For each schedule date:
   - Determines period start/end dates
   - Validates for overlapping schedules
   - Creates schedule entries

Example for MONTHLY frequency (if today is May 19, 2025):
\`\`\`
Schedules created:
1. June 1, 2025   (Period: May 1-31, 2025)
2. July 1, 2025   (Period: June 1-30, 2025)
3. August 1, 2025 (Period: July 1-31, 2025)
...and so on until December
\`\`\`

## Error Handling

The system implements a retry mechanism for failed schedules:
- Maximum 3 retry attempts
- Failed status with error message storage
- Automatic retry during next processing cycle

Error Response Format:
\`\`\`json
{
  "success": false,
  "message": "Error message",
  "error": {
    "code": "ERROR_CODE",
    "details": "Detailed error information"
  }
}
\`\`\`

## Frontend Integration Guidelines

1. Award Creation Flow:
   ```ascii
   ┌──────────┐    ┌───────────┐    ┌──────────┐    ┌─────────┐
   │  Input   │───▶│ Validate  │───▶│  Create  │───▶│ Success │
   │   Form   │    │  Fields   │    │  Award   │    │ Message │
   └──────────┘    └───────────┘    └──────────┘    └─────────┘
   ```

2. Schedule Management Flow:
   ```ascii
   ┌──────────┐    ┌──────────┐    ┌──────────┐    ┌─────────┐
   │ Select   │───▶│ Configure│───▶│  Create  │───▶│ View    │
   │Frequency │    │ Settings │    │Schedule  │    │Status   │
   └──────────┘    └──────────┘    └──────────┘    └─────────┘
   ```

### Best Practices

1. Implement real-time status updates using WebSocket for schedule processing
2. Cache award configurations for better performance
3. Implement proper error handling and user feedback
4. Use optimistic UI updates for better user experience
5. Implement proper validation before API calls

## Schedule Processing States

```ascii
┌──────────┐    ┌──────────┐    ┌───────────┐    ┌──────────┐
│ PENDING  │───▶│PROCESSING│───▶│ COMPLETED │    │ FAILED   │
└──────────┘    └──────────┘    └───────────┘    └──────────┘
                      │                                ▲
                      └────────────────────────────────┘
                           (After max retries)
```

## Security Considerations

1. All endpoints require authentication
2. Admin-only access for schedule management
3. Rate limiting on API endpoints
4. Input validation for all parameters
5. Audit logging for schedule modifications

# Award Management System - Frontend Integration Flow

This document provides a comprehensive integration flow for frontend developers to implement the Award Management System, with minimal verbal communication needed. It focuses exclusively on API endpoints and data flows without prescribing specific frontend implementation approaches.

## Table of Contents

1. [Award Management](#award-management)
   - [Award Creation](#award-creation)
   - [Award Configuration](#award-configuration)
   - [Award Criteria](#award-criteria)
2. [Schedule Management](#schedule-management)
   - [Schedule Creation](#schedule-creation)
   - [Frequency Management](#frequency-management)
   - [Period Calculations](#period-calculations)
3. [Award Distribution](#award-distribution)
   - [Distribution Process](#distribution-process)
   - [Distribution History](#distribution-history)
   - [Student Awards](#student-awards)
4. [Monitoring and Reports](#monitoring-and-reports)
   - [Award Analytics](#award-analytics)
   - [Distribution Reports](#distribution-reports)
   - [Performance Metrics](#performance-metrics)

## Prerequisites

Before integrating the Award Management System:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (admin, operator)
3. Set up your API testing tool (Postman recommended)
4. Ensure test database has sample award data

## System Overview

The Award Management System enables operators to create and manage awards with different frequencies (DAILY, WEEKLY, MONTHLY, QUARTERLY). The system automatically creates schedule entries based on frequency and validates for overlaps.

## Award Management

### Award Creation

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Create     │      │  Configure  │      │  Activate   │
│  Award      │ ──▶  │  Criteria   │ ──▶  │  Award      │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /awards│      │ POST /awards│      │ PATCH       │
│             │      │ /:id/       │      │ /awards/:id │
│             │      │ criteria    │      │ /activate   │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /api/v1/awards` - Create new award
- `PATCH /api/v1/awards/:id` - Update award
- `GET /api/v1/awards/:id` - Get award details
- `GET /api/v1/awards` - List all awards with filtering

**Create Award Request Body:**
```json
{
  "name": "Gold Star Diarist",
  "description": "Awarded for exceptional diary entries",
  "module": "DIARY",
  "criteria": ["DIARY_SCORE", "ATTENDANCE"],
  "frequency": "MONTHLY",
  "rewardPoints": 100,
  "criteriaConfig": {
    "minScore": 90,
    "entriesRequired": 5
  }
}
```

### Schedule Management

Schedule creation workflow with frequency-based calculations.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Select     │      │  Create     │      │  Generate   │
│  Frequency  │ ──▶  │  Schedule   │ ──▶  │  Entries    │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Validate    │      │ Calculate   │      │ Create      │
│ Overlap     │      │ Periods     │      │ Multiple    │
│             │      │             │      │ Schedules   │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /api/v1/award-schedules` - Create schedule
- `GET /api/v1/award-schedules` - List schedules
- `PATCH /api/v1/award-schedules/:id` - Update schedule
- `DELETE /api/v1/award-schedules/:id` - Delete schedule

**Create Schedule Request Body:**
```json
{
  "module": "DIARY",
  "frequency": "QUARTERLY",
  "isActive": true
}
```

### Distribution Process

Award distribution workflow showing automated and manual processes.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Schedule   │      │  Process    │      │  Distribute │
│  Trigger    │ ──▶  │  Awards     │ ──▶  │  Rewards    │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Cron Job or │      │ Evaluate    │      │ Send        │
│ Manual      │      │ Criteria    │      │ Notifications│
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /api/v1/award-schedules/:id/process` - Process schedule
- `GET /api/v1/award-distributions` - List distributions
- `GET /api/v1/award-distributions/:id` - Get distribution details

**Process Schedule Response:**
```json
{
  "success": true,
  "message": "Schedule processed successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "scheduledDate": "2025-07-01",
    "processedDate": "2025-07-01T00:00:00Z",
    "status": "COMPLETED",
    "awardsDistributed": 50,
    "totalRewardPoints": 5000
  }
}
```

## Integration Testing

### Award Creation Flow

1. **Create Award Test**
   ```typescript
   describe('Award Creation', () => {
     it('should create award with valid data', async () => {
       const response = await request(app)
         .post('/api/v1/awards')
         .set('Authorization', `Bearer ${adminToken}`)
         .send({
           name: "Monthly Excellence",
           description: "Top performers of the month",
           module: "DIARY",
           frequency: "MONTHLY",
           rewardPoints: 100
         });

       expect(response.status).toBe(201);
       expect(response.body.data).toHaveProperty('id');
     });
   });
   ```

### Schedule Management Flow

1. **Create Schedule Test**
   ```typescript
   describe('Schedule Creation', () => {
     it('should create quarterly schedules', async () => {
       const response = await request(app)
         .post('/api/v1/award-schedules')
         .set('Authorization', `Bearer ${adminToken}`)
         .send({
           module: "DIARY",
           frequency: "QUARTERLY",
           isActive: true
         });

       expect(response.status).toBe(201);
       expect(response.body.data.schedules.length).toBeGreaterThan(0);
     });
   });
   ```

## Error Handling

The system provides detailed error responses for various scenarios:

```json
{
  "success": false,
  "message": "Error creating award schedule",
  "error": {
    "code": "SCHEDULE_OVERLAP",
    "details": "Another schedule exists for the same period"
  }
}
```

## Schedule Processing States

```ascii
┌──────────┐    ┌──────────┐    ┌───────────┐    ┌──────────┐
│ PENDING  │───▶│PROCESSING│───▶│ COMPLETED │    │ FAILED   │
└──────────┘    └──────────┘    └───────────┘    └──────────┘
                     │                                ▲
                     └────────────────────────────────┘
                          (After max retries)
```

## Security Considerations

1. All endpoints require authentication
2. Admin-only access for award management
3. Rate limiting on API endpoints
4. Input validation and sanitization
5. Audit logging for all operations

For any questions or issues, please contact the development team.
