import { MigrationInterface, QueryRunner } from "typeorm";

export class RemovePriceEquivalentToRewardPoint1747000000001 implements MigrationInterface {
    name = 'RemovePriceEquivalentToRewardPoint1747000000001'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // We don't actually remove the column to maintain backward compatibility
        // Instead, we'll just stop using it in the code
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // No action needed for rollback since we're not actually removing the column
    }
}
