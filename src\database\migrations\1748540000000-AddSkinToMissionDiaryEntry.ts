import { MigrationInterface, QueryRunner, TableColumn, TableForeignKey } from 'typeorm';

export class AddSkinToMissionDiaryEntry1748540000000 implements MigrationInterface {
  name = 'AddSkinToMissionDiaryEntry1748540000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the skin_id column already exists
    const table = await queryRunner.getTable('mission_diary_entry');
    const skinIdColumn = table?.findColumnByName('skin_id');

    if (!skinIdColumn) {
      // Add skin_id column to mission_diary_entry table
      await queryRunner.addColumn(
        'mission_diary_entry',
        new TableColumn({
          name: 'skin_id',
          type: 'uuid',
          isNullable: true,
        })
      );

      // Add foreign key constraint to diary_skin table
      await queryRunner.createForeignKey(
        'mission_diary_entry',
        new TableForeignKey({
          columnNames: ['skin_id'],
          referencedTableName: 'diary_skin',
          referencedColumnNames: ['id'],
          onDelete: 'SET NULL',
          onUpdate: 'CASCADE',
        })
      );

      console.log('✅ Added skin_id column and foreign key to mission_diary_entry table');
    } else {
      console.log('ℹ️ skin_id column already exists in mission_diary_entry table');
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if the foreign key exists before trying to drop it
    const table = await queryRunner.getTable('mission_diary_entry');
    const foreignKey = table?.foreignKeys.find(
      fk => fk.columnNames.indexOf('skin_id') !== -1
    );

    if (foreignKey) {
      await queryRunner.dropForeignKey('mission_diary_entry', foreignKey);
    }

    // Check if the column exists before trying to drop it
    const skinIdColumn = table?.findColumnByName('skin_id');
    if (skinIdColumn) {
      await queryRunner.dropColumn('mission_diary_entry', 'skin_id');
    }
  }
}
