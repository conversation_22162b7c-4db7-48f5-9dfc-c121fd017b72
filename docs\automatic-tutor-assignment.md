# Automatic Tutor Assignment System

## Overview

The HEC platform includes a sophisticated automatic tutor assignment system that assigns tutors to students based on their subscription plans and modules. This document provides a comprehensive overview of how the system works, its key components, and the assignment process.

## Key Concepts

### Plan Features and Modules

- **Plan Features**: Each subscription plan includes a set of features that define what services a student can access.
- **Feature Types**: Features are categorized by type (e.g., `HEC_USER_DIARY`, `ENGLISH_ESSAY`, `MODULE`).
- **Modules**: Features of type `MODULE` represent specific educational modules that require tutor support.

### Student-Tutor Mapping

- **Mapping Entity**: The `StudentTutorMapping` entity establishes a relationship between a student, a tutor, and a specific module.
- **Unique Constraint**: Each student can have only one active tutor per module (enforced by a unique constraint on `studentId` and `planFeatureId`).
- **Mapping Status**: Mappings can be either `ACTIVE` or `INACTIVE`.

## Assignment Process

### 1. Plan Subscription Trigger

When a student subscribes to a plan, the system automatically initiates the tutor assignment process:

```typescript
// In PlansService.subscribeToPlan
// After creating the user plan
if (autoAssignTutors) {
    // Ensure plan has features loaded before assigning tutors
    if (!plan.planFeatures) {
        // Load plan with features if they weren't loaded
        const planWithFeatures = await this.planRepository.findOne({
            where: { id: plan.id },
            relations: ['planFeatures']
        });

        if (planWithFeatures) {
            // Assign tutors for all modules in the plan
            await this.assignTutorsForPlan(userIdToUse, planWithFeatures);
        } else {
            // If we can't load the plan with features, just pass the user ID
            await this.assignTutorsForPlan(userIdToUse);
        }
    } else {
        // Plan already has features loaded
        await this.assignTutorsForPlan(userIdToUse, plan);
    }
}
```

### 2. Module Identification

The system identifies all module features in the student's active plan:

```typescript
// In PlansService.assignTutorsForPlan
// Filter only MODULE type features from the plan
// The presence of a feature in plan.planFeatures means it's active for this plan
const moduleFeatures = plan.planFeatures;
```

### 3. Tutor Selection and Assignment

For each module, the system:

1. Checks if the student already has a tutor assigned
2. If not, selects an appropriate tutor based on workload balancing
3. Creates a mapping between the student, tutor, and module

```typescript
// In PlansService.assignTutorsForPlan
for (const moduleFeature of moduleFeatures) {
    // Check if student already has a tutor for this module
    const existingAssignment = await this.tutorMatchingService.getStudentTutorForModule(
        studentId, 
        moduleId
    );

    if (existingAssignment) {
        // Student already has a tutor for this module
        continue;
    }

    // Auto-assign a tutor for this module
    const assignments = await this.tutorMatchingService.autoAssignTutorsWithoutNotifications({
        planFeatureId: moduleId,
        studentIds: [studentId],
        reassignExisting: false
    });
}
```

### 4. Workload Balancing

The system selects tutors based on their current workload to ensure fair distribution:

```typescript
// In TutorMatchingService.selectTutorForModule
async selectTutorForModule(moduleId: string): Promise<User> {
    // Get all tutors
    const tutors = await this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.userRoles', 'userRoles')
        .leftJoinAndSelect('userRoles.role', 'role')
        .where('role.name = :roleName', { roleName: 'tutor' })
        .getMany();

    // Calculate workload for each tutor
    const tutorWorkloads = await Promise.all(
        tutors.map(async tutor => {
            const activeAssignments = await this.studentTutorMappingRepository.count({
                where: {
                    tutorId: tutor.id,
                    status: MappingStatus.ACTIVE
                }
            });
            
            return {
                tutor,
                workload: activeAssignments
            };
        })
    );

    // Sort tutors by workload (ascending)
    tutorWorkloads.sort((a, b) => a.workload - b.workload);
    
    // Return the tutor with the lowest workload
    return tutorWorkloads[0].tutor;
}
```

### 5. Notification

After assignment, the system sends notifications to both the student and the tutor:

```typescript
// In PlansService.assignTutorsForPlan
// Send notifications to student
await this.sendStudentNotifications(student, validAssignments);

// Send notifications to tutors
for (const assignment of validAssignments) {
    try {
        await this.sendTutorNotification(
            assignment.tutor, 
            student, 
            assignment.module, 
            assignment.mapping.id
        );
    } catch (error) {
        // Log error but continue with other notifications
    }
}
```

## On-Demand Assignment

In addition to the automatic assignment during plan subscription, the system also supports on-demand assignment in specific scenarios:

### Diary Entry Submission

When a student submits a diary entry and no tutor is assigned for the diary module:

```typescript
// In DiaryEntryService.submitDiaryEntry
if (!studentTutorMapping) {
    // Try to get any available tutor for the diary module
    const availableTutors = await this.tutorMatchingService.getAvailableTutorsForModule(
        diaryModuleId
    );

    if (availableTutors && availableTutors.length > 0) {
        // Auto-assign the first available tutor
        const assignResult = await this.tutorMatchingService.assignTutor({
            studentId: userId,
            tutorId: availableTutors[0].id,
            planFeatureId: diaryModuleId,
            notes: 'Auto-assigned during diary submission'
        });
    }
}
```

## Manual Assignment

Administrators can also manually assign tutors through the admin interface:

```typescript
// In AdminTutorMatchingController
@Post('assign')
@ApiOperation({ summary: 'Assign a tutor to a student (admin only)' })
async assignTutor(
    @Body() assignTutorDto: AssignTutorDto
): Promise<ApiResponse<TutorAssignmentResponseDto>> {
    const assignment = await this.tutorMatchingService.assignTutor(assignTutorDto);
    return ApiResponse.success(assignment, 'Tutor assigned successfully');
}
```

## Data Model

### StudentTutorMapping Entity

```typescript
@Entity()
@Unique(['studentId', 'planFeatureId'])
export class StudentTutorMapping extends AuditableBaseEntity {
    @Column({ name: 'student_id' })
    studentId: string;

    @ManyToOne(() => User)
    @JoinColumn({ name: 'student_id' })
    student: User;

    @Column({ name: 'tutor_id' })
    tutorId: string;

    @ManyToOne(() => User)
    @JoinColumn({ name: 'tutor_id' })
    tutor: User;

    @Column({ name: 'plan_feature_id' })
    planFeatureId: string;

    @ManyToOne(() => PlanFeature)
    @JoinColumn({ name: 'plan_feature_id' })
    planFeature: PlanFeature;

    @Column({
        name: 'status',
        type: 'enum',
        enum: MappingStatus,
        default: MappingStatus.ACTIVE
    })
    status: MappingStatus;

    @Column({ name: 'assigned_date' })
    assignedDate: Date;

    @Column({ name: 'last_activity_date', nullable: true })
    lastActivityDate: Date;

    @Column({ name: 'notes', nullable: true, type: 'text' })
    notes: string;
}
```

### PlanFeature Entity

```typescript
export enum FeatureType {
    HEC_USER_DIARY = 'hec_user_diary',
    HEC_PLAY = 'hec_play',
    ENGLISH_QA_WRITING = 'english_qa_writing',
    ENGLISH_ESSAY = 'english_essay',
    ENGLISH_NOVEL = 'english_novel',
    MODULE = 'module'
}

@Entity()
export class PlanFeature extends AuditableBaseEntity {
    @Column({
        name: 'type',
        type: 'enum',
        enum: FeatureType,
        unique: true
    })
    type: FeatureType;

    @Column({ name: 'name' })
    name: string;

    @Column({ name: 'description', type: 'text' })
    description: string;

    @ManyToMany(() => Plan, plan => plan.planFeatures)
    plans: Plan[];
}
```

## Best Practices

1. **Workload Balancing**: The system distributes students among tutors based on current workload to prevent any single tutor from being overwhelmed.

2. **Notification System**: Both students and tutors receive notifications about new assignments through multiple channels (email, in-app, push).

3. **Transaction Safety**: All database operations related to tutor assignment are performed within transactions to ensure data consistency.

4. **Error Handling**: The system includes robust error handling to prevent assignment failures from disrupting the subscription process.

5. **Automatic Reassignment**: When a student renews a subscription, the system checks and reassigns tutors if necessary.

## Integration Points

The tutor assignment system integrates with several other modules:

1. **Plans Module**: Triggers assignment when a student subscribes to a plan
2. **Diary Module**: Uses assigned tutors for diary entry reviews
3. **Notification Module**: Sends notifications about assignments
4. **Chat Module**: Enables communication between assigned students and tutors

## Conclusion

The automatic tutor assignment system ensures that students are promptly connected with appropriate tutors for each module in their subscription plan. The system balances tutor workloads, maintains clear student-tutor relationships, and provides timely notifications to all parties involved.
