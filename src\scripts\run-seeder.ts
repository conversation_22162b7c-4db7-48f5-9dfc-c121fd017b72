import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { AppModule } from '../app.module';
import { SeedService } from '../config/seed.service';

/**
 * <PERSON><PERSON>t to run the database seeder manually
 * Usage: npm run seed
 */
async function bootstrap() {
  const logger = new Logger('RunSeeder');
  logger.log('Starting manual database seeding...');

  try {
    // Create a NestJS application context
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the SeedService from the application context
    const seedService = app.get(SeedService);
    
    // Run the seeding process
    const result = await seedService.runSeeding();
    
    if (result.success) {
      logger.log(result.message);
    } else {
      logger.error(result.message);
    }
    
    // Close the application context
    await app.close();
    
    // Exit with appropriate code
    process.exit(result.success ? 0 : 1);
  } catch (error) {
    logger.error(`Failed to run seeder: ${error.message}`, error.stack);
    process.exit(1);
  }
}

// Run the bootstrap function
bootstrap();
