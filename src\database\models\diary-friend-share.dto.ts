import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUUID, IsDateString, IsBoolean } from 'class-validator';
import { MessageDto } from './chat.dto';

/**
 * DTO for sharing a diary entry with a friend
 */
export class ShareDiaryEntryWithFriendDto {
  @ApiProperty({
    description: 'Target friend user ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  targetUserId: string;
}

/**
 * DTO for diary friend share details
 */
export class DiaryFriendShareDetailsDto {
  @ApiProperty({ description: 'Share ID' })
  id: string;

  @ApiProperty({ description: 'Unique share token' })
  shareToken: string;

  @ApiProperty({ description: 'Whether share is active' })
  isActive: boolean;

  @ApiProperty({ description: 'When the share was created' })
  createdAt: Date;

  @ApiProperty({ description: 'ID of the chat message sent' })
  chatMessageId?: string;
}

/**
 * Response DTO for diary entry friend sharing
 */
export class DiaryFriendShareResponseDto {
  @ApiProperty({
    description: 'Share details',
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      shareToken: 'abc123def456',
      isActive: true,
      createdAt: '2024-01-15T10:30:00.000Z'
    }
  })
  shareDetails: DiaryFriendShareDetailsDto;

  @ApiProperty({
    description: 'Deep link for the shared entry',
    example: 'hec://diary/friend-shared/abc123def456'
  })
  deepLink: string;

  @ApiProperty({
    description: 'Chat message sent to friend',
    type: MessageDto
  })
  chatMessage: MessageDto;

  @ApiProperty({
    description: 'Whether notification was sent successfully',
    example: true
  })
  notificationSent: boolean;
}

/**
 * DTO for friend shared diary entry view
 */
export class FriendSharedDiaryEntryDto {
  @ApiProperty({ description: 'Diary entry ID' })
  id: string;

  @ApiProperty({ description: 'Entry title' })
  title: string;

  @ApiProperty({ description: 'Entry content' })
  content: string;

  @ApiProperty({ description: 'Entry date' })
  entryDate: Date;

  @ApiProperty({ description: 'Sharer name' })
  sharedByName: string;

  @ApiProperty({ description: 'Sharing message', required: false })
  sharingMessage?: string;

  @ApiProperty({ description: 'When it was shared' })
  sharedAt: Date;

  @ApiProperty({ description: 'Deep link to the entry' })
  deepLink: string;
}
