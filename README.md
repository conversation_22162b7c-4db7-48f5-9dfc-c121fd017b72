# HEC Backend

## Overview

This is the backend service for the HEC application, providing APIs for user management, authentication, diary management, subscription plans, and tutor approval workflows.

## File Storage System

The HEC Backend features a comprehensive file storage system that supports both local filesystem and AWS S3 storage with dynamic configuration and seamless migration capabilities.

### **Storage Features**

- **Single Provider Per Environment**: Local filesystem OR AWS S3 (no hybrid mode)
- **Dynamic Configuration**: Environment-based storage selection via `.env`
- **Backward Compatibility**: Existing local files continue to work
- **Unified API**: Same interface for both storage types
- **Performance Optimization**: CDN integration for S3
- **Reliable Error Handling**: Proper error handling for upload failures

### **Quick Setup**

#### **Local Storage (Development/Staging)**
```bash
# .env
STORAGE_PROVIDER=local
```

#### **AWS S3 Storage (Production)**
```bash
# .env
STORAGE_PROVIDER=s3

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_S3_BUCKET_NAME=hec-production-files
AWS_CLOUDFRONT_DOMAIN=d123456789.cloudfront.net
```

### **Supported File Types**

The system manages files for:
- **Profile Pictures**: User avatar images
- **Shop Items**: Product images and assets
- **Diary Skins**: Customizable diary themes
- **QR Codes**: Generated diary sharing codes
- **Story Maker**: Story creation assets
- **Message Attachments**: Chat file attachments

### **Storage Providers**

#### **Local Storage Provider**
- Uses local filesystem for file storage
- JWT-based signed URLs for secure access
- Suitable for development and small deployments
- No external dependencies

#### **AWS S3 Storage Provider**
- Scalable cloud storage with 99.999999999% durability
- Presigned URLs for secure, time-limited access
- CloudFront CDN integration for global performance
- Server-side encryption and versioning support
- Automatic metadata management

### **How It Works**

The system automatically detects the configured storage provider and routes all file operations accordingly:

```typescript
// File uploads automatically use the configured provider
const result = await fileRegistryService.uploadFile(
  FileEntityType.PROFILE_PICTURE,
  file,
  userId
);

// File URLs automatically work for both providers
const url = await fileRegistryService.getFileUrl(
  FileEntityType.PROFILE_PICTURE,
  userId
);
```

### **Environment Configuration**

Copy `.env.s3.example` to `.env` and configure:

```bash
# Storage Provider Selection
STORAGE_PROVIDER=s3                    # 'local' or 's3'

# AWS S3 Configuration (only needed if STORAGE_PROVIDER=s3)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_S3_BUCKET_NAME=hec-files
AWS_CLOUDFRONT_DOMAIN=d123.cloudfront.net  # Optional CDN

# Advanced S3 Settings
AWS_S3_ACL=private                     # Access control
AWS_S3_STORAGE_CLASS=STANDARD          # Storage class
AWS_S3_SERVER_SIDE_ENCRYPTION=AES256   # Encryption
AWS_S3_PRESIGNED_URL_EXPIRY=3600       # URL expiry (seconds)
```

## Development Guide and Conventions

This project follows a set of development conventions to ensure consistency, maintainability, and high code quality. Please refer to the following documentation for detailed guidelines:

- [General Development Conventions](docs/conventions/DEVELOPMENT_CONVENTION.md) - Overall project conventions and best practices
- [API Response Conventions](docs/conventions/API_RESPONSE_CONVENTION.md) - Guidelines for API response formatting and documentation
- [Authentication Conventions](docs/conventions/AUTHENTICATION_CONVENTION.md) - Guidelines for authentication and authorization
- [Database Conventions](docs/conventions/DATABASE_CONVENTION.md) - Guidelines for database design and data access
- [Audit Logging Conventions](docs/conventions/AUDIT_LOGGING_CONVENTION.md) - Guidelines for tracking entity creation and modification
- [File Upload Conventions](docs/conventions/FILE_UPLOAD_CONVENTION.md) - Guidelines for file uploads, storage, and serving
- [Testing Conventions](docs/conventions/TESTING_CONVENTION.md) - Guidelines for ensuring code quality and API stability
- [API Versioning Conventions](docs/conventions/API_VERSIONING_CONVENTION.md) - Guidelines for API versioning and evolution
- [Feature Flag Conventions](docs/conventions/FEATURE_FLAG_CONVENTION.md) - Guidelines for controlled feature rollout
- [API Stability Strategy](docs/conventions/STABILITY_STRATEGY.md) - Comprehensive strategy for maintaining API stability

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm (v6 or later)
- PostgreSQL (v12 or later)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up environment variables (copy `.env.example` to `.env` and update values)
4. Start the development server:
   ```bash
   npm run start:dev
   ```

### Database Management

- Run migrations:
  ```bash
  npm run migration:run
  ```
- Generate a new migration:
  ```bash
  npm run migration:generate -- -n src/database/migrations/MigrationName
  ```
- Revert migrations:
  ```bash
  npm run migration:revert
  ```

## API Documentation

API documentation is available via Swagger UI at `/api-docs` when the server is running.

Detailed documentation of all APIs and their main algorithms is available in the [API Documentation](docs/api-documentation/README.md) directory. This documentation is organized by modules and includes information about endpoints, request/response formats, and implementation details.

## Git Workflow

- Development branch: `dev`
- Staging branch: `staging`
- Production branch: `production`

All pull requests should be made to the `dev` branch. The lead developer will handle merges to other branches.

## Contributing to Conventions

The development conventions are living documents that should evolve with the project. If you have suggestions for improving the conventions or notice inconsistencies, please:

1. Discuss the proposed changes with the team
2. Update the relevant convention document
3. Submit a pull request with your changes

All team members are encouraged to contribute to the conventions to ensure they remain relevant and effective.

---
