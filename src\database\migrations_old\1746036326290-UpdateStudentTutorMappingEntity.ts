import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateStudentTutorMappingEntity1746036326290 implements MigrationInterface {
    name = 'UpdateStudentTutorMappingEntity1746036326290'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "student_tutor_mapping" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "student_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "plan_feature_id" uuid NOT NULL, "status" "public"."student_tutor_mapping_status_enum" NOT NULL DEFAULT 'active', "assigned_date" TIMESTAMP NOT NULL, "last_activity_date" TIMESTAMP, "notes" text, CONSTRAINT "UQ_923a8a79073b6f291fafe813288" UNIQUE ("student_id", "plan_feature_id"), CONSTRAINT "PK_c7fd7f7a2f25d9230bce48147ab" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" ADD CONSTRAINT "FK_2c4b0b8b5a845fa92703bd5f9c9" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" ADD CONSTRAINT "FK_e244b2500b4cd575dde3aca20d6" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" ADD CONSTRAINT "FK_77c7b2d7e9b869ab6c22d7461a9" FOREIGN KEY ("plan_feature_id") REFERENCES "plan_feature"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" DROP CONSTRAINT "FK_77c7b2d7e9b869ab6c22d7461a9"`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" DROP CONSTRAINT "FK_e244b2500b4cd575dde3aca20d6"`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" DROP CONSTRAINT "FK_2c4b0b8b5a845fa92703bd5f9c9"`);
        await queryRunner.query(`DROP TABLE "student_tutor_mapping"`);
    }

}
