# Tutor Assignment Production Monitoring Guide

## 🚨 Critical Alerts (Immediate Response Required)

### 1. No Tutors Available
**Log Pattern**: `No active and confirmed tutors available for assignment`
**Impact**: Students won't get tutors assigned
**Action**: 
- Check tutor status in database
- Verify tutor confirmation process
- Consider manual assignment

### 2. Assignment Process Failure
**Log Pattern**: `CRITICAL: Failed to assign tutors for student`
**Impact**: Complete assignment failure for a student
**Action**:
- Check error context in logs
- Verify plan features are valid
- Manual assignment may be needed

### 3. High Assignment Failure Rate
**Metric**: >10% of assignments failing
**Impact**: Systematic issue affecting multiple students
**Action**:
- Check database connectivity
- Verify tutor availability
- Review recent code changes

## ⚠️ Warning Alerts (Monitor Closely)

### 1. Race Condition Detection
**Log Pattern**: `Race condition detected: Student .* already has assignment`
**Impact**: Indicates concurrent assignment attempts
**Action**: Monitor frequency, consider if load balancing needed

### 2. Chat Service Failures
**Log Pattern**: `Error creating chat conversation`
**Impact**: Tutors and students can't communicate
**Action**: Check chat service health

### 3. Tutor Reuse
**Log Pattern**: `All preferred tutors are unavailable, assigning excluded tutor`
**Impact**: Some tutors handling multiple modules per student
**Action**: Consider adding more tutors if frequent

## 📈 Success Metrics to Track

### 1. Assignment Success Rate
- **Target**: >95% of students get all required tutors
- **Calculation**: (Successful assignments / Total required assignments) * 100

### 2. Tutor Distribution Balance
- **Target**: No tutor has >3x the workload of others
- **Calculation**: Max tutor workload / Min tutor workload

### 3. Assignment Speed
- **Target**: <30 seconds per student
- **Measurement**: Time from subscription to all tutors assigned

## 🔍 Database Queries for Monitoring

### Check Unassigned Students
```sql
SELECT DISTINCT up.userId, pf.name as module_name
FROM user_plans up
JOIN plans p ON up.planId = p.id
JOIN plan_features pf ON p.id = pf.planId
LEFT JOIN student_tutor_mapping stm ON (
    stm.studentId = up.userId 
    AND stm.planFeatureId = pf.id 
    AND stm.status = 'ACTIVE'
)
WHERE up.isActive = true 
AND stm.id IS NULL;
```

### Check Tutor Workload Distribution
```sql
SELECT 
    u.name as tutor_name,
    COUNT(stm.id) as active_assignments,
    COUNT(DISTINCT stm.planFeatureId) as modules_covered
FROM users u
JOIN user_roles ur ON u.id = ur.userId
JOIN roles r ON ur.roleId = r.id
LEFT JOIN student_tutor_mapping stm ON (
    u.id = stm.tutorId 
    AND stm.status = 'ACTIVE'
)
WHERE r.name = 'tutor' 
AND u.isActive = true 
AND u.isConfirmed = true
GROUP BY u.id, u.name
ORDER BY active_assignments DESC;
```

### Check Assignment Failures
```sql
-- Look for students with incomplete assignments
SELECT 
    u.name as student_name,
    p.name as plan_name,
    COUNT(pf.id) as required_modules,
    COUNT(stm.id) as assigned_modules,
    (COUNT(pf.id) - COUNT(stm.id)) as missing_assignments
FROM users u
JOIN user_plans up ON u.id = up.userId
JOIN plans p ON up.planId = p.id
JOIN plan_features pf ON p.id = pf.planId
LEFT JOIN student_tutor_mapping stm ON (
    u.id = stm.studentId 
    AND pf.id = stm.planFeatureId 
    AND stm.status = 'ACTIVE'
)
WHERE up.isActive = true
GROUP BY u.id, u.name, p.id, p.name
HAVING COUNT(stm.id) < COUNT(pf.id);
```

## 🛠️ Troubleshooting Playbook

### Issue: Student has no tutors assigned
1. Check if student has active subscription
2. Verify plan has features
3. Check if tutors are active and confirmed
4. Look for assignment errors in logs
5. Manual assignment if needed

### Issue: Uneven tutor distribution
1. Check tutor workload query
2. Verify preference logic is working
3. Consider adding more tutors
4. Review assignment algorithm

### Issue: Assignment taking too long
1. Check database performance
2. Verify chat service response times
3. Look for blocking operations
4. Consider async processing

## 📋 Daily Health Checks

### Morning Checklist
- [ ] Check overnight assignment failures
- [ ] Verify tutor availability
- [ ] Review assignment success rate
- [ ] Check for stuck assignments

### Weekly Review
- [ ] Analyze tutor workload distribution
- [ ] Review assignment performance trends
- [ ] Check for new failure patterns
- [ ] Update tutor capacity if needed

## 🚀 Performance Optimization

### Database Optimization
- Index on `student_tutor_mapping(studentId, planFeatureId, status)`
- Index on `users(isActive, isConfirmed, type)`
- Regular cleanup of inactive mappings

### Application Optimization
- Cache tutor availability queries
- Batch assignment operations
- Async chat conversation creation
- Connection pooling for database
