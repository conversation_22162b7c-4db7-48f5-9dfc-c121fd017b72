import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { NovelEntry } from './novel-entry.entity';
import { User } from './user.entity';

@Entity()
export class NovelFeedback extends AuditableBaseEntity {
  @Column({ name: 'entry_id' })
  entryId: string;

  @ManyToOne(() => NovelEntry, entry => entry.feedbacks)
  @JoinColumn({ name: 'entry_id' })
  entry: NovelEntry;

  @Column({ name: 'tutor_id' })
  tutorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @Column({ name: 'feedback', type: 'text' })
  feedback: string;
}
