import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateStudentOwnedItemTable1746400000000 implements MigrationInterface {
    name = 'CreateStudentOwnedItemTable1746400000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create owned item status enum
        await queryRunner.query(`CREATE TYPE "public"."owned_item_status_enum" AS ENUM('available', 'in_use', 'expired')`);
        
        // Create student owned item table
        await queryRunner.query(`
            CREATE TABLE "student_owned_item" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP DEFAULT now(),
                "created_by" character varying(36),
                "updated_by" character varying(36),
                "student_id" uuid NOT NULL,
                "shop_item_id" uuid NOT NULL,
                "purchase_id" uuid,
                "status" "public"."owned_item_status_enum" NOT NULL DEFAULT 'available',
                "acquired_date" TIMESTAMP NOT NULL DEFAULT now(),
                "expiry_date" TIMESTAMP,
                "last_used_date" TIMESTAMP,
                "is_favorite" boolean NOT NULL DEFAULT false,
                "notes" character varying,
                CONSTRAINT "PK_student_owned_item" PRIMARY KEY ("id")
            )
        `);
        
        // Create unique index on student_id and shop_item_id
        await queryRunner.query(`
            CREATE UNIQUE INDEX "IDX_student_owned_item_student_shop_item" ON "student_owned_item" ("student_id", "shop_item_id")
        `);
        
        // Add foreign key constraints
        await queryRunner.query(`
            ALTER TABLE "student_owned_item" 
            ADD CONSTRAINT "FK_student_owned_item_student" 
            FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        
        await queryRunner.query(`
            ALTER TABLE "student_owned_item" 
            ADD CONSTRAINT "FK_student_owned_item_shop_item" 
            FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE CASCADE ON UPDATE NO ACTION
        `);
        
        await queryRunner.query(`
            ALTER TABLE "student_owned_item" 
            ADD CONSTRAINT "FK_student_owned_item_purchase" 
            FOREIGN KEY ("purchase_id") REFERENCES "shop_item_purchase"("id") ON DELETE SET NULL ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_student_owned_item_purchase"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_student_owned_item_shop_item"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_student_owned_item_student"`);
        
        // Drop index
        await queryRunner.query(`DROP INDEX "IDX_student_owned_item_student_shop_item"`);
        
        // Drop table
        await queryRunner.query(`DROP TABLE "student_owned_item"`);
        
        // Drop enum
        await queryRunner.query(`DROP TYPE "public"."owned_item_status_enum"`);
    }
}
