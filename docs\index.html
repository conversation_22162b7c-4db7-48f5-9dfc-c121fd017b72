<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HEC Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        h1 {
            color: #2c3e50;
            margin-top: 50px;
            font-size: 2.5em;
        }

        h2 {
            color: #2c3e50;
            margin-top: 30px;
            font-size: 1.8em;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        h3 {
            color: #2c3e50;
            margin-top: 25px;
            font-size: 1.4em;
        }

        p {
            margin-bottom: 20px;
            font-size: 18px;
            color: #555;
        }

        .button-container {
            text-align: center;
            margin: 40px 0;
        }

        .button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 15px 30px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 18px;
            transition: all 0.3s;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }

        .logo {
            max-width: 150px;
            margin-top: 50px;
        }

        .loading {
            display: inline-block;
            width: 50px;
            height: 50px;
            border: 3px solid rgba(52, 152, 219, 0.3);
            border-radius: 50%;
            border-top-color: #3498db;
            animation: spin 1s ease-in-out infinite;
            margin: 20px auto;
        }

        .feature-list {
            margin: 20px 0;
            padding-left: 20px;
        }

        .feature-list li {
            margin-bottom: 10px;
        }

        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .module-card {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
        }

        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        .module-card h3 {
            margin-top: 0;
            color: #3498db;
        }

        .redirect-notice {
            text-align: center;
            margin-top: 40px;
            padding: 15px;
            background-color: #f1f9ff;
            border-radius: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
    <script>
        // Redirect to the documentation browser after a delay
        window.onload = function() {
            setTimeout(function() {
                window.location.href = "/docs/view-markdown.html?path=project-overview.md";
            }, 5000); // 5 seconds delay before redirecting
        };
    </script>
</head>
<body>
    <div class="header">
        <h1>HEC Backend Documentation</h1>
        <p>Comprehensive documentation for the HEC (Holistic Education Center) platform</p>
    </div>

    <h2>Welcome to HEC Documentation</h2>
    <p>
        The HEC (Holistic Education Center) platform is a comprehensive educational system designed to connect students with tutors
        for personalized learning experiences.
    </p>

    <p>
        This documentation provides detailed information about the platform's architecture, APIs, and integration guides.
        You will be redirected to the full documentation browser in a few seconds, where you can explore all aspects of the system.
    </p>

    <p>
        The documentation is organized into five main categories:
    </p>

    <ul class="feature-list">
        <li><strong>1. System Documentation</strong> - Core system architecture and concepts</li>
        <li><strong>2. API Reference</strong> - Comprehensive API endpoints and usage</li>
        <li><strong>3. Integration Guides</strong> - Practical guides for frontend developers</li>
        <li><strong>4. Development Resources</strong> - Conventions, guides, and best practices</li>
        <li><strong>5. API Testing Flow</strong> - Testing procedures and examples for APIs</li>
    </ul>

    <div class="redirect-notice">
        <div class="loading"></div>
        <p>You will be redirected to the detailed project documentation in a few seconds...</p>
        <p>If you are not redirected automatically, please click the button below to view the full documentation:</p>
        <div class="button-container">
            <a href="/docs/view-markdown.html?path=project-overview.md" class="button">Open Documentation Browser</a>
        </div>
    </div>
</body>
</html>
