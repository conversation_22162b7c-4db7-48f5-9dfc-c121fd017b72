import { ApiProperty } from '@nestjs/swagger';

/**
 * Represents a detailed error message
 */
export class ErrorDetail {
  @ApiProperty({ description: 'Field that caused the error', required: false })
  field?: string;

  @ApiProperty({ description: 'Error message', example: 'Invalid input' })
  error: string;

  constructor(error: string, field?: string) {
    this.error = error;
    this.field = field;
  }
}

/**
 * Unified API response structure for all endpoints
 * @template T Type of data returned in the response
 */
export class ApiResponse<T> {
  @ApiProperty({ example: true, description: 'Indicates if the request was successful' })
  success: boolean;

  @ApiProperty({ description: 'Response message', example: 'Operation completed successfully' })
  message: string;

  @ApiProperty({ description: 'HTTP status code', example: 200 })
  statusCode: number;

  @ApiProperty({ description: 'Response data', required: false })
  data?: T;

  @ApiProperty({ description: 'Error details if success is false', required: false })
  error?: {
    type: string;
    status: number;
    refId: string;
    detail?: string;
  };

  @ApiProperty({
    description: 'Validation errors by field',
    required: false,
    example: {
      email: ['Email must be a valid email address'],
      password: ['Password must be at least 8 characters long']
    }
  })
  validationErrors?: Record<string, string[]>;

  /**
   * Create a successful response
   * @param data The data to include in the response
   * @param message Success message
   * @param statusCode HTTP status code (default: 200)
   * @returns ApiResponse instance
   */
  static success<T>(data: T, message = 'Operation completed successfully', statusCode = 200): ApiResponse<T> {
    const response = new ApiResponse<T>();
    response.success = true;
    response.message = message;
    response.statusCode = statusCode;
    response.data = data;
    return response;
  }

  /**
   * Create an error response
   * @param message Error message
   * @param statusCode HTTP status code (default: 400)
   * @param validationErrors Validation errors by field
   * @returns ApiResponse instance
   */
  static error<T>(message: string, statusCode = 400, validationErrors?: Record<string, string[]>): ApiResponse<T> {
    const response = new ApiResponse<T>();
    response.success = false;
    response.message = message;
    response.statusCode = statusCode;

    // Create error object
    response.error = {
      type: 'Error',
      status: statusCode,
      refId: `ERR-${Date.now().toString(36)}-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`.toUpperCase()
    };

    // Add validation errors if present
    if (validationErrors) {
      response.validationErrors = validationErrors;
    }

    return response;
  }
}
