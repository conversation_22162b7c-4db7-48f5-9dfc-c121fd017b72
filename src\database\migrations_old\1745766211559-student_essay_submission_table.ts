import { MigrationInterface, QueryRunner } from "typeorm";

export class StudentEssaySubmissionTable1745766211559 implements MigrationInterface {
    name = 'StudentEssaySubmissionTable1745766211559'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_703b42d862207320da43d5144ad"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1cb1730e57651f9168f9b01017"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "task_id" SET NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_1cb1730e57651f9168f9b01017" ON "essay_task_submissions" ("task_id", "created_by") `);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_703b42d862207320da43d5144ad" FOREIGN KEY ("task_id") REFERENCES "essay_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_703b42d862207320da43d5144ad"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1cb1730e57651f9168f9b01017"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "task_id" DROP NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_1cb1730e57651f9168f9b01017" ON "essay_task_submissions" ("created_by", "task_id") `);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_703b42d862207320da43d5144ad" FOREIGN KEY ("task_id") REFERENCES "essay_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
