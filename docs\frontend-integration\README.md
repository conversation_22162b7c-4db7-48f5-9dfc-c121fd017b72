# HEC Frontend Integration Guides

This documentation provides clear integration flows for all major features of the HEC platform to minimize the need for verbal communication between backend and frontend teams. These guides focus exclusively on API endpoints, data flows, and integration patterns without prescribing specific frontend implementation approaches.

## Getting Started

Start with the [API Integration Guide](API_INTEGRATION_GUIDE.md) for a comprehensive overview of authentication, error handling, common patterns, and available integration guides.

## Module-Specific Integration Guides

1. [Student Friendship Integration Flow](student-friendship-integration.md)
   - **API Flow Diagrams**: Clear sequence of API calls for each user action
   - **Request/Response Examples**: Complete examples with all required fields
   - **Error Handling**: Common error scenarios and how to handle them
   - **API Endpoints**: Detailed API specifications for friendship management

2. [Diary Module Integration Flow](diary-module-integration.md)
   - **API Flow Diagrams**: Step-by-step API sequences for diary operations
   - **Lifecycle Management**: Clear transitions between diary entry states
   - **Data Models**: Complete data structures for all diary-related entities
   - **API Endpoints**: Detailed API specifications for diary management

3. [Member Management Integration Flow](member-management-integration-flow.md)
   - **User Registration**: Student registration, tutor registration, email verification
   - **Authentication**: User login, token management, password recovery
   - **Profile Management**: View profile, update profile, profile picture management
   - **Tutor Approval**: Approval process, approval management
   - **Student-Tutor Matching**: Manual assignment, auto assignment, view assignments

4. [Diary Module Integration Flow](diary-module-integration-flow.md)
   - **Diary Management**: Student diary, default skin management, tutor greeting
   - **Diary Entry Management**: Create and update entries, entry submission workflow, entry sharing
   - **Diary Skin Management**: Global skins, student skins, apply skins
   - **Tutor Feedback and Evaluation**: Review workflow, feedback and scoring, corrections
   - **Diary Missions**: Mission management, mission entries, mission feedback

5. [Shop Integration Flow](shop-integration-flow-revised.md)
   - **Shop Item Management**: Category management, shop item CRUD, skin to shop conversion
   - **Promotion Management**: Promotion CRUD, promotion code generation
   - **Shop Item Promotion Management**: Apply promotions to items and categories
   - **Shopping**: Cart management, checkout process, reward point purchase

6. [Promotion Management Integration Flow](promotion-management-integration.md)
   - **API Flow Diagrams**: Step-by-step API sequences for promotion management and application
   - **Category-Based Promotions**: How to implement category-specific discounts
   - **Integration with Shop**: How promotions integrate with shop items, cart, and checkout
   - **API Endpoints**: Detailed API specifications for promotion management

7. [Plan Promotion Integration](plan-promotion-integration.md)
   - **API Flow Diagrams**: Step-by-step API sequences for plan promotion management
   - **Plan-Based Promotions**: How to implement plan-specific discounts
   - **Integration with Plans**: How promotions integrate with subscription plans
   - **API Endpoints**: Detailed API specifications for plan promotion management

8. [Automatic Tutor Assignment Integration Flow](tutor-assignment-integration.md)
   - **API Flow Diagrams**: Step-by-step API sequences for tutor assignment
   - **Notification Integration**: How tutor assignments trigger notifications
   - **Chat Integration**: How tutor assignments enable student-tutor communication
   - **API Endpoints**: Detailed API specifications for tutor assignment

9. [Frontend Integration Review](frontend-integration-review.md)
   - **Existing Guides Analysis**: Review of current integration documentation
   - **Integration Flow Analysis**: Common patterns, strengths, and weaknesses
   - **Missing Guides**: Identification of features needing documentation

## Implementation Status

All the required backend functionality has been implemented, including:

- Student search and friendship management
- Diary follow requests and access control
- Shop management system with categories, items, cart, and checkout
- Student owned items management for purchased shop items
- Skin application system for diary and other features
- Automatic tutor assignment based on subscription plans
- Integration with the chat system for automatic conversation creation
- Integration with the notification system for all relevant events
- QR code generation for diary sharing
- Deep linking for all major features

The frontend team can now use these guides to implement the UI based on the API integration flows provided.

## Next Steps

1. Frontend team to implement the UI based on these integration guides
2. Testing of the integrated system
3. Deployment to production

## Contact

For any questions or issues with the implementation, please contact the backend team.
