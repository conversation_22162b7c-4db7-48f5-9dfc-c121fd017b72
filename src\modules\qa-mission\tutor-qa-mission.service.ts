import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { QATaskSubmissionMarking } from "src/database/entities/qa-task-submission-marking.entity";
import { QATaskSubmissions, QASubmissionStatus } from "src/database/entities/qa-task-submissions.entity";
import { QATaskSubmissionHistory } from "src/database/entities/qa-task-submission-history.entity";
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { QATaskSubmissionMarkingDto, CreateQATaskSubmissionMarkingDto } from 'src/database/models/qa-mission.dto';

import { TutorStudentDto } from 'src/database/models/tutor-matching.dto';
import { FeatureType } from 'src/database/entities/plan-feature.entity';
import { User } from 'src/database/entities/user.entity';
import { StudentTutorMapping } from 'src/database/entities/student-tutor-mapping.entity';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { PaginationService } from '../../common/services/pagination.service';
import { QASubmissionWithDetailsDto } from 'src/database/models/qa.dto';


@Injectable()
export class TutorQAMissionService {
  private readonly logger = new Logger(TutorQAMissionService.name);

  constructor(
    private readonly paginationService: PaginationService,
    @InjectRepository(QATaskSubmissions)
    private readonly qaTaskSubmissionsRepository: Repository<QATaskSubmissions>,
    @InjectRepository(QATaskSubmissionHistory)
    private readonly qaTaskSubmissionHistoryRepository: Repository<QATaskSubmissionHistory>,

    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    private dataSource: DataSource,
    private readonly notificationHelper: NotificationHelperService,
    private readonly deeplinkService: DeeplinkService
  ) {}

  private toQATaskSubmissionMarkingDto(taskSubmissionMark: QATaskSubmissionMarking): QATaskSubmissionMarkingDto {
    return {
      id: taskSubmissionMark.id,
      submissionId: taskSubmissionMark.submissionId,
      submissionHistoryId: taskSubmissionMark.submissionHistoryId,
      submissionFeedback: taskSubmissionMark.submissionFeedback,
      score: taskSubmissionMark.score,
      taskRemarks: taskSubmissionMark.taskRemarks,
    };
  }

  async getTutorStudents(tutorId: string): Promise<TutorStudentDto[]> {
    try {
      // Check if tutor exists
      const tutor = await this.userRepository.findOne({ where: { id: tutorId } });
      if (!tutor) {
        throw new NotFoundException(`Tutor with ID ${tutorId} not found`);
      }

      // Get assignments
      const assignments = await this.studentTutorMappingRepository.find({
        where: {
          tutorId,
        planFeature: {
          type: FeatureType.ENGLISH_QA_WRITING
        }},
        relations: ['student', 'planFeature']
      });

      // Map to DTOs
      return assignments.map(assignment => ({
        id: assignment.studentId,
        name: assignment.student.name,
        email: assignment.student.email,
        profilePicture: assignment.student.profilePicture,
        planFeatureId: assignment.planFeatureId,
        moduleName: assignment.planFeature.name,
        moduleType: assignment.planFeature.type,
        status: assignment.status,
        assignedDate: assignment.assignedDate,
        lastActivityDate: assignment.lastActivityDate
      }));
    } catch (error) {
      this.logger.error(`Error getting tutor students: ${error.message}`, error.stack);
      throw error;
    }
  }

  async markQATaskSubmission(
    qaSubmissionMarkDto: CreateQATaskSubmissionMarkingDto
  ): Promise<QATaskSubmissionMarkingDto> {
    const { submissionId } = qaSubmissionMarkDto;

    const queryRunner = this.dataSource.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const submissionRepo = queryRunner.manager.getRepository(QATaskSubmissions);
      const historyRepo = queryRunner.manager.getRepository(QATaskSubmissionHistory);
      const markingRepo = queryRunner.manager.getRepository(QATaskSubmissionMarking);

      const taskSubmission = await submissionRepo.findOne({
        where: {
          id: submissionId,
          status: QASubmissionStatus.SUBMITTED,
        },
      });

      if (!taskSubmission) {
        throw new NotFoundException('Task submission not found');
      }

      const taskSubmissionHistory = await historyRepo.findOne({
        where: {
          id: taskSubmission.latestSubmissionId,
          submissionId: submissionId,
        },
      });

      if (!taskSubmissionHistory) {
        throw new NotFoundException('Task submission history not found');
      }

      // Update submission status to MARKED
      taskSubmission.status = QASubmissionStatus.REVIEWED;
      await submissionRepo.save(taskSubmission);

      // Create marking record
      const taskSubmissionMark = markingRepo.create({
        submissionId: submissionId,
        submissionHistoryId: taskSubmissionHistory.id,
        submissionFeedback: qaSubmissionMarkDto.submissionFeedback,
        score: qaSubmissionMarkDto.score,
        taskRemarks: qaSubmissionMarkDto.taskRemarks,
      });

      const savedMark = await markingRepo.save(taskSubmissionMark);

      await queryRunner.commitTransaction();

      // Send notification to the student
      try {
        // Get the student ID from the submission
        const studentId = taskSubmission.createdBy;

        if (studentId) {
          // Generate deeplinks
          const webLink = this.deeplinkService.getWebLink(DeeplinkType.QA_REVIEW, {
            id: submissionId
          });

          const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.QA_REVIEW, {
            id: submissionId
          });

          // Create HTML content for rich notifications
          const htmlContent = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #333;">Q&A Submission Reviewed</h2>
              <p>Your Q&A submission has been reviewed by your tutor.</p>
              <p><strong>Score:</strong> ${qaSubmissionMarkDto.score}</p>
              <div style="margin: 20px 0;">
                <a href="${webLink}" style="background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; display: inline-block;">View Review</a>
              </div>
            </div>
          `;

          // Send notification
          await this.notificationHelper.notify(
            studentId,
            NotificationType.QA_REVIEW,
            'Q&A Submission Reviewed',
            `Your Q&A submission has been reviewed by your tutor.`,
            {
              relatedEntityId: submissionId,
              relatedEntityType: 'qa_submission',
              htmlContent: htmlContent,
              webLink: webLink,
              deepLink: deepLink,
              sendEmail: true,
              sendPush: true,
              sendInApp: true,
              sendMobile: true,
              sendSms: false,
              sendRealtime: false
            }
          );

          this.logger.log(`Sent notification to student ${studentId} for submission ${submissionId}`);
        }
      } catch (notificationError) {
        // Log the error but don't fail the marking process
        this.logger.error(`Error sending notification: ${notificationError.message}`, notificationError.stack);
      }

      return this.toQATaskSubmissionMarkingDto(savedMark);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to mark QA submission: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  // async getStudentSubmissionsForTutor(
  //       tutorId: string,
  //       paginationDto: PaginationDto
  //     ): Promise<ApiResponse<PagedListDto<QASubmissionListResponseDto>>> {
  //       const { skip, take } = this.paginationService.getPaginationParameters(paginationDto);
  //   // Step 1: Get assigned students
  //   const students = await this.getTutorStudents(tutorId);
  //   const studentUserIds = students.map(s => s.id);

  //   //if (!studentUserIds.length) return [];
  //   if (studentUserIds.length === 0) {
  //     return ApiResponse.success(
  //       this.paginationService.createPagedList([], 0, paginationDto),
  //       'No pending submissions found for your students'
  //     );
  //   }

  //   // Step 2: Get submissions with task and submission history where createdBy is in studentUserIds
  //   const submissions = await this.qaTaskSubmissionsRepository
  //     .createQueryBuilder("submission")
  //     .leftJoinAndSelect("submission.task", "task")
  //     .leftJoinAndSelect("submission.submissionHistory", "submissionHistory")
  //     .where("submission.createdBy IN (:...studentUserIds)", { studentUserIds })
  //     .getMany();

  //   return submissions;
  // }

  async getStudentSubmissionsForTutor(
      tutorId: string,
      paginationDto: PaginationDto
    ): Promise<ApiResponse<PagedListDto<QASubmissionWithDetailsDto>>> {
      try {
        const students = await this.getTutorStudents(tutorId);
        const studentUserIds = students.map(s => s.id);

        if (studentUserIds.length === 0) {
          return ApiResponse.success(
            this.paginationService.createPagedList([], 0, paginationDto),
            'No pending submissions found for your students'
          );
        }

        const submissions = await this.qaTaskSubmissionsRepository
          .createQueryBuilder('submission')
          .leftJoinAndSelect('submission.task', 'task')
          .where('submission.createdBy IN (:...studentUserIds)', { studentUserIds })
          .getMany();

        const totalCount = submissions.length;

        const result: QASubmissionWithDetailsDto[] = [];

        for (const submission of submissions) {
          const history = await this.qaTaskSubmissionHistoryRepository.findOne({
            where: {
              submissionId: submission.id,
              createdBy: submission.createdBy
            },
            order: {
              submissionDate: 'DESC',
              sequenceNumber: 'DESC'
            }
          });

          result.push({
            id: submission.id,
            status: submission.status,
            createdBy: submission.createdBy,
            createdAt: submission.createdAt,
            task: {
              id: submission.task?.id,
              title: submission.task?.title,
              description: submission.task?.description,
            },
            // submissionHistory: history.map(h => ({
            //   id: h.id,
            //   content: h.content,
            //   wordCount: h.wordCount,
            //   submissionDate: h.submissionDate
            // }))
            submissionHistory: history
            ? [{
                id: history.id,
                content: history.content,
                wordCount: history.wordCount,
                submissionDate: history.submissionDate
              }]
            : []
          });
        }
        return ApiResponse.success(
          this.paginationService.createPagedList(result, totalCount, paginationDto),
          'Submissions retrieved successfully'
        );

        //return ApiResponse.success(result, 'Submissions retrieved successfully');
      } catch (error) {
        this.logger.error(`Error getting student submissions for tutor: ${error.message}`, error.stack);
        throw error;
      }
    }

}


