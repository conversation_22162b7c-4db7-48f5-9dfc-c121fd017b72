import { Test, TestingModule } from '@nestjs/testing';
import { StudentFriendshipService } from './student-friendship.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { StudentFriendship, FriendshipStatus } from '../../database/entities/student-friendship.entity';
import { DiaryFollowRequest, FollowRequestStatus } from '../../database/entities/diary-follow-request.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { Repository } from 'typeorm';
import { ChatService } from '../chat/chat.service';
import { NotificationHelperService } from '../notification/notification-helper.service';

describe('StudentFriendshipService', () => {
  let service: StudentFriendshipService;
  let studentFriendshipRepository: Repository<StudentFriendship>;
  let diaryFollowRequestRepository: Repository<DiaryFollowRequest>;
  let userRepository: Repository<User>;
  let fileRegistryService: FileRegistryService;
  let profilePictureService: ProfilePictureService;
  let chatService: ChatService;
  let notificationService: NotificationHelperService;

  const mockStudentFriendshipRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    findAndCount: jest.fn(),
  };

  const mockDiaryFollowRequestRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    findAndCount: jest.fn(),
  };

  const mockQueryBuilder = {
    where: jest.fn().mockReturnThis(),
    andWhere: jest.fn().mockReturnThis(),
    getCount: jest.fn().mockResolvedValue(2),
    select: jest.fn().mockReturnThis(),
    orderBy: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    take: jest.fn().mockReturnThis(),
    getMany: jest.fn().mockResolvedValue([
      { id: 'user2', userId: 'USER2', name: 'Test User 2', profilePicture: 'profile2.jpg' },
      { id: 'user3', userId: 'USER3', name: 'Test User 3', profilePicture: null },
    ]),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
  };

  const mockFileRegistryService = {
    getFileUrlWithFallback: jest.fn(),
  };

  const mockProfilePictureService = {
    getProfilePictureUrl: jest.fn().mockResolvedValue('http://example.com/profile.jpg'),
  };

  const mockChatService = {
    getOrCreateConversation: jest.fn().mockResolvedValue({ id: 'conversation1' }),
  };

  const mockNotificationService = {
    notify: jest.fn().mockResolvedValue(true),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        StudentFriendshipService,
        {
          provide: getRepositoryToken(StudentFriendship),
          useValue: mockStudentFriendshipRepository,
        },
        {
          provide: getRepositoryToken(DiaryFollowRequest),
          useValue: mockDiaryFollowRequestRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: FileRegistryService,
          useValue: mockFileRegistryService,
        },
        {
          provide: ProfilePictureService,
          useValue: mockProfilePictureService,
        },
        {
          provide: ChatService,
          useValue: mockChatService,
        },
        {
          provide: NotificationHelperService,
          useValue: mockNotificationService,
        },
      ],
    }).compile();

    service = module.get<StudentFriendshipService>(StudentFriendshipService);
    studentFriendshipRepository = module.get<Repository<StudentFriendship>>(getRepositoryToken(StudentFriendship));
    diaryFollowRequestRepository = module.get<Repository<DiaryFollowRequest>>(getRepositoryToken(DiaryFollowRequest));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    fileRegistryService = module.get<FileRegistryService>(FileRegistryService);
    profilePictureService = module.get<ProfilePictureService>(ProfilePictureService);
    chatService = module.get<ChatService>(ChatService);
    notificationService = module.get<NotificationHelperService>(NotificationHelperService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('searchStudents', () => {
    it('should search for students and return results', async () => {
      // Mock data
      const query = 'test';
      const type = 'name';
      const currentUserId = 'user1';
      const paginationDto = { page: 1, limit: 10 };

      const friendships = [
        {
          id: 'friendship1',
          requesterId: currentUserId,
          requestedId: 'user2',
          status: FriendshipStatus.ACCEPTED,
          canViewDiary: true
        },
      ];

      // Mock repository methods

      mockStudentFriendshipRepository.find.mockResolvedValue(friendships);
      mockFileRegistryService.getFileUrlWithFallback.mockResolvedValue('http://example.com/profile2.jpg');

      // Call the method
      const result = await service.searchStudents(query, type, currentUserId, paginationDto);

      // Assertions
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('user.type = :type', { type: UserType.STUDENT });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('user.id != :currentUserId', { currentUserId });
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith('user.name LIKE :query', { query: '%test%' });
      expect(mockQueryBuilder.getCount).toHaveBeenCalled();
      expect(mockQueryBuilder.select).toHaveBeenCalledWith(['user.id', 'user.userId', 'user.name', 'user.profilePicture']);
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith('user.name', 'ASC');
      expect(mockQueryBuilder.skip).toHaveBeenCalledWith(0);
      expect(mockQueryBuilder.take).toHaveBeenCalledWith(10);
      expect(mockQueryBuilder.getMany).toHaveBeenCalled();

      expect(mockStudentFriendshipRepository.find).toHaveBeenCalledWith({
        where: [
          { requesterId: currentUserId },
          { requestedId: currentUserId }
        ]
      });

      expect(mockFileRegistryService.getFileUrlWithFallback).toHaveBeenCalledTimes(1);

      expect(result).toEqual({
        items: [
          {
            id: 'user2',
            userId: 'USER2',
            name: 'Test User 2',
            profilePicture: 'http://example.com/profile2.jpg',
            friendshipStatus: FriendshipStatus.ACCEPTED,
            isFriend: true,
            canViewDiary: true
          },
          {
            id: 'user3',
            userId: 'USER3',
            name: 'Test User 3',
            profilePicture: null,
            friendshipStatus: 'none',
            isFriend: false,
            canViewDiary: false
          }
        ],
        meta: {
          page: 1,
          limit: 10,
          total: 2
        }
      });
    });
  });

  describe('sendFriendRequest', () => {
    it('should send a friend request', async () => {
      // Mock data
      const requesterId = 'user1';
      const sendFriendRequestDto = { requestedId: 'user2', requestMessage: 'Hello!' };

      const requester = { id: requesterId, name: 'User 1', profilePicture: null };
      const requested = { id: 'user2', name: 'User 2', profilePicture: 'profile2.jpg' };

      const friendship = {
        id: 'friendship1',
        requesterId,
        requestedId: 'user2',
        status: FriendshipStatus.PENDING,
        requestMessage: 'Hello!',
        canViewDiary: false
      };

      // Mock repository methods
      mockUserRepository.findOne.mockImplementation((options) => {
        if (options.where.id === requesterId) {
          return Promise.resolve(requester);
        } else if (options.where.id === 'user2') {
          return Promise.resolve(requested);
        }
        return Promise.resolve(null);
      });

      mockStudentFriendshipRepository.findOne.mockResolvedValue(null);
      mockStudentFriendshipRepository.create.mockReturnValue(friendship);
      mockStudentFriendshipRepository.save.mockResolvedValue(friendship);
      mockFileRegistryService.getFileUrlWithFallback.mockResolvedValue('http://example.com/profile2.jpg');

      // Call the method
      const result = await service.sendFriendRequest(requesterId, sendFriendRequestDto);

      // Assertions
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: requesterId, type: UserType.STUDENT }
      });
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'user2', type: UserType.STUDENT }
      });
      expect(mockStudentFriendshipRepository.findOne).toHaveBeenCalledWith({
        where: [
          { requesterId, requestedId: 'user2' },
          { requesterId: 'user2', requestedId: requesterId }
        ]
      });
      expect(mockStudentFriendshipRepository.create).toHaveBeenCalledWith({
        requesterId,
        requestedId: 'user2',
        status: FriendshipStatus.PENDING,
        requestMessage: 'Hello!',
        canViewDiary: false
      });
      expect(mockStudentFriendshipRepository.save).toHaveBeenCalledWith(friendship);
      // The getFileUrlWithFallback method is called for both requester and requested profile pictures
      expect(mockFileRegistryService.getFileUrlWithFallback).toHaveBeenCalled();

      expect(result).toEqual({
        id: 'friendship1',
        requesterId,
        requesterName: 'User 1',
        requesterProfilePicture: 'http://example.com/profile.jpg',
        requestedId: 'user2',
        requestedName: 'User 2',
        requestedProfilePicture: 'http://example.com/profile.jpg',
        status: FriendshipStatus.PENDING,
        requestMessage: 'Hello!',
        canViewDiary: false,
        createdAt: undefined,
        friendId: requesterId,
        friendName: 'User 1',
        friendProfilePicture: 'http://example.com/profile.jpg',
        conversationId: null // null because status is PENDING, not ACCEPTED
      });
    });
  });
});
