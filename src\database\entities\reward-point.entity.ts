import { Enti<PERSON>, Column, <PERSON>To<PERSON>ne, JoinColumn } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

/**
 * Source of reward points
 * @enum {string}
 */
export enum RewardPointSource {
  /** Points from diary awards */
  DIARY_AWARD = 'diary_award',
  /** Points from play module */
  PLAY = 'play',
  /** Points from Q&A module */
  QA = 'qa',
  /** Points from novel module */
  NOVEL = 'novel',
  /** Points from essay module */
  ESSAY = 'essay',
  /** Points from shop purchases */
  SHOP_PURCHASE = 'shop_purchase',
  /** Points from admin adjustment */
  ADMIN_ADJUSTMENT = 'admin_adjustment'
}

/**
 * Type of reward point transaction
 * @enum {string}
 */
export enum RewardPointType {
  /** Points earned */
  EARNED = 'earned',
  /** Points spent */
  SPENT = 'spent',
  /** Points adjusted by admin */
  ADJUSTED = 'adjusted',
  /** Points expired */
  EXPIRED = 'expired'
}

@Entity()
export class RewardPoint extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({
    name: 'source',
    type: 'enum',
    enum: RewardPointSource
  })
  source: RewardPointSource;

  @Column({
    name: 'type',
    type: 'enum',
    enum: RewardPointType
  })
  type: RewardPointType;

  @Column({ name: 'points', type: 'int' })
  points: number;

  @Column({ name: 'reference_id', nullable: true })
  referenceId: string;

  @Column({ name: 'description', nullable: true, type: 'text' })
  description: string;

  @Column({ name: 'expiry_date', nullable: true })
  expiryDate: Date;
}
