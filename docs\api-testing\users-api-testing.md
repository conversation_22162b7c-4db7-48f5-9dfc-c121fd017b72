# Users API Testing Flow

This document outlines the testing flow for the Users API endpoints.

## Prerequisites

Before testing the Users API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (admin, tutor, student)
3. Set up your API testing tool (<PERSON><PERSON> recommended)

## User Profile Testing Flow

### Test Case 1: Get Own Profile

1. Authenticate with a valid user token
2. Send a GET request to `/api/users/profile`
3. Verify HTTP status code is 200 OK
4. Verify response contains all user profile fields
5. Verify data matches the authenticated user's information

### Test Case 2: Authentication Validation

1. Send a GET request to `/api/users/profile` without authentication
2. Send a GET request with an invalid token
3. Send a GET request with an expired token
4. Verify appropriate 401 Unauthorized responses for each case

### Test Case 3: Update Own Profile

1. Authenticate with a valid user token
2. Send a PUT request to `/api/users/profile` with updated profile data
3. Verify HTTP status code is 200 OK
4. Verify response contains updated profile information
5. Verify changes are persisted in the database
6. Verify non-updatable fields (userId/email) remain unchanged

### Test Case 4: Profile Update Validation

1. Test with invalid data formats (e.g., negative age)
2. Test with missing required fields
3. Test with attempt to update restricted fields (userId, email)
4. Verify appropriate validation errors are returned for each case

## Profile Picture Testing Flow

### Test Case 1: Upload Profile Picture

1. Authenticate with a valid user token
2. Send a POST request to `/api/users/profile-picture` with a valid image file
3. Verify HTTP status code is 200 OK
4. Verify response contains the URL of the uploaded image
5. Verify the image is accessible via the provided URL
6. Verify the profile picture URL is updated in the user profile

### Test Case 2: Profile Picture Validation

1. Test with non-image file types
2. Test with oversized images
3. Test with unsupported image formats
4. Verify appropriate validation errors are returned for each case

### Test Case 3: Delete Profile Picture

1. Authenticate with a valid user token
2. Send a DELETE request to `/api/users/profile-picture`
3. Verify HTTP status code is 200 OK
4. Verify the profile picture URL is removed from the user profile
5. Verify the image file is no longer accessible

## Admin User Management Testing Flow

### Test Case 1: Get All Users (Admin)

1. Authenticate with an admin token
2. Send a GET request to `/api/admin/users` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of users
5. Verify pagination metadata (totalItems, currentPage, etc.)

### Test Case 2: Filter and Sort Users

1. Test filtering by user type (student, tutor, admin)
2. Test filtering by active status
3. Test filtering by confirmation status
4. Test sorting by different fields (createdAt, name, etc.)
5. Test combining multiple filters
6. Verify filtered and sorted results match the criteria

### Test Case 3: Get User by ID (Admin)

1. Authenticate with an admin token
2. Send a GET request to `/api/admin/users/{userId}`
3. Verify HTTP status code is 200 OK
4. Verify response contains the user's complete information
5. Test with non-existent user ID and verify 404 Not Found response

### Test Case 4: Update User (Admin)

1. Authenticate with an admin token
2. Send a PUT request to `/api/admin/users/{userId}` with updated user data
3. Verify HTTP status code is 200 OK
4. Verify response contains updated user information
5. Verify changes are persisted in the database
6. Test updating different user types (student, tutor, admin)

### Test Case 5: Deactivate/Activate User (Admin)

1. Authenticate with an admin token
2. Send a PUT request to `/api/admin/users/{userId}` with isActive=false
3. Verify HTTP status code is 200 OK
4. Verify user is deactivated in the database
5. Verify deactivated user cannot log in
6. Repeat with isActive=true to reactivate the user
7. Verify reactivated user can log in

### Test Case 6: Role-Based Access Control

1. Authenticate with a student token
2. Attempt to access admin endpoints
3. Verify 403 Forbidden responses
4. Repeat with a tutor token
5. Verify 403 Forbidden responses

## Tutor Management Testing Flow

### Test Case 1: Get All Tutors

1. Authenticate with any valid token
2. Send a GET request to `/api/tutors` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of tutors
5. Verify only tutor-type users are returned

### Test Case 2: Filter and Sort Tutors

1. Test filtering by specialization
2. Test filtering by rating range
3. Test sorting by rating, experience, etc.
4. Verify filtered and sorted results match the criteria

### Test Case 3: Get Tutor by ID

1. Authenticate with any valid token
2. Send a GET request to `/api/tutors/{tutorId}`
3. Verify HTTP status code is 200 OK
4. Verify response contains the tutor's public information
5. Test with non-existent tutor ID and verify 404 Not Found response

### Test Case 4: Get Assigned Tutors

1. Authenticate with a student token
2. Send a GET request to `/api/students/assigned-tutors`
3. Verify HTTP status code is 200 OK
4. Verify response contains list of tutors assigned to the student
5. Verify tutor assignments match the student's active plan

## Student Management Testing Flow

### Test Case 1: Get All Students (Tutor)

1. Authenticate with a tutor token
2. Send a GET request to `/api/tutors/assigned-students` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of students assigned to the tutor
5. Verify only students assigned to the authenticated tutor are returned

### Test Case 2: Get Student by ID (Tutor)

1. Authenticate with a tutor token
2. Send a GET request to `/api/tutors/students/{studentId}`
3. Verify HTTP status code is 200 OK if the student is assigned to the tutor
4. Verify 403 Forbidden if the student is not assigned to the tutor
5. Test with non-existent student ID and verify 404 Not Found response

## Edge Cases and Security Testing

### Test Case 1: Cross-User Access Attempts

1. Authenticate with a student token
2. Attempt to access another student's profile
3. Verify 403 Forbidden response
4. Authenticate with a tutor token
5. Attempt to access another tutor's assigned students
6. Verify 403 Forbidden response

### Test Case 2: Input Sanitization

1. Test with inputs containing potentially malicious content (SQL injection, XSS)
2. Verify inputs are properly sanitized
3. Verify no security vulnerabilities are exposed

### Test Case 3: Rate Limiting

1. Send multiple requests in rapid succession
2. Verify rate limiting is applied after threshold is reached
3. Verify appropriate error messages are returned
