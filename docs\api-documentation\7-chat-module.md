# HEC Chat System

The HEC Chat System provides realtime messaging capabilities between students and tutors. It allows for one-to-one communication with support for text messages, images, files, and quiz deep links.

## Features

- **Realtime Messaging**: Instant message delivery using WebSockets (Socket.io)
- **Message History**: Persistent storage of all messages with pagination
- **File Sharing**: Support for image and file attachments up to 10MB
- **Quiz Deep Links**: <PERSON><PERSON> can send quiz deep links to students
- **Read Receipts**: Track when messages are delivered and read
- **Typing Indicators**: Show when a user is typing in real-time
- **Online Status**: Display user online/offline status
- **Notifications**: Receive in-app and email notifications for new messages
- **Conversation Management**: Create, view, and manage conversations
- **User Contacts**: Automatically available contacts based on student-tutor mappings
- **Admin Oversight**: Admin tools to view and manage all conversations

## Architecture

The chat system consists of the following components:

1. **WebSocket Gateway**: Handles realtime communication using Socket.io
   - Manages user connections and authentication
   - Handles message delivery in real-time
   - Provides typing indicators and read receipts
   - Tracks user online/offline status

2. **REST API**: Provides endpoints for message history and file uploads
   - Chat Controller: User-facing API for conversations and messages
   - Admin Chat Controller: Admin-specific endpoints for oversight
   - File upload and retrieval endpoints

3. **Database Entities**: Stores conversations, messages, and attachments
   - Conversation: Represents a chat thread between two users
   - Message: Stores individual messages with metadata
   - MessageAttachment: Stores file attachments for messages
   - MessageRegistry: Registers files in the file system

4. **File Storage**: Manages uploaded files and images
   - Handles file uploads with size validation
   - Stores files in a structured directory
   - Provides secure file retrieval
   - Supports various file types including images, documents, etc.

5. **Notification System**: Alerts users about new messages
   - Integrates with the platform's notification module
   - Sends in-app and email notifications
   - Provides unread message counts

## Database Schema

The chat system uses the following database entities:

### Conversation Entity

```typescript
@Entity()
export class Conversation extends AuditableBaseEntity {
  @Column({
    name: 'type',
    type: 'enum',
    enum: ConversationType,
    default: ConversationType.DIRECT
  })
  type: ConversationType; // 'direct' or 'group'

  @Column({
    name: 'status',
    type: 'enum',
    enum: ConversationStatus,
    default: ConversationStatus.ACTIVE
  })
  status: ConversationStatus; // 'active', 'archived', or 'blocked'

  @Column({ name: 'participant1_id' })
  participant1Id: string;

  @Column({ name: 'participant2_id' })
  participant2Id: string;

  @Column({ name: 'last_message_at', nullable: true })
  lastMessageAt: Date;

  @Column({ name: 'last_message_text', nullable: true, type: 'text' })
  lastMessageText: string;

  @Column({ name: 'last_message_sender_id', nullable: true })
  lastMessageSenderId: string;

  @Column({ name: 'participant1_unread_count', default: 0 })
  participant1UnreadCount: number;

  @Column({ name: 'participant2_unread_count', default: 0 })
  participant2UnreadCount: number;
}
```

### Message Entity

```typescript
@Entity()
export class Message extends AuditableBaseEntity {
  @Column({ name: 'conversation_id' })
  conversationId: string;

  @Column({ name: 'sender_id' })
  senderId: string;

  @Column({ name: 'recipient_id' })
  recipientId: string;

  @Column({
    name: 'type',
    type: 'enum',
    enum: MessageType,
    default: MessageType.TEXT
  })
  type: MessageType; // 'text', 'image', 'file', 'quiz', 'system'

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: MessageStatus,
    default: MessageStatus.SENT
  })
  status: MessageStatus; // 'sent', 'delivered', 'read', 'deleted'

  @Column({ name: 'read_at', nullable: true })
  readAt: Date;

  @Column({ name: 'delivered_at', nullable: true })
  deliveredAt: Date;

  @Column({ name: 'metadata', type: 'json', nullable: true })
  metadata: any;
}
```

### MessageAttachment Entity

```typescript
@Entity()
export class MessageAttachment extends AuditableBaseEntity {
  @Column({ name: 'message_id' })
  messageId: string;

  @Column({ name: 'file_path' })
  filePath: string;

  @Column({ name: 'file_name' })
  fileName: string;

  @Column({ name: 'mime_type' })
  mimeType: string;

  @Column({ name: 'file_size' })
  fileSize: number;

  @Column({ name: 'thumbnail_path', nullable: true })
  thumbnailPath: string;
}
```

### MessageRegistry Entity

```typescript
@Entity()
export class MessageRegistry extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'file_path' })
  filePath: string;

  @Column({ name: 'file_name' })
  fileName: string;

  @Column({ name: 'mime_type' })
  mimeType: string;

  @Column({ name: 'file_size' })
  fileSize: number;

  @Column({ name: 'thumbnail_path', nullable: true })
  thumbnailPath: string;

  @Column({ name: 'is_temporary', default: true })
  isTemporary: boolean;
}
```

## API Endpoints

### Chat Controller

#### Conversations

- `GET /chat/conversations`
  - **Description**: Get all conversations for the current user
  - **Query Parameters**:
    - `page`: Page number (default: 1)
    - `limit`: Items per page (default: 10)
    - `sortBy`: Field to sort by (default: 'lastMessageAt')
    - `sortOrder`: Sort order ('ASC' or 'DESC', default: 'DESC')
    - `status`: Filter by status ('active', 'archived', 'blocked')
  - **Response**: PagedConversationListDto

- `GET /chat/conversations/:id`
  - **Description**: Get a conversation by ID
  - **Path Parameters**:
    - `id`: Conversation ID
  - **Response**: ConversationDto

#### Messages

- `GET /chat/conversations/:id/messages`
  - **Description**: Get messages for a conversation
  - **Path Parameters**:
    - `id`: Conversation ID
  - **Query Parameters**:
    - `type`: Filter by message type
    - `status`: Filter by message status
    - `search`: Search term for partial match on message content
    - `page`: Page number (default: 1)
    - `limit`: Items per page (default: 20)
  - **Response**: PagedMessageListDto

- `POST /chat/messages`
  - **Description**: Send a message
  - **Request Body**: CreateMessageDto
    ```json
    {
      "recipientId": "user-id",
      "type": "text",
      "content": "Hello, world!",
      "attachmentIds": ["attachment-id-1", "attachment-id-2"]
    }
    ```
  - **Response**: MessageDto

- `PUT /chat/messages/:id`
  - **Description**: Update a message (only sender can update)
  - **Path Parameters**:
    - `id`: Message ID
  - **Request Body**: UpdateMessageDto
    ```json
    {
      "content": "Updated message content"
    }
    ```
  - **Response**: MessageDto

- `POST /chat/conversations/:id/read`
  - **Description**: Mark all messages in a conversation as read
  - **Path Parameters**:
    - `id`: Conversation ID
  - **Response**: Empty success response

#### Files

- `POST /chat/upload`
  - **Description**: Upload a file for a message
  - **Request**: multipart/form-data with 'file' field
  - **Response**: ChatFileUploadResponseDto
    ```json
    {
      "id": "file-id",
      "fileName": "example.jpg",
      "fileUrl": "http://example.com/chat/files/file-id",
      "mimeType": "image/jpeg",
      "fileSize": 1024
    }
    ```

- `GET /chat/files/:id`
  - **Description**: Get a file by ID
  - **Path Parameters**:
    - `id`: File ID
  - **Response**: File content with appropriate Content-Type

#### Contacts

- `GET /chat/contacts`
  - **Description**: Get available chat contacts for the current user
  - **Response**: Array of ConversationParticipantDto

### Admin Chat Controller

#### Conversations

- `GET /admin/chat/conversations`
  - **Description**: Get all conversations (admin)
  - **Query Parameters**:
    - `page`: Page number (default: 1)
    - `limit`: Items per page (default: 10)
    - `sortBy`: Field to sort by (default: 'lastMessageAt')
    - `sortOrder`: Sort order ('ASC' or 'DESC', default: 'DESC')
    - `status`: Filter by status ('active', 'archived', 'blocked')
  - **Response**: PagedConversationListDto

- `GET /admin/chat/users/:userId/conversations`
  - **Description**: Get conversations for a specific user (admin)
  - **Path Parameters**:
    - `userId`: User ID
  - **Query Parameters**: Same as above
  - **Response**: PagedConversationListDto

- `GET /admin/chat/conversations/:id`
  - **Description**: Get a conversation by ID (admin)
  - **Path Parameters**:
    - `id`: Conversation ID
  - **Response**: ConversationDto

#### Messages

- `GET /admin/chat/conversations/:id/messages`
  - **Description**: Get messages for a conversation (admin)
  - **Path Parameters**:
    - `id`: Conversation ID
  - **Query Parameters**:
    - `type`: Filter by message type
    - `status`: Filter by message status
    - `search`: Search term for partial match on message content
    - `page`: Page number (default: 1)
    - `limit`: Items per page (default: 20)
  - **Response**: PagedMessageListDto

- `POST /admin/chat/messages`
  - **Description**: Send a message (admin)
  - **Request Body**: CreateMessageDto
  - **Response**: MessageDto

#### Users

- `GET /admin/chat/users`
  - **Description**: Get all users who can chat (admin)
  - **Query Parameters**:
    - `page`: Page number (default: 1)
    - `limit`: Items per page (default: 10)
    - `search`: Search term for name or email
    - `role`: Filter by role ('admin', 'tutor', 'student')
  - **Response**: PagedUserListDto

## WebSocket Events

### Client to Server Events

- `subscribe_conversation`
  - **Description**: Subscribe to a conversation to receive real-time updates
  - **Payload**:
    ```json
    {
      "conversationId": "conversation-id"
    }
    ```

- `unsubscribe_conversation`
  - **Description**: Unsubscribe from a conversation
  - **Payload**:
    ```json
    {
      "conversationId": "conversation-id"
    }
    ```

- `send_message`
  - **Description**: Send a message via WebSocket
  - **Payload**:
    ```json
    {
      "recipientId": "user-id",
      "type": "text",
      "content": "Hello, world!",
      "attachmentIds": ["attachment-id-1", "attachment-id-2"]
    }
    ```

- `mark_read`
  - **Description**: Mark all messages in a conversation as read
  - **Payload**:
    ```json
    {
      "conversationId": "conversation-id"
    }
    ```

- `typing`
  - **Description**: Indicate typing status
  - **Payload**:
    ```json
    {
      "conversationId": "conversation-id",
      "isTyping": true
    }
    ```

### Server to Client Events

- `connected`
  - **Description**: Connection confirmation
  - **Payload**:
    ```json
    {
      "userId": "user-id",
      "name": "User Name"
    }
    ```

- `subscribed_conversation`
  - **Description**: Subscription confirmation
  - **Payload**:
    ```json
    {
      "conversationId": "conversation-id"
    }
    ```

- `unsubscribed_conversation`
  - **Description**: Unsubscription confirmation
  - **Payload**:
    ```json
    {
      "conversationId": "conversation-id"
    }
    ```

- `new_message`
  - **Description**: New message notification
  - **Payload**: Complete MessageDto object
    ```json
    {
      "id": "message-id",
      "conversationId": "conversation-id",
      "senderId": "sender-id",
      "senderName": "Sender Name",
      "senderProfilePicture": "profile-picture-url",
      "recipientId": "recipient-id",
      "type": "text",
      "content": "Hello, world!",
      "status": "sent",
      "createdAt": "2023-01-01T12:00:00Z",
      "attachments": [
        {
          "id": "attachment-id",
          "fileName": "example.jpg",
          "fileUrl": "file-url",
          "thumbnailUrl": "thumbnail-url",
          "mimeType": "image/jpeg",
          "fileSize": 1024
        }
      ]
    }
    ```

- `messages_read`
  - **Description**: Messages read notification
  - **Payload**:
    ```json
    {
      "conversationId": "conversation-id",
      "userId": "user-id"
    }
    ```

- `messages_delivered`
  - **Description**: Messages delivered notification
  - **Payload**:
    ```json
    {
      "conversationId": "conversation-id",
      "userId": "user-id"
    }
    ```

- `typing_indicator`
  - **Description**: Typing status notification
  - **Payload**:
    ```json
    {
      "conversationId": "conversation-id",
      "userId": "user-id",
      "userName": "User Name",
      "isTyping": true
    }
    ```

- `user_status`
  - **Description**: User online/offline status notification
  - **Payload**:
    ```json
    {
      "userId": "user-id",
      "isOnline": true
    }
    ```

- `error`
  - **Description**: Error notification
  - **Payload**:
    ```json
    {
      "message": "Error message"
    }
    ```

## Usage

### Authentication

The chat system uses JWT authentication. The token must be provided in the `Authorization` header for REST API calls and in the `auth.token` field for WebSocket connections.

```javascript
// REST API Authentication
const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
};

// WebSocket Authentication
const socket = io('http://your-api-url/chat', {
  auth: { token },
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000
});
```

### Sending Messages

To send a message, use the `POST /chat/messages` endpoint with the following payload:

```json
{
  "recipientId": "user-id",
  "type": "text",
  "content": "Hello, world!",
  "attachmentIds": ["attachment-id-1", "attachment-id-2"]
}
```

Alternatively, you can send messages via WebSocket:

```javascript
socket.emit('send_message', {
  recipientId: 'user-id',
  type: 'text',
  content: 'Hello, world!',
  attachmentIds: ['attachment-id-1', 'attachment-id-2']
});
```

### Uploading Files

To upload a file, use the `POST /chat/upload` endpoint with a multipart/form-data request containing a `file` field:

```javascript
const formData = new FormData();
formData.append('file', fileObject);

fetch('/api/chat/upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    const fileId = data.data.id;
    // Use the file ID in a message
  }
});
```

### Handling Read Receipts

To mark messages as read, use the WebSocket event:

```javascript
socket.emit('mark_read', { conversationId: 'conversation-id' });
```

Listen for read receipts:

```javascript
socket.on('messages_read', (data) => {
  console.log(`Messages in conversation ${data.conversationId} were read by user ${data.userId}`);
  // Update message status in UI
});
```

### Typing Indicators

To send typing indicators:

```javascript
// When user starts typing
socket.emit('typing', { conversationId: 'conversation-id', isTyping: true });

// When user stops typing
socket.emit('typing', { conversationId: 'conversation-id', isTyping: false });
```

Listen for typing indicators:

```javascript
socket.on('typing_indicator', (data) => {
  if (data.userId !== currentUserId) {
    // Show or hide typing indicator in UI
    updateTypingIndicator(data.isTyping);
  }
});
```

## Next.js Integration

The chat system is designed to be integrated with a Next.js frontend application. For detailed integration instructions, please refer to the [Frontend Integration Guide](./FRONTEND_INTEGRATION_GUIDE.md).

### Quick Start

1. Install the required dependencies:

```bash
npm install socket.io-client
# or
yarn add socket.io-client
```

2. Create a custom hook to manage the WebSocket connection:

```typescript
// hooks/useChat.ts
import { useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';

export const useChat = (token: string) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!token) return;

    const socketInstance = io('http://your-api-url/chat', {
      auth: { token },
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    socketInstance.on('connect', () => {
      setIsConnected(true);
      setError(null);
    });

    socketInstance.on('disconnect', () => {
      setIsConnected(false);
    });

    socketInstance.on('connect_error', (err) => {
      setError(err.message);
      setIsConnected(false);
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, [token]);

  return { socket, isConnected, error };
};
```

3. Use the hook in your components to connect to the chat system:

```typescript
const { socket, isConnected } = useChat(token);

useEffect(() => {
  if (socket && isConnected) {
    // Subscribe to conversation
    socket.emit('subscribe_conversation', { conversationId });

    // Listen for new messages
    socket.on('new_message', (message) => {
      // Handle new message
    });

    return () => {
      socket.emit('unsubscribe_conversation', { conversationId });
      socket.off('new_message');
    };
  }
}, [socket, isConnected, conversationId]);
```

For complete implementation details, including handling file uploads, typing indicators, and read receipts, please refer to the [Frontend Integration Guide](./FRONTEND_INTEGRATION_GUIDE.md).

## Implementation Details

### Message Types

The chat system supports the following message types:

- `text`: Plain text messages
- `image`: Image attachments
- `file`: File attachments
- `quiz`: Quiz deep links
- `system`: System messages

### Message Status

Messages can have the following statuses:

- `sent`: Message has been sent
- `delivered`: Message has been delivered to the recipient
- `read`: Message has been read by the recipient
- `deleted`: Message has been deleted

### Conversation Status

Conversations can have the following statuses:

- `active`: Conversation is active
- `archived`: Conversation is archived
- `blocked`: Conversation is blocked

## Security Considerations

- All WebSocket connections are authenticated using JWT tokens
- Users can only access conversations they are participants in
- File uploads are validated for size and type
- Messages are sanitized to prevent XSS attacks
- Rate limiting is applied to prevent abuse

## Limitations

- Currently only supports one-to-one conversations
- Group conversations will be implemented in a future release
- Student-to-student chat is not enabled in the initial release
- File size is limited to 10MB per upload

## Conclusion

The HEC Chat System provides a comprehensive solution for real-time messaging between students and tutors. It offers a wide range of features including real-time messaging, file sharing, typing indicators, read receipts, and more. The system is designed to be easily integrated with the frontend application using the provided APIs and WebSocket events.

For detailed integration instructions, please refer to the [Frontend Integration Guide](./FRONTEND_INTEGRATION_GUIDE.md).
