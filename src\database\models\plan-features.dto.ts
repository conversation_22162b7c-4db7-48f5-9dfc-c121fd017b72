import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsOptional, IsArray, IsUUID, ArrayMinSize } from 'class-validator';
import { FeatureType } from '../entities/plan-feature.entity';

export class CreatePlanFeatureDto {
  @ApiProperty({
    example: 'hec_user_diary',
    description: 'The type of the feature',
    enum: FeatureType
  })
  @IsEnum(FeatureType)
  @IsNotEmpty({ message: 'Feature type is required' })
  type: FeatureType;

  @ApiProperty({
    example: 'HEC User Diary',
    description: 'The name of the feature'
  })
  @IsString()
  @IsNotEmpty({ message: 'Name is required' })
  name: string;

  @ApiProperty({
    example: 'Access to the HEC User Diary platform',
    description: 'Description of the feature'
  })
  @IsString()
  @IsNotEmpty({ message: 'Description is required' })
  description: string;
}

export class UpdatePlanFeatureDto {
  @ApiProperty({
    example: 'HEC User Diary',
    description: 'The name of the feature',
    required: false
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    example: 'Access to the HEC User Diary platform',
    description: 'Description of the feature',
    required: false
  })
  @IsString()
  @IsOptional()
  description?: string;
}

export class PlanFeatureResponseDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'The ID of the feature'
  })
  id: string;

  @ApiProperty({
    example: 'hec_user_diary',
    description: 'The type of the feature',
    enum: FeatureType
  })
  type: FeatureType;

  @ApiProperty({
    example: 'HEC User Diary',
    description: 'The name of the feature'
  })
  name: string;

  @ApiProperty({
    example: 'Access to the HEC User Diary platform',
    description: 'Description of the feature'
  })
  description: string;

  @ApiProperty({
    example: '2023-01-01T00:00:00.000Z',
    description: 'When the feature was created'
  })
  createdAt: Date;

  @ApiProperty({
    example: '2023-01-01T00:00:00.000Z',
    description: 'When the feature was last updated'
  })
  updatedAt: Date;
}

export class AssignFeaturesToPlanDto {
  @ApiProperty({
    example: ['123e4567-e89b-12d3-a456-************', '223e4567-e89b-12d3-a456-************'],
    description: 'Array of feature IDs to assign to the plan',
    type: [String]
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one feature ID is required' })
  @IsUUID('4', { each: true, message: 'Each feature ID must be a valid UUID' })
  featureIds: string[];
}
