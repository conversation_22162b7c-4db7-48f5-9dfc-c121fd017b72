import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TutorPermissionController } from './tutor-permission.controller';
import { TutorPermissionService } from './tutor-permission.service';
import { TutorPermission } from '../../database/entities/tutor-permission.entity';
import { User } from '../../database/entities/user.entity';
import { PlanFeature } from '../../database/entities/plan-feature.entity';
import { CommonModule } from '../../common/common.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TutorPermission,
      User,
      PlanFeature
    ]),
    CommonModule
  ],
  controllers: [TutorPermissionController],
  providers: [TutorPermissionService],
  exports: [TutorPermissionService]
})
export class PermissionsModule {}
