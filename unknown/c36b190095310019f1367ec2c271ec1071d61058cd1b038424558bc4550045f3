import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

/**
 * Enum representing the status of a friendship between students
 */
export enum FriendshipStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected'
}

/**
 * Entity representing a friendship between two students
 */
@Entity()
export class StudentFriendship extends AuditableBaseEntity {
  @Column({ name: 'requester_id' })
  requesterId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'requester_id' })
  requester: User;

  @Column({ name: 'requested_id' })
  requestedId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'requested_id' })
  requested: User;

  @Column({
    type: 'enum',
    enum: FriendshipStatus,
    default: FriendshipStatus.PENDING
  })
  status: FriendshipStatus;

  @Column({ name: 'request_message', nullable: true })
  requestMessage: string;

  @Column({ name: 'can_view_diary', default: false })
  canViewDiary: boolean;
}
