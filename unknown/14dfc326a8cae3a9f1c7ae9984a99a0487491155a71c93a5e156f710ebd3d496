# Authentication Flow

## Overview

This document describes the authentication flow in the HEC system, including registration, login, token management, and security considerations.

## Authentication Architecture

The HEC system uses a token-based authentication system with JWT (JSON Web Tokens). The authentication flow is designed to be secure, scalable, and provide a good user experience.

### Key Components

1. **Auth Controller**: Handles authentication-related HTTP requests
2. **Auth Service**: Implements authentication business logic
3. **JWT Strategy**: Validates JWT tokens for protected routes
4. **Auth Guard**: Protects routes based on authentication status and user roles
5. **Current User Middleware**: Attaches the current user to the request object

## Registration Flow

```
┌─────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Client  │      │Auth Controller│      │ Auth Service │      │ User Service │
└────┬────┘      └──────┬──────┘      └──────┬──────┘      └──────┬──────┘
     │                  │                     │                    │
     │  Register Request│                     │                    │
     │─────────────────>│                     │                    │
     │                  │                     │                    │
     │                  │ validateRegistration│                    │
     │                  │────────────────────>│                    │
     │                  │                     │                    │
     │                  │                     │  checkUserExists   │
     │                  │                     │───────────────────>│
     │                  │                     │                    │
     │                  │                     │<───────────────────│
     │                  │                     │                    │
     │                  │                     │  createUser        │
     │                  │                     │───────────────────>│
     │                  │                     │                    │
     │                  │                     │<───────────────────│
     │                  │                     │                    │
     │                  │                     │  sendVerificationEmail
     │                  │                     │─────────────────────>
     │                  │                     │                    │
     │                  │<────────────────────│                    │
     │                  │                     │                    │
     │ Registration Response                  │                    │
     │<─────────────────│                     │                    │
     │                  │                     │                    │
```

1. Client sends a registration request with user details (email, password, name, etc.)
2. Auth Controller validates the request format
3. Auth Service validates the registration data (password strength, email format, etc.)
4. Auth Service checks if the user already exists
5. If validation passes, Auth Service creates a new user with a hashed password
6. Auth Service generates a verification token and sends a verification email
7. Client receives a success response

## Login Flow

```
┌─────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Client  │      │Auth Controller│      │ Auth Service │      │ JWT Service  │
└────┬────┘      └──────┬──────┘      └──────┬──────┘      └──────┬──────┘
     │                  │                     │                    │
     │  Login Request   │                     │                    │
     │─────────────────>│                     │                    │
     │                  │                     │                    │
     │                  │  validateCredentials│                    │
     │                  │────────────────────>│                    │
     │                  │                     │                    │
     │                  │                     │  validatePassword  │
     │                  │                     │───────────────────>│
     │                  │                     │                    │
     │                  │                     │<───────────────────│
     │                  │                     │                    │
     │                  │                     │  generateTokens    │
     │                  │                     │───────────────────>│
     │                  │                     │                    │
     │                  │                     │<───────────────────│
     │                  │                     │                    │
     │                  │<────────────────────│                    │
     │                  │                     │                    │
     │ Login Response   │                     │                    │
     │ (tokens + user)  │                     │                    │
     │<─────────────────│                     │                    │
     │                  │                     │                    │
```

1. Client sends a login request with credentials (userId, password, rememberMe)
2. Auth Controller validates the request format
3. Auth Service validates the credentials against the stored user data
4. If validation passes, JWT Service generates access token and a random refresh token
5. The refresh token and its expiry date are stored in the user entity
6. Client receives tokens, user information, and refresh token expiry date
7. If "rememberMe" is true, tokens have extended expiration (30 days vs 1 day)

## Token Structure

### Access Token

```json
{
  "sub": "user-123",
  "username": "userId123",
  "email": "<EMAIL>",
  "roles": ["STUDENT"],
  "selectedRole": "STUDENT",
  "type": "STUDENT",
  "name": "John Doe",
  "defaultSkinId": "skin-123",
  "activePlan": "STANDARD",
  "planType": "STANDARD",
  "planId": "plan-456",
  "planExpiryDate": "2023-08-24T12:00:00.000Z",
  "planActive": true,
  "iat": 1625097600,
  "exp": 1625184000
}
```

### Refresh Token

The refresh token is a random string generated using crypto.randomBytes, not a JWT token. It is stored in the user entity in the database.

Example:
```
a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0
```

The refresh token has an expiry date stored in the database, which is returned to the client along with the token.

## Protected Route Access

```
┌─────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Client  │      │ API Gateway  │      │ JWT Strategy │      │ Auth Guard  │
└────┬────┘      └──────┬──────┘      └──────┬──────┘      └──────┬──────┘
     │                  │                     │                    │
     │  Request with    │                     │                    │
     │  Bearer Token    │                     │                    │
     │─────────────────>│                     │                    │
     │                  │                     │                    │
     │                  │  Extract Token      │                    │
     │                  │────────────────────>│                    │
     │                  │                     │                    │
     │                  │                     │  Validate Token    │
     │                  │                     │────────────────────>
     │                  │                     │                    │
     │                  │                     │<────────────────────
     │                  │                     │                    │
     │                  │<────────────────────│                    │
     │                  │                     │                    │
     │                  │  Check Permissions  │                    │
     │                  │────────────────────────────────────────>│
     │                  │                     │                    │
     │                  │<────────────────────────────────────────│
     │                  │                     │                    │
     │  Response        │                     │                    │
     │<─────────────────│                     │                    │
     │                  │                     │                    │
```

1. Client sends a request with the access token in the Authorization header
2. API Gateway extracts the token and passes it to the JWT Strategy
3. JWT Strategy validates the token (signature, expiration, etc.)
4. If the token is valid, the user information is attached to the request
5. Auth Guard checks if the user has the required permissions for the route
6. If authorized, the request is processed and a response is returned

## Token Refresh Flow

```
┌─────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Client  │      │Auth Controller│      │ Auth Service │      │ JWT Service  │
└────┬────┘      └──────┬──────┘      └──────┬──────┘      └──────┬──────┘
     │                  │                     │                    │
     │  Refresh Request │                     │                    │
     │  (refresh token) │                     │                    │
     │─────────────────>│                     │                    │
     │                  │                     │                    │
     │                  │  validateRefreshToken                    │
     │                  │────────────────────>│                    │
     │                  │                     │                    │
     │                  │                     │  verifyToken       │
     │                  │                     │───────────────────>│
     │                  │                     │                    │
     │                  │                     │<───────────────────│
     │                  │                     │                    │
     │                  │                     │  checkTokenReuse   │
     │                  │                     │────────────────────>
     │                  │                     │                    │
     │                  │                     │<────────────────────
     │                  │                     │                    │
     │                  │                     │  generateNewTokens │
     │                  │                     │───────────────────>│
     │                  │                     │                    │
     │                  │                     │<───────────────────│
     │                  │                     │                    │
     │                  │<────────────────────│                    │
     │                  │                     │                    │
     │ Refresh Response │                     │                    │
     │ (new tokens)     │                     │                    │
     │<─────────────────│                     │                    │
     │                  │                     │                    │
```

1. Client sends a refresh request with the refresh token
2. Auth Controller validates the request format
3. Auth Service validates the refresh token
4. JWT Service verifies the token signature and expiration
5. Auth Service checks if the token has been reused (security measure)
6. If validation passes, JWT Service generates new access and refresh tokens
7. Client receives new tokens

## Password Reset Flow

```
┌─────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Client  │      │Auth Controller│      │ Auth Service │      │Email Service │
└────┬────┘      └──────┬──────┘      └──────┬──────┘      └──────┬──────┘
     │                  │                     │                    │
     │ Forgot Password  │                     │                    │
     │ Request (email)  │                     │                    │
     │─────────────────>│                     │                    │
     │                  │                     │                    │
     │                  │ initiatePasswordReset                    │
     │                  │────────────────────>│                    │
     │                  │                     │                    │
     │                  │                     │ generateResetToken │
     │                  │                     │────────────────────>
     │                  │                     │                    │
     │                  │                     │<────────────────────
     │                  │                     │                    │
     │                  │                     │ sendResetEmail     │
     │                  │                     │───────────────────>│
     │                  │                     │                    │
     │                  │                     │<───────────────────│
     │                  │                     │                    │
     │                  │<────────────────────│                    │
     │                  │                     │                    │
     │ Success Response │                     │                    │
     │<─────────────────│                     │                    │
     │                  │                     │                    │
     │                  │                     │                    │
     │ Reset Password   │                     │                    │
     │ Request (token,  │                     │                    │
     │ new password)    │                     │                    │
     │─────────────────>│                     │                    │
     │                  │                     │                    │
     │                  │ resetPassword       │                    │
     │                  │────────────────────>│                    │
     │                  │                     │                    │
     │                  │                     │ validateResetToken │
     │                  │                     │────────────────────>
     │                  │                     │                    │
     │                  │                     │<────────────────────
     │                  │                     │                    │
     │                  │                     │ updatePassword     │
     │                  │                     │────────────────────>
     │                  │                     │                    │
     │                  │                     │<────────────────────
     │                  │                     │                    │
     │                  │<────────────────────│                    │
     │                  │                     │                    │
     │ Success Response │                     │                    │
     │<─────────────────│                     │                    │
     │                  │                     │                    │
```

1. Client sends a forgot password request with the user's email
2. Auth Controller validates the request format
3. Auth Service generates a password reset token
4. Email Service sends a reset email with a link containing the token
5. Client receives a success response
6. User clicks the link in the email and enters a new password
7. Client sends a reset password request with the token and new password
8. Auth Service validates the token and updates the password
9. Client receives a success response

## Email Verification Flow

```
┌─────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Client  │      │Auth Controller│      │ Auth Service │      │ User Service │
└────┬────┘      └──────┬──────┘      └──────┬──────┘      └──────┬──────┘
     │                  │                     │                    │
     │ Verify Email     │                     │                    │
     │ Request (token)  │                     │                    │
     │─────────────────>│                     │                    │
     │                  │                     │                    │
     │                  │ verifyEmail         │                    │
     │                  │────────────────────>│                    │
     │                  │                     │                    │
     │                  │                     │ validateToken      │
     │                  │                     │────────────────────>
     │                  │                     │                    │
     │                  │                     │<────────────────────
     │                  │                     │                    │
     │                  │                     │ markEmailVerified  │
     │                  │                     │───────────────────>│
     │                  │                     │                    │
     │                  │                     │<───────────────────│
     │                  │                     │                    │
     │                  │<────────────────────│                    │
     │                  │                     │                    │
     │ Success Response │                     │                    │
     │<─────────────────│                     │                    │
     │                  │                     │                    │
```

1. User clicks the verification link in the email
2. Client sends a verify email request with the token
3. Auth Controller validates the request format
4. Auth Service validates the verification token
5. User Service marks the user's email as verified
6. Client receives a success response

## Security Considerations

### Password Storage

Passwords are never stored in plain text. Instead, they are hashed using bcrypt with a salt:

```typescript
// Hashing a password
const hashedPassword = await bcrypt.hash(password, 10);

// Verifying a password
const isMatch = await bcrypt.compare(password, hashedPassword);
```

### Token Security

1. **Short-lived access tokens**: Access tokens expire after a short period (15-60 minutes)
2. **Token rotation**: Refresh tokens are rotated on each use to prevent token reuse
3. **Token revocation**: Refresh tokens can be revoked in case of suspicious activity
4. **HTTPS only**: Tokens are only transmitted over HTTPS
5. **Secure cookies**: When using cookies, they are set with HttpOnly, Secure, and SameSite flags

### Rate Limiting

To prevent brute force attacks, rate limiting is applied to authentication endpoints:

```typescript
// Rate limiting middleware
app.use('/api/auth/login', rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 requests per windowMs
  message: 'Too many login attempts, please try again later'
}));
```

### CORS Configuration

Cross-Origin Resource Sharing (CORS) is configured to only allow requests from trusted domains:

```typescript
// CORS configuration
app.enableCors({
  origin: process.env.ALLOWED_ORIGINS.split(','),
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  credentials: true
});
```

## Role-Based Access Control

The system implements role-based access control (RBAC) to restrict access to resources based on user roles:

```typescript
// Role guard
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>('roles', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.role === role);
  }
}

// Usage in controller
@Get('admin-dashboard')
@Roles(Role.ADMIN)
getAdminDashboard() {
  // Only accessible to admins
}
```

## Conclusion

The authentication flow in the HEC system is designed to be secure, scalable, and provide a good user experience. It follows industry best practices for token-based authentication and includes features like email verification, password reset, and role-based access control.
