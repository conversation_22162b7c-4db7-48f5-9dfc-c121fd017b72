import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Inject } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { UserType } from '../../database/entities/user.entity';
import LoggerService from '../services/logger.service';
import { Messages } from '../../constants/messages';
import { JwtPayload } from '../../modules/auth/interfaces/jwt-payload.interface';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    @Inject(LoggerService) private logger: LoggerService
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<UserType[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()]
    );

    // If no roles are required, allow access
    if (!requiredRoles || requiredRoles.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user as JwtPayload;

    // If no user is present in the request, deny access
    if (!user) {
      this.logger.warn(`Role check failed: No user in request - ${request.method} ${request.url}`);
      throw new ForbiddenException(Messages.FORBIDDEN);
    }

    this.logger.log(`Role check for user ${user.id} with type ${user.type} and roles ${JSON.stringify(user.roles)} - Required roles: ${JSON.stringify(requiredRoles)}`);

    // Check if user has the required role
    const hasRequiredRole = requiredRoles.some(role => user.type === role ||
      (user.roles && Array.isArray(user.roles) && user.roles.includes(role)));

    // Admin users can access all endpoints
    const isAdmin = user.type === UserType.ADMIN ||
      (user.roles && Array.isArray(user.roles) && user.roles.includes('admin'));

    if (hasRequiredRole || isAdmin) {
      this.logger.info(`Role check passed for user ${user.id} - ${request.method} ${request.url}`);
      return true;
    }

    // Log unauthorized access attempt
    this.logger.warn(
      `Role check failed: User ${user.id} with role ${user.type} attempted to access resource requiring ${requiredRoles.join(', ')} - ${request.method} ${request.url}`
    );

    throw new ForbiddenException(Messages.FORBIDDEN);
  }
}
