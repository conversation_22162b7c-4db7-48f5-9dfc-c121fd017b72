import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { AwardScheduleService } from './award-schedule.service';
import { AwardsService } from './awards.service';
import { AwardModule, AwardFrequency } from '../../database/entities/award.entity';
import { getCurrentUTCDate, addDaysUTC, addMonthsUTC } from '../../common/utils/date-utils';

@Injectable()
export class AwardScheduleScheduler {
  private readonly logger = new Logger(AwardScheduleScheduler.name);
  private isProcessing = false;
  private isGenerating = false;

  constructor(
    private readonly awardScheduleService: AwardScheduleService,
    private readonly awardsService: AwardsService,
  ) {}

  /**
   * Run at 1:00 AM UTC every day to generate new award schedules
   * This ensures we have schedules created well in advance for planning purposes
   */
  @Cron(CronExpression.EVERY_DAY_AT_1AM)
  async generateAwardSchedules() {
    if (this.isGenerating) {
      this.logger.log('Already generating schedules, skipping this run');
      return;
    }

    try {
      this.isGenerating = true;
      this.logger.log('Starting daily award schedule generation');

      // Get current date and end of year
      const today = getCurrentUTCDate();
      const currentYear = today.getUTCFullYear();
      const yearEnd = new Date(Date.UTC(currentYear, 11, 31)); // December 31st

      // Get all active awards
      this.logger.log('Fetching active awards from database');
      const allAwards = await this.awardsService.getAllAwards(undefined, false);

      // Group awards by module
      const awardsByModule = new Map<AwardModule, any[]>();
      for (const award of allAwards.items) {
        if (!awardsByModule.has(award.module)) {
          awardsByModule.set(award.module, []);
        }
        awardsByModule.get(award.module)!.push(award);
      }

      // Create schedules for each module's awards
      for (const [module, awards] of awardsByModule) {
        this.logger.log(`Creating schedules for module: ${module}`);
        for (const award of awards) {
          try {
            // Calculate number of schedules needed based on frequency
            let schedules: Date[] = [];
            const currentMonth = today.getUTCMonth();
            
            switch (award.frequency) {
              case AwardFrequency.WEEKLY:
                // Calculate remaining weeks in the year
                let weekDate = today;
                while (weekDate <= yearEnd) {
                  schedules.push(new Date(weekDate));
                  weekDate = addDaysUTC(weekDate, 7);
                }
                break;

              case AwardFrequency.MONTHLY:
                // Generate for remaining months in the year
                for (let month = currentMonth; month < 12; month++) {
                  schedules.push(new Date(Date.UTC(currentYear, month, 1)));
                }
                break;

              case AwardFrequency.QUARTERLY:
                // Calculate remaining quarters
                const currentQuarter = Math.floor(currentMonth / 3);
                for (let quarter = currentQuarter; quarter < 4; quarter++) {
                  schedules.push(new Date(Date.UTC(currentYear, quarter * 3, 1)));
                }
                break;

              case AwardFrequency.YEARLY:
                // Include next year's January 1st for yearly awards
                schedules.push(new Date(Date.UTC(currentYear + 1, 0, 1)));
                break;

              default:
                this.logger.warn(`Unsupported frequency ${award.frequency} for award ${award.name}`);
                continue;
            }

            // Create schedule for each date
            for (const scheduleDate of schedules) {
              try {
                const createScheduleDto = {
                  module: award.module,
                  frequency: award.frequency,
                  isActive: true
                };
                await this.awardScheduleService.createManualSchedule(createScheduleDto);
                this.logger.log(`Created schedule for award: ${award.name} (${award.frequency}) on ${scheduleDate.toISOString()}`);
              } catch (error) {
                if (!error.message?.includes('overlap')) {
                  this.logger.error(
                    `Error creating schedule for award ${award.name} on ${scheduleDate.toISOString()}: ${error.message}`,
                    error.stack
                  );
                } else {
                  this.logger.debug(`Skipping schedule creation for ${award.name} on ${scheduleDate.toISOString()} due to overlap`);
                }
              }
            }
          } catch (error) {
            this.logger.error(`Error processing award ${award.name}: ${error.message}`, error.stack);
          }
        }
      }

      this.logger.log('Successfully generated award schedules');
    } catch (error) {
      this.logger.error(`Error generating award schedules: ${error.message}`, error.stack);
    } finally {
      this.isGenerating = false;
    }
  }

  /**
   * Run at midnight every day to process active award schedules
   * This processes all active schedules that are due for the day
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async processAwardSchedules() {
    if (this.isProcessing) {
      this.logger.log('Already processing schedules, skipping this run');
      return;
    }

    try {
      this.isProcessing = true;
      this.logger.log('Starting daily award schedule processing');

      await this.awardScheduleService.processPendingSchedules();

      this.logger.log('Successfully processed award schedules');
    } catch (error) {
      this.logger.error(`Error processing award schedules: ${error.message}`, error.stack);
    } finally {
      this.isProcessing = false;
    }
  }
}
