import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserNotificationPreference } from '../../database/entities/user-notification-preference.entity';
import { NotificationType } from '../../database/entities/notification.entity';
import { NotificationChannel } from '../../database/entities/notification-delivery.entity';
import { User, UserType } from '../../database/entities/user.entity';

interface ChannelDecision {
  channel: NotificationChannel;
  isEnabled: boolean;
  reason?: string;
}

@Injectable()
export class NotificationChannelDecisionService {
  private readonly logger = new Logger(NotificationChannelDecisionService.name);

  constructor(
    @InjectRepository(UserNotificationPreference)
    private readonly userNotificationPreferenceRepository: Repository<UserNotificationPreference>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}

  /**
   * Make decisions about which channels to use for a notification
   * @param userId User ID
   * @param notificationType Notification type
   * @param requestedChannels Explicitly requested channels (optional)
   * @returns Array of channel decisions
   */
  async makeChannelDecisions(
    userId: string,
    notificationType: NotificationType,
    requestedChannels?: NotificationChannel[]
  ): Promise<ChannelDecision[]> {
    try {
      // Get user
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        return this.getDefaultDecisions(requestedChannels);
      }

      // Get user preferences
      const preferences = await this.userNotificationPreferenceRepository.find({
        where: { userId, notificationType }
      });

      // Get all available channels
      const allChannels = requestedChannels || Object.values(NotificationChannel);

      // Make decisions for each channel
      return await Promise.all(
        allChannels.map(channel => this.decideForChannel(channel, user, preferences))
      );
    } catch (error) {
      this.logger.error(`Error making channel decisions for user ${userId}: ${error.message}`, error.stack);
      // Fall back to in-app only in case of error
      return [
        { channel: NotificationChannel.IN_APP, isEnabled: true, reason: 'Fallback due to error' }
      ];
    }
  }

  /**
   * Decide whether to use a specific channel for a notification
   * @param channel Channel to decide on
   * @param user User
   * @param preferences User preferences
   * @returns Channel decision
   */
  private async decideForChannel(
    channel: NotificationChannel,
    user: User,
    preferences: UserNotificationPreference[]
  ): Promise<ChannelDecision> {
    // Find preference for this channel
    const preference = preferences.find(p => p.channel === channel);

    // If user has explicitly disabled this channel, respect that
    if (preference && !preference.isEnabled) {
      return {
        channel,
        isEnabled: false,
        reason: 'User preference'
      };
    }

    // Time-based preferences are not implemented in this version

    // Channel-specific logic
    switch (channel) {
      case NotificationChannel.EMAIL:
        // Email requires a valid email address
        if (!user.email) {
          return {
            channel,
            isEnabled: false,
            reason: 'No email address'
          };
        }
        break;

      case NotificationChannel.MOBILE:
        // Mobile requires a mobile app installation (not implemented yet)
        return {
          channel,
          isEnabled: false,
          reason: 'Mobile notifications not implemented'
        };

      case NotificationChannel.SMS:
        // SMS requires a phone number
        if (!user.phone) {
          return {
            channel,
            isEnabled: false,
            reason: 'No phone number'
          };
        }
        // SMS is not implemented yet
        return {
          channel,
          isEnabled: false,
          reason: 'SMS notifications not implemented'
        };
    }

    // Apply user type specific rules
    if (user.type === UserType.ADMIN) {
      // Admins get all notifications through all channels
      return {
        channel,
        isEnabled: true,
        reason: 'Admin user'
      };
    }

    // Default to enabled
    return {
      channel,
      isEnabled: true,
      reason: preference ? 'User preference' : 'Default setting'
    };
  }

  /**
   * Get default channel decisions
   * @param requestedChannels Requested channels
   * @returns Array of default channel decisions
   */
  private getDefaultDecisions(requestedChannels?: NotificationChannel[]): ChannelDecision[] {
    const channels = requestedChannels || [NotificationChannel.IN_APP];
    return channels.map(channel => ({
      channel,
      isEnabled: channel === NotificationChannel.IN_APP, // Only enable IN_APP by default
      reason: 'Default setting'
    }));
  }

  // Time-based preferences are not implemented in this version
}
