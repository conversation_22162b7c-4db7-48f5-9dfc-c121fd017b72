# Tutor Permission Management System - Frontend Integration Flow

This document provides a comprehensive integration flow for frontend developers to implement the Tutor Permission Management System, which allows admins to grant specific tutors permission to manage different features in the application.

## Table of Contents

1. [Permission Management](#permission-management)
   - [Permission CRUD](#permission-crud)
   - [Permission Filtering](#permission-filtering)
2. [Feature Access Control](#feature-access-control)
   - [Q&A Management Access](#qa-management-access)
   - [Permission Verification](#permission-verification)
3. [Implementation Notes](#implementation-notes)

## Permission Management

### Permission CRUD

First, create and manage tutor permissions to allow specific tutors to access admin features.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Get All    │      │  Create     │      │  Update     │      │  Delete     │
│  Permissions│ ──▶  │  Permission │ ──▶  │  Permission │ ──▶  │  Permission │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /admin/ │      │ POST /admin/│      │ PUT /admin/ │      │ DELETE      │
│ permissions │      │ permissions │      │ permissions/│      │ /admin/     │
│             │      │             │      │ :id         │      │ permissions/│
│             │      │             │      │             │      │ :id         │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /admin/permissions` - Get all permissions
- `GET /admin/permissions/:id` - Get permission by ID
- `POST /admin/permissions` - Create permission
- `PUT /admin/permissions/:id` - Update permission
- `DELETE /admin/permissions/:id` - Delete permission

### Permission Filtering

Filter and search for permissions based on various criteria.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Filter by  │      │  Filter by  │      │  Search by  │      │  Paginate   │
│  Status     │ ──▶  │  Feature    │ ──▶  │  Tutor Name │ ──▶  │  Results    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /admin/ │      │ GET /admin/ │      │ GET /admin/ │      │ GET /admin/ │
│ permissions?│      │ permissions?│      │ permissions?│      │ permissions?│
│ isActive=   │      │ planFeature │      │ searchTerm= │      │ page=2&     │
│ true        │      │ Id=xyz      │      │ john        │      │ limit=10    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /admin/permissions?isActive=true` - Filter by active status
- `GET /admin/permissions?planFeatureId=xyz` - Filter by plan feature
- `GET /admin/permissions?searchTerm=john` - Search by tutor name or email
- `GET /admin/permissions?page=2&limit=10` - Paginate results
- `GET /admin/permissions?sortBy=createdAt&sortDirection=DESC` - Sort results

## Feature Access Control

### Q&A Management Access

Tutors with permission can access Q&A management features.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Create     │      │  View All   │      │  Update     │      │  Delete     │
│  Question   │ ──▶  │  Questions  │ ──▶  │  Question   │ ──▶  │  Question   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /admin/│      │ GET /admin/ │      │ PUT /admin/ │      │ DELETE      │
│ qa/create   │      │ qa/questions│      │ qa/question/│      │ /admin/qa/  │
│             │      │             │      │ :id         │      │ question/:id│
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /admin/qa/create` - Create Q&A question
- `GET /admin/qa/questions` - Get all Q&A questions
- `GET /admin/qa/question/:id` - Get Q&A question by ID
- `PUT /admin/qa/question/:id` - Update Q&A question
- `DELETE /admin/qa/question/:id` - Delete Q&A question
- `POST /admin/qa/assign` - Assign Q&A question to student
- `POST /admin/qa/review/:id` - Review Q&A submission

### Permission Verification

The system verifies permissions before allowing access to protected features.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Request    │      │  Check JWT  │      │  Verify     │      │  Grant/Deny │
│  Access     │ ──▶  │  Token      │ ──▶  │  Permission │ ──▶  │  Access     │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ HTTP Request│      │ JWT Auth    │      │ Permission  │      │ 200 OK or   │
│ with JWT    │      │ Guard       │      │ Guard       │      │ 403 Forbidden│
│ Token       │      │             │      │             │      │             │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Request/Response Examples:**

**Create Permission Request:**
```json
POST /admin/permissions
{
  "tutorId": "123e4567-e89b-12d3-a456-426614174000",
  "planFeatureId": "123e4567-e89b-12d3-a456-426614174001",
  "notes": "Assigned to handle English grammar Q&A"
}
```

**Create Permission Response:**
```json
{
  "success": true,
  "message": "Permission granted successfully",
  "statusCode": 201,
  "data": {
    "id": "501480ed-8a3c-473f-a588-4a517e4a2a0e",
    "tutorId": "123e4567-e89b-12d3-a456-426614174000",
    "tutorName": "Tutor User",
    "tutorEmail": "<EMAIL>",
    "planFeatureId": "123e4567-e89b-12d3-a456-426614174001",
    "planFeatureName": "English Q&A Writing Platform",
    "grantedBy": "b0a784f1-0bda-4016-ab7d-94d0d2a52615",
    "grantedByName": "Admin User",
    "isActive": true,
    "notes": "Assigned to handle English grammar Q&A",
    "createdAt": "2023-05-07T13:20:06.024Z",
    "updatedAt": "2023-05-07T13:20:06.024Z"
  }
}
```

**Update Permission Request:**
```json
PUT /admin/permissions/501480ed-8a3c-473f-a588-4a517e4a2a0e
{
  "isActive": true,
  "notes": "Updated to handle all Q&A categories"
}
```

**Get Permissions with Filtering Response:**
```json
{
  "success": true,
  "message": "Permissions retrieved successfully",
  "statusCode": 200,
  "data": {
    "items": [
      {
        "id": "501480ed-8a3c-473f-a588-4a517e4a2a0e",
        "tutorId": "123e4567-e89b-12d3-a456-426614174000",
        "tutorName": "Tutor User",
        "tutorEmail": "<EMAIL>",
        "planFeatureId": "123e4567-e89b-12d3-a456-426614174001",
        "planFeatureName": "English Q&A Writing Platform",
        "grantedBy": "b0a784f1-0bda-4016-ab7d-94d0d2a52615",
        "grantedByName": "Admin User",
        "isActive": true,
        "notes": "Updated to handle all Q&A categories",
        "createdAt": "2023-05-07T13:20:06.024Z",
        "updatedAt": "2023-05-07T13:25:12.345Z"
      }
    ],
    "totalItems": 1,
    "currentPage": 1,
    "itemsPerPage": 10,
    "totalPages": 1
  }
}
```

## Implementation Notes

- **Permission Management**:
  - The system uses a unified permission model based on plan features
  - Permissions are stored in the `tutor_permission` table with references to tutors and plan features
  - Permissions can be temporarily disabled by setting `isActive` to false without deleting them
  - When creating or updating permissions, the system fetches the admin user information to include in the response

- **Permission Guard**:
  - The `TutorPermissionGuard` checks if a user is an admin or a tutor with permission for the specified plan feature
  - The `RequireFeaturePermission` decorator is used to specify which plan feature to check permissions for
  - Admins always have access to all features, regardless of permissions
  - The guard is applied to specific endpoints in feature controllers (e.g., Q&A controller)

- **Frontend Integration**:
  - The frontend should check the user's JWT token to determine which features they have permission to access
  - The navigation menu should only show admin feature links for features the user has permission to access
  - Permission management UI should include filtering, sorting, and pagination controls
  - Error handling should provide clear messages for permission-related issues

- **Error Handling**:
  - 400 Bad Request: Invalid input data (e.g., invalid tutor ID or plan feature ID)
  - 401 Unauthorized: User not authenticated
  - 403 Forbidden: User does not have permission to access the resource
  - 404 Not Found: Resource not found (e.g., tutor, plan feature, or permission)
  - 409 Conflict: Duplicate permission (e.g., tutor already has permission for the plan feature)

- **Security Considerations**:
  - All permission management endpoints are protected by the AdminGuard
  - Feature-specific endpoints are protected by both JwtAuthGuard and TutorPermissionGuard
  - The system validates that the user being granted permission is actually a tutor
  - The system validates that the plan feature exists before granting permission
