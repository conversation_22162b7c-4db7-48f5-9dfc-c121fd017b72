# Diary Skin to Shop Item Workflow

This document describes the workflow for converting diary skins to shop items, allowing administrators to publish diary skins to the shop for students to purchase and use.

## Overview

The diary skin to shop item workflow allows administrators to:

1. Create diary skins using the diary skin builder
2. Preview diary skins before publishing
3. Draft shop items from diary skins
4. Publish diary skins to the shop with customized details
5. Manage published diary skins in the shop

This workflow integrates the Diary Module with the Shop Module, providing a seamless experience for both administrators and students.

## Architecture

The diary skin to shop item workflow is implemented using the following components:

- **DiaryService**: Manages diary skins
- **ShopSkinService**: Handles the conversion of diary skins to shop items
- **ShopItemService**: Manages shop items
- **FileRegistryService**: Handles file URLs for diary skin preview images

The workflow uses a mapping table (`ShopSkinMapping`) to maintain the relationship between diary skins and shop items.

## Workflow Steps

### 1. Create Diary Skin

Administrators create diary skins using the diary skin builder. This is handled by the Diary Module.

**API Endpoint:** `POST /admin/diary/skins`

### 2. Draft Shop Item from Diary Skin

Before publishing a diary skin to the shop, administrators can get a draft shop item with suggested values.

**API Endpoint:** `GET /admin/shop/diary-skins/:id/draft`

**Response:**
```json
{
  "success": true,
  "message": "Draft shop item created successfully",
  "data": {
    "diarySkinId": "123e4567-e89b-12d3-a456-426614174000",
    "diarySkinName": "Modern Blue Theme",
    "diarySkinDescription": "A modern blue theme with clean typography",
    "previewImagePath": "/media/diary-skins/123e4567-e89b-12d3-a456-426614174000",
    "skinCategoryId": "123e4567-e89b-12d3-a456-426614174000",
    "skinCategoryName": "Skin",
    "suggestedItemNumber": "SK-001",
    "suggestedTitle": "Modern Blue Theme",
    "suggestedDescription": "A modern blue theme with clean typography",
    "suggestedPrice": 9.99,
    "suggestedIsPurchasableInRewardpoint": true,
    "suggestedPriceEquivalentToRewardPoint": 99,
    "suggestedType": "IN_APP_PURCHASE",
    "suggestedIsActive": true,
    "suggestedIsFeatured": false,
    "suggestedPromotionId": null,
    "suggestedMetadata": "",
    "isStudentCreated": false,
    "studentId": null
  },
  "errors": null,
  "statusCode": 200
}
```

### 3. Publish Diary Skin to Shop

Administrators publish diary skins to the shop with customized details.

**API Endpoint:** `POST /admin/shop/diary-skins/:id/publish`

**Request Body:**
```json
{
  "itemNumber": "SK-001",
  "title": "Modern Blue Theme",
  "description": "A modern blue theme with clean typography for your diary",
  "categoryId": "123e4567-e89b-12d3-a456-426614174000",
  "type": "IN_APP_PURCHASE",
  "price": 9.99,
  "isPurchasableInRewardpoint": true,
  "isActive": true,
  "isFeatured": false,
  "promotionId": null,
  "metadata": "modern,blue,clean"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Diary skin published to shop successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "itemNumber": "SK-001",
    "title": "Modern Blue Theme",
    "description": "A modern blue theme with clean typography for your diary",
    "categoryId": "123e4567-e89b-12d3-a456-426614174000",
    "categoryName": "Skin",
    "type": "IN_APP_PURCHASE",
    "price": 9.99,
    "isPurchasableInRewardpoint": true,
    "filePath": "/media/diary-skins/123e4567-e89b-12d3-a456-426614174000",
    "isActive": true,
    "isFeatured": false,
    "promotionId": null,
    "finalPrice": 9.99,
    "isOnSale": false,
    "discountPercentage": 0,
    "metadata": "modern,blue,clean",
    "purchaseCount": 0,
    "viewCount": 0,
    "createdAt": "2023-07-25T00:00:00.000Z",
    "updatedAt": "2023-07-25T00:00:00.000Z"
  },
  "errors": null,
  "statusCode": 200
}
```

## Key Features

### Automatic Category Assignment

If no category is provided when publishing a diary skin, the system automatically assigns the "Skin" category.

### File Path Handling

The diary skin's preview image is automatically used as the shop item's file path. No file upload is needed when publishing a diary skin to the shop.

### Free Items

When an item is marked as free (type = FREE), price is automatically set to 0 and isPurchasableInRewardpoint is set to false.

### Metadata Handling

The metadata field is passed through exactly as provided without any modifications.

## Implementation Details

### ShopSkinService

The `ShopSkinService` handles the conversion of diary skins to shop items. It provides the following methods:

- `createDraftShopItemFromDiarySkin`: Creates a draft shop item from a diary skin
- `createShopItemFromDiarySkin`: Creates a shop item from a diary skin
- `getDiarySkinIdForShopItem`: Gets the diary skin ID for a shop item
- `applyPurchasedSkinToDiary`: Applies a purchased skin to a user's diary

### ShopSkinMapping

The `ShopSkinMapping` entity maintains the relationship between diary skins and shop items:

```typescript
@Entity()
export class ShopSkinMapping {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  shopItemId: string;

  @Column({ type: 'uuid' })
  diarySkinId: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### File URL Generation

The `FileRegistryService` is used to generate proper URLs for diary skin preview images:

```typescript
const previewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(
  FileEntityType.DIARY_SKIN,
  diarySkin.id
);
```

## API Reference

### Draft Shop Item from Diary Skin

**Endpoint:** `GET /admin/shop/diary-skins/:id/draft`

**Description:** Returns a draft shop item with suggested values based on the diary skin. This endpoint provides default values for all fields that can be modified in the publish endpoint. The image file is automatically taken from the diary skin and cannot be changed. This does not create anything in the database.

**Parameters:**
- `id` (path): Diary skin ID

**Response:** `DiarySkinDraftShopItemResponseDto`

### Publish Diary Skin to Shop

**Endpoint:** `POST /admin/shop/diary-skins/:id/publish`

**Description:** Publishes an existing diary skin to the shop as a purchasable item. This endpoint is similar to the create shop item API, but with two key differences: 1) The category is fixed to "Skin" if not provided, and 2) The image file is automatically taken from the diary skin and cannot be changed - the file upload part is skipped and the diary skin's preview image path is used directly. All other fields match the standard shop item creation. The response will include the created shop item with filePath set to the diary skin's preview image path.

**Parameters:**
- `id` (path): Diary skin ID
- `publishDto` (body): `PublishDiarySkinToShopDto`

**Response:** `ShopItemResponseDto`

## Data Transfer Objects

### PublishDiarySkinToShopDto

```typescript
export class PublishDiarySkinToShopDto {
  @IsOptional()
  @IsString()
  itemNumber?: string;

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  description: string;

  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @IsOptional()
  @IsEnum(ShopItemType)
  type?: ShopItemType;

  @IsOptional()
  @IsNumber()
  @Min(0)
  price?: number;

  @IsOptional()
  @IsBoolean()
  isPurchasableInRewardpoint?: boolean;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsBoolean()
  isFeatured?: boolean;

  @IsOptional()
  @ValidateIf(o => o.promotionId !== null && o.promotionId !== undefined && o.promotionId !== '')
  @IsUUID()
  @Transform(({ value }) => {
    if (value === '' || value === undefined) {
      return null;
    }
    return value;
  })
  promotionId?: string | null;

  @IsOptional()
  @IsString()
  metadata?: string;
}
```

### DiarySkinDraftShopItemResponseDto

```typescript
export class DiarySkinDraftShopItemResponseDto {
  diarySkinId: string;
  diarySkinName: string;
  diarySkinDescription: string;
  previewImagePath: string;
  skinCategoryId: string;
  skinCategoryName: string;
  suggestedItemNumber: string;
  suggestedTitle: string;
  suggestedDescription: string;
  suggestedPrice: number;
  suggestedIsPurchasableInRewardpoint: boolean;
  suggestedPriceEquivalentToRewardPoint: number;
  suggestedType: ShopItemType;
  suggestedIsActive: boolean;
  suggestedIsFeatured: boolean;
  suggestedPromotionId: string | null;
  suggestedMetadata: string;
  isStudentCreated: boolean;
  studentId?: string;
}
```

## Frontend Integration

### Draft Shop Item

1. Call the draft endpoint to get suggested values
2. Display the draft values in a form
3. Allow the user to modify the values
4. Submit the form to the publish endpoint

### Publish Diary Skin

1. Submit the form with the modified values to the publish endpoint
2. Display the created shop item
3. Redirect to the shop item list or detail page

## Best Practices

1. Always call the draft endpoint before publishing to get suggested values
2. Set appropriate prices and reward points for shop items
3. Use meaningful metadata to help with search and filtering
4. Set appropriate active and featured flags based on your marketing strategy
5. Use promotions to offer discounts on shop items
