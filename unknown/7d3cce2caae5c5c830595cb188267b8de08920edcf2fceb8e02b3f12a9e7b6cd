# Users Module

The Users Module handles user management, including creating, updating, and retrieving user information. It supports different user types (admin, tutor, student) and provides APIs for profile management.

## Epics

1. **User Management**
2. **Profile Management**
3. **Role Management**

## APIs

### 1. Get All Users

**Endpoint:** `GET /users`

**Description:** Retrieves a paginated list of all users. Only accessible by admins.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by (default: 'createdAt')
- `sortOrder`: Sort order ('ASC' or 'DESC', default: 'DESC')

**Response:**
```json
{
  "success": true,
  "message": "Users retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "<PERSON>",
        "userId": "john123",
        "email": "<EMAIL>",
        "type": "student",
        "isActive": true,
        "isConfirmed": true,
        "roles": ["student"],
        "activePlan": "Pro",
        "activePlanDetails": {
          "id": "123e4567-e89b-12d3-a456-426614174000",
          "planId": "123e4567-e89b-12d3-a456-426614174000",
          "startDate": "2023-01-01T00:00:00.000Z",
          "endDate": "2024-01-01T00:00:00.000Z",
          "isActive": true,
          "isPaid": true,
          "plan": {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "name": "Pro",
            "type": "monthly"
          }
        }
      }
    ],
    "meta": {
      "totalItems": 100,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Apply pagination parameters
3. Retrieve users with pagination
4. Transform users to DTOs
5. Return paginated response

### 2. Get User Profile

**Endpoint:** `GET /users/profile`

**Description:** Retrieves the profile of the currently authenticated user.

**Response:**
```json
{
  "success": true,
  "message": "User profile retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "John Doe",
    "userId": "john123",
    "email": "<EMAIL>",
    "type": "student",
    "isActive": true,
    "isConfirmed": true,
    "roles": ["student"],
    "activePlan": "Pro",
    "activePlanDetails": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "planId": "123e4567-e89b-12d3-a456-426614174000",
      "startDate": "2023-01-01T00:00:00.000Z",
      "endDate": "2024-01-01T00:00:00.000Z",
      "isActive": true,
      "isPaid": true,
      "plan": {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Pro",
        "type": "monthly"
      }
    },
    "profilePicture": "uploads/profiles/123e4567-e89b-12d3-a456-426614174000.jpg",
    "phoneNumber": "1234567890",
    "address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "country": "USA",
    "postalCode": "10001",
    "bio": "Student at XYZ University",
    "dateOfBirth": "1990-01-01",
    "age": 33
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Retrieve user with relations (roles, plans)
3. Transform user to DTO
4. Return user profile

### 3. Update User Profile

**Endpoint:** `PATCH /users/profile`

**Description:** Updates the profile of the currently authenticated user.

**Request Body:**
```json
{
  "name": "John Doe",
  "phoneNumber": "1234567890",
  "address": "123 Main St",
  "city": "New York",
  "state": "NY",
  "country": "USA",
  "postalCode": "10001",
  "bio": "Student at XYZ University",
  "dateOfBirth": "1990-01-01",
  "socialLinks": {
    "facebook": "https://facebook.com/johndoe",
    "twitter": "https://twitter.com/johndoe"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "John Doe",
    "userId": "john123",
    "email": "<EMAIL>",
    "type": "student",
    "isActive": true,
    "isConfirmed": true,
    "roles": ["student"],
    "activePlan": "Pro",
    "profilePicture": "uploads/profiles/123e4567-e89b-12d3-a456-426614174000.jpg",
    "phoneNumber": "1234567890",
    "address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "country": "USA",
    "postalCode": "10001",
    "bio": "Student at XYZ University",
    "dateOfBirth": "1990-01-01",
    "age": 33,
    "socialLinks": {
      "facebook": "https://facebook.com/johndoe",
      "twitter": "https://twitter.com/johndoe"
    }
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Retrieve user
3. Update user with provided data
4. Save updated user
5. Transform updated user to DTO
6. Return updated profile

### 4. Upload Profile Picture

**Endpoint:** `POST /users/profile/picture`

**Description:** Uploads a profile picture for the currently authenticated user.

**Request:** Multipart form data with 'file' field containing the image

**Response:**
```json
{
  "success": true,
  "message": "Profile picture uploaded successfully",
  "data": {
    "profilePicture": "uploads/profiles/123e4567-e89b-12d3-a456-426614174000.jpg"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Validate file (type, size)
3. Generate unique filename
4. Save file to uploads directory
5. Update user's profile picture path
6. Return updated profile picture path

### 5. Create Admin User

**Endpoint:** `POST /users/admin`

**Description:** Creates a new admin user. Only accessible by admins.

**Request Body:**
```json
{
  "userId": "admin123",
  "name": "Admin User",
  "email": "<EMAIL>",
  "password": "Password123!",
  "confirmPassword": "Password123!",
  "phoneNumber": "1234567890",
  "gender": "male",
  "dateOfBirth": "1990-01-01"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Admin user created successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Admin User",
    "userId": "admin123",
    "email": "<EMAIL>",
    "type": "admin",
    "isActive": true,
    "isConfirmed": true,
    "roles": ["admin"]
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Validate admin access
2. Validate input data
3. Check if user with same email or userId already exists
4. Create new user with admin type
5. Hash the password
6. Assign admin role
7. Return created user

### 6. Create Tutor User

**Endpoint:** `POST /users/tutor`

**Description:** Creates a new tutor user. Only accessible by admins. Admin-created tutors are automatically approved.

**Request Body:**
```json
{
  "userId": "tutor123",
  "name": "Tutor User",
  "email": "<EMAIL>",
  "password": "Password123!",
  "confirmPassword": "Password123!",
  "phoneNumber": "1234567890",
  "gender": "female",
  "dateOfBirth": "1990-01-01",
  "bio": "Experienced tutor in mathematics"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tutor user created successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Tutor User",
    "userId": "tutor123",
    "email": "<EMAIL>",
    "type": "tutor",
    "isActive": true,
    "isConfirmed": true,
    "roles": ["tutor"]
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Validate admin access
2. Validate input data
3. Check if user with same email or userId already exists
4. Create new user with tutor type
5. Hash the password
6. Assign tutor role
7. Mark as confirmed (skip verification)
8. Return created user

### 7. Create Student User

**Endpoint:** `POST /users/student`

**Description:** Creates a new student user. Only accessible by admins. Admin-created students are automatically confirmed.

**Request Body:**
```json
{
  "userId": "student123",
  "name": "Student User",
  "email": "<EMAIL>",
  "password": "Password123!",
  "confirmPassword": "Password123!",
  "phoneNumber": "1234567890",
  "gender": "male",
  "dateOfBirth": "2000-01-01"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Student user created successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Student User",
    "userId": "student123",
    "email": "<EMAIL>",
    "type": "student",
    "isActive": true,
    "isConfirmed": true,
    "roles": ["student"]
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Validate admin access
2. Validate input data
3. Check if user with same email or userId already exists
4. Create new user with student type
5. Hash the password
6. Assign student role
7. Mark as confirmed (skip verification)
8. Return created user

### 8. Calculate Age

**Endpoint:** `POST /users/calculate-age`

**Description:** Calculates age based on date of birth.

**Request Body:**
```json
{
  "dateOfBirth": "1990-01-01"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Age calculated successfully",
  "data": {
    "age": 33
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Parse date of birth
2. Calculate age using current date
3. Return calculated age

## Features

1. **User Management**
   - Create admin users
   - Create tutor users
   - Create student users
   - List all users (admin only)

2. **Profile Management**
   - View user profile
   - Update user profile
   - Upload profile picture
   - Calculate age from date of birth

3. **Role Management**
   - Assign roles to users
   - View user roles

## Tasks

1. **Implement User Management APIs**
   - Create endpoints for creating different user types
   - Implement user listing with pagination
   - Add role assignment functionality

2. **Implement Profile Management APIs**
   - Create profile retrieval endpoint
   - Create profile update endpoint
   - Implement profile picture upload
   - Add age calculation functionality

3. **Implement Role Management**
   - Create role assignment endpoint
   - Implement role validation
   - Add role-based access control
