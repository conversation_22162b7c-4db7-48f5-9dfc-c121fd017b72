import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BlockGame } from '../../../database/entities/block-game.entity';
import { BlockGameSentence } from '../../../database/entities/block-game-sentence.entity';
import { BlockGameAttempt } from '../../../database/entities/block-game-attempt.entity';
import { User } from '../../../database/entities/user.entity';
import { BlockGameAdminController } from './block-game-admin.controller';
import { BlockGameAdminService } from './block-game-admin.service';
import { BlockGameController } from './block-game.controller';
import { BlockGameService } from './block-game.service';

@Module({
  imports: [TypeOrmModule.forFeature([BlockGame, BlockGameSentence, BlockGameAttempt, User])],
  controllers: [BlockGameAdminController, BlockGameController],
  providers: [BlockGameAdminService, BlockGameService],
  exports: [BlockGameAdminService, BlockGameService],
})
export class BlockGameModule {}
