import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DeeplinkService } from './deeplink.service';
import { ProfileLinkService } from './profile-link.utils';

@Module({
  imports: [ConfigModule],
  providers: [
    DeeplinkService,
    // Keep ProfileLinkService for backward compatibility
    {
      provide: ProfileLinkService,
      useExisting: DeeplinkService
    }
  ],
  exports: [DeeplinkService, ProfileLinkService],
})
export class DeeplinkModule {}
