import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { AppModule } from '../app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Plan } from '../database/entities/plan.entity';
import { PlanFeature, FeatureType } from '../database/entities/plan-feature.entity';
import { DiarySettingsTemplate } from '../database/entities/diary-settings-template.entity';

/**
 * Script to add diary settings templates as module features to plans
 */
async function bootstrap() {
  const logger = new Logger('AddModulesToPlans');
  logger.log('Starting to add modules to plans...');

  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    // Get repositories
    const planRepository = app.get<Repository<Plan>>(getRepositoryToken(Plan));
    const planFeatureRepository = app.get<Repository<PlanFeature>>(getRepositoryToken(PlanFeature));
    const diarySettingsTemplateRepository = app.get<Repository<DiarySettingsTemplate>>(
      getRepositoryToken(DiarySettingsTemplate)
    );

    // Get all diary settings templates
    const templates = await diarySettingsTemplateRepository.find();
    logger.log(`Found ${templates.length} diary settings templates`);

    if (templates.length === 0) {
      logger.warn('No diary settings templates found. Please create some first.');
      return;
    }

    // Create module features for each template if they don't exist
    const moduleFeatures: PlanFeature[] = [];
    for (const template of templates) {
      // Check if a module feature already exists for this template
      let moduleFeature = await planFeatureRepository.findOne({
        where: {
          type: FeatureType.MODULE,
          name: template.title
        }
      });

      if (!moduleFeature) {
        // Create a new module feature
        moduleFeature = planFeatureRepository.create({
          type: FeatureType.MODULE,
          name: template.title,
          description: `Module for ${template.title} (${template.description || 'No description'})`,
          // Set the ID to match the template ID for easy reference
          id: template.id
        });

        moduleFeature = await planFeatureRepository.save(moduleFeature);
        logger.log(`Created module feature: ${moduleFeature.name} with ID ${moduleFeature.id}`);
      } else {
        logger.log(`Module feature already exists for template: ${template.title}`);
      }

      moduleFeatures.push(moduleFeature);
    }

    // Get all plans
    const plans = await planRepository.find({
      relations: ['planFeatures']
    });
    logger.log(`Found ${plans.length} plans`);

    // Add module features to each plan
    for (const plan of plans) {
      logger.log(`Processing plan: ${plan.name}`);

      // Get existing feature IDs
      const existingFeatureIds = plan.planFeatures.map(feature => feature.id);

      // Add all module features to the plan
      for (const moduleFeature of moduleFeatures) {
        if (!existingFeatureIds.includes(moduleFeature.id)) {
          plan.planFeatures.push(moduleFeature);
          logger.log(`Added module feature ${moduleFeature.name} to plan ${plan.name}`);
        } else {
          logger.log(`Plan ${plan.name} already has module feature ${moduleFeature.name}`);
        }
      }

      // Save the updated plan
      await planRepository.save(plan);
      logger.log(`Updated plan: ${plan.name}`);
    }

    logger.log('Successfully added modules to plans');
  } catch (error) {
    logger.error(`Error adding modules to plans: ${error.message}`, error.stack);
  } finally {
    await app.close();
  }
}

bootstrap();
