import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WaterfallSet } from '../../../database/entities/waterfall-set.entity';
import { WaterfallQuestion } from '../../../database/entities/waterfall-question.entity';
import { WaterfallParticipation } from '../../../database/entities/waterfall-participation.entity';
import { WaterfallAnswer } from '../../../database/entities/waterfall-answer.entity';
import { WaterfallController } from './waterfall.controller';
import { WaterfallAdminController } from './waterfall-admin.controller';
import { WaterfallService } from './waterfall.service';
import { WaterfallAdminService } from './waterfall-admin.service';

@Module({
  imports: [TypeOrmModule.forFeature([WaterfallSet, WaterfallQuestion, WaterfallParticipation, WaterfallAnswer])],
  controllers: [WaterfallController, WaterfallAdminController],
  providers: [WaterfallService, WaterfallAdminService],
  exports: [WaterfallService, WaterfallAdminService]
})
export class WaterfallModule {}
