# Implementation Guide: Award Management, Shop, Promotions, and Skin Items

This document provides specific instructions for UX Designers, Frontend Developers, and QA Engineers regarding the recent backend implementation of the award management system, shop functionality, promotion management, and skin items.

## For UX Designers

### Key Features to Design

1. **Shop Interface**
   - Shop item listings with categories
   - Item detail view showing price, description, and preview
   - Purchase confirmation dialog
   - Purchase history view
   - Admin item creation flow with automatic item number generation
   - Admin item update flow with file upload

2. **Promotion Display**
   - Original price and discounted price display
   - Visual indicator for items with active promotions

3. **Skin Management**
   - Skin preview before purchase
   - Interface for applying purchased skins to diaries
   - Skin builder interface for creating custom skins

4. **Award Display**
   - Award notification
   - Reward points balance display
   - Award history view

## For Frontend Lead

### Implementation Steps

1. **Shop System Integration**
   - Implement shop browsing:
     ```
     GET /shop/categories - Get all categories
     GET /shop/items - Get all shop items (with optional filters)
     GET /shop/items/:id - Get details for a specific item
     ```
   - Implement admin item management:
     ```
     POST /admin/shop/items/generate-number - Generate a unique item number
     POST /admin/shop/items - Create a new item with file upload
     PATCH /admin/shop/items/:id - Update an item with optional file upload
     ```
   - Implement purchase flow:
     ```
     POST /shop/purchase - Purchase an item
     GET /shop/purchases - View purchase history
     GET /shop/items/:id/has-purchased - Check if user already owns an item
     ```
   - Handle secure file URLs for shop items

2. **Skin Integration**
   - Connect skin builder with shop:
     ```
     GET /admin/shop/diary-skins/:id/draft - Get draft shop item from diary skin
     POST /admin/shop/diary-skins/:id/publish - Publish skin to shop (admin)
     ```
   - Implement skin application:
     ```
     POST /shop/items/:id/apply-skin - Apply purchased skin to diary
     ```
   - Handle free skins (no purchase required)
   - Handle automatic file path generation for skin preview images

3. **Promotion Integration**
   - Display discounted prices
   - Show promotion details on items

## For QA Engineers

### Testing Sequence

1. **Shop System Testing**
   - Test category listing:
     ```
     GET /shop/categories
     ```
   - Test item number generation (categoryId is required):
     ```
     POST /admin/shop/items/generate-number
     Body: { categoryId: "123" }
     Response: "GR-001" (prefix derived from category name)
     ```
   - Test item creation with generated number and file upload:
     ```
     POST /admin/shop/items
     Content-Type: multipart/form-data
     Form fields:
       - file: [binary file]
       - itemNumber: "GR-001" (from previous step)
       - title: "Test Item"
       - description: "Test Description"
       - categoryId: "123"
       - price: 9.99
       - type: "in_app_purchase"
     ```
   - Test item listing with filters:
     ```
     GET /shop/items
     GET /shop/items?categoryId=123
     GET /shop/items?type=in_app_purchase
     GET /shop/items?featuredOnly=true
     ```
   - Test item detail view:
     ```
     GET /shop/items/:id
     ```
   - Test purchase flow:
     ```
     POST /shop/purchase
     Body: { shopItemId: "123", paymentMethod: "reward_points" }
     ```
   - Test purchase history:
     ```
     GET /shop/purchases
     GET /shop/purchases/:id
     ```
   - Test ownership check:
     ```
     GET /shop/items/:id/has-purchased
     ```

2. **Skin Testing**
   - Test admin skin draft:
     ```
     GET /admin/shop/diary-skins/:id/draft
     ```
   - Test admin skin publishing:
     ```
     POST /admin/shop/diary-skins/:id/publish
     Body: {
       title: "Blue Theme",
       description: "A blue theme for your diary",
       categoryId: "123",
       type: "IN_APP_PURCHASE",
       price: 9.99,
       priceEquivalentToRewardPoint: 99
     }
     ```
   - Test free skin publishing:
     ```
     POST /admin/shop/diary-skins/:id/publish
     Body: {
       title: "Free Blue Theme",
       description: "A free blue theme for your diary",
       type: "FREE"
     }
     ```
   - Test skin application:
     ```
     POST /shop/items/:id/apply-skin
     ```
   - Verify free skins can be acquired without purchase
   - Verify purchased skins appear in user's diary
   - Verify file paths are correctly generated for skin preview images

3. **File Security Testing**
   - Verify secure URLs work for authorized users
   - Verify secure URLs expire after the set time
   - Verify direct file access is blocked

4. **Special Cases**
   - Test free items (type: FREE) can be acquired without payment
   - Test that shop items are organized in category-specific directories
   - Test the item number generation API and verify it creates sequential numbers with category-specific prefixes
   - Test that the generated item numbers can be used when creating shop items

## Important Implementation Details

1. **File Organization**
   - Shop item files are stored in category-specific subdirectories
   - Example: `shop-items/graphics/GR-001-1234567890.png`

2. **Item Number Generation**
   - Admin must request a generated item number using `POST /admin/shop/items/generate-number`
   - Admin must provide a category ID in the request
   - The system automatically derives the prefix from the first 2 letters of the category name
   - Format: `{PREFIX}-{SEQUENTIAL_NUMBER}` (e.g., GR-001 for Graphics category)
   - The generated item number is then used when creating a shop item

3. **Simplified Shop Item Creation**
   - Files are uploaded along with other shop item data in a single API call
   - The system automatically uploads the file to the appropriate category directory
   - The file path is automatically assigned to the shop item

4. **Skin Items**
   - Skin items are created using the skin builder, not uploaded as files
   - Skins are connected to shop items via a mapping table
   - Skin preview images are used for display in the shop

4. **Security**
   - All file access uses secure, time-limited URLs
   - Users can only access files they've purchased (except free items)

---

For any questions or clarifications, please contact the backend team.
