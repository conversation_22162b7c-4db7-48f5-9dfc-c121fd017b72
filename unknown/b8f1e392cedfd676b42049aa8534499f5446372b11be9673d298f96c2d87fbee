# Notification API Testing Flow

This document outlines the testing flow for the Notification API endpoints.

## Prerequisites

Before testing the Notification API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (admin, tutor, student)
3. Set up your API testing tool (<PERSON><PERSON> recommended)
4. Ensure the notification services (email, push, socket) are properly configured

## Notification Retrieval Testing Flow

### Test Case 1: Get User Notifications

1. Authenticate with any valid token
2. Generate notifications for the user (through various actions)
3. Send a GET request to `/api/notifications` with pagination parameters
4. Verify HTTP status code is 200 OK
5. Verify response contains paginated list of notifications
6. Verify only notifications for the authenticated user are returned
7. Verify pagination metadata (totalItems, currentPage, etc.)

### Test Case 2: Filter and Sort Notifications

1. Test filtering by notification type (system, diary, chat, etc.)
2. Test filtering by read status (read/unread)
3. Test filtering by date range
4. Test sorting by different fields (createdAt, type, etc.)
5. Verify filtered and sorted results match the criteria

### Test Case 3: Get Notification Count

1. Authenticate with any valid token
2. Generate notifications for the user
3. Send a GET request to `/api/notifications/count`
4. Verify HTTP status code is 200 OK
5. Verify response contains the total count and unread count
6. Mark some notifications as read and verify the unread count decreases

## Notification Management Testing Flow

### Test Case 1: Mark Notification as Read

1. Authenticate with any valid token
2. Generate an unread notification for the user
3. Send a PUT request to `/api/notifications/{notificationId}/read`
4. Verify HTTP status code is 200 OK
5. Verify notification is marked as read in the database
6. Verify readAt timestamp is set

### Test Case 2: Mark All Notifications as Read

1. Authenticate with any valid token
2. Generate multiple unread notifications for the user
3. Send a PUT request to `/api/notifications/read-all`
4. Verify HTTP status code is 200 OK
5. Verify all notifications are marked as read in the database
6. Verify readAt timestamp is set for all notifications

### Test Case 3: Delete Notification

1. Authenticate with any valid token
2. Generate a notification for the user
3. Send a DELETE request to `/api/notifications/{notificationId}`
4. Verify HTTP status code is 200 OK
5. Verify notification is deleted from the database
6. Verify notification no longer appears in the user's notification list

## Notification Generation Testing Flow

### Test Case 1: Diary-Related Notifications

1. Test notification generation when a student submits a diary entry
   - Verify notification is sent to the assigned tutor
   - Verify notification contains the correct information (entry ID, student name, etc.)
   - Verify notification is delivered through all configured channels

2. Test notification generation when a tutor reviews a diary entry
   - Verify notification is sent to the student
   - Verify notification contains the correct information (entry ID, tutor name, etc.)
   - Verify notification is delivered through all configured channels

### Test Case 2: Chat-Related Notifications

1. Test notification generation when a user receives a new message
   - Verify notification is sent to the recipient
   - Verify notification contains the correct information (sender name, message preview, etc.)
   - Verify notification is delivered through all configured channels

2. Test notification generation when a user is mentioned in a chat
   - Verify notification is sent to the mentioned user
   - Verify notification contains the correct information (sender name, message preview, etc.)
   - Verify notification is delivered through all configured channels

### Test Case 3: System Notifications

1. Test notification generation for system events (plan expiry, account changes, etc.)
   - Verify notification is sent to the appropriate user
   - Verify notification contains the correct information
   - Verify notification is delivered through all configured channels

## Notification Channel Testing Flow

### Test Case 1: In-App Notifications

1. Generate a notification that should be delivered in-app
2. Verify notification appears in the user's notification list
3. Verify notification is properly formatted
4. Verify notification links to the appropriate resource

### Test Case 2: Email Notifications

1. Generate a notification that should be delivered via email
2. Verify email is sent to the user's email address
3. Verify email content matches the notification
4. Verify email contains appropriate links and formatting

### Test Case 3: Push Notifications

1. Generate a notification that should be delivered via push notification
2. Verify push notification is sent to the user's registered devices
3. Verify push notification content matches the notification
4. Verify push notification deep links to the appropriate resource

### Test Case 4: Socket Notifications

1. Connect to the WebSocket server with a valid token
2. Generate a notification that should be delivered via socket
3. Verify notification is received through the WebSocket connection
4. Verify notification content matches the expected format
5. Verify notification is received in real-time

## Notification Retry Testing Flow

### Test Case 1: Failed Notification Retry

1. Configure a notification channel to fail (e.g., disable email server)
2. Generate a notification that should be delivered through that channel
3. Verify notification is marked as failed in the database
4. Verify notification is queued for retry
5. Re-enable the notification channel
6. Trigger the retry mechanism (manually or wait for scheduled retry)
7. Verify notification is successfully delivered after retry

### Test Case 2: Manual Retry

1. Authenticate with an admin token
2. Generate failed notifications
3. Send a POST request to `/api/admin/notifications/retry-failed`
4. Verify HTTP status code is 200 OK
5. Verify failed notifications are retried
6. Verify successfully retried notifications are marked as delivered

## Edge Cases and Security Testing

### Test Case 1: Role-Based Access Control

1. Authenticate with a student token
2. Attempt to access another user's notifications
3. Verify 403 Forbidden responses
4. Authenticate with a tutor token
5. Attempt to access admin notification endpoints
6. Verify 403 Forbidden responses

### Test Case 2: Notification Template Testing

1. Test notifications with missing template variables
2. Test notifications with invalid template variables
3. Test notifications with very long content
4. Verify appropriate error handling for each case

### Test Case 3: Notification Preferences

1. Authenticate with any valid token
2. Update notification preferences (opt-out of certain channels)
3. Generate notifications that should respect these preferences
4. Verify notifications are only delivered through the preferred channels
