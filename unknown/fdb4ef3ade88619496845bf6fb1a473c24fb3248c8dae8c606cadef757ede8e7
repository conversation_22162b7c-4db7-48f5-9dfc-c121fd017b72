#!/usr/bin/env ts-node

/**
 * Payment Test Validation Script
 * 
 * This script validates that all payment tests are properly configured
 * and can be executed without errors.
 */

import * as fs from 'fs';
import * as path from 'path';

interface ValidationResult {
  category: string;
  checks: { name: string; passed: boolean; message?: string }[];
}

function validateFileExists(filePath: string): boolean {
  return fs.existsSync(filePath);
}

function validateFileContent(filePath: string, requiredContent: string[]): { passed: boolean; missing: string[] } {
  if (!fs.existsSync(filePath)) {
    return { passed: false, missing: requiredContent };
  }

  const content = fs.readFileSync(filePath, 'utf-8');
  const missing = requiredContent.filter(required => !content.includes(required));
  
  return { passed: missing.length === 0, missing };
}

function validateTestFiles(): ValidationResult {
  const checks = [
    {
      name: 'KCP Service Unit Tests',
      passed: validateFileExists('src/modules/payment/services/kcp.service.spec.ts'),
      message: validateFileExists('src/modules/payment/services/kcp.service.spec.ts') 
        ? undefined 
        : 'KCP service unit test file missing'
    },
    {
      name: 'Payment Service Unit Tests',
      passed: validateFileExists('src/modules/payment/services/payment.service.spec.ts'),
      message: validateFileExists('src/modules/payment/services/payment.service.spec.ts') 
        ? undefined 
        : 'Payment service unit test file missing'
    },
    {
      name: 'KCP Config Service Unit Tests',
      passed: validateFileExists('src/modules/payment/services/kcp-config.service.spec.ts'),
      message: validateFileExists('src/modules/payment/services/kcp-config.service.spec.ts') 
        ? undefined 
        : 'KCP config service unit test file missing'
    },
    {
      name: 'Payment Controller Unit Tests',
      passed: validateFileExists('src/modules/payment/payment.controller.spec.ts'),
      message: validateFileExists('src/modules/payment/payment.controller.spec.ts') 
        ? undefined 
        : 'Payment controller unit test file missing'
    },
    {
      name: 'Payment Integration Tests',
      passed: validateFileExists('test/payment-integration.test.ts'),
      message: validateFileExists('test/payment-integration.test.ts') 
        ? undefined 
        : 'Payment integration test file missing'
    },
  ];

  return { category: 'Test Files', checks };
}

function validateTestUtilities(): ValidationResult {
  const checks = [
    {
      name: 'Test Database Config',
      passed: validateFileExists('test/utils/test-database.config.ts'),
      message: validateFileExists('test/utils/test-database.config.ts') 
        ? undefined 
        : 'Test database configuration missing'
    },
    {
      name: 'Test Helpers',
      passed: validateFileExists('test/utils/test-helpers.ts'),
      message: validateFileExists('test/utils/test-helpers.ts') 
        ? undefined 
        : 'Test helper utilities missing'
    },
    {
      name: 'Test Environment Config',
      passed: validateFileExists('.env.test'),
      message: validateFileExists('.env.test') 
        ? undefined 
        : 'Test environment configuration missing'
    },
  ];

  return { category: 'Test Utilities', checks };
}

function validateTestConfiguration(): ValidationResult {
  const checks = [
    {
      name: 'Jest E2E Configuration',
      passed: validateFileExists('test/jest-e2e.json'),
      message: validateFileExists('test/jest-e2e.json') 
        ? undefined 
        : 'Jest E2E configuration missing'
    },
    {
      name: 'Package.json Test Scripts',
      ...(() => {
        const { passed, missing } = validateFileContent('package.json', [
          'test:payment',
          'test:payment:unit',
          'test:payment:integration'
        ]);
        return {
          passed,
          message: passed ? undefined : `Missing test scripts: ${missing.join(', ')}`
        };
      })()
    },
  ];

  return { category: 'Test Configuration', checks };
}

function validateTestContent(): ValidationResult {
  const checks = [
    {
      name: 'KCP Service Test Coverage',
      ...(() => {
        const { passed, missing } = validateFileContent('src/modules/payment/services/kcp.service.spec.ts', [
          'registerTrade',
          'processPayment',
          'initiatePayment',
          'validateWebhookSignature'
        ]);
        return {
          passed,
          message: passed ? undefined : `Missing test cases: ${missing.join(', ')}`
        };
      })()
    },
    {
      name: 'Payment Service Test Coverage',
      ...(() => {
        const { passed, missing } = validateFileContent('src/modules/payment/services/payment.service.spec.ts', [
          'initiatePayment',
          'getPaymentStatus',
          'processWebhook'
        ]);
        return {
          passed,
          message: passed ? undefined : `Missing test cases: ${missing.join(', ')}`
        };
      })()
    },
    {
      name: 'Integration Test Coverage',
      ...(() => {
        const { passed, missing } = validateFileContent('test/payment-integration.test.ts', [
          'Payment Initiation API',
          'Payment Status',
          'Webhook Processing',
          'Error Handling'
        ]);
        return {
          passed,
          message: passed ? undefined : `Missing test suites: ${missing.join(', ')}`
        };
      })()
    },
  ];

  return { category: 'Test Content', checks };
}

function printValidationResults(results: ValidationResult[]): void {
  console.log('🔍 Payment Test Validation Report');
  console.log('═'.repeat(60));

  let totalChecks = 0;
  let passedChecks = 0;

  results.forEach(result => {
    console.log(`\n📋 ${result.category}`);
    console.log('─'.repeat(40));

    result.checks.forEach(check => {
      totalChecks++;
      const status = check.passed ? '✅' : '❌';
      console.log(`${status} ${check.name}`);
      
      if (check.passed) {
        passedChecks++;
      } else if (check.message) {
        console.log(`   └─ ${check.message}`);
      }
    });
  });

  console.log('\n' + '═'.repeat(60));
  console.log(`📊 Summary: ${passedChecks}/${totalChecks} checks passed`);

  if (passedChecks === totalChecks) {
    console.log('🎉 All payment tests are properly configured!');
    console.log('\n🚀 You can now run the tests using:');
    console.log('   npm run test:payment');
    console.log('   npm run test:payment:unit');
    console.log('   npm run test:payment:integration');
  } else {
    console.log('⚠️  Some issues need to be resolved before running tests.');
    console.log('\n📝 Next steps:');
    console.log('   1. Fix the issues listed above');
    console.log('   2. Re-run this validation script');
    console.log('   3. Run the payment tests');
    process.exit(1);
  }
}

async function main(): Promise<void> {
  console.log('🔍 Validating Payment Test Configuration...\n');

  const validationResults = [
    validateTestFiles(),
    validateTestUtilities(),
    validateTestConfiguration(),
    validateTestContent(),
  ];

  printValidationResults(validationResults);
}

// Run validation
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Validation failed:', error);
    process.exit(1);
  });
}

export { validateTestFiles, validateTestUtilities, validateTestConfiguration, validateTestContent };
