# Diary Module - Integration Flow

This document provides a clear integration flow for frontend developers to implement the Diary Module features with minimal verbal communication needed. It focuses exclusively on API endpoints and data flows without prescribing specific frontend implementation approaches.

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Integration Flow Diagrams](#integration-flow-diagrams)
   - [Diary Entry Creation Flow](#diary-entry-creation-flow)
   - [Diary Review Flow](#diary-review-flow)
   - [Diary Sharing Flow](#diary-sharing-flow)
4. [API Endpoints](#api-endpoints)
   - [Diary Entries](#diary-entries)
   - [Diary Skins](#diary-skins)
   - [Diary Comments](#diary-comments)
   - [Diary Sharing](#diary-sharing)
5. [Diary Entry Lifecycle](#diary-entry-lifecycle)
6. [Tutor Evaluation](#tutor-evaluation)
7. [Privacy Controls](#privacy-controls)
8. [QR Code Sharing](#qr-code-sharing)
9. [Error Handling](#error-handling)

## Overview

The Diary Module allows students to:
- Create and manage diary entries
- Customize the appearance of their diary with skins
- Receive feedback from tutors on their entries
- Share entries with friends and via QR codes
- Control the privacy of their diary entries

## Authentication

All API requests require authentication using a JWT token. Include the token in the Authorization header:

```
Authorization: Bearer <token>
```

The token contains user information including the user's ID and role, which is used to enforce access control.

## Integration Flow Diagrams

### Diary Entry Creation Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Create New │      │  Save as    │      │   Submit    │      │  Tutor      │
│  Diary Entry│ ──▶  │   Draft     │ ──▶  │  for Review │ ──▶  │  Review     │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /diary/│      │ PUT /diary/ │      │POST /diary/ │      │GET /diary/  │
│ entries     │      │entries/:id  │      │entries/:id/ │      │entries/:id  │
│             │      │isDraft=true │      │submit       │      │             │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**State Transitions:**
1. Initial state: No entry
2. After creation: NEW (draft state)
3. After submission: SUBMIT
4. After tutor adds feedback and corrections: REVIEWED
5. After tutor confirms review: CONFIRM

Note: When a tutor starts reviewing an entry in SUBMIT status, the entry remains in SUBMIT status but is locked for 2 hours with reviewingTutorId set.

### Diary Review Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  View Diary │      │  Add        │      │  Provide    │      │  Complete   │
│  Entry      │ ──▶  │  Comments   │ ──▶  │  Evaluation │ ──▶  │  Review     │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /diary/ │      │ POST /diary/│      │POST /diary/ │      │PUT /diary/  │
│entries/:id  │      │entries/:id/ │      │entries/:id/ │      │entries/:id/ │
│             │      │comments     │      │evaluation   │      │complete     │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**UI States for Tutors:**
1. Entry needs review: Show "Start Review" button
2. Under review: Show comment and evaluation forms
3. Review completed: Show "Review Completed" status

### Diary Sharing Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Set Privacy│      │  Generate   │      │  Share      │      │  View Shared│
│  Level      │ ──▶  │  QR Code    │ ──▶  │  Link/QR    │ ──▶  │  Entry      │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ PUT /diary/ │      │ GET /diary/ │      │ Share via   │      │GET /diary/  │
│entries/:id  │      │entries/:id/ │      │ external    │      │shared/:shareId│
│privacyLevel │      │qr-code      │      │ means       │      │             │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**Privacy Levels and Sharing:**
1. Private: Only visible to student and tutors
2. Friends: Visible to friends with diary access
3. Public: Visible to anyone with the share link/QR code

## API Endpoints

### Diary Entries

#### Create Diary Entry

```
POST /api/diary/entries
```

Request Body:
```json
{
  "entryDate": "2023-07-25",
  "title": "My First Diary Entry",
  "content": "Today I learned about...",
  "skinId": "123e4567-e89b-12d3-a456-426614174000",
  "backgroundColor": "#f5f5f5",
  "isPrivate": false,
  "settingsTemplateId": "123e4567-e89b-12d3-a456-426614174000"
}
```

Response:
```json
{
  "id": "entry-id",
  "title": "My Diary Entry",
  "content": "Today I learned about...",
  "mood": "happy",
  "skinId": "skin-id",
  "skin": {
    "id": "skin-id",
    "name": "Skin Name",
    "thumbnailUrl": "https://example.com/thumbnail.jpg",
    "cssProperties": {
      "backgroundColor": "#ffffff",
      "fontFamily": "Arial",
      "color": "#000000"
    }
  },
  "privacyLevel": "private",
  "status": "draft",
  "images": [
    {
      "id": "image-id-1",
      "url": "https://example.com/image1.jpg"
    },
    {
      "id": "image-id-2",
      "url": "https://example.com/image2.jpg"
    }
  ],
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

#### Get Diary Entries

```
GET /api/diary/entries
```

Query Parameters:
- `status` (string, optional): Filter by status (draft, submitted, under_review, reviewed)
- `startDate` (string, optional): Filter by start date (YYYY-MM-DD)
- `endDate` (string, optional): Filter by end date (YYYY-MM-DD)
- `page` (number, default: 1): Page number
- `limit` (number, default: 10): Items per page

Response:
```json
{
  "items": [
    {
      "id": "entry-id",
      "title": "My Diary Entry",
      "content": "Today I learned about...",
      "mood": "happy",
      "skinId": "skin-id",
      "skin": {
        "id": "skin-id",
        "name": "Skin Name",
        "thumbnailUrl": "https://example.com/thumbnail.jpg"
      },
      "privacyLevel": "private",
      "status": "submitted",
      "hasComments": true,
      "hasEvaluation": false,
      "createdAt": "2023-01-01T00:00:00Z",
      "updatedAt": "2023-01-01T00:00:00Z"
    }
  ],
  "totalCount": 50,
  "totalItems": 50,
  "itemsPerPage": 10,
  "currentPage": 1,
  "totalPages": 5
}
```

#### Get Diary Entry Details

```
GET /api/diary/entries/:entryId
```

Response:
```json
{
  "id": "entry-id",
  "title": "My Diary Entry",
  "content": "Today I learned about...",
  "mood": "happy",
  "skinId": "skin-id",
  "skin": {
    "id": "skin-id",
    "name": "Skin Name",
    "thumbnailUrl": "https://example.com/thumbnail.jpg",
    "cssProperties": {
      "backgroundColor": "#ffffff",
      "fontFamily": "Arial",
      "color": "#000000"
    }
  },
  "privacyLevel": "private",
  "status": "submitted",
  "images": [
    {
      "id": "image-id-1",
      "url": "https://example.com/image1.jpg"
    }
  ],
  "comments": [
    {
      "id": "comment-id",
      "content": "Great job!",
      "authorId": "author-id",
      "authorName": "Author Name",
      "authorType": "tutor",
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ],
  "evaluation": {
    "id": "evaluation-id",
    "score": 85,
    "feedback": "Well done! Here's how you can improve...",
    "tutorId": "tutor-id",
    "tutorName": "Tutor Name",
    "createdAt": "2023-01-01T00:00:00Z"
  },
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z"
}
```

#### Update Diary Entry

```
PUT /api/diary/entries/:entryId
```

Request Body:
```json
{
  "title": "Updated Title",
  "content": "Updated content...",
  "skinId": "123e4567-e89b-12d3-a456-426614174000",
  "backgroundColor": "#f5f5f5",
  "isPrivate": false,
  "settingsTemplateId": "123e4567-e89b-12d3-a456-426614174000"
}
```

Response: Same as Get Diary Entry Details

#### Delete Diary Entry

```
DELETE /api/diary/entries/:entryId
```

Response:
```json
{
  "success": true,
  "message": "Diary entry deleted successfully"
}
```

#### Submit Diary Entry for Review

```
POST /api/diary/entries/:entryId/submit
```

Response: Same as Get Diary Entry Details with updated status

### Diary Skins

#### Get Available Skins

```
GET /api/diary/skins
```

Response:
```json
{
  "items": [
    {
      "id": "skin-id",
      "name": "Skin Name",
      "description": "A beautiful skin for your diary",
      "thumbnailUrl": "https://example.com/thumbnail.jpg",
      "cssProperties": {
        "backgroundColor": "#ffffff",
        "fontFamily": "Arial",
        "color": "#000000"
      },
      "isDefault": false,
      "isUsedIn": true
    }
  ],
  "totalCount": 20,
  "totalItems": 20,
  "itemsPerPage": 10,
  "currentPage": 1,
  "totalPages": 2
}
```

#### Set Default Skin

```
POST /api/diary/skins/default
```

Request Body:
```json
{
  "skinId": "skin-id"
}
```

Response:
```json
{
  "id": "skin-id",
  "name": "Skin Name",
  "description": "A beautiful skin for your diary",
  "thumbnailUrl": "https://example.com/thumbnail.jpg",
  "cssProperties": {
    "backgroundColor": "#ffffff",
    "fontFamily": "Arial",
    "color": "#000000"
  },
  "isDefault": true
}
```

### Diary Comments

#### Add Comment to Diary Entry

```
POST /api/diary/entries/:entryId/comments
```

Request Body:
```json
{
  "content": "This is a great diary entry!"
}
```

Response:
```json
{
  "id": "comment-id",
  "content": "This is a great diary entry!",
  "authorId": "author-id",
  "authorName": "Author Name",
  "authorType": "student|tutor",
  "createdAt": "2023-01-01T00:00:00Z"
}
```

#### Get Comments for Diary Entry

```
GET /api/diary/entries/:entryId/comments
```

Response:
```json
{
  "items": [
    {
      "id": "comment-id",
      "content": "This is a great diary entry!",
      "authorId": "author-id",
      "authorName": "Author Name",
      "authorType": "student|tutor",
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ],
  "totalCount": 5,
  "totalItems": 5,
  "itemsPerPage": 10,
  "currentPage": 1,
  "totalPages": 1
}
```

### Diary Sharing

#### Generate QR Code for Diary Entry

```
GET /api/diary/entries/:entryId/qr-code
```

Response:
```json
{
  "qrCodeUrl": "https://example.com/qr-code.png",
  "shareUrl": "https://example.com/shared/diary/abc123"
}
```

#### Get Shared Diary Entry

```
GET /api/diary/shared/:shareId
```

Response: Same as Get Diary Entry Details

## Diary Entry Lifecycle

Diary entries go through the following lifecycle:

1. **NEW**: The entry is still being edited by the student (equivalent to a draft)
2. **SUBMIT**: The entry has been submitted for review by a tutor
   - When a tutor starts reviewing, the entry remains in SUBMIT status but is locked for 2 hours
   - The entry includes `reviewStartTime`, `reviewExpiryTime`, and `reviewingTutorId` fields
3. **REVIEWED**: The entry has been reviewed by a tutor with corrections and scores added
4. **CONFIRM**: The tutor has confirmed the review is complete (final state)

The status flow is:
- NEW → SUBMIT: Student submits the entry for review
- SUBMIT → REVIEWED: Tutor reviews the entry and adds corrections with scores
- REVIEWED → CONFIRM: Tutor confirms the review is complete

The status of an entry is included in the response when fetching entries as an enum string: "new", "submit", "reviewed", or "confirm".

## Tutor Evaluation

Tutors can provide evaluations for diary entries, which include:

- A score (0-100)
- Written feedback
- Comments on specific parts of the entry

Students can view these evaluations in the diary entry details.

## Privacy Controls

Diary entries have the following privacy levels:

- **Private**: Only visible to the student and assigned tutors
- **Friends**: Visible to the student, assigned tutors, and friends with diary access
- **Public**: Visible to anyone with the share link or QR code

The privacy level can be set when creating or updating a diary entry.

## QR Code Sharing

Students can generate QR codes for sharing their diary entries. The QR code contains a link to a public view of the entry, which can be accessed without authentication.

When the privacy level of an entry changes, the QR code is automatically updated.



## Error Handling

| Error Code | Description | API Response |
|------------|-------------|--------------|
| 400 | Bad Request - Invalid input parameters | Returns validation errors in the response body |
| 401 | Unauthorized - Invalid or expired token | Returns authentication error |
| 403 | Forbidden - User doesn't have permission | Returns permission error |
| 404 | Not Found - Diary entry not found | Returns not found error |
| 409 | Conflict - Entry is locked for editing | Returns conflict error with lock information |
| 500 | Server Error | Returns server error |

### Common Error Scenarios

1. **Editing a diary entry that's being reviewed**
   - Error: 409 Conflict
   - Response includes remaining lock time and tutor information
   - This occurs when trying to edit an entry in SUBMIT status that has an active review lock

2. **Accessing a diary entry without permission**
   - Error: 403 Forbidden
   - Response includes permission details

3. **Submitting a diary entry with missing required fields**
   - Error: 400 Bad Request
   - Response includes validation errors for specific fields

4. **Accessing a shared diary entry that has been made private**
   - Error: 404 Not Found
   - Response indicates the entry is no longer available

### Data Validation Rules

| Field | Validation Rules |
|-------|------------------|
| title | Required, max 100 chars |
| content | Required, max 5000 chars |
| mood | Must be one of: happy, sad, neutral, excited, tired, anxious |
| privacyLevel | Must be one of: private, friends, public |
| images | Max 10 images, each max 5MB |
