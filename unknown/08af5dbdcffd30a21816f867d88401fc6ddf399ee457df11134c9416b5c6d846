version: '3.9' # Use the latest version of Docker Compose

services: # Define the services to be started
  api: # Define the API service
    container_name: hec-api # Name the container for easy identification
    build: # Build the production image from the Dockerfile
      context: . # Use the current directory as the build context
      dockerfile: Dockerfile # Use the Dockerfile in the current directory
    ports:
      - '3012:3012' # Map container port 3000 to host port 3012
    # restart: always # Restart the container automatically on failure or restart
    volumes:
      - ./logs:/app/logs # Mount logs directory for persistent logs
      - ./uploads:/app/uploads # Mount uploads directory for persistent file storage
      - ./.env:/app/.env # Mount .env file for environment variables
      - ./tsconfig.json:/app/tsconfig.json # Mount TypeScript configuration
      - ./tsconfig.webpack.json:/app/tsconfig.webpack.json # Mount webpack TypeScript configuration
      - ./nest-cli.json:/app/nest-cli.json # Mount NestJS CLI configuration
      - ./webpack.prod.js:/app/webpack.prod.js # Mount webpack production configuration
      - ./src:/app/src # Mount source code for development
    networks:
      - hec-network-prod # Connect the container to the hec-network network
    environment:
      NODE_ENV: production # Set the environment to production
      NODE_PATH: . # Set the module resolution path
      UPLOAD_DIR: /app/uploads # Define the upload directory
      LOG_PATH: /app/logs # Define the log directory
    # Use a different command for production to build and run the application
    command: sh -c "npm install && npm run start:ts-node" # Run with npm script

networks: # Define the networks for containers to communicate with each other
  hec-network-prod: # Define the hec-network network for containers to communicate with each other
    driver: bridge # Use the bridge driver for the network
