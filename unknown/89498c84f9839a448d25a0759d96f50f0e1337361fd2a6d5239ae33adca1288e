import { ApiProperty } from '@nestjs/swagger';

/**
 * Base API response model for all API responses
 */
export class ApiResponse<T> {
  @ApiProperty({ example: true, description: 'Indicates if the request was successful' })
  success: boolean;

  @ApiProperty({ description: 'Response message', example: 'Operation completed successfully' })
  message: string;

  @ApiProperty({ description: 'Response data', required: false })
  data?: T;

  @ApiProperty({
    description: 'Error details if success is false',
    required: false,
    example: {
      type: 'Error',
      status: 500,
      refId: 'ERR-M9BF5NQQ-31028'
    }
  })
  error?: Record<string, any>;

  @ApiProperty({ description: 'Timestamp of the response', example: '2023-07-25T12:34:56.789Z' })
  timestamp: string;

  @ApiProperty({ description: 'Path of the request', example: '/api/users', required: false })
  path?: string;
}

/**
 * Pagination metadata for paginated responses
 */
export class PaginationMeta {
  @ApiProperty({ example: 1, description: 'Current page number' })
  currentPage: number;

  @ApiProperty({ example: 10, description: 'Number of items per page' })
  itemsPerPage: number;

  @ApiProperty({ example: 100, description: 'Total number of items' })
  totalItems: number;

  @ApiProperty({ example: 10, description: 'Total number of pages' })
  totalPages: number;

  @ApiProperty({ example: true, description: 'Whether there is a next page' })
  hasNextPage: boolean;

  @ApiProperty({ example: false, description: 'Whether there is a previous page' })
  hasPreviousPage: boolean;
}

/**
 * Paginated API response model for list endpoints
 */
export class PaginatedResponse<T> extends ApiResponse<T[]> {
  @ApiProperty({ description: 'Pagination metadata' })
  pagination: PaginationMeta;
}

/**
 * Authentication token response
 */
export class AuthTokenResponse {
  @ApiProperty({
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    description: 'JWT access token for authentication',
  })
  access_token: string;

  @ApiProperty({
    description: 'User information',
    example: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'John Doe',
      email: '<EMAIL>',
      type: 'student',
      selectedRole: 'student',
      roles: ['student'],
      isActive: true,
      isConfirmed: true,
      activePlan: 'Premium'
    }
  })
  user: Record<string, any>;

  @ApiProperty({
    example: 'a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0...',
    description: 'JWT refresh token for obtaining a new access token',
    required: false
  })
  refresh_token?: string;

  @ApiProperty({
    example: '2023-05-07T08:39:32.406Z',
    description: 'Expiration date of the refresh token',
    required: false
  })
  refresh_token_expires?: string;
}

/**
 * Validation error response
 */
export class ValidationErrorResponse {
  @ApiProperty({ example: false, description: 'Indicates that the request failed' })
  success: boolean;

  @ApiProperty({ example: 'Validation failed', description: 'Error message' })
  message: string;

  @ApiProperty({
    example: {
      email: ['email must be a valid email address'],
      password: ['password must be at least 8 characters long']
    },
    description: 'Validation errors by field'
  })
  validationErrors: Record<string, string[]>;

  @ApiProperty({ example: '2023-07-25T12:34:56.789Z', description: 'Timestamp of the response' })
  timestamp: string;
}

/**
 * Conflict error response (409)
 */
export class ConflictErrorResponse {
  @ApiProperty({ example: false, description: 'Indicates that the request failed' })
  success: boolean;

  @ApiProperty({ example: 'Resource already exists', description: 'Error message' })
  message: string;

  @ApiProperty({
    example: {
      type: 'ConflictException',
      status: 409,
      refId: 'ERR-M9BF5NQQ-31028'
    },
    description: 'Error details'
  })
  error: Record<string, any>;

  @ApiProperty({
    example: {
      email: ['Email <EMAIL> is already registered']
    },
    description: 'Validation errors by field'
  })
  validationErrors: Record<string, string[]>;

  @ApiProperty({ example: '2023-07-25T12:34:56.789Z', description: 'Timestamp of the response' })
  timestamp: string;
}

/**
 * General error response
 */
export class ErrorResponse {
  @ApiProperty({ example: false, description: 'Indicates that the request failed' })
  success: boolean;

  @ApiProperty({ example: 'An error occurred', description: 'Error message' })
  message: string;

  @ApiProperty({
    example: {
      type: 'Error',
      status: 500,
      refId: 'ERR-M9BF5NQQ-31028'
    },
    description: 'Error details'
  })
  error: Record<string, any>;

  @ApiProperty({ example: '2023-07-25T12:34:56.789Z', description: 'Timestamp of the response' })
  timestamp: string;
}
