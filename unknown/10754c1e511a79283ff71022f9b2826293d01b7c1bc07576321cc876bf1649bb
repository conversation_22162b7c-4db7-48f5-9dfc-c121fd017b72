import { applyDecorators } from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';

/**
 * Decorator for documenting error responses in Swagger
 * @param status HTTP status code
 * @param description Description of the error
 * @returns Decorator
 */
export const ApiErrorResponse = (status: number, description: string) => {
  return applyDecorators(
    ApiResponse({
      status,
      description,
      schema: {
        type: 'object',
        properties: {
          success: {
            type: 'boolean',
            example: false
          },
          message: {
            type: 'string',
            example: description
          },
          data: {
            type: 'null',
            example: null
          }
        }
      }
    })
  );
};
