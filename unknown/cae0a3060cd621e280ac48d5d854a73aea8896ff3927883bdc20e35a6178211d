import { Entity, Column, OneToMany, Index } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

export enum NovelTopicCategory {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly'
}

@Entity()
@Index(['sequenceTitle'], { unique: true })
export class NovelTopic extends AuditableBaseEntity {
  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'sequence_title', unique: true })
  sequenceTitle: string;

  @Column({
    name: 'category',
    type: 'enum',
    enum: NovelTopicCategory
  })
  category: NovelTopicCategory;

  @Column({ name: 'instruction', type: 'text' })
  instruction: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @OneToMany('NovelEntry', 'topic')
  entries: any[];
}
