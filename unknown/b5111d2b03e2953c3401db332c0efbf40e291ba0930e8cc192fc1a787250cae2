# Diary Module

The Diary Module handles student diary management, including creating, updating, and retrieving diary entries, as well as tutor feedback and evaluation.

## Epics

1. **Diary Management**
2. **Diary Entry Management**
3. **Diary Skin Management**
4. **Tutor Feedback and Evaluation**
5. **Diary Awards**

## APIs

### Student Diary APIs

#### 1. Get Student Diary

**Endpoint:** `GET /diary`

**Description:** Retrieves the diary of the currently authenticated student. Creates a new diary if one doesn't exist.

**Response:**
```json
{
  "success": true,
  "message": "Diary retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "defaultSkinId": "123e4567-e89b-12d3-a456-426614174000",
    "defaultSkin": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Default Skin",
      "description": "Default diary skin",
      "previewImagePath": "uploads/diary-skins/default.jpg",
      "isActive": true,
      "isGlobal": true
    },
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Check if user has an active subscription
3. Check if diary already exists for the user
4. If not, create a new diary with default skin
5. Return diary details

#### 2. Get All Diary Skins

**Endpoint:** `GET /diary/skins`

**Description:** Retrieves all available diary skins for the currently authenticated student, including global skins and student's own skins.

**Response:**
```json
{
  "success": true,
  "message": "Diary skins retrieved successfully",
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Default Skin",
      "description": "Default diary skin",
      "previewImagePath": "uploads/diary-skins/default.jpg",
      "isActive": true,
      "isGlobal": true,
      "createdById": "123e4567-e89b-12d3-a456-426614174000"
    },
    {
      "id": "223e4567-e89b-12d3-a456-426614174000",
      "name": "My Custom Skin",
      "description": "Student's custom skin",
      "previewImagePath": "uploads/diary-skins/custom.jpg",
      "isActive": true,
      "isGlobal": false,
      "createdById": "123e4567-e89b-12d3-a456-426614174000"
    }
  ],
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Retrieve all global skins
3. Retrieve all student's own skins
4. Combine and return all skins

#### 3. Create Diary Skin

**Endpoint:** `POST /diary/skins`

**Description:** Creates a new diary skin for the currently authenticated student.

**Request Body:**
```json
{
  "name": "My Custom Skin",
  "description": "Student's custom skin",
  "templateContent": "<div class=\"custom-skin\">{{content}}</div>",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Diary skin created successfully",
  "data": {
    "id": "223e4567-e89b-12d3-a456-426614174000",
    "name": "My Custom Skin",
    "description": "Student's custom skin",
    "templateContent": "<div class=\"custom-skin\">{{content}}</div>",
    "previewImagePath": null,
    "isActive": true,
    "isGlobal": false,
    "createdById": "123e4567-e89b-12d3-a456-426614174000",
    "createdAt": "2023-07-25T00:00:00.000Z",
    "updatedAt": "2023-07-25T00:00:00.000Z"
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Validate input data
3. Create new skin with student as creator
4. Set isGlobal to false
5. Save skin to database
6. Return created skin

#### 4. Get All Diary Entries

**Endpoint:** `GET /diary/entries`

**Description:** Retrieves all diary entries for the currently authenticated student.

**Query Parameters:**
- `date`: Filter entries by date (optional)

#### 4.1 Get Today's Diary Entry

**Endpoint:** `GET /diary/entries/today`

**Description:** Gets or creates a diary entry for today for the currently authenticated student. If no entry exists for today, a new one will be automatically created with minimal initialization.

**Response:**
```json
{
  "success": true,
  "message": "Today's diary entry retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "diaryId": "123e4567-e89b-12d3-a456-426614174000",
    "entryDate": "2023-07-25",
    "title": "",
    "content": "",
    "status": "new",
    "skinId": "123e4567-e89b-12d3-a456-426614174000",
    "skin": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Default Skin",
      "description": "Default diary skin",
      "previewImagePath": "uploads/diary-skins/default.jpg"
    },
    "backgroundColor": null,
    "isPrivate": false,
    "createdAt": "2023-07-25T00:00:00.000Z",
    "updatedAt": "2023-07-25T00:00:00.000Z"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Get or create diary for the user
3. Check if an entry already exists for today
4. If not, create a new entry with minimal initialization
5. Return the entry (either existing or newly created)
```json
{
  "success": true,
  "message": "Diary entries retrieved successfully",
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "diaryId": "123e4567-e89b-12d3-a456-426614174000",
      "entryDate": "2023-07-25",
      "title": "My First Diary Entry",
      "content": "Today I learned about...",
      "status": "submitted",
      "skinId": "123e4567-e89b-12d3-a456-426614174000",
      "skin": {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Default Skin",
        "description": "Default diary skin",
        "previewImagePath": "uploads/diary-skins/default.jpg"
      },
      "backgroundColor": "#f5f5f5",
      "isPrivate": false,
      "score": null,
      "feedbacks": [],
      "createdAt": "2023-07-25T00:00:00.000Z",
      "updatedAt": "2023-07-25T00:00:00.000Z"
    },
    {
      "id": "223e4567-e89b-12d3-a456-426614174000",
      "diaryId": "123e4567-e89b-12d3-a456-426614174000",
      "entryDate": "2023-07-24",
      "title": "Yesterday's Entry",
      "content": "Yesterday I worked on...",
      "status": "reviewed",
      "skinId": "223e4567-e89b-12d3-a456-426614174000",
      "skin": {
        "id": "223e4567-e89b-12d3-a456-426614174000",
        "name": "My Custom Skin",
        "description": "Student's custom skin",
        "previewImagePath": "uploads/diary-skins/custom.jpg"
      },
      "backgroundColor": "#e0f7fa",
      "isPrivate": false,
      "score": 85,
      "feedbacks": [
        {
          "id": "123e4567-e89b-12d3-a456-426614174000",
          "diaryEntryId": "223e4567-e89b-12d3-a456-426614174000",
          "tutorId": "123e4567-e89b-12d3-a456-426614174000",
          "feedback": "Great work! Your understanding of the topic is excellent.",
          "createdAt": "2023-07-24T12:00:00.000Z",
          "tutor": {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "name": "Tutor Name"
          }
        }
      ],
      "createdAt": "2023-07-24T00:00:00.000Z",
      "updatedAt": "2023-07-24T12:00:00.000Z"
    }
  ],
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Get or create diary for the user
3. Retrieve all entries for the diary
4. Apply date filter if provided
5. Include skin and feedback details
6. Return entries sorted by date (newest first)

#### 5. Create Diary Entry for Specific Date

**Endpoint:** `POST /diary/entries`

**Description:** Creates a new diary entry for a specific date (past or future) for the currently authenticated student. For today's entry, use the GET /diary/entries/today endpoint instead.

**Request Body:**
```json
{
  "entryDate": "2023-07-25",
  "title": "My First Diary Entry",
  "content": "Today I learned about..."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Diary entry for 2023-07-25 created successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "diaryId": "123e4567-e89b-12d3-a456-426614174000",
    "entryDate": "2023-07-25",
    "title": "My First Diary Entry",
    "content": "Today I learned about...",
    "status": "new",
    "skinId": "123e4567-e89b-12d3-a456-426614174000",
    "skin": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Default Skin",
      "description": "Default diary skin",
      "previewImagePath": "uploads/diary-skins/default.jpg"
    },
    "backgroundColor": null,
    "isPrivate": false,
    "createdAt": "2023-07-25T00:00:00.000Z",
    "updatedAt": "2023-07-25T00:00:00.000Z"
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Validate input data (entryDate is required)
3. Check if entry for the same date already exists
4. Get or create diary for the user
5. Create new entry with minimal initialization
6. Save entry to database
7. Return created entry

#### 6. Update Diary Entry

**Endpoint:** `PATCH /diary/entries/:id`

**Description:** Updates an existing diary entry for the currently authenticated student. Note that the entry date cannot be modified as each entry is strictly bound to a specific date.

**Path Parameters:**
- `id`: The ID of the entry to update

**Request Body:**
```json
{
  "title": "Updated Diary Entry",
  "content": "I've updated my diary entry with more details...",
  "skinId": "223e4567-e89b-12d3-a456-426614174000",
  "backgroundColor": "#e0f7fa",
  "isPrivate": true
}
```

**Important Notes:**
- The entry date cannot be modified after creation
- Only entries in NEW or SUBMIT status can be updated
- Entries in REVIEWED or CONFIRM status cannot be modified

**Response:**
```json
{
  "success": true,
  "message": "Diary entry updated successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "diaryId": "123e4567-e89b-12d3-a456-426614174000",
    "entryDate": "2023-07-25",
    "title": "Updated Diary Entry",
    "content": "I've updated my diary entry with more details...",
    "status": "draft",
    "skinId": "223e4567-e89b-12d3-a456-426614174000",
    "skin": {
      "id": "223e4567-e89b-12d3-a456-426614174000",
      "name": "My Custom Skin",
      "description": "Student's custom skin",
      "previewImagePath": "uploads/diary-skins/custom.jpg"
    },
    "backgroundColor": "#e0f7fa",
    "isPrivate": true,
    "createdAt": "2023-07-25T00:00:00.000Z",
    "updatedAt": "2023-07-25T01:00:00.000Z"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Validate entry ID
3. Retrieve entry and check ownership
4. Validate input data
5. Update entry with provided data
6. Save updated entry
7. Return updated entry

#### 7. Submit Diary Entry

**Endpoint:** `POST /diary/entries/:id/submit`

**Description:** Submits a diary entry for review by a tutor. Requires complete entry data including title, content, and settings.

**Path Parameters:**
- `id`: The ID of the entry to submit

**Request Body:**
```json
{
  "title": "My Completed Diary Entry",
  "content": "This is the full content of my diary entry...",
  "settingsTemplateId": "123e4567-e89b-12d3-a456-426614174000",
  "skinId": "223e4567-e89b-12d3-a456-426614174000",
  "backgroundColor": "#e0f7fa",
  "isPrivate": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Diary entry submitted for review",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "diaryId": "123e4567-e89b-12d3-a456-426614174000",
    "entryDate": "2023-07-25",
    "title": "My Completed Diary Entry",
    "content": "This is the full content of my diary entry...",
    "status": "submit",
    "skinId": "223e4567-e89b-12d3-a456-426614174000",
    "skin": {
      "id": "223e4567-e89b-12d3-a456-426614174000",
      "name": "My Custom Skin",
      "description": "Student's custom skin",
      "previewImagePath": "uploads/diary-skins/custom.jpg"
    },
    "backgroundColor": "#e0f7fa",
    "isPrivate": true,
    "createdAt": "2023-07-25T00:00:00.000Z",
    "updatedAt": "2023-07-25T02:00:00.000Z"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract user ID from JWT token
2. Validate entry ID
3. Retrieve entry and check ownership
4. Validate all required fields are provided
5. Update entry with all provided fields
6. Update entry status to submit
7. Apply settings template
8. Save updated entry
9. Return updated entry

**Diary Entry Lifecycle:**

1. **Creation (NEW)**: A student creates a new diary entry with a title, content, and optional settings.
2. **Submission (SUBMIT)**: The student submits the entry for review by a tutor.
3. **Review (REVIEWED)**: A tutor starts reviewing the entry, provides feedback, and adds corrections with scores.
4. **Confirmation (CONFIRM)**: The tutor confirms the review is complete.
5. **Thanks**: The student may optionally add a thanks message to the tutor.

**Status Flow:**
- NEW → SUBMIT: Entry is submitted for review
- SUBMIT → REVIEWED: Tutor starts reviewing the entry and adds corrections with scores
- REVIEWED → CONFIRM: Tutor confirms the review is complete
- Entries in NEW or SUBMIT status can be updated
- Entries in REVIEWED or CONFIRM status cannot be modified

### Tutor Diary APIs

#### 1. Get Pending Review Entries

**Endpoint:** `GET /tutor/diary/pending-reviews`

**Description:** Retrieves all diary entries with SUBMIT status that are pending review for tutors. Only entries with SUBMIT status are included in this list. Entries are organized by module and can be filtered and sorted.

**Query Parameters:**
- `page`: Page number for pagination
- `limit`: Number of items per page
- `sortBy`: Field to sort by (entryDate, submittedAt, studentName, moduleTitle, moduleLevel)
- `sortDirection`: Sort direction (ASC or DESC)
- `moduleTitle`: Filter by module title (partial match)
- `studentName`: Filter by student name (partial match)
- `dateFrom`: Filter entries with date on or after this date (YYYY-MM-DD)
- `dateTo`: Filter entries with date on or before this date (YYYY-MM-DD)

**Response:**
```json
{
  "success": true,
  "message": "Pending review entries retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "title": "My First Diary Entry",
        "entryDate": "2023-07-25",
        "studentName": "John Doe",
        "studentId": "123e4567-e89b-12d3-a456-426614174000",
        "submittedAt": "2023-07-25T02:00:00.000Z",
        "status": "submit",
        "reviewedByCurrentTutor": false,
        "underReviewByOtherTutor": false,
        "moduleTitle": "Beginner Level",
        "moduleLevel": 1,
        "wordLimit": 100,
        "settingsTemplateId": "123e4567-e89b-12d3-a456-426614174000"
      },
      {
        "id": "223e4567-e89b-12d3-a456-426614174000",
        "title": "Yesterday's Entry",
        "entryDate": "2023-07-24",
        "studentName": "Jane Smith",
        "studentId": "223e4567-e89b-12d3-a456-426614174000",
        "submittedAt": "2023-07-24T02:00:00.000Z",
        "status": "submit",
        "reviewedByCurrentTutor": false,
        "underReviewByOtherTutor": false,
        "moduleTitle": "Intermediate Level",
        "moduleLevel": 2,
        "wordLimit": 200,
        "settingsTemplateId": "223e4567-e89b-12d3-a456-426614174000"
      }
    ],
    "totalCount": 2
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract tutor ID from JWT token
2. Get all diary entries with SUBMIT status, including their settings and module information
3. Apply filters based on query parameters (module, student, date range)
4. Sort entries based on sortBy and sortDirection parameters
5. Filter out entries that are already being reviewed by other tutors
6. Return the organized list of entries

#### 2. Start Review

**Endpoint:** `POST /tutor/diary/entries/:id/start-review`

**Description:** Starts the review process for a diary entry. Locks the entry for 2 hours.

**Path Parameters:**
- `id`: The ID of the entry to review

**Response:**
```json
{
  "success": true,
  "message": "Review started successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "diaryId": "123e4567-e89b-12d3-a456-426614174000",
    "entryDate": "2023-07-25",
    "title": "My First Diary Entry",
    "content": "Today I learned about...",
    "status": "submit",
    "reviewStartTime": "2023-07-25T10:00:00.000Z",
    "reviewExpiryTime": "2023-07-25T12:00:00.000Z",
    "reviewingTutorId": "123e4567-e89b-12d3-a456-426614174000"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract tutor ID from JWT token
2. Validate entry ID
3. Check if entry is in SUBMIT status
4. Check if entry is not already being reviewed by another tutor
5. Set review start time to current time
6. Set reviewing tutor ID to current tutor
7. Set review expiry time to 2 hours from now
8. Save updated entry
9. Return updated entry

#### 3. Add Feedback

**Endpoint:** `POST /tutor/diary/entries/:id/feedback`

**Description:** Adds feedback to a diary entry.

**Path Parameters:**
- `id`: The ID of the entry to add feedback to

**Request Body:**
```json
{
  "feedback": "Great work! Your understanding of the topic is excellent."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Feedback added successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "diaryEntryId": "123e4567-e89b-12d3-a456-426614174000",
    "tutorId": "123e4567-e89b-12d3-a456-426614174000",
    "feedback": "Great work! Your understanding of the topic is excellent.",
    "createdAt": "2023-07-25T11:00:00.000Z",
    "updatedAt": "2023-07-25T11:00:00.000Z",
    "tutor": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Tutor Name"
    }
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Extract tutor ID from JWT token
2. Validate entry ID
3. Check if entry is under review by the current tutor
4. Create new feedback
5. Save feedback to database
6. Return created feedback

#### 4. Submit Correction

**Endpoint:** `POST /tutor/diary/entries/:id/correction`

**Description:** Submits a correction with score for a diary entry. This changes the entry status to REVIEWED.

**Path Parameters:**
- `id`: The ID of the entry to submit correction for

**Request Body:**
```json
{
  "correctionText": "Here are the corrections to your diary entry...",
  "score": 85,
  "comments": "Good effort, but pay attention to grammar"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Correction submitted successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "diaryEntryId": "123e4567-e89b-12d3-a456-426614174000",
    "tutorId": "123e4567-e89b-12d3-a456-426614174000",
    "tutorName": "Tutor Name",
    "correctionText": "Here are the corrections to your diary entry...",
    "score": 85,
    "comments": "Good effort, but pay attention to grammar",
    "createdAt": "2023-07-25T11:15:00.000Z",
    "updatedAt": "2023-07-25T11:15:00.000Z"
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Extract tutor ID from JWT token
2. Validate entry ID
3. Check if entry is in SUBMIT status and being reviewed by the current tutor
4. Create new correction with score
5. Update entry status to REVIEWED
6. Set evaluated at to current time
7. Set evaluated by to current tutor
8. Set score on the entry
9. Save correction and updated entry in a transaction
10. Return created correction

#### 5. Complete Review

**Endpoint:** `POST /tutor/diary/entries/:id/complete-review`

**Description:** Completes the review process for a diary entry. This changes the entry status from REVIEWED to CONFIRM.

**Path Parameters:**
- `id`: The ID of the entry to complete review for

**Response:**
```json
{
  "success": true,
  "message": "Review completed successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "diaryId": "123e4567-e89b-12d3-a456-426614174000",
    "entryDate": "2023-07-25",
    "title": "My First Diary Entry",
    "content": "Today I learned about...",
    "status": "confirm",
    "score": 85,
    "evaluatedAt": "2023-07-25T11:30:00.000Z",
    "evaluatedBy": "123e4567-e89b-12d3-a456-426614174000",
    "feedbacks": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "diaryEntryId": "123e4567-e89b-12d3-a456-426614174000",
        "tutorId": "123e4567-e89b-12d3-a456-426614174000",
        "feedback": "Great work! Your understanding of the topic is excellent.",
        "createdAt": "2023-07-25T11:00:00.000Z",
        "tutor": {
          "id": "123e4567-e89b-12d3-a456-426614174000",
          "name": "Tutor Name"
        }
      }
    ],
    "correction": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "diaryEntryId": "123e4567-e89b-12d3-a456-426614174000",
      "tutorId": "123e4567-e89b-12d3-a456-426614174000",
      "correctionText": "Here are the corrections to your diary entry...",
      "score": 85,
      "comments": "Good effort, but pay attention to grammar"
    }
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract tutor ID from JWT token
2. Validate entry ID
3. Check if entry is in REVIEWED status
4. Check if entry has a correction
5. Check if entry has at least one feedback
6. Update entry status to CONFIRM
7. Save updated entry
8. Return updated entry with feedbacks and correction

### Admin Diary APIs

#### 1. Create Global Diary Skin

**Endpoint:** `POST /admin/diary/skins`

**Description:** Creates a new global diary skin. Only accessible by admins.

**Request Body:**
```json
{
  "name": "New Global Skin",
  "description": "A new global skin for all students",
  "templateContent": "<div class=\"global-skin\">{{content}}</div>",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Global diary skin created successfully",
  "data": {
    "id": "323e4567-e89b-12d3-a456-426614174000",
    "name": "New Global Skin",
    "description": "A new global skin for all students",
    "templateContent": "<div class=\"global-skin\">{{content}}</div>",
    "previewImagePath": null,
    "isActive": true,
    "isGlobal": true,
    "createdById": "123e4567-e89b-12d3-a456-426614174000",
    "createdAt": "2023-07-25T00:00:00.000Z",
    "updatedAt": "2023-07-25T00:00:00.000Z"
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Validate admin access
2. Validate input data
3. Create new skin with admin as creator
4. Set isGlobal to true
5. Save skin to database
6. Return created skin

#### 2. Update Global Diary Skin

**Endpoint:** `PATCH /admin/diary/skins/:id`

**Description:** Updates an existing global diary skin. Only accessible by admins.

**Path Parameters:**
- `id`: The ID of the skin to update

**Request Body:**
```json
{
  "name": "Updated Global Skin",
  "description": "Updated description for global skin",
  "templateContent": "<div class=\"updated-global-skin\">{{content}}</div>",
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Global diary skin updated successfully",
  "data": {
    "id": "323e4567-e89b-12d3-a456-426614174000",
    "name": "Updated Global Skin",
    "description": "Updated description for global skin",
    "templateContent": "<div class=\"updated-global-skin\">{{content}}</div>",
    "previewImagePath": null,
    "isActive": true,
    "isGlobal": true,
    "createdById": "123e4567-e89b-12d3-a456-426614174000",
    "createdAt": "2023-07-25T00:00:00.000Z",
    "updatedAt": "2023-07-25T01:00:00.000Z"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Validate skin ID
3. Retrieve skin and check if it's global
4. Validate input data
5. Update skin with provided data
6. Save updated skin
7. Return updated skin

#### 3. Delete Global Diary Skin

**Endpoint:** `DELETE /admin/diary/skins/:id`

**Description:** Deletes a global diary skin. Only accessible by admins. Cannot delete a skin that is in use.

**Path Parameters:**
- `id`: The ID of the skin to delete

**Response:**
```json
{
  "success": true,
  "message": "Global diary skin deleted successfully",
  "data": null,
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Validate skin ID
3. Retrieve skin and check if it's global
4. Check if skin is in use by any diary entry
5. Delete skin if not in use
6. Return success response

## Features

1. **Diary Management**
   - Create and retrieve student diaries
   - Set default diary skin

2. **Diary Entry Management**
   - Create diary entries
   - Update diary entries
   - Submit entries for review
   - View entry history

3. **Diary Skin Management**
   - Create global skins (admin)
   - Create student-specific skins
   - Apply skins to diary entries

4. **Tutor Feedback and Evaluation**
   - Review diary entries
   - Provide feedback
   - Assign scores
   - Lock entries during review

5. **Diary Awards**
   - Monthly/weekly awards for top scorers
   - Award calculation and distribution

## Tasks

1. **Implement Diary Management APIs**
   - Create diary creation and retrieval endpoints
   - Implement default skin assignment
   - Add diary ownership validation

2. **Implement Diary Entry Management APIs**
   - Create entry CRUD endpoints
   - Implement entry submission workflow
   - Add entry status management

3. **Implement Diary Skin Management APIs**
   - Create skin CRUD endpoints for admins
   - Create student skin endpoints
   - Implement skin application to entries

4. **Implement Tutor Feedback and Evaluation APIs**
   - Create review workflow endpoints
   - Implement feedback submission
   - Add scoring functionality
   - Implement review locking mechanism

5. **Implement Diary Awards System**
   - Create award calculation logic
   - Implement scheduled award distribution
   - Add award notification
