import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiTags, ApiQuery } from '@nestjs/swagger';
import { DiaryService } from './diary.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { 
  CreateDiarySettingsTemplateDto, 
  UpdateDiarySettingsTemplateDto, 
  DiarySettingsTemplateResponseDto 
} from '../../database/models/diary-settings.dto';
import { ApiResponse } from 'src/common/dto/api-response.dto';
import { 
  ApiOkResponseWithType, 
  ApiOkResponseWithArrayType, 
  ApiErrorResponse 
} from 'src/common/decorators/api-response.decorator';
import { PagedListDto } from 'src/common/models/paged-list.dto';
import { PaginationDto } from 'src/common/models/pagination.dto';

@Controller('admin/diary/settings')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('admin-diary-settings')
export class AdminDiarySettingsController {
  constructor(private readonly diaryService: DiaryService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new diary settings template',
    description: 'Create a new diary settings template with title, level, and word limit.'
  })
  @ApiBody({
    type: CreateDiarySettingsTemplateDto,
    description: 'Diary settings template creation data'
  })
  @ApiOkResponseWithType(DiarySettingsTemplateResponseDto, 'Diary settings template created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(403, 'Forbidden - Only admins can create diary settings templates')
  @ApiErrorResponse(500, 'Internal server error')
  async createDiarySettingsTemplate(
    @Body() createDiarySettingsTemplateDto: CreateDiarySettingsTemplateDto
  ): Promise<ApiResponse<DiarySettingsTemplateResponseDto>> {
    const template = await this.diaryService.createDiarySettingsTemplate(createDiarySettingsTemplateDto);
    return ApiResponse.success(template, 'Diary settings template created successfully', 201);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all diary settings templates',
    description: 'Get a list of all diary settings templates.'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    type: Number
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number
  })
  @ApiOkResponseWithArrayType(DiarySettingsTemplateResponseDto, 'Diary settings templates retrieved successfully')
  @ApiErrorResponse(403, 'Forbidden - Only admins can view all diary settings templates')
  @ApiErrorResponse(500, 'Internal server error')
  async getAllDiarySettingsTemplates(
    @Query() paginationDto: PaginationDto
  ): Promise<ApiResponse<PagedListDto<DiarySettingsTemplateResponseDto>>> {
    const templates = await this.diaryService.getAllDiarySettingsTemplates(paginationDto);
    return ApiResponse.success(templates, 'Diary settings templates retrieved successfully');
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a diary settings template by ID',
    description: 'Get a specific diary settings template by its ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary settings template',
    type: String
  })
  @ApiOkResponseWithType(DiarySettingsTemplateResponseDto, 'Diary settings template retrieved successfully')
  @ApiErrorResponse(404, 'Diary settings template not found')
  @ApiErrorResponse(403, 'Forbidden - Only admins can view diary settings templates')
  @ApiErrorResponse(500, 'Internal server error')
  async getDiarySettingsTemplate(
    @Param('id') id: string
  ): Promise<ApiResponse<DiarySettingsTemplateResponseDto>> {
    const template = await this.diaryService.getDiarySettingsTemplateById(id);
    return ApiResponse.success(template, 'Diary settings template retrieved successfully');
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update a diary settings template',
    description: 'Update a specific diary settings template by its ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary settings template to update',
    type: String
  })
  @ApiBody({
    type: UpdateDiarySettingsTemplateDto,
    description: 'Diary settings template update data'
  })
  @ApiOkResponseWithType(DiarySettingsTemplateResponseDto, 'Diary settings template updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(404, 'Diary settings template not found')
  @ApiErrorResponse(403, 'Forbidden - Only admins can update diary settings templates')
  @ApiErrorResponse(500, 'Internal server error')
  async updateDiarySettingsTemplate(
    @Param('id') id: string,
    @Body() updateDiarySettingsTemplateDto: UpdateDiarySettingsTemplateDto
  ): Promise<ApiResponse<DiarySettingsTemplateResponseDto>> {
    const template = await this.diaryService.updateDiarySettingsTemplate(id, updateDiarySettingsTemplateDto);
    return ApiResponse.success(template, 'Diary settings template updated successfully');
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete a diary settings template',
    description: 'Delete a specific diary settings template by its ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the diary settings template to delete',
    type: String
  })
  @ApiOkResponseWithType(Object, 'Diary settings template deleted successfully')
  @ApiErrorResponse(404, 'Diary settings template not found')
  @ApiErrorResponse(403, 'Forbidden - Only admins can delete diary settings templates')
  @ApiErrorResponse(409, 'Conflict - Cannot delete a diary settings template that is in use')
  @ApiErrorResponse(500, 'Internal server error')
  async deleteDiarySettingsTemplate(
    @Param('id') id: string
  ): Promise<ApiResponse<any>> {
    await this.diaryService.deleteDiarySettingsTemplate(id);
    return ApiResponse.success({ success: true }, 'Diary settings template deleted successfully');
  }
}
