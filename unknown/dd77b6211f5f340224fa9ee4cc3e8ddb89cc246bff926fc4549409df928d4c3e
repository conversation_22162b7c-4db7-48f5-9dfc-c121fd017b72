import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { WaterfallParticipation } from './waterfall-participation.entity';
import { WaterfallQuestion } from './waterfall-question.entity';

@Entity()
export class WaterfallAnswer extends AuditableBaseEntity {
  @Column({ name: 'participation_id', type: 'uuid' })
  participationId: string;

  @Column({ name: 'question_id', type: 'uuid' })
  questionId: string;

  @Column({ name: 'submitted_answers', type: 'simple-array' })
  submittedAnswers: string[];

  @Column({ name: 'is_correct', type: 'boolean' })
  isCorrect: boolean;

  // Relationships
  @ManyToOne(() => WaterfallParticipation, (participation) => participation.answers, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'participation_id' })
  participation: WaterfallParticipation;

  @ManyToOne(() => WaterfallQuestion)
  @JoinColumn({ name: 'question_id' })
  question: WaterfallQuestion;
}
