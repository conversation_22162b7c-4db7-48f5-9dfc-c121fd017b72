# Authentication and Authorization Convention

This document provides detailed guidelines for authentication and authorization in the HEC Backend project.

## Table of Contents

1. [Authentication Flow](#authentication-flow)
2. [JWT Structure](#jwt-structure)
3. [User Types and Roles](#user-types-and-roles)
4. [Guards and Decorators](#guards-and-decorators)
5. [API Endpoints](#api-endpoints)
6. [Security Best Practices](#security-best-practices)

## Authentication Flow

### Registration

1. User submits registration data (userId, email, password, etc.)
2. System validates the data
3. System creates a new user with unverified status
4. System sends a verification email with a time-limited token
5. User clicks the verification link
6. System verifies the email and activates the account

### Login

1. User submits login credentials (userId, password)
2. System validates the credentials
3. System checks if the user is verified and active
4. For tutors, system checks if they are approved
5. <PERSON> generates a JWT token with appropriate claims
6. System returns the token along with user information and returnUrl

### Password Reset

1. User requests a password reset by providing email or userId
2. System sends a password reset email with a time-limited token
3. User clicks the reset link and submits a new password
4. System validates the token and updates the password

## JWT Structure

### Token Payload

```typescript
{
  sub: string;            // User ID
  name: string;           // User name
  email: string;          // User email
  roles: string[];        // User roles
  type: UserType;         // User type (admin, tutor, student)
  selectedRole: UserType; // Selected role for this session
  isActive: boolean;      // User active status
  activePlan?: {          // Active subscription plan (for students)
    id: string;
    name: string;
    type: string;
    expiryDate: string;
  };
  iat: number;            // Issued at timestamp
  exp: number;            // Expiration timestamp
}
```

### Token Expiration

- Regular tokens: 1 day
- "Remember me" tokens: 30 days
- Refresh tokens: 30 days

## User Types and Roles

### User Types

- `admin`: Administrator users with full system access
- `tutor`: Tutor users who can review and provide feedback on student diaries
- `student`: Student users who can create diary entries and access learning materials

### Role Assignment

- Users can have multiple roles
- Each user has a primary type (admin, tutor, or student)
- Additional roles can be assigned by administrators
- During login, users can select which role to use for the session

### Role Hierarchy

1. Admin (highest)
2. Tutor
3. Student (lowest)

If no role is selected during login, the system will use the highest available role.

## Guards and Decorators

### Authentication Guard

```typescript
@UseGuards(JwtAuthGuard)
```

Ensures that the request has a valid JWT token.

### Role Guards

```typescript
@UseGuards(AdminGuard)
@UseGuards(TutorGuard)
@UseGuards(StudentGuard)
```

Ensures that the authenticated user has the required role.

### Role Decorator

```typescript
@Roles(UserType.ADMIN)
@Roles(UserType.TUTOR)
@Roles(UserType.STUDENT)
```

Specifies which roles are allowed to access the endpoint.

### Public Decorator

```typescript
@Public()
```

Marks an endpoint as public, bypassing the authentication guard.

### Bearer Auth Decorator

```typescript
@ApiBearerAuth('JWT-auth')
```

Documents that the endpoint requires JWT authentication in Swagger.

## API Endpoints

### Authentication Endpoints

- `POST /auth/register`: Register a new user
- `POST /auth/verify-email`: Verify email address
- `POST /auth/login`: Authenticate a user
- `POST /auth/forgot-password`: Request password reset
- `POST /auth/reset-password`: Reset password
- `POST /auth/forgot-userid`: Request userId recovery
- `POST /auth/resend-verification-email`: Resend verification email

### User Management Endpoints

- `GET /users/profile`: Get current user profile
- `PATCH /users/profile`: Update current user profile
- `POST /users/profile/picture`: Upload profile picture
- `POST /users/admin/create`: Create a new admin user (Admin only)
- `POST /users/admin/create-tutor`: Create a new tutor user (Admin only)
- `POST /users/admin/create-student`: Create a new student user (Admin only)
- `POST /users/admin/assign-role/:userId/:roleName`: Assign a role to a user (Admin only)
- `POST /users/admin/remove-role/:userId/:roleName`: Remove a role from a user (Admin only)

### Tutor Approval Endpoints

- `GET /tutor-approval`: Get all tutor approval requests (Admin only)
- `GET /tutor-approval/pending`: Get pending tutor approval requests (Admin only)
- `GET /tutor-approval/:id`: Get a tutor approval request by ID (Admin only)
- `POST /tutor-approval/approve`: Approve a tutor (Admin only)
- `POST /tutor-approval/reject`: Reject a tutor (Admin only)

## Security Best Practices

### Password Handling

- Store passwords using bcrypt with appropriate salt rounds
- Never store plain-text passwords
- Validate password strength during registration
- Require password confirmation for sensitive operations

### Token Security

- Use HTTPS in production
- Set appropriate token expiration times
- Include only necessary claims in the token
- Validate tokens on every request
- Implement token revocation for logout

### Rate Limiting

- Implement rate limiting for authentication endpoints
- Limit failed login attempts to prevent brute force attacks
- Add delays after failed attempts

### Input Validation

- Validate all input data
- Sanitize user inputs to prevent injection attacks
- Use parameterized queries for database operations

### Error Messages

- Provide generic error messages for authentication failures
- Don't reveal whether a user exists or not
- Log detailed error information for debugging

### Audit Logging

- Log all authentication events (success and failure)
- Log all sensitive operations (role changes, etc.)
- Include IP address, user agent, and timestamp in logs

---

Following these conventions ensures a secure and consistent authentication system across the application.
