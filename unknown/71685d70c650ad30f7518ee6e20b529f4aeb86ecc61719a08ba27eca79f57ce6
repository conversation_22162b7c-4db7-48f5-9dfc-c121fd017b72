import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import CommonEmailService from '../../common/services/email.service';
import LoggerService from '../../common/services/logger.service';
import { EmailService } from './email.service';
import { DeeplinkModule } from '../../common/utils/deeplink.module';

@Module({
  imports: [ConfigModule, DeeplinkModule],
  providers: [
    CommonEmailService,
    LoggerService,
    EmailService
  ],
  exports: [EmailService]
})
export class EmailModule {}
