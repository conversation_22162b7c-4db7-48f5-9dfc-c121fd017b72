import { MigrationInterface, QueryRunner } from "typeorm";

export class QAAssignmentEnhancement1748412144529 implements MigrationInterface {
    name = 'QAAssignmentEnhancement1748412144529'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_submission" ADD "set_sequence" integer`);
        await queryRunner.query(`ALTER TABLE "qa_submission" ADD CONSTRAINT "FK_22e759fa8c64ef8abe88c89e4ba" FOREIGN KEY ("set_sequence") REFERENCES "qa-assignment-sets"("set_sequence") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_submission" DROP CONSTRAINT "FK_22e759fa8c64ef8abe88c89e4ba"`);        
        await queryRunner.query(`ALTER TABLE "qa_submission" DROP COLUMN "set_sequence"`);
    }

}
