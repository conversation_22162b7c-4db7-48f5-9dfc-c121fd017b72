import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsNotEmpty, ArrayMinSize, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
/**
 * DTO for creating waterfall questions
 */

export class CreateWaterfallQuestionsDto {
  @ApiProperty({
    description: 'The question text in rich HTML format',
    example: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>'
  })
  @IsString()
  @IsNotEmpty()
  question_text: string;

  @ApiProperty({
    description: 'The plain text version of the question with [[gap]] markers for evaluation',
    example: 'The cat [[gap]] on the mat.'
  })
  @IsString()
  @IsNotEmpty()
  question_text_plain: string;

  @ApiProperty({
    description: 'Array of correct answers for each gap',
    example: ['sat']
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true }) // ✅ validate each answer is a non-empty string
  correct_answers: string[];

  @ApiProperty({
    description: 'Array of options for the question (should include all correct answers)',
    example: ['sits', 'sat', 'standing', 'lying']
  })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true }) // ✅ validate each option is a string
  options: string[];
}

/**
 * DTO for updating a waterfall question
 */
export class UpdateWaterfallQuestionDto {
  @ApiProperty({
    description: 'The question text in rich HTML format',
    example: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
    required: false
  })
  @IsString()
  @IsOptional()
  question_text?: string;

  @ApiProperty({
    description: 'The plain text version of the question with [[gap]] markers for evaluation',
    example: 'The cat [[gap]] on the mat.',
    required: false
  })
  @IsString()
  @IsOptional()
  question_text_plain?: string;

  @ApiProperty({
    description: 'Array of correct answers for each gap',
    example: ['sits', 'sat'],
    required: false
  })
  @IsArray()
  @IsOptional()
  correct_answers?: string[];

  @ApiProperty({
    description: 'Array of options for the question (should include all correct answers)',
    example: ['sits', 'sat', 'standing', 'lying'],
    required: false
  })
  @IsArray()
  @IsOptional()
  @ArrayMinSize(1, { message: 'Options array must contain at least one option' })
  options?: string[];
}

/**
 * DTO for waterfall question response
 */
export class WaterfallQuestionResponseDto {
  @ApiProperty({
    description: 'The ID of the question',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'The question text in rich HTML format',
    example: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>'
  })
  question_text: string;

  @ApiProperty({
    description: 'The plain text version of the question with [[gap]] markers',
    example: 'The cat [[gap]] on the mat.'
  })
  question_text_plain: string;

  @ApiProperty({
    description: 'Array of correct answers for each gap',
    example: ['sat']
  })
  correct_answers: string[];

  @ApiProperty({
    description: 'Array of options for the question',
    example: ['sits', 'sat', 'standing', 'lying']
  })
  options: string[];

  @ApiProperty({
    description: 'The date the question was created',
    example: '2023-07-25T12:34:56.789Z'
  })
  created_at: Date;

  @ApiProperty({
    description: 'The date the question was last updated',
    example: '2023-07-25T12:34:56.789Z'
  })
  updated_at: Date;
}

/**
 * Wrapper DTO for validating arrays of waterfall questions
 * This ensures proper validation of each question in the array
 */

export class CreateWaterfallQuestionsWrapperDto {
  @ApiProperty({
    description: 'Array of questions to add to the waterfall set',
    type: [CreateWaterfallQuestionsDto],
    isArray: true,
    example: [
      {
        question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
        question_text_plain: 'The cat [[gap]] on the mat.',
        correct_answers: ['sat'],
        options: ['sits', 'sat', 'standing', 'lying']
      }
    ]
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one question must be provided' })
  @ValidateNested({ each: true }) // ✅ Required for nested validation
  @Type(() => CreateWaterfallQuestionsDto) // ✅ Required for transformation
  questions: CreateWaterfallQuestionsDto[];
}

/**
 * Wrapper DTO for validating a single waterfall question
 * This ensures proper validation of the question object
 */
export class CreateWaterfallQuestionWrapperDto {
  @ApiProperty({
    description: 'Question to add to the waterfall set',
    type: CreateWaterfallQuestionsDto,
    example: {
      question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
      question_text_plain: 'The cat [[gap]] on the mat.',
      correct_answers: ['sat'],
      options: ['sits', 'sat', 'standing', 'lying']
    }
  })
  @ValidateNested()
  @Type(() => CreateWaterfallQuestionsDto)
  question: CreateWaterfallQuestionsDto;
}

/**
 * Wrapper DTO for validating a waterfall question update
 * This ensures proper validation of the question update object
 */
export class UpdateWaterfallQuestionWrapperDto {
  @ApiProperty({
    description: 'Question update data',
    type: UpdateWaterfallQuestionDto,
    example: {
      question_text: '<p>The cat <span class="blank-highlight">___</span> on the mat.</p>',
      options: ['sits', 'sat', 'standing', 'lying']
    }
  })
  @ValidateNested()
  @Type(() => UpdateWaterfallQuestionDto)
  question: UpdateWaterfallQuestionDto;
}
