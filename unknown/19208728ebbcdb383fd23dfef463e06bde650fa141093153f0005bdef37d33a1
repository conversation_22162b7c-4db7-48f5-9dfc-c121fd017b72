# Tutor Approval Module

The Tutor Approval Module handles the approval workflow for tutor registrations. When a user registers as a tutor, they must be approved by an admin before they can log in and access tutor features.

## Epics

1. **Tutor Approval Workflow**
2. **Tutor Approval Management**

## APIs

### 1. Get All Tutor Approval Requests

**Endpoint:** `GET /tutor-approval`

**Description:** Retrieves all tutor approval requests. Only accessible by admins.

**Response:**
```json
{
  "success": true,
  "message": "Tutor approval requests retrieved successfully",
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "userId": "123e4567-e89b-12d3-a456-426614174000",
      "status": "pending",
      "adminId": null,
      "adminNotes": null,
      "rejectionReason": null,
      "approvedAt": null,
      "rejectedAt": null,
      "createdAt": "2023-07-25T00:00:00.000Z",
      "updatedAt": "2023-07-25T00:00:00.000Z",
      "user": {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "<PERSON>e",
        "email": "<EMAIL>",
        "userId": "john123",
        "phoneNumber": "1234567890",
        "bio": "Experienced tutor in mathematics"
      }
    },
    {
      "id": "223e4567-e89b-12d3-a456-426614174000",
      "userId": "223e4567-e89b-12d3-a456-426614174000",
      "status": "pending",
      "adminId": null,
      "adminNotes": null,
      "rejectionReason": null,
      "approvedAt": null,
      "rejectedAt": null,
      "createdAt": "2023-07-24T00:00:00.000Z",
      "updatedAt": "2023-07-24T00:00:00.000Z",
      "user": {
        "id": "223e4567-e89b-12d3-a456-426614174000",
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "userId": "jane123",
        "phoneNumber": "9876543210",
        "bio": "Experienced tutor in science"
      }
    }
  ],
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Retrieve all tutor approval requests
3. Include user details for each request
4. Return approval requests

### 2. Get Pending Tutor Approval Requests

**Endpoint:** `GET /tutor-approval/pending`

**Description:** Retrieves all pending tutor approval requests. Only accessible by admins.

**Response:**
```json
{
  "success": true,
  "message": "Pending tutor approval requests retrieved successfully",
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "userId": "123e4567-e89b-12d3-a456-426614174000",
      "status": "pending",
      "adminId": null,
      "adminNotes": null,
      "rejectionReason": null,
      "approvedAt": null,
      "rejectedAt": null,
      "createdAt": "2023-07-25T00:00:00.000Z",
      "updatedAt": "2023-07-25T00:00:00.000Z",
      "user": {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "John Doe",
        "email": "<EMAIL>",
        "userId": "john123",
        "phoneNumber": "1234567890",
        "bio": "Experienced tutor in mathematics"
      }
    },
    {
      "id": "223e4567-e89b-12d3-a456-426614174000",
      "userId": "223e4567-e89b-12d3-a456-426614174000",
      "status": "pending",
      "adminId": null,
      "adminNotes": null,
      "rejectionReason": null,
      "approvedAt": null,
      "rejectedAt": null,
      "createdAt": "2023-07-24T00:00:00.000Z",
      "updatedAt": "2023-07-24T00:00:00.000Z",
      "user": {
        "id": "223e4567-e89b-12d3-a456-426614174000",
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "userId": "jane123",
        "phoneNumber": "9876543210",
        "bio": "Experienced tutor in science"
      }
    }
  ],
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Retrieve tutor approval requests with pending status
3. Include user details for each request
4. Return pending approval requests

### 3. Get Tutor Approval Request by ID

**Endpoint:** `GET /tutor-approval/:id`

**Description:** Retrieves a specific tutor approval request by its ID. Only accessible by admins.

**Path Parameters:**
- `id`: The ID of the approval request to retrieve

**Response:**
```json
{
  "success": true,
  "message": "Tutor approval request retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "status": "pending",
    "adminId": null,
    "adminNotes": null,
    "rejectionReason": null,
    "approvedAt": null,
    "rejectedAt": null,
    "createdAt": "2023-07-25T00:00:00.000Z",
    "updatedAt": "2023-07-25T00:00:00.000Z",
    "user": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "John Doe",
      "email": "<EMAIL>",
      "userId": "john123",
      "phoneNumber": "1234567890",
      "bio": "Experienced tutor in mathematics"
    }
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Validate approval request ID
3. Retrieve approval request with user details
4. Return approval request

### 4. Approve Tutor

**Endpoint:** `POST /tutor-approval/approve`

**Description:** Approves a tutor approval request. Only accessible by admins.

**Request Body:**
```json
{
  "approvalId": "123e4567-e89b-12d3-a456-426614174000",
  "adminNotes": "Approved based on qualifications and experience"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tutor approved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "status": "approved",
    "adminId": "123e4567-e89b-12d3-a456-426614174000",
    "adminNotes": "Approved based on qualifications and experience",
    "rejectionReason": null,
    "approvedAt": "2023-07-25T12:00:00.000Z",
    "rejectedAt": null,
    "createdAt": "2023-07-25T00:00:00.000Z",
    "updatedAt": "2023-07-25T12:00:00.000Z",
    "user": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "John Doe",
      "email": "<EMAIL>",
      "userId": "john123",
      "phoneNumber": "1234567890",
      "bio": "Experienced tutor in mathematics"
    }
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Validate approval request ID
3. Check if request is in pending status
4. Update request status to approved
5. Set admin ID to current admin
6. Set admin notes
7. Set approved at to current time
8. Update user's active status to true
9. Send approval email to tutor
10. Return updated approval request

### 5. Reject Tutor

**Endpoint:** `POST /tutor-approval/reject`

**Description:** Rejects a tutor approval request. Only accessible by admins.

**Request Body:**
```json
{
  "approvalId": "223e4567-e89b-12d3-a456-426614174000",
  "rejectionReason": "Insufficient qualifications for the role",
  "adminNotes": "Consider reapplying after gaining more experience"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Tutor rejected successfully",
  "data": {
    "id": "223e4567-e89b-12d3-a456-426614174000",
    "userId": "223e4567-e89b-12d3-a456-426614174000",
    "status": "rejected",
    "adminId": "123e4567-e89b-12d3-a456-426614174000",
    "adminNotes": "Consider reapplying after gaining more experience",
    "rejectionReason": "Insufficient qualifications for the role",
    "approvedAt": null,
    "rejectedAt": "2023-07-25T12:30:00.000Z",
    "createdAt": "2023-07-24T00:00:00.000Z",
    "updatedAt": "2023-07-25T12:30:00.000Z",
    "user": {
      "id": "223e4567-e89b-12d3-a456-426614174000",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "userId": "jane123",
      "phoneNumber": "9876543210",
      "bio": "Experienced tutor in science"
    }
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate admin access
2. Validate approval request ID
3. Check if request is in pending status
4. Update request status to rejected
5. Set admin ID to current admin
6. Set admin notes and rejection reason
7. Set rejected at to current time
8. Send rejection email to tutor
9. Return updated approval request

## Features

1. **Tutor Approval Workflow**
   - Automatic creation of approval request on tutor registration
   - Email notification to admin on new tutor registration
   - Email notification to tutor on approval/rejection

2. **Tutor Approval Management**
   - View all approval requests
   - View pending approval requests
   - Approve tutor with notes
   - Reject tutor with reason

## Tasks

1. **Implement Tutor Approval Workflow**
   - Create approval request on tutor registration
   - Implement email notifications
   - Add approval status check on login

2. **Implement Tutor Approval Management APIs**
   - Create endpoints for listing approval requests
   - Implement approval endpoint
   - Implement rejection endpoint
   - Add admin notes and rejection reason
