import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for student dropdown items
 */
export class StudentDropdownDto {
  @ApiProperty({
    description: 'Student ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'Student name',
    example: '<PERSON>'
  })
  name: string;

  @ApiProperty({
    description: 'Student user ID',
    example: 'john123'
  })
  userId: string;

  @ApiProperty({
    description: 'Student email',
    example: '<EMAIL>'
  })
  email: string;
}
