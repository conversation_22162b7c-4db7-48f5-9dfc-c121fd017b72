# HEC Backend User Stories

This document outlines the user stories implemented in the current HEC backend codebase. It serves as a reference for understanding existing functionality and as a basis for adding new requirements.

## Table of Contents

- [Authentication & Authorization](#authentication--authorization)
- [User Management](#user-management)
- [Profile Management](#profile-management)
- [Subscription & Plans](#subscription--plans)
- [Promotion Management](#promotion-management)
- [Design Object Shop Management](#design-object-shop-management)
- [HEC Diary Module](#hec-diary-module)
- [System Administration](#system-administration)
- [Security & Infrastructure](#security--infrastructure)

## Authentication & Authorization

### User Registration

**As a** prospective user,
**I want to** register for an account,
**So that** I can access the system's features.

- Users can register as either students or tutors
- Registration requires userId, email, phone, gender, password, confirmPassword, and agreement to terms
- The userId is used as the initial display name
- Email verification is required to activate the account
- Verification emails expire after 5 minutes
- Users can request resending of verification emails

### User Login

**As a** registered user,
**I want to** log in to the system,
**So that** I can access my account and use the system.

- Users log in using their userId (not display name) and password
- "Remember me" functionality extends session to 30 days
- <PERSON><PERSON> can specify a return URL (defaults to /home)
- Login automatically selects the highest available role if none is specified
- Users can select a specific role if they have multiple roles
- JWT tokens include user information and active subscription details for students

### Password Management

**As a** user,
**I want to** recover my account if I forget my credentials,
**So that** I can regain access to my account.

- Users can request password reset using either userId or email
- Password reset links are time-limited (5 minutes)
- Users can request their userId if forgotten

### Role-Based Access

**As a** user with multiple roles,
**I want to** access different parts of the system based on my role,
**So that** I can perform different functions within the system.

- Users can have multiple roles (admin, tutor, student)
- Different controllers are protected by role-specific guards
- Only admins can create admin users
- Tutors require approval before they can log in
- Admin-created accounts (admin, student, tutor) are immediately usable without email confirmation

## User Management

### Admin User Management

**As an** administrator,
**I want to** manage user accounts,
**So that** I can maintain the system's user base.

- Admins can create users of any type (admin, tutor, student)
- Admins can assign and remove roles from users
- Admin-created accounts don't require email verification
- Admins can view all users with pagination

### Tutor Approval

**As an** administrator,
**I want to** approve or reject tutor registrations,
**So that** I can control who can act as a tutor in the system.

- Tutors require admin approval before they can log in
- Admins can approve or reject tutor applications with a reason
- Tutors are notified of their approval status

## Profile Management

### Profile Information

**As a** user,
**I want to** update my profile information,
**So that** my account reflects my current details.

- Users can update their profile information (name, phone, address, bio, etc.)
- The name field is for display purposes only and doesn't affect login
- Users can only update their own profile
- Age is calculated from date of birth

### Profile Pictures

**As a** user,
**I want to** upload and manage my profile picture,
**So that** I can personalize my account.

- Users can upload profile pictures (JPEG, PNG, or GIF)
- Profile pictures are stored in the file system with a reference in the database
- Only one profile picture is allowed per user
- Profile pictures are served via secure, token-based URLs
- Compact token-based URLs are used for better performance and security
- Profile picture URLs expire after a set time for security

## Subscription & Plans

### Subscription Management

**As a** student,
**I want to** subscribe to a plan,
**So that** I can access premium features.

- Students can subscribe to one of four plans (Starter, Standard, Pro, Ultimate)
- Plans have monthly or yearly options
- When a user subscribes to a plan, any previously active plan is deactivated
- Subscription details are included in the authentication token
- Students can view their active plan

### Plan Features

**As a** student,
**I want to** know what features are included in my plan,
**So that** I can make use of all available features.

- Plans include specific features
- Features are structured as an array of feature objects (type, name, description, isActive)
- The active plan's features are returned in API responses

## Promotion Management

### Manage Promotions

**As an** administrator,
**I want to** create and manage promotions or coupons,
**So that** users can receive discounts or special offers.

- Admins can create, update, and delete promotions
- Promotions can be applied to subscription plans or shop items
- Promotions have start and end dates
- Promotions can offer percentage or fixed amount discounts
- Promotions can be limited to specific product categories or plans
- Promotion codes can be generated for user redemption
- Active promotions are visible to eligible categories
- All promotions are targeted at student users

## Design Object Shop Management

### Manage Design Objects

**As an** administrator,
**I want to** add, delete, modify, apply discounts, and change pricing of design objects,
**So that** students can buy and use them.

- Admins can create, update, and delete design objects in the shop
- Design objects have properties including: file, title, category, description, type (in-app purchase, free), price, and applied discount
- Admins can apply promotions to specific design objects or categories
- Pricing can be updated individually or in bulk
- Design objects can be categorized for easier browsing
- Admins can mark items as featured or recommended

### Manage Diary Skins in Shop

**As an** administrator,
**I want to** add diary skins to the shop,
**So that** students can purchase and use them to customize their diaries.

- Admins can create diary skins and add them to the shop
- Only admin-created skins can be added to the shop
- Skins in the shop have the same properties as other design objects
- Admins can apply promotions to diary skins
- Admins can categorize skins for easier browsing
- Purchased skins are immediately available for students to use in their diaries

### Numbering Control

**As an** administrator,
**I want to** control and manage the item numbers of design objects,
**So that** the shop maintains consistency.

- Each design object has a unique item number
- Item numbers follow a consistent format
- Admins can specify or override item numbers
- The system prevents duplicate item numbers
- Item numbers can be used for inventory tracking and reporting

### Purchase Design Objects

**As a** student,
**I want to** browse and purchase design objects,
**So that** I can enhance my learning experience.

- Students can browse design objects by category
- Students can view detailed information about each design object
- Students can purchase design objects using in-app currency (reward points) or real money
- Purchased design objects are immediately available for use
- Students can view their purchase history
- Students can apply promotion codes during checkout
- Free design objects can be acquired without payment

## HEC Diary Module

### Diary Entries

**As a** student,
**I want to** create and manage diary entries,
**So that** I can record my learning progress.

- Students can create daily diary entries
- Entries have a lifecycle: NEW (draft), SUBMIT, REVIEWED, and CONFIRM
- Students can edit entries without affecting scores
- Students can only view their own diary entries
- Students can filter diary entries by date or subject/title

### Diary Skins

**As a** student,
**I want to** customize the appearance of my diary,
**So that** I can personalize my learning experience.

- Students can use skins to customize diary appearance
- Both admin-created global skins and student-created private skins are supported
- Admin-created skins can be included in the shop as purchasable design objects
- Student-created skins are private and cannot be sold in the shop
- Students can only delete their own skins if not in use by diary entries
- Admins can only edit/delete global skins created by them
- Skins are one type of item in the design object shop

### Diary Review

**As a** tutor,
**I want to** review and provide feedback on student diary entries,
**So that** I can help students improve.

- Tutors evaluate and provide feedback within a 2-hour lock period
- Entries under review are locked for 2 hours
- Single immutable score per entry, multiple feedbacks allowed
- Tutors can see all diary entries pending review
- Entries already reviewed or picked by other tutors are filtered out

### Diary Awards

**As a** student,
**I want to** receive recognition for my diary entries,
**So that** I am motivated to continue learning.

- Monthly awards for top scorers
- Awards are based on diary entry scores, attendance, and decoration of the diary
- All awards across all modules provide equivalent reward points
- Reward points can be used to purchase objects from the design object shop
- Reward points serve as an in-app currency for students

### Award Management

**As an** administrator,
**I want to** configure and manage awards across all modules,
**So that** students receive appropriate recognition and reward points for their achievements.

- Admins can create and configure different types of awards for various modules
- Admins can set the criteria for each award (scores, attendance, diary decoration, etc.)
- Admins can assign reward point values to different awards
- Admins can schedule when awards are calculated and distributed (monthly, after specific events)
- Admins can view award history and statistics
- Admins can manually adjust reward points for individual students if needed
- The system automatically calculates and distributes awards based on the configured criteria
- Admins can create special or seasonal awards for specific events or promotions

## System Administration

### System Initialization

**As a** system administrator,
**I want to** initialize the system with required data,
**So that** it's ready for use.

- Seed admin, tutor, and student users with respective roles
- Seed multiple students with different subscription plans
- Seed diary skins
- Support automatic database migrations during startup

### Audit & Logging

**As a** system administrator,
**I want to** track system activities,
**So that** I can monitor usage and troubleshoot issues.

- Global error handling and logging
- Audit/operation logs for all requests
- Specific error messages and proper logging

## Security & Infrastructure

### Security Features

**As a** system administrator,
**I want to** ensure the system is secure,
**So that** user data is protected.

- JWT-based authentication
- Role-based access control
- Password hashing using bcryptjs
- Protected resources (like images) served via secure, token-based URLs
- Token-based URLs are time-limited, non-guessable, and validated without database access

### API Documentation

**As a** developer,
**I want to** have clear API documentation,
**So that** I can understand and use the APIs correctly.

- Swagger documentation with consistent API response structures
- Real seeded credentials for login and registration endpoints in API documentation
- Documentation organized by group/module/area
- Standardized response format (success, message, data, errors, statusCode)

### System Configuration

**As a** system administrator,
**I want to** configure the system easily,
**So that** it can be adapted to different environments.

- Environment-based configuration
- Email configuration for sending notifications
- File storage configuration
- CORS configuration
