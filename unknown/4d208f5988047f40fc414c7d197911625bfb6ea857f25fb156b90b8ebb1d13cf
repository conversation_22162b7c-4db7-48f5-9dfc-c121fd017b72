import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { StoryMakerParticipation } from './story-maker-participation.entity';
import { StoryMakerEvaluation } from './story-maker-evaluation.entity';

@Entity()
export class StoryMakerSubmission extends AuditableBaseEntity {
  @Column({ name: 'participation_id', type: 'uuid' })
  participationId: string;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'submitted_at' })
  submittedAt: Date;

  @Column({ name: 'is_evaluated', default: false })
  isEvaluated: boolean;

  // Relationships
  @ManyToOne(() => StoryMakerParticipation, (participation) => participation.submissions)
  @JoinColumn({ name: 'participation_id' })
  participation: StoryMakerParticipation;

  @OneToMany(() => StoryMakerEvaluation, (evaluation) => evaluation.submission)
  evaluations: StoryMakerEvaluation[];
}
