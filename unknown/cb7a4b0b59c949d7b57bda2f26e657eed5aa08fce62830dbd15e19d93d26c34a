import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { NotificationService } from './notification.service';

@Injectable()
export class NotificationScheduler {
  private readonly logger = new Logger(NotificationScheduler.name);

  constructor(private readonly notificationService: NotificationService) {}

  @Cron(CronExpression.EVERY_HOUR)
  async retryFailedDeliveries() {
    this.logger.log('Running scheduled task to retry failed notification deliveries');
    
    try {
      const count = await this.notificationService.retryFailedDeliveries();
      this.logger.log(`Successfully retried ${count} failed notification deliveries`);
    } catch (error) {
      this.logger.error(`Error retrying failed notification deliveries: ${error.message}`, error.stack);
    }
  }
}
