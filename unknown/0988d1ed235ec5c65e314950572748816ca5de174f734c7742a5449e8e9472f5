# API Response Convention

This document provides detailed guidelines for API response formatting and documentation in the HEC Backend project.

## Table of Contents

1. [Response Structure](#response-structure)
2. [Response Types](#response-types)
3. [Swagger Documentation](#swagger-documentation)
4. [Error Handling](#error-handling)
5. [Examples](#examples)

## Response Structure

All API responses must follow the unified `ApiResponse<T>` structure:

```typescript
{
  success: boolean;       // Indicates if the request was successful
  message: string;        // Human-readable message
  statusCode: number;     // HTTP status code
  data?: T;               // Response data (optional)
  errors?: ErrorDetail[]; // Error details if success is false (optional)
}
```

The `ErrorDetail` structure:

```typescript
{
  error: string;          // Error message
}
```

## Response Types

### Success Response

For successful operations, use `ApiResponse.success()`:

```typescript
return ApiResponse.success(data, 'Operation completed successfully', statusCode);
```

Parameters:
- `data`: The response data (can be null for operations that don't return data)
- `message`: A human-readable success message
- `statusCode`: HTTP status code (default: 200)

### Error Response

For error responses, throw appropriate exceptions that will be caught by the global exception filter:

```typescript
throw new BadRequestException('Invalid input data');
throw new NotFoundException('User not found');
throw new ConflictException('User already exists');
```

## Swagger Documentation

### Success Responses

Use the following decorators for documenting success responses:

#### Single Object Response

```typescript
@ApiOkResponseWithType(UserResponseDto, 'User retrieved successfully')
```

#### Array Response

```typescript
@ApiOkResponseWithArrayType(UserResponseDto, 'Users retrieved successfully')
```

#### Paginated Response

```typescript
@ApiPaginatedResponse('Users retrieved successfully', UserResponseDto)
```

#### Paged List Response

```typescript
@ApiOkResponseWithPagedListType(UserResponseDto, 'Users retrieved successfully')
```

#### Custom Response

For responses with a specific structure (like login response):

1. Create a dedicated DTO:

```typescript
// auth-response.dto.ts
export class LoginResponseDto {
  @ApiProperty({ example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' })
  access_token: string;

  @ApiProperty({ type: UserResponseDto })
  user: UserResponseDto;

  @ApiProperty({ example: '/dashboard' })
  returnUrl: string;
}
```

2. Use the DTO with `ApiOkResponseWithType`:

```typescript
@ApiOkResponseWithType(LoginResponseDto, 'Login successful')
```

### Error Responses

Use `ApiErrorResponse` for documenting error responses:

```typescript
@ApiErrorResponse(400, 'Invalid input data', ['Email must be valid', 'Password is required'])
@ApiErrorResponse(404, 'User not found')
@ApiErrorResponse(500, 'An error occurred')
```

## Error Handling

### Global Exception Filter

All exceptions are handled by the global exception filter, which transforms them into the unified API response format.

### Custom Exceptions

Use NestJS built-in exceptions or create custom exceptions that extend `HttpException`:

```typescript
throw new BadRequestException('Invalid input data');
throw new NotFoundException('User not found');
throw new ConflictException('User already exists');
```

### Validation Errors

Validation errors are automatically handled by the validation pipe and transformed into the unified API response format:

```json
{
  "success": false,
  "message": "Validation failed",
  "statusCode": 400,
  "errors": [
    { "error": "Email must be a valid email address" },
    { "error": "Password must be at least 8 characters long" }
  ]
}
```

## Examples

### Controller Method (Single Object)

```typescript
@Get(':id')
@ApiOperation({
  summary: 'Get a user by ID',
  description: 'Retrieves a user by their ID.'
})
@ApiOkResponseWithType(UserResponseDto, 'User retrieved successfully')
@ApiErrorResponse(404, 'User not found')
async findOne(@Param('id') id: string): Promise<ApiResponse<UserResponseDto>> {
  const user = await this.usersService.findById(id);
  return ApiResponse.success(user, 'User retrieved successfully');
}
```

### Controller Method (Array)

```typescript
@Get()
@ApiOperation({
  summary: 'Get all users',
  description: 'Retrieves all users.'
})
@ApiOkResponseWithArrayType(UserResponseDto, 'Users retrieved successfully')
async findAll(): Promise<ApiResponse<UserResponseDto[]>> {
  const users = await this.usersService.findAll();
  return ApiResponse.success(users, 'Users retrieved successfully');
}
```

### Controller Method (Paginated)

```typescript
@Get()
@ApiOperation({
  summary: 'Get all users (paginated)',
  description: 'Retrieves all users with pagination.'
})
@ApiOkResponseWithType(PaginatedResponse, 'Users retrieved successfully')
async findAllPaginated(
  @Query() paginationDto: PaginationDto
): Promise<ApiResponse<PaginatedResponse<UserResponseDto>>> {
  const { items, totalItems } = await this.usersService.findAllPaginated(paginationDto);
  const paginatedResponse = this.paginationService.createPaginatedResponse(
    items,
    paginationDto,
    totalItems,
    'users'
  );
  return ApiResponse.success(paginatedResponse, 'Users retrieved successfully');
}
```

### Controller Method (PagedList)

```typescript
@Get()
@ApiOperation({
  summary: 'Get all users (paged list)',
  description: 'Retrieves all users with pagination using PagedListDto.'
})
@ApiOkResponseWithPagedListType(UserResponseDto, 'Users retrieved successfully')
async findAll(@Query() paginationDto: PaginationDto): Promise<ApiResponse<PagedListDto<UserResponseDto>>> {
  const pagedList = await this.usersService.findAll(paginationDto);
  return ApiResponse.success(pagedList, 'Users retrieved successfully');
}
```

### Controller Method (Custom Response)

```typescript
@Post('login')
@Public()
@HttpCode(HttpStatus.OK)
@ApiOperation({
  summary: 'User login',
  description: 'Authenticates a user and returns a JWT token.'
})
@ApiOkResponseWithType(LoginResponseDto, 'Login successful')
@ApiErrorResponse(401, 'Invalid credentials')
async login(@Body() loginDto: LoginUserDto): Promise<ApiResponse<LoginResponseDto>> {
  const result = await this.authService.login(loginDto);
  return ApiResponse.success(result, 'Login successful');
}
```

### Controller Method (No Content)

```typescript
@Delete(':id')
@ApiOperation({
  summary: 'Delete a user',
  description: 'Deletes a user by their ID.'
})
@ApiOkResponseWithType(null, 'User deleted successfully')
@ApiErrorResponse(404, 'User not found')
async remove(@Param('id') id: string): Promise<ApiResponse<null>> {
  await this.usersService.remove(id);
  return ApiResponse.success(null, 'User deleted successfully');
}
```

---

Following these conventions ensures consistent API responses across the application, making it easier for frontend developers to work with the API and for backend developers to maintain the codebase.
