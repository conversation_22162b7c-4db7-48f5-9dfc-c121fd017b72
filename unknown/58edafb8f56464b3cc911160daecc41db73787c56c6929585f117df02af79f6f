import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DiarySettingsTemplate } from '../../database/entities/diary-settings-template.entity';
import { DiaryEntrySettings } from '../../database/entities/diary-entry-settings.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import {
  CreateDiarySettingsTemplateDto,
  UpdateDiarySettingsTemplateDto,
  DiarySettingsTemplateResponseDto
} from '../../database/models/diary-settings.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import LoggerService from '../../common/services/logger.service';

@Injectable()
export class DiarySettingsService {
  private readonly logger = new LoggerService();

  constructor(
    @InjectRepository(DiarySettingsTemplate)
    private readonly diarySettingsTemplateRepository: Repository<DiarySettingsTemplate>,
    @InjectRepository(DiaryEntrySettings)
    private readonly diaryEntrySettingsRepository: Repository<DiaryEntrySettings>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>
  ) {}

  /**
   * Create a new diary settings template
   * @param createDiarySettingsTemplateDto Data for creating a new diary settings template
   * @returns The created diary settings template
   */
  async createDiarySettingsTemplate(
    createDiarySettingsTemplateDto: CreateDiarySettingsTemplateDto
  ): Promise<DiarySettingsTemplateResponseDto> {
    try {
      // Create the settings template
      const template = this.diarySettingsTemplateRepository.create({
        title: createDiarySettingsTemplateDto.title,
        level: createDiarySettingsTemplateDto.level,
        wordLimit: createDiarySettingsTemplateDto.wordLimit,
        description: createDiarySettingsTemplateDto.description,
        isActive: createDiarySettingsTemplateDto.isActive !== undefined ? createDiarySettingsTemplateDto.isActive : true
      });

      // Save the template
      const savedTemplate = await this.diarySettingsTemplateRepository.save(template);

      // Return the template
      return this.mapTemplateToResponseDto(savedTemplate);
    } catch (error) {
      this.logger.error(`Error creating diary settings template: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all diary settings templates
   * @param paginationDto Pagination parameters
   * @returns A paged list of diary settings templates
   */
  async getAllDiarySettingsTemplates(
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<DiarySettingsTemplateResponseDto>> {
    try {
      const page = paginationDto?.page || 1;
      const limit = paginationDto?.limit || 10;
      const skip = (page - 1) * limit;

      // Get the total count
      const totalCount = await this.diarySettingsTemplateRepository.count();

      // Get the templates
      const templates = await this.diarySettingsTemplateRepository.find({
        skip,
        take: limit,
        order: { createdAt: 'DESC' }
      });

      // Map the templates to response DTOs
      const templateDtos = templates.map(template => this.mapTemplateToResponseDto(template));

      // Return the paged list
      return new PagedListDto(templateDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting all diary settings templates: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get active diary settings templates
   * @param paginationDto Pagination parameters
   * @returns A paged list of active diary settings templates
   */
  async getActiveDiarySettingsTemplates(
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<DiarySettingsTemplateResponseDto>> {
    try {
      const page = paginationDto?.page || 1;
      const limit = paginationDto?.limit || 10;
      const skip = (page - 1) * limit;

      // Get the total count of active templates
      const totalCount = await this.diarySettingsTemplateRepository.count({
        where: { isActive: true }
      });

      // Get the active templates
      const templates = await this.diarySettingsTemplateRepository.find({
        where: { isActive: true },
        skip,
        take: limit,
        order: { level: 'ASC' }
      });

      // Map the templates to response DTOs
      const templateDtos = templates.map(template => this.mapTemplateToResponseDto(template));

      // Return the paged list
      return new PagedListDto(templateDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting active diary settings templates: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a diary settings template by ID
   * @param id The ID of the diary settings template
   * @returns The diary settings template
   */
  async getDiarySettingsTemplateById(id: string): Promise<DiarySettingsTemplateResponseDto> {
    try {
      if (!id) {
        this.logger.warn('No template ID provided');
        return null;
      }

      // Get the template
      const template = await this.diarySettingsTemplateRepository.findOne({
        where: { id }
      });

      // Check if the template exists
      if (!template) {
        throw new NotFoundException(`Diary settings template with ID ${id} not found`);
      }

      // Return the template
      return this.mapTemplateToResponseDto(template);
    } catch (error) {
      this.logger.error(`Error getting diary settings template by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a default diary settings template (first active template)
   * @returns The default diary settings template or null if none exists
   */
  async getDefaultDiarySettingsTemplate(): Promise<DiarySettingsTemplateResponseDto> {
    try {
      this.logger.log('Getting default diary settings template');

      // Get the first active template ordered by level
      const template = await this.diarySettingsTemplateRepository.findOne({
        where: { isActive: true },
        order: { level: 'ASC' }
      });

      if (!template) {
        this.logger.warn('No active diary settings template found');
        return null;
      }

      this.logger.log(`Found default template: ${template.id} - ${template.title}`);
      return this.mapTemplateToResponseDto(template);
    } catch (error) {
      this.logger.error(`Error getting default diary settings template: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Update a diary settings template
   * @param id The ID of the diary settings template
   * @param updateDiarySettingsTemplateDto Data for updating the diary settings template
   * @returns The updated diary settings template
   */
  async updateDiarySettingsTemplate(
    id: string,
    updateDiarySettingsTemplateDto: UpdateDiarySettingsTemplateDto
  ): Promise<DiarySettingsTemplateResponseDto> {
    try {
      // Get the template
      const template = await this.diarySettingsTemplateRepository.findOne({
        where: { id }
      });

      // Check if the template exists
      if (!template) {
        throw new NotFoundException(`Diary settings template with ID ${id} not found`);
      }

      // Update the template
      if (updateDiarySettingsTemplateDto.title !== undefined) {
        template.title = updateDiarySettingsTemplateDto.title;
      }
      if (updateDiarySettingsTemplateDto.level !== undefined) {
        template.level = updateDiarySettingsTemplateDto.level;
      }
      if (updateDiarySettingsTemplateDto.wordLimit !== undefined) {
        template.wordLimit = updateDiarySettingsTemplateDto.wordLimit;
      }
      if (updateDiarySettingsTemplateDto.description !== undefined) {
        template.description = updateDiarySettingsTemplateDto.description;
      }
      if (updateDiarySettingsTemplateDto.isActive !== undefined) {
        template.isActive = updateDiarySettingsTemplateDto.isActive;
      }

      // Save the template
      const updatedTemplate = await this.diarySettingsTemplateRepository.save(template);

      // Return the template
      return this.mapTemplateToResponseDto(updatedTemplate);
    } catch (error) {
      this.logger.error(`Error updating diary settings template: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete a diary settings template
   * @param id The ID of the diary settings template
   */
  async deleteDiarySettingsTemplate(id: string): Promise<void> {
    try {
      // Get the template
      const template = await this.diarySettingsTemplateRepository.findOne({
        where: { id }
      });

      // Check if the template exists
      if (!template) {
        throw new NotFoundException(`Diary settings template with ID ${id} not found`);
      }

      // Check if the template is in use
      const settingsCount = await this.diaryEntrySettingsRepository.count({
        where: { settingsTemplateId: id }
      });

      if (settingsCount > 0) {
        throw new ConflictException(`Cannot delete diary settings template with ID ${id} as it is in use by ${settingsCount} diary entries`);
      }

      // Delete the template
      await this.diarySettingsTemplateRepository.remove(template);
    } catch (error) {
      this.logger.error(`Error deleting diary settings template: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create diary entry settings for a diary entry
   * @param diaryEntryId The ID of the diary entry
   * @param settingsTemplateId The ID of the settings template
   * @returns The created diary entry settings
   */
  async createDiaryEntrySettings(
    diaryEntryId: string,
    settingsTemplateId: string
  ): Promise<DiaryEntrySettings> {
    try {
      // Get the settings template
      const template = await this.diarySettingsTemplateRepository.findOne({
        where: { id: settingsTemplateId }
      });

      // Check if the template exists
      if (!template) {
        throw new NotFoundException(`Diary settings template with ID ${settingsTemplateId} not found`);
      }

      // Check if the template is active
      if (!template.isActive) {
        throw new BadRequestException(`Diary settings template with ID ${settingsTemplateId} is not active`);
      }

      // Check if the diary entry exists
      const diaryEntry = await this.diaryEntryRepository.findOne({
        where: { id: diaryEntryId }
      });

      if (!diaryEntry) {
        throw new NotFoundException(`Diary entry with ID ${diaryEntryId} not found`);
      }

      // Check if the diary entry already has settings
      const existingSettings = await this.diaryEntrySettingsRepository.findOne({
        where: { diaryEntryId }
      });

      if (existingSettings) {
        throw new ConflictException(`Diary entry with ID ${diaryEntryId} already has settings`);
      }

      // Create the settings
      const settings = this.diaryEntrySettingsRepository.create({
        diaryEntryId,
        settingsTemplateId,
        title: template.title,
        level: template.level,
        wordLimit: template.wordLimit
      });

      // Save the settings
      return await this.diaryEntrySettingsRepository.save(settings);
    } catch (error) {
      this.logger.error(`Error creating diary entry settings: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Seed default diary settings templates if none exist
   * This ensures there's always at least one template available
   */
  async seedDefaultTemplates(): Promise<void> {
    try {
      // Check if any templates exist
      const count = await this.diarySettingsTemplateRepository.count();

      if (count === 0) {
        this.logger.info('No diary settings templates found. Seeding default templates...');

        // Create default templates
        const defaultTemplates = [
          {
            title: 'Beginner Level',
            level: 1,
            wordLimit: 50,
            description: 'For beginners - focus on basic vocabulary and simple sentences',
            isActive: true
          },
          {
            title: 'Intermediate Level',
            level: 2,
            wordLimit: 100,
            description: 'For intermediate students - practice more complex sentences and grammar',
            isActive: true
          },
          {
            title: 'Advanced Level',
            level: 3,
            wordLimit: 200,
            description: 'For advanced students - focus on sophisticated vocabulary and complex structures',
            isActive: true
          }
        ];

        // Save all templates
        for (const template of defaultTemplates) {
          const newTemplate = this.diarySettingsTemplateRepository.create(template);
          await this.diarySettingsTemplateRepository.save(newTemplate);
          this.logger.info(`Created default template: ${template.title}`);
        }

        this.logger.info('Default diary settings templates seeded successfully');
      } else {
        this.logger.info(`Found ${count} existing diary settings templates. Skipping seed.`);
      }
    } catch (error) {
      this.logger.error(`Error seeding default diary settings templates: ${error.message}`, error.stack);
    }
  }

  /**
   * Map a diary settings template entity to a response DTO
   * @param template The diary settings template entity
   * @returns The diary settings template response DTO
   */
  private mapTemplateToResponseDto(
    template: DiarySettingsTemplate
  ): DiarySettingsTemplateResponseDto {
    return {
      id: template.id,
      title: template.title,
      level: template.level,
      wordLimit: template.wordLimit,
      description: template.description,
      isActive: template.isActive,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt
    };
  }
}
