import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { UserType } from 'src/database/entities/user.entity';

@Injectable()
export class TutorGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      return false;
    }

    // Check if user has tutor type
    if (user.type === UserType.TUTOR) {
      return true;
    }

    // Check if user has tutor role
    if (user.roles && Array.isArray(user.roles)) {
      return user.roles.includes('tutor');
    }

    // Admin can also access tutor endpoints
    if (user.type === UserType.ADMIN || (user.roles && user.roles.includes('admin'))) {
      return true;
    }

    return false;
  }
}
