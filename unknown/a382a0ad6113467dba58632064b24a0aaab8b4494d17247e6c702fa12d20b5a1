import { Controller, Get, Param, NotFoundException, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { UsersService } from './users.service';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { AdminProfileViewDto, TutorProfileViewDto, StudentProfileViewDto } from './dto/profile-view.dto';
import { UserType } from '../../database/entities/user.entity';
import { formatToYYYYMMDD } from '../../common/utils/date-utils';

@ApiTags('Profile View')
@ApiBearerAuth('JWT-auth')
@Controller('profiles')
@UseGuards(JwtAuthGuard)
export class ProfileViewController {
  constructor(
    private readonly usersService: UsersService,
    private readonly profilePictureService: ProfilePictureService
  ) {}

  /**
   * Format a Date object to YYYY-MM-DD string
   * @param date Date object to format
   * @returns Formatted date string
   */
  private formatDateToYYYYMMDD(date: Date): string {
    if (!date) return '';
    return formatToYYYYMMDD(date) || '';
  }

  @Get('admin/:id')
  @ApiOperation({
    summary: 'View admin profile',
    description: 'View the profile of an admin user by ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'Admin user ID',
    type: String
  })
  @ApiOkResponseWithType(AdminProfileViewDto, 'Admin profile retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(404, 'Admin not found')
  async viewAdminProfile(@Param('id') id: string): Promise<ApiResponse<AdminProfileViewDto>> {
    const user = await this.usersService.findById(id);

    if (!user || user.type !== UserType.ADMIN) {
      throw new NotFoundException('Admin not found');
    }

    // Create the profile view DTO
    const profileView: AdminProfileViewDto = {
      id: user.id,
      name: user.name,
      userId: user.userId,
      email: user.email,
      type: user.type,
      isActive: user.isActive,
      isConfirmed: user.isConfirmed,
      roles: user.userRoles?.map(ur => ur.role.name) || ['admin'],
      bio: user.bio,
      profilePictureUrl: await this.profilePictureService.getProfilePictureUrl(id),
      profilePicture: user.profilePicture,
      phoneNumber: user.phoneNumber,
      address: user.address,
      city: user.city,
      state: user.state,
      country: user.country,
      postalCode: user.postalCode,
      gender: user.gender,
      dateOfBirth: user.dateOfBirth ? this.formatDateToYYYYMMDD(user.dateOfBirth) : undefined,
      age: user.dateOfBirth ? this.usersService.calculateAge(this.formatDateToYYYYMMDD(user.dateOfBirth)).age : undefined,
      socialLinks: user.socialLinks,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      userRoles: user.userRoles?.map(ur => ({
        userId: ur.userId,
        roleId: ur.roleId,
        roleName: ur.role?.name || 'Unknown'
      }))
    };

    return ApiResponse.success(profileView, 'Admin profile retrieved successfully');
  }

  @Get('tutor/:id')
  @ApiOperation({
    summary: 'View tutor profile',
    description: 'View the profile of a tutor by ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'Tutor user ID',
    type: String
  })
  @ApiOkResponseWithType(TutorProfileViewDto, 'Tutor profile retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(404, 'Tutor not found')
  async viewTutorProfile(@Param('id') id: string): Promise<ApiResponse<TutorProfileViewDto>> {
    const user = await this.usersService.findById(id);

    if (!user || user.type !== UserType.TUTOR) {
      throw new NotFoundException('Tutor not found');
    }

    // Get tutor approval status
    const isApproved = user.isConfirmed;

    // Get student count (if available)
    let studentCount: number | undefined;
    try {
      studentCount = await this.usersService.getTutorStudentCount(id);
    } catch (error) {
      // If there's an error, just leave studentCount as undefined
      console.error(`Error getting student count for tutor ${id}:`, error);
    }

    // Create the profile view DTO
    const profileView: TutorProfileViewDto = {
      id: user.id,
      name: user.name,
      userId: user.userId,
      email: user.email,
      type: user.type,
      isActive: user.isActive,
      isConfirmed: user.isConfirmed,
      roles: user.userRoles?.map(ur => ur.role.name) || ['tutor'],
      bio: user.bio,
      isApproved,
      studentCount,
      profilePictureUrl: await this.profilePictureService.getProfilePictureUrl(id),
      profilePicture: user.profilePicture,
      phoneNumber: user.phoneNumber,
      address: user.address,
      city: user.city,
      state: user.state,
      country: user.country,
      postalCode: user.postalCode,
      gender: user.gender,
      dateOfBirth: user.dateOfBirth ? this.formatDateToYYYYMMDD(user.dateOfBirth) : undefined,
      age: user.dateOfBirth ? this.usersService.calculateAge(this.formatDateToYYYYMMDD(user.dateOfBirth)).age : undefined,
      socialLinks: user.socialLinks,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      userRoles: user.userRoles?.map(ur => ({
        userId: ur.userId,
        roleId: ur.roleId,
        roleName: ur.role?.name || 'Unknown'
      })),
      assignedStudents: user['_assignedStudents'] || [],
      assignedModules: user['_assignedModules'] || [],
      education: user['_education'] || []
    };

    return ApiResponse.success(profileView, 'Tutor profile retrieved successfully');
  }

  @Get('student/:id')
  @ApiOperation({
    summary: 'View student profile',
    description: 'View the profile of a student by ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'Student user ID',
    type: String
  })
  @ApiOkResponseWithType(StudentProfileViewDto, 'Student profile retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(404, 'Student not found')
  async viewStudentProfile(@Param('id') id: string): Promise<ApiResponse<StudentProfileViewDto>> {
    const user = await this.usersService.findById(id);

    if (!user || user.type !== UserType.STUDENT) {
      throw new NotFoundException('Student not found');
    }

    // Get active plan information
    const activePlan = user.getActivePlan();

    // Create the profile view DTO
    const profileView: StudentProfileViewDto = {
      id: user.id,
      name: user.name,
      userId: user.userId,
      email: user.email,
      type: user.type,
      isActive: user.isActive,
      isConfirmed: user.isConfirmed,
      roles: user.userRoles?.map(ur => ur.role.name) || ['student'],
      bio: user.bio,
      activePlan: activePlan?.plan?.name,
      planExpiryDate: activePlan?.endDate ? this.formatDateToYYYYMMDD(activePlan.endDate) : undefined,
      activePlanDetails: activePlan?.toSimpleObject() || null,
      profilePictureUrl: await this.profilePictureService.getProfilePictureUrl(id),
      profilePicture: user.profilePicture,
      phoneNumber: user.phoneNumber,
      address: user.address,
      city: user.city,
      state: user.state,
      country: user.country,
      postalCode: user.postalCode,
      gender: user.gender,
      dateOfBirth: user.dateOfBirth ? this.formatDateToYYYYMMDD(user.dateOfBirth) : undefined,
      age: user.dateOfBirth ? this.usersService.calculateAge(this.formatDateToYYYYMMDD(user.dateOfBirth)).age : undefined,
      socialLinks: user.socialLinks,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      userRoles: user.userRoles?.map(ur => ({
        userId: ur.userId,
        roleId: ur.roleId,
        roleName: ur.role?.name || 'Unknown'
      })),
      defaultSkinId: user['_defaultSkinId'] || null,
      assignedTutors: user['_assignedTutors'] || [],
      ownedSkins: user['_ownedSkins'] || []
    };

    return ApiResponse.success(profileView, 'Student profile retrieved successfully');
  }
}
