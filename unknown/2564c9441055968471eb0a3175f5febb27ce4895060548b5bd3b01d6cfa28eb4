import { ApiProperty } from '@nestjs/swagger';

/**
 * Standard paged list response for all list endpoints
 * @template T Type of items in the list
 */
export class PagedListDto<T> {
  @ApiProperty({ description: 'List of items', isArray: true })
  items: T[];

  @ApiProperty({ description: 'Total number of items', example: 100 })
  totalCount: number;

  @ApiProperty({ description: 'Total number of items', example: 100 })
  totalItems: number;

  @ApiProperty({ description: 'Number of items per page', example: 20 })
  itemsPerPage: number;

  @ApiProperty({ description: 'Current page number', example: 2 })
  currentPage: number;

  @ApiProperty({ description: 'Total number of pages', example: 5 })
  totalPages: number;

  /**
   * Create a new paged list
   * @param items List of items
   * @param totalCount Total number of items
   * @param page Current page number (default: 1)
   * @param limit Number of items per page (default: items.length)
   */
  constructor(
    items: T[],
    totalCount: number,
    page: number = 1,
    limit: number = items.length
  ) {
    this.items = items;
    this.totalCount = totalCount;
    this.totalItems = totalCount;
    this.itemsPerPage = limit;
    this.currentPage = page;
    this.totalPages = Math.ceil(totalCount / limit) || 1;
  }
}
