import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Promotion, DiscountType, PromotionApplicableType } from '../../database/entities/promotion.entity';
import { addDaysUTC, getCurrentUTCDate } from '../../common/utils/date-utils';

@Injectable()
export class PromotionSeed {
  private readonly logger = new Logger(PromotionSeed.name);

  constructor(
    @InjectRepository(Promotion)
    private readonly promotionRepository: Repository<Promotion>,
  ) {}

  async seed(): Promise<void> {
    this.logger.log('Seeding promotions...');

    const now = getCurrentUTCDate();

    // Define initial promotions
    const initialPromotions = [
      // Promotion for all shop items
      {
        name: 'Welcome Discount',
        description: 'Get 10% off on all shop items for new users',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 10,
        applicableType: PromotionApplicableType.SHOP_ITEM,
        applicableCategoryIds: null, // Applies to all categories
        applicablePlanIds: null,
        promotionCode: 'WELCOME10',
        startDate: now,
        endDate: addDaysUTC(now, 90), // Valid for 90 days
        isActive: true,
        usageLimit: null, // Unlimited usage
        usageCount: 0,
        minimumPurchaseAmount: null,
        maximumDiscountAmount: null,
      },

      // Promotion for specific shop item categories
      {
        name: 'Graphics Special',
        description: 'Get 15% off on all graphics items',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 15,
        applicableType: PromotionApplicableType.SHOP_ITEM,
        applicableCategoryIds: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001'], // IDs for graphics and templates categories
        applicablePlanIds: null,
        promotionCode: 'GRAPHICS15',
        startDate: now,
        endDate: addDaysUTC(now, 30), // Valid for 30 days
        isActive: true,
        usageLimit: 100, // Limited to 100 uses
        usageCount: 0,
        minimumPurchaseAmount: null,
        maximumDiscountAmount: 50, // Maximum discount of $50
      },

      // Promotion for subscription plans
      {
        name: 'Premium Plan Discount',
        description: 'Get $10 off on Premium subscription plans',
        discountType: DiscountType.FIXED_AMOUNT,
        discountValue: 10,
        applicableType: PromotionApplicableType.PLAN,
        applicableCategoryIds: null,
        applicablePlanIds: ['123e4567-e89b-12d3-a456-426614174002', '123e4567-e89b-12d3-a456-426614174003'], // IDs for pro and ultimate plans
        promotionCode: 'PREMIUM10',
        startDate: now,
        endDate: addDaysUTC(now, 60), // Valid for 60 days
        isActive: true,
        usageLimit: null, // Unlimited usage
        usageCount: 0,
        minimumPurchaseAmount: null,
        maximumDiscountAmount: null,
      },

      // Promotion for both plans and shop items
      {
        name: 'Summer Sale',
        description: 'Get 20% off on everything during summer',
        discountType: DiscountType.PERCENTAGE,
        discountValue: 20,
        applicableType: PromotionApplicableType.ALL,
        applicableCategoryIds: null, // Applies to all categories
        applicablePlanIds: null, // Applies to all plans
        promotionCode: 'SUMMER20',
        startDate: now,
        endDate: addDaysUTC(now, 45), // Valid for 45 days
        isActive: true,
        usageLimit: null, // Unlimited usage
        usageCount: 0,
        minimumPurchaseAmount: 20, // Minimum purchase of $20
        maximumDiscountAmount: 100, // Maximum discount of $100
      },
    ];

    // Seed each promotion
    for (const promotionData of initialPromotions) {
      // Check if promotion already exists
      const existingPromotion = await this.promotionRepository.findOne({
        where: { promotionCode: promotionData.promotionCode }
      });

      if (!existingPromotion) {
        // Create new promotion
        const promotion = this.promotionRepository.create({
          ...promotionData,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        await this.promotionRepository.save(promotion);
        this.logger.log(`Created promotion: ${promotion.name} (${promotion.promotionCode})`);
      } else {
        this.logger.log(`Promotion already exists: ${existingPromotion.name} (${existingPromotion.promotionCode})`);
      }
    }

    this.logger.log('Promotion seeding completed');
  }
}
