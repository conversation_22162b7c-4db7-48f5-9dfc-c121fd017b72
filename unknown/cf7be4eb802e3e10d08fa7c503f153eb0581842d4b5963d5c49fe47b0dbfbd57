# Common Services

The Common Services module provides shared functionality used across the application, including logging, email, file upload, and security services.

## Epics

1. **Logging and Monitoring**
2. **Email Services**
3. **File Management**
4. **Security and Authorization**

## Services

### 1. Logger Service

**Description:** Provides centralized logging functionality for the application.

**Main Features:**
- Log different levels (debug, info, warn, error)
- Format log messages with timestamp, level, and context
- Support for structured logging
- Console and file logging

**Usage Example:**
```typescript
@Injectable()
export class SomeService {
  constructor(private readonly logger: LoggerService) {
    this.logger.setContext(SomeService.name);
  }

  doSomething() {
    this.logger.log('Doing something');
    try {
      // Some operation
    } catch (error) {
      this.logger.error(`Error doing something: ${error.message}`, error.stack);
    }
  }
}
```

**Algorithm:**
1. Create logger instance with context
2. Format log messages with timestamp, level, and context
3. Write logs to console and/or file
4. Handle different log levels

### 2. Email Service

**Description:** Handles sending emails using configured email provider.

**Main Features:**
- Send transactional emails
- Support for HTML templates
- Email verification
- Password reset emails
- Tutor approval/rejection emails

**Usage Example:**
```typescript
@Injectable()
export class SomeService {
  constructor(private readonly emailService: EmailService) {}

  async sendWelcomeEmail(user: User) {
    const emailSent = await this.emailService.sendWelcomeEmail(
      user.email,
      user.name
    );
    return emailSent;
  }
}
```

**Email Types:**
1. **Welcome Email**: Sent when a user registers
2. **Verification Email**: Sent to verify user's email
3. **Password Reset Email**: Sent when user requests password reset
4. **Forgot User ID Email**: Sent when user forgets their user ID
5. **Tutor Approval Email**: Sent when a tutor is approved
6. **Tutor Rejection Email**: Sent when a tutor is rejected

**Algorithm:**
1. Create email content using templates
2. Configure email options (from, to, subject, content)
3. Send email using configured provider
4. Return success/failure status

### 3. File Upload Service

**Description:** Handles file uploads and storage.

**Main Features:**
- Upload profile pictures
- Upload diary skin images
- Validate file types and sizes
- Generate unique filenames
- Serve static files

**Usage Example:**
```typescript
@Injectable()
export class SomeService {
  constructor(private readonly fileUploadService: FileUploadService) {}

  async uploadProfilePicture(userId: string, file: Express.Multer.File) {
    const filePath = await this.fileUploadService.uploadProfilePicture(
      userId,
      file
    );
    return filePath;
  }
}
```

**File Types:**
1. **Profile Pictures**: User profile images
2. **Diary Skin Images**: Preview images for diary skins

**Algorithm:**
1. Validate file (type, size)
2. Generate unique filename
3. Save file to appropriate directory
4. Return file path

### 4. Audit Log Service

**Description:** Records user actions for auditing purposes.

**Main Features:**
- Log user actions
- Record timestamps
- Store user information
- Support for filtering and searching

**Usage Example:**
```typescript
@Injectable()
export class SomeService {
  constructor(private readonly auditLogService: AuditLogService) {}

  async performAction(userId: string, action: string) {
    // Perform the action
    await this.auditLogService.log(userId, action, 'resource', 'resourceId');
    return result;
  }
}
```

**Log Fields:**
1. **User ID**: ID of the user performing the action
2. **Action**: Type of action performed
3. **Resource**: Resource being acted upon
4. **Resource ID**: ID of the resource
5. **Timestamp**: When the action occurred
6. **IP Address**: User's IP address
7. **User Agent**: User's browser/device information

**Algorithm:**
1. Extract user information from request
2. Record action details
3. Save log entry to database

### 5. JWT Auth Service

**Description:** Handles JWT token generation and validation.

**Main Features:**
- Generate JWT tokens
- Validate tokens
- Extract user information from tokens
- Role-based access control

**Usage Example:**
```typescript
@Injectable()
export class SomeService {
  constructor(private readonly jwtService: JwtService) {}

  generateToken(user: User) {
    const payload = {
      id: user.id,
      username: user.userId,
      sub: user.id,
      name: user.name,
      type: user.type,
      roles: user.roles
    };
    return this.jwtService.sign(payload);
  }
}
```

**Token Payload:**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "username": "john123",
  "sub": "123e4567-e89b-12d3-a456-426614174000",
  "name": "John Doe",
  "type": "student",
  "selectedRole": "student",
  "roles": ["student"],
  "activePlan": "Pro",
  "planType": "monthly",
  "planId": "123e4567-e89b-12d3-a456-426614174000",
  "planExpiryDate": "2023-08-24T00:00:00.000Z",
  "planActive": true,
  "iat": 1627142400,
  "exp": 1627146000
}
```

**Algorithm:**
1. Create payload with user information
2. Sign payload with secret key
3. Set token expiry
4. Return signed token

### 6. Guards and Interceptors

**Description:** Provides security and request/response transformation.

**Main Guards:**
1. **JwtAuthGuard**: Validates JWT token
2. **RolesGuard**: Checks user roles
3. **AdminGuard**: Restricts access to admins
4. **TutorGuard**: Restricts access to tutors
5. **StudentGuard**: Restricts access to students

**Main Interceptors:**
1. **ResponseTransformerInterceptor**: Transforms responses to standard format
2. **AuditLogInterceptor**: Logs user actions
3. **OperationLogInterceptor**: Logs API operations

**Usage Example:**
```typescript
@Controller('some-resource')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserType.ADMIN)
export class SomeController {
  @Get()
  @UseInterceptors(ResponseTransformerInterceptor)
  getSomeResource() {
    // Implementation
  }
}
```

**Algorithm (Guards):**
1. Extract token from request
2. Validate token
3. Check user roles/permissions
4. Allow or deny request

**Algorithm (Interceptors):**
1. Intercept request/response
2. Perform pre-processing (logging, validation)
3. Execute handler
4. Perform post-processing (transformation, logging)
5. Return response

## Features

1. **Logging and Monitoring**
   - Centralized logging
   - Audit logging
   - Operation logging
   - Error handling

2. **Email Services**
   - Transactional emails
   - Email templates
   - Email verification
   - Password reset

3. **File Management**
   - File uploads
   - File validation
   - File storage
   - File serving

4. **Security and Authorization**
   - JWT authentication
   - Role-based access control
   - Request validation
   - Response transformation

## Tasks

1. **Implement Logging Services**
   - Create logger service
   - Implement audit logging
   - Add operation logging
   - Configure log levels and formats

2. **Implement Email Services**
   - Create email service
   - Implement email templates
   - Add email sending functionality
   - Configure email provider

3. **Implement File Management**
   - Create file upload service
   - Implement file validation
   - Add file storage functionality
   - Configure file serving

4. **Implement Security and Authorization**
   - Create JWT auth service
   - Implement guards
   - Add interceptors
   - Configure security settings
