import { Controller, Post, Body, UseGuards, Param, Get } from '@nestjs/common';
import { Api<PERSON><PERSON><PERSON>, ApiBearerAuth, ApiOperation, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserType } from '../../database/entities/user.entity';
import { NotificationService } from './notification.service';
import { CreateNotificationDto } from '../../database/models/notification.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { DeliveryStatus } from '../../database/entities/notification-delivery.entity';

@ApiTags('admin-notifications')
@Controller('admin/notifications')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class AdminNotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Post()
  @Roles(UserType.ADMIN)
  @ApiOperation({ summary: 'Create a notification (admin only)' })
  @ApiOkResponseWithType(Object, 'Notification created successfully')
  @ApiErrorResponse(404, 'User not found')
  @ApiErrorResponse(500, 'Internal server error')
  async createNotification(
    @Body() createNotificationDto: CreateNotificationDto
  ): Promise<ApiResponse<any>> {
    const notification = await this.notificationService.notify(createNotificationDto);
    return ApiResponse.success(notification, 'Notification created successfully');
  }

  @Post('retry-failed')
  @Roles(UserType.ADMIN)
  @ApiOperation({ summary: 'Retry all failed notification deliveries (admin only)' })
  @ApiOkResponseWithType(Number, 'Failed deliveries retried successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async retryFailedDeliveries(): Promise<ApiResponse<number>> {
    const count = await this.notificationService.retryFailedDeliveries();
    return ApiResponse.success(count, `${count} failed deliveries retried successfully`);
  }

  @Post('retry-delivery/:id')
  @Roles(UserType.ADMIN)
  @ApiOperation({ summary: 'Retry a specific failed notification delivery (admin only)' })
  @ApiParam({ name: 'id', description: 'Delivery ID to retry' })
  @ApiOkResponseWithType(Object, 'Delivery retried successfully')
  @ApiErrorResponse(404, 'Delivery not found')
  @ApiErrorResponse(400, 'Delivery is not in a failed state')
  @ApiErrorResponse(500, 'Internal server error')
  async retryFailedDelivery(@Param('id') id: string): Promise<ApiResponse<any>> {
    const delivery = await this.notificationService.retryFailedDelivery(id);
    return ApiResponse.success(delivery, `Delivery ${id} retried successfully`);
  }

  @Get('failed-deliveries')
  @Roles(UserType.ADMIN)
  @ApiOperation({ summary: 'Get statistics about failed deliveries (admin only)' })
  @ApiOkResponseWithType(Object, 'Failed delivery statistics retrieved successfully')
  @ApiErrorResponse(500, 'Internal server error')
  async getFailedDeliveryStats(): Promise<ApiResponse<any>> {
    const stats = {
      failedCount: await this.notificationService.countDeliveriesByStatus(DeliveryStatus.FAILED),
      permanentlyFailedCount: await this.notificationService.countDeliveriesByStatus(DeliveryStatus.FAILED_PERMANENT),
      retryScheduledCount: await this.notificationService.countDeliveriesByStatus(DeliveryStatus.RETRY_SCHEDULED),
      totalFailedCount: await this.notificationService.countAllFailedDeliveries()
    };

    return ApiResponse.success(stats, 'Failed delivery statistics retrieved successfully');
  }
}
