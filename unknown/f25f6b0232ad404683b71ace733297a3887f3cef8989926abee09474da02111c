import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { NotificationService } from './notification.service';
import { NotificationChannelService } from './notification-channel.service';
import { NotificationChannelDecisionService } from './notification-channel-decision.service';
import { NotificationHelperService } from './notification-helper.service';
import { NotificationRetryService } from './notification-retry.service';
import { NotificationController } from './notification.controller';
import { AdminNotificationController } from './admin-notification.controller';
import { NotificationPreferenceController } from './notification-preference.controller';
import { NotificationScheduler } from './notification.scheduler';
import { NotificationRetryScheduler } from './notification-retry.scheduler';
import { NotificationGateway } from './notification.gateway';
import { NotificationEmailScheduler } from './notification-email.scheduler';
import { Notification } from '../../database/entities/notification.entity';
import { NotificationDelivery } from '../../database/entities/notification-delivery.entity';
import { UserNotificationPreference } from '../../database/entities/user-notification-preference.entity';
import { User } from '../../database/entities/user.entity';
import { EmailModule } from '../email/email.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DeeplinkModule } from '../../common/utils/deeplink.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Notification,
      NotificationDelivery,
      UserNotificationPreference,
      User
    ]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '1d' }
      })
    }),
    EmailModule,
    DeeplinkModule
  ],
  controllers: [
    NotificationController,
    AdminNotificationController,
    NotificationPreferenceController
  ],
  providers: [
    NotificationService,
    NotificationChannelService,
    NotificationChannelDecisionService,
    NotificationHelperService,
    NotificationRetryService,
    NotificationScheduler,
    NotificationRetryScheduler,
    NotificationGateway,
    NotificationEmailScheduler
  ],
  exports: [NotificationService, NotificationHelperService]
})
export class NotificationModule {}
