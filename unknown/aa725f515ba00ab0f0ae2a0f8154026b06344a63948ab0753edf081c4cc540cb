import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsOptional, IsBoolean, IsString } from 'class-validator';
import { PaginationDto } from '../../common/models/pagination.dto';

/**
 * DTO for creating a new tutor permission
 * This allows admins to grant feature management permissions to tutors
 */
export class CreateTutorPermissionDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'UUID of the tutor to grant permissions to'
  })
  @IsNotEmpty()
  @IsUUID()
  tutorId: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'UUID of the plan feature to grant permissions for'
  })
  @IsNotEmpty()
  @IsUUID()
  planFeatureId: string;

  @ApiProperty({
    example: 'Assigned to handle English grammar Q&A',
    description: 'Notes about the permission assignment',
    required: false
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

/**
 * DTO for updating a tutor permission
 */
export class UpdateTutorPermissionDto {
  @ApiProperty({
    example: true,
    description: 'Whether the permission is active'
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    example: 'Updated to handle all Q&A categories',
    description: 'Notes about the permission assignment',
    required: false
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

/**
 * DTO for tutor permission response
 */
export class TutorPermissionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  tutorId: string;

  @ApiProperty()
  tutorName: string;

  @ApiProperty()
  tutorEmail: string;

  @ApiProperty()
  planFeatureId: string;

  @ApiProperty()
  planFeatureName: string;

  @ApiProperty()
  grantedBy: string;

  @ApiProperty()
  grantedByName: string;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty({ required: false })
  notes?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

/**
 * DTO for tutor permission pagination
 */
export class TutorPermissionPaginationDto extends PaginationDto {
  @ApiProperty({
    description: 'Filter by active status',
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Filter by plan feature ID',
    required: false
  })
  @IsOptional()
  @IsUUID()
  planFeatureId?: string;

  @ApiProperty({
    description: 'Search term for tutor name or email',
    required: false
  })
  @IsOptional()
  @IsString()
  searchTerm?: string;
}
