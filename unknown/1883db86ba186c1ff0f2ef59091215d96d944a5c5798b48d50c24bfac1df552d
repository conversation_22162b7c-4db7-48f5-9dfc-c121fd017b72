import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { TutorApproval, TutorApprovalStatus } from '../../database/entities/tutor-approval.entity';
import { User, UserType } from '../../database/entities/user.entity';
import { TutorApprovalResponseDto, ApproveTutorDto, RejectTutorDto } from '../../database/models/tutor-approval.dto';
import { EmailService } from '../email/email.service';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

@Injectable()
export class TutorApprovalService {
    private readonly logger = new Logger(TutorApprovalService.name);

    constructor(
        @InjectRepository(TutorApproval)
        private readonly tutorApprovalRepository: Repository<TutorApproval>,
        @InjectRepository(User)
        private readonly userRepository: Repository<User>,
        private readonly emailService: EmailService
    ) {}

    async findAll(status?: TutorApprovalStatus, paginationDto?: PaginationDto): Promise<PagedListDto<TutorApprovalResponseDto>> {
        this.logger.log(`Fetching tutor approval requests${status ? ` with status ${status}` : ''}`);

        try {
            // Build where clause based on status filter
            const whereClause = status ? { status } : {};

            // Get total count for pagination
            const totalCount = await this.tutorApprovalRepository.count({
                where: whereClause
            });

            // Apply pagination if provided
            let options: any = {
                where: whereClause,
                order: { createdAt: 'DESC' }
            };

            if (paginationDto) {
                const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
                const skip = (page - 1) * limit;

                options.skip = skip;
                options.take = limit;

                if (sortBy && sortDirection) {
                    options.order = { [sortBy]: sortDirection };
                }
            }

            // Get all approvals
            const approvals = await this.tutorApprovalRepository.find(options);

            this.logger.log(`Found ${approvals.length} tutor approval requests`);

            if (approvals.length === 0) {
                return new PagedListDto([], totalCount);
            }

            // Get all user IDs from approvals
            const userIds = approvals.map(approval => approval.userId);

            // Fetch all users in a single query
            const users = await this.userRepository.find({
                where: { id: In(userIds) }
            });

            this.logger.log(`Found ${users.length} users associated with approval requests`);

            // Create a map of user IDs to users for quick lookup
            const userMap = new Map<string, User>();
            users.forEach(user => userMap.set(user.id, user));

            // Map approvals to response DTOs
            const result = approvals.map(approval => {
                const user = userMap.get(approval.userId);

                return {
                    id: approval.id,
                    userId: approval.userId,
                    status: approval.status,
                    adminId: approval.adminId,
                    adminNotes: approval.adminNotes,
                    rejectionReason: approval.rejectionReason,
                    approvedAt: approval.approvedAt,
                    rejectedAt: approval.rejectedAt,
                    createdAt: approval.createdAt,
                    updatedAt: approval.updatedAt,
                    user: user ? {
                        id: user.id,
                        name: user.name,
                        email: user.email,
                        userId: user.userId,
                        phoneNumber: user.phoneNumber,
                        bio: user.bio
                    } : null
                };
            });

            return new PagedListDto(result, totalCount);
        } catch (error) {
            this.logger.error(`Error fetching all tutor approval requests: ${error.message}`, error.stack);
            throw error;
        }
    }

    async findPending(paginationDto?: PaginationDto): Promise<PagedListDto<TutorApprovalResponseDto>> {
        this.logger.log('Fetching pending tutor approval requests');

        try {
            // Get total count for pagination
            const totalCount = await this.tutorApprovalRepository.count({
                where: { status: TutorApprovalStatus.PENDING }
            });

            // Apply pagination if provided
            let options: any = {
                where: { status: TutorApprovalStatus.PENDING },
                order: { createdAt: 'ASC' }
            };

            if (paginationDto) {
                const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
                const skip = (page - 1) * limit;

                options.skip = skip;
                options.take = limit;

                if (sortBy && sortDirection) {
                    options.order = { [sortBy]: sortDirection };
                }
            }

            // Get pending approvals
            const approvals = await this.tutorApprovalRepository.find(options);

            this.logger.log(`Found ${approvals.length} pending tutor approval requests`);

            if (approvals.length === 0) {
                return new PagedListDto([], totalCount);
            }

            // Get all user IDs from approvals
            const userIds = approvals.map(approval => approval.userId);

            // Fetch all users in a single query
            const users = await this.userRepository.find({
                where: { id: In(userIds) }
            });

            this.logger.log(`Found ${users.length} users associated with pending approval requests`);

            // Create a map of user IDs to users for quick lookup
            const userMap = new Map<string, User>();
            users.forEach(user => userMap.set(user.id, user));

            // Map approvals to response DTOs
            const result = approvals.map(approval => {
                const user = userMap.get(approval.userId);

                return {
                    id: approval.id,
                    userId: approval.userId,
                    status: approval.status,
                    adminId: approval.adminId,
                    adminNotes: approval.adminNotes,
                    rejectionReason: approval.rejectionReason,
                    approvedAt: approval.approvedAt,
                    rejectedAt: approval.rejectedAt,
                    createdAt: approval.createdAt,
                    updatedAt: approval.updatedAt,
                    user: user ? {
                        id: user.id,
                        name: user.name,
                        email: user.email,
                        userId: user.userId,
                        phoneNumber: user.phoneNumber,
                        bio: user.bio
                    } : null
                };
            });

            return new PagedListDto(result, totalCount);
        } catch (error) {
            this.logger.error(`Error fetching pending tutor approval requests: ${error.message}`, error.stack);
            throw error;
        }
    }

    async findById(id: string): Promise<TutorApprovalResponseDto> {
        this.logger.log(`Fetching tutor approval request with ID ${id}`);

        try {
            const approval = await this.tutorApprovalRepository.findOne({ where: { id: id } });

            if (!approval) {
                this.logger.warn(`Tutor approval request with ID ${id} not found`);
                throw new NotFoundException(`Tutor approval request with ID ${id} not found`);
            }

            this.logger.log(`Found approval request with status: ${approval.status}`);

            const user = await this.userRepository.findOne({ where: { id: approval.userId } });

            if (user) {
                this.logger.log(`Found associated user: ${user.name}`);
            } else {
                this.logger.warn(`Associated user with ID ${approval.userId} not found`);
            }

            return {
                id: approval.id,
                userId: approval.userId,
                status: approval.status,
                adminId: approval.adminId,
                adminNotes: approval.adminNotes,
                rejectionReason: approval.rejectionReason,
                approvedAt: approval.approvedAt,
                rejectedAt: approval.rejectedAt,
                createdAt: approval.createdAt,
                updatedAt: approval.updatedAt,
                user: user ? {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    userId: user.userId,
                    phoneNumber: user.phoneNumber,
                    bio: user.bio
                } : null
            };
        } catch (error) {
            if (error instanceof NotFoundException) {
                throw error;
            }
            this.logger.error(`Error fetching tutor approval request: ${error.message}`, error.stack);
            throw error;
        }
    }

    async approveTutor(adminId: string, dto: ApproveTutorDto): Promise<TutorApprovalResponseDto> {
        this.logger.log(`Admin ${adminId} is attempting to approve tutor with approval ID ${dto.approvalId}`);

        try {
            // Find the approval request
            const approval = await this.tutorApprovalRepository.findOne({ where: { id: dto.approvalId } });

            if (!approval) {
                this.logger.warn(`Tutor approval request with ID ${dto.approvalId} not found`);
                throw new NotFoundException(`Tutor approval request with ID ${dto.approvalId} not found`);
            }

            this.logger.log(`Found approval request with status: ${approval.status}`);

            if (approval.status !== TutorApprovalStatus.PENDING) {
                this.logger.warn(`Tutor approval request with ID ${dto.approvalId} has already been ${approval.status}`);
                throw new BadRequestException(`This tutor approval request has already been ${approval.status}`);
            }

            // Find the user
            const user = await this.userRepository.findOne({ where: { id: approval.userId } });

            if (!user) {
                this.logger.warn(`User with ID ${approval.userId} not found`);
                throw new NotFoundException(`User with ID ${approval.userId} not found`);
            }

            this.logger.log(`Found user ${user.name} with type: ${user.type}`);

            if (user.type !== UserType.TUTOR) {
                this.logger.warn(`User with ID ${approval.userId} is not a tutor`);
                throw new BadRequestException(`User with ID ${approval.userId} is not a tutor`);
            }

            // Use a transaction to ensure both operations succeed or fail together
            const queryRunner = this.tutorApprovalRepository.manager.connection.createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();

            try {
                // Update approval status
                approval.status = TutorApprovalStatus.APPROVED;
                approval.adminId = adminId;
                approval.adminNotes = dto.adminNotes;
                approval.approvedAt = new Date();

                await queryRunner.manager.save(approval);
                this.logger.log(`Updated approval status to APPROVED`);

                // Update user status
                user.isActive = true;
                await queryRunner.manager.save(user);
                this.logger.log(`Updated user status to active`);

                // Commit the transaction
                await queryRunner.commitTransaction();
                this.logger.log(`Transaction committed successfully`);
            } catch (error) {
                // Rollback the transaction in case of error, but only if it's active
                try {
                    if (queryRunner.isTransactionActive) {
                        await queryRunner.rollbackTransaction();
                    }
                } catch (rollbackError) {
                    this.logger.error(`Error during transaction rollback: ${rollbackError.message}`, rollbackError.stack);
                }
                this.logger.error(`Transaction failed: ${error.message}`, error.stack);
                throw error;
            } finally {
                // Release the query runner
                await queryRunner.release();
            }

            // Send approval email (outside transaction as it's not critical)
            try {
                const emailSent = await this.emailService.sendTutorApprovalEmail(user.email, user.name);
                if (emailSent) {
                    this.logger.log(`Approval email sent to ${user.email}`);
                } else {
                    this.logger.warn(`Failed to send approval email to ${user.email}`);
                }
            } catch (error) {
                this.logger.error(`Error sending approval email to ${user.email}: ${error.message}`, error.stack);
                // Continue even if email fails
            }

            this.logger.log(`Tutor ${user.name} (${user.id}) has been approved successfully`);

            return {
                id: approval.id,
                userId: approval.userId,
                status: approval.status,
                adminId: approval.adminId,
                adminNotes: approval.adminNotes,
                rejectionReason: approval.rejectionReason,
                approvedAt: approval.approvedAt,
                rejectedAt: approval.rejectedAt,
                createdAt: approval.createdAt,
                updatedAt: approval.updatedAt,
                user: {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    userId: user.userId,
                    phoneNumber: user.phoneNumber,
                    bio: user.bio
                }
            };
        } catch (error) {
            this.logger.error(`Error approving tutor: ${error.message}`, error.stack);
            throw error;
        }
    }

    async rejectTutor(adminId: string, dto: RejectTutorDto): Promise<TutorApprovalResponseDto> {
        this.logger.log(`Admin ${adminId} is attempting to reject tutor with approval ID ${dto.approvalId}`);

        try {
            // Find the approval request
            const approval = await this.tutorApprovalRepository.findOne({ where: { id: dto.approvalId } });

            if (!approval) {
                this.logger.warn(`Tutor approval request with ID ${dto.approvalId} not found`);
                throw new NotFoundException(`Tutor approval request with ID ${dto.approvalId} not found`);
            }

            this.logger.log(`Found approval request with status: ${approval.status}`);

            if (approval.status !== TutorApprovalStatus.PENDING) {
                this.logger.warn(`Tutor approval request with ID ${dto.approvalId} has already been ${approval.status}`);
                throw new BadRequestException(`This tutor approval request has already been ${approval.status}`);
            }

            // Find the user
            const user = await this.userRepository.findOne({ where: { id: approval.userId } });

            if (!user) {
                this.logger.warn(`User with ID ${approval.userId} not found`);
                throw new NotFoundException(`User with ID ${approval.userId} not found`);
            }

            this.logger.log(`Found user ${user.name} with type: ${user.type}`);

            // Use a transaction to ensure both operations succeed or fail together
            const queryRunner = this.tutorApprovalRepository.manager.connection.createQueryRunner();
            await queryRunner.connect();
            await queryRunner.startTransaction();

            try {
                // Update approval status
                approval.status = TutorApprovalStatus.REJECTED;
                approval.adminId = adminId;
                approval.adminNotes = dto.adminNotes;
                approval.rejectionReason = dto.rejectionReason;
                approval.rejectedAt = new Date();

                await queryRunner.manager.save(approval);
                this.logger.log(`Updated approval status to REJECTED`);

                // Update user status - set to inactive when rejected
                user.isActive = false; // Set to inactive when rejected (isActive is a boolean field)
                await queryRunner.manager.save(user);
                this.logger.log(`Updated user status to inactive`);

                // Commit the transaction
                await queryRunner.commitTransaction();
                this.logger.log(`Transaction committed successfully`);
            } catch (error) {
                // Rollback the transaction in case of error, but only if it's active
                try {
                    if (queryRunner.isTransactionActive) {
                        await queryRunner.rollbackTransaction();
                    }
                } catch (rollbackError) {
                    this.logger.error(`Error during transaction rollback: ${rollbackError.message}`, rollbackError.stack);
                }
                this.logger.error(`Transaction failed: ${error.message}`, error.stack);
                throw error;
            } finally {
                // Release the query runner
                await queryRunner.release();
            }

            // Send rejection email (outside transaction as it's not critical)
            try {
                const emailSent = await this.emailService.sendTutorRejectionEmail(user.email, user.name, dto.rejectionReason);
                if (emailSent) {
                    this.logger.log(`Rejection email sent to ${user.email}`);
                } else {
                    this.logger.warn(`Failed to send rejection email to ${user.email}`);
                }
            } catch (error) {
                this.logger.error(`Error sending rejection email to ${user.email}: ${error.message}`, error.stack);
                // Continue even if email fails
            }

            this.logger.log(`Tutor ${user.name} (${user.id}) has been rejected successfully`);

            return {
                id: approval.id,
                userId: approval.userId,
                status: approval.status,
                adminId: approval.adminId,
                adminNotes: approval.adminNotes,
                rejectionReason: approval.rejectionReason,
                approvedAt: approval.approvedAt,
                rejectedAt: approval.rejectedAt,
                createdAt: approval.createdAt,
                updatedAt: approval.updatedAt,
                user: {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    userId: user.userId,
                    phoneNumber: user.phoneNumber,
                    bio: user.bio
                }
            };
        } catch (error) {
            this.logger.error(`Error rejecting tutor: ${error.message}`, error.stack);
            throw error;
        }
    }
}
