# HEC Documentation Structure

This document outlines the new organization of the HEC documentation into five main categories.

## 1. System Documentation

Core information about the system architecture, database design, and key concepts.

- **Project Overview** - High-level description of the HEC platform
- **System Architecture** - Technical design and components
- **Database Schema** - Database tables, relationships, and constraints
- **Authentication Flow** - User authentication and authorization process
- **Automatic Tutor Assignment** - How tutors are assigned to students

## 2. API Reference

Comprehensive documentation of all API endpoints organized by module.

- **Authentication Module** - Login, registration, token management
- **Users Module** - User management and profiles
- **Plans Module** - Subscription plans and features
- **Diary Module** - Student diary entries and tutor feedback
- **Shop Module** - Shop items, cart, checkout, and reward points
- **Tutor Module** - Tutor approval and assignment
- **Chat Module** - Real-time messaging
- **Notification Module** - System notifications
- **Essay Module** - Essay submissions and feedback

## 3. Integration Guides

Practical guides for frontend developers to integrate with the backend.

- **Authentication Integration** - How to implement login, registration, and token management
- **Diary Module Integration** - How to implement diary creation, submission, and review
- **Shop Integration** - How to implement shop browsing, cart, and checkout
- **Chat Integration** - How to implement real-time messaging
- **Notification Integration** - How to implement system notifications
- **Student Friendship Integration** - How to implement student connections

## 4. Development Resources

Resources for developers working on the backend codebase.

- **Development Conventions** - Coding standards and practices
- **Testing Conventions** - Unit, integration, and E2E testing
- **File Upload Conventions** - How file uploads are handled
- **API Versioning Conventions** - How API versioning is managed
- **Feature Flag Conventions** - How feature flags are implemented
- **Deployment Guide** - How to deploy the application

## 5. API Testing Flow

Comprehensive guides for testing API endpoints with examples and procedures.

- **API Testing Overview** - Introduction to API testing approach
- **Authentication API Testing** - Testing authentication endpoints
- **Users API Testing** - Testing user management endpoints
- **Plans API Testing** - Testing subscription plan endpoints
- **Diary API Testing** - Testing diary module endpoints
- **Shop API Testing** - Testing shop module endpoints
- **Notification API Testing** - Testing notification system endpoints
- **Student Friendship API Testing** - Testing student social features
- **Tutor Assignment API Testing** - Testing tutor assignment functionality
