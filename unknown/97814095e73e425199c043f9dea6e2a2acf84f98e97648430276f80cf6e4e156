# Student Friendship API Testing Flow

This document outlines the testing flow for the Student Friendship API endpoints.

## Prerequisites

Before testing the Student Friendship API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for multiple student accounts
3. Set up your API testing tool (<PERSON><PERSON> recommended)

## Friendship Request Testing Flow

### Test Case 1: Send Friendship Request

1. Authenticate with a student token
2. Send a POST request to `/api/student-friendship/request` with target student ID
3. Verify HTTP status code is 200 OK
4. Verify friendship request is created in the database with status "PENDING"
5. Verify appropriate notification is sent to the target student

### Test Case 2: Get Sent Friendship Requests

1. Authenticate with a student token
2. Send multiple friendship requests to different students
3. Send a GET request to `/api/student-friendship/sent-requests` with pagination parameters
4. Verify HTTP status code is 200 OK
5. Verify response contains paginated list of sent requests
6. Verify each request contains the correct status and recipient information

### Test Case 3: Get Received Friendship Requests

1. Authenticate with a student token
2. Have other students send friendship requests to this student
3. Send a GET request to `/api/student-friendship/received-requests` with pagination parameters
4. Verify HTTP status code is 200 OK
5. Verify response contains paginated list of received requests
6. Verify each request contains the correct status and sender information

### Test Case 4: Cancel Friendship Request

1. Authenticate with a student token
2. Send a friendship request to another student
3. Send a DELETE request to `/api/student-friendship/request/{requestId}`
4. Verify HTTP status code is 200 OK
5. Verify request is removed from the database
6. Verify appropriate notification is sent to the target student

## Friendship Response Testing Flow

### Test Case 1: Accept Friendship Request

1. Authenticate with a student token
2. Have another student send a friendship request to this student
3. Send a POST request to `/api/student-friendship/accept/{requestId}`
4. Verify HTTP status code is 200 OK
5. Verify request status is updated to "ACCEPTED" in the database
6. Verify friendship is established between both students
7. Verify appropriate notification is sent to the requesting student

### Test Case 2: Reject Friendship Request

1. Authenticate with a student token
2. Have another student send a friendship request to this student
3. Send a POST request to `/api/student-friendship/reject/{requestId}`
4. Verify HTTP status code is 200 OK
5. Verify request status is updated to "REJECTED" in the database
6. Verify no friendship is established
7. Verify appropriate notification is sent to the requesting student

## Friend Management Testing Flow

### Test Case 1: Get Friends List

1. Authenticate with a student token
2. Establish friendships with multiple students
3. Send a GET request to `/api/student-friendship/friends` with pagination parameters
4. Verify HTTP status code is 200 OK
5. Verify response contains paginated list of friends
6. Verify each friend entry contains the correct user information

### Test Case 2: Search Friends

1. Authenticate with a student token
2. Establish friendships with multiple students
3. Send a GET request to `/api/student-friendship/friends?search=keyword` with search parameters
4. Verify HTTP status code is 200 OK
5. Verify response contains friends matching the search criteria
6. Test with different search terms (name, userId, etc.)

### Test Case 3: Remove Friend

1. Authenticate with a student token
2. Establish a friendship with another student
3. Send a DELETE request to `/api/student-friendship/friend/{friendId}`
4. Verify HTTP status code is 200 OK
5. Verify friendship is removed from the database
6. Verify the student no longer appears in the friends list
7. Verify appropriate notification is sent to the removed friend

## Friend Diary Access Testing Flow

### Test Case 1: Get Friend's Diary Entries

1. Authenticate with a student token
2. Establish a friendship with another student
3. Have the friend create public diary entries
4. Send a GET request to `/api/student-friendship/friend/{friendId}/diary-entries` with pagination parameters
5. Verify HTTP status code is 200 OK
6. Verify response contains paginated list of the friend's public diary entries
7. Verify private entries are not included in the response

### Test Case 2: Get Friend's Diary Entry by ID

1. Authenticate with a student token
2. Establish a friendship with another student
3. Have the friend create a public diary entry
4. Send a GET request to `/api/student-friendship/friend/{friendId}/diary-entries/{entryId}`
5. Verify HTTP status code is 200 OK
6. Verify response contains the complete entry details
7. Test with a private entry and verify 403 Forbidden response

## Friend Suggestion Testing Flow

### Test Case 1: Get Friend Suggestions

1. Authenticate with a student token
2. Send a GET request to `/api/student-friendship/suggestions` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of suggested students
5. Verify suggestions are based on relevant criteria (same tutor, similar interests, etc.)
6. Verify existing friends are not included in suggestions

### Test Case 2: Filter Friend Suggestions

1. Authenticate with a student token
2. Send a GET request to `/api/student-friendship/suggestions` with filter parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains filtered suggestions
5. Test with different filter criteria (age range, interests, etc.)

## Friendship Statistics Testing Flow

### Test Case 1: Get Friendship Statistics

1. Authenticate with a student token
2. Establish friendships with multiple students
3. Send a GET request to `/api/student-friendship/statistics`
4. Verify HTTP status code is 200 OK
5. Verify response contains friendship statistics (total friends, pending requests, etc.)
6. Verify statistics match the actual data in the database

## Edge Cases and Security Testing

### Test Case 1: Role-Based Access Control

1. Authenticate with a tutor token
2. Attempt to access student friendship endpoints
3. Verify 403 Forbidden responses
4. Authenticate with an admin token
5. Attempt to access student friendship endpoints
6. Verify 403 Forbidden responses

### Test Case 2: Duplicate Friendship Requests

1. Authenticate with a student token
2. Send a friendship request to another student
3. Attempt to send another request to the same student
4. Verify appropriate error response
5. Have the other student send a request back
6. Verify appropriate error response or automatic acceptance

### Test Case 3: Self-Friendship Prevention

1. Authenticate with a student token
2. Attempt to send a friendship request to self
3. Verify appropriate error response

### Test Case 4: Blocked User Handling

1. Authenticate with a student token
2. Block another student
3. Attempt to send a friendship request to the blocked student
4. Verify appropriate error response
5. Have the blocked student attempt to send a request
6. Verify appropriate error response

### Test Case 5: Privacy Settings

1. Authenticate with a student token
2. Update privacy settings to restrict friendship requests
3. Have another student attempt to send a friendship request
4. Verify appropriate error response
5. Update privacy settings to allow requests from friends of friends only
6. Test request from a friend of friend vs. a non-connected student
