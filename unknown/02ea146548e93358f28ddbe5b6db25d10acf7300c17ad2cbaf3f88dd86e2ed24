# Reward Point Settings Management

This document describes the APIs for managing reward point settings in the HEC system. Reward point settings control the conversion rate between reward points and currency for shop item purchases.

## Overview

Reward point settings allow administrators to configure how reward points are converted to currency when students purchase items using reward points. Only one setting can be active at a time, and the active setting is used for all reward point transactions.

## API Endpoints

### Create a Reward Point Setting

Creates a new reward point setting.

**Endpoint:** `POST /admin/reward-point-settings`

**Authentication:** Admin only

**Request Body:**

```json
{
  "name": "Standard Conversion Rate",
  "description": "Standard conversion rate for reward points",
  "conversionRate": 100,
  "isActive": true
}
```

**Response:**

```json
{
  "success": true,
  "message": "Reward point setting created successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Standard Conversion Rate",
    "description": "Standard conversion rate for reward points",
    "conversionRate": 100,
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Update a Reward Point Setting

Updates an existing reward point setting.

**Endpoint:** `PUT /admin/reward-point-settings/{id}`

**Authentication:** Admin only

**Request Body:**

```json
{
  "name": "Updated Conversion Rate",
  "description": "Updated conversion rate for reward points",
  "conversionRate": 150
}
```

**Response:**

```json
{
  "success": true,
  "message": "Reward point setting updated successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Updated Conversion Rate",
    "description": "Updated conversion rate for reward points",
    "conversionRate": 150,
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Set a Reward Point Setting as Active

Sets a reward point setting as active and deactivates all others.

**Endpoint:** `PUT /admin/reward-point-settings/{id}/activate`

**Authentication:** Admin only

**Response:**

```json
{
  "success": true,
  "message": "Reward point setting activated successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Standard Conversion Rate",
    "description": "Standard conversion rate for reward points",
    "conversionRate": 100,
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Get All Reward Point Settings

Gets a list of all reward point settings with pagination.

**Endpoint:** `GET /admin/reward-point-settings`

**Authentication:** Admin only

**Query Parameters:**

- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of items per page (default: 10)
- `sortBy` (optional): Field to sort by (default: createdAt)
- `sortDirection` (optional): Sort direction (ASC or DESC, default: DESC)

**Response:**

```json
{
  "success": true,
  "message": "Reward point settings retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Standard Conversion Rate",
        "description": "Standard conversion rate for reward points",
        "conversionRate": 100,
        "isActive": true,
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      },
      {
        "id": "123e4567-e89b-12d3-a456-426614174001",
        "name": "Premium Conversion Rate",
        "description": "Premium conversion rate for reward points",
        "conversionRate": 150,
        "isActive": false,
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      }
    ],
    "totalItems": 2,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  }
}
```

### Get the Active Reward Point Setting

Gets the currently active reward point setting.

**Endpoint:** `GET /admin/reward-point-settings/active`

**Authentication:** Admin only

**Response:**

```json
{
  "success": true,
  "message": "Active reward point setting retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Standard Conversion Rate",
    "description": "Standard conversion rate for reward points",
    "conversionRate": 100,
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Get a Reward Point Setting by ID

Gets a reward point setting by its ID.

**Endpoint:** `GET /admin/reward-point-settings/{id}`

**Authentication:** Admin only

**Response:**

```json
{
  "success": true,
  "message": "Reward point setting retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Standard Conversion Rate",
    "description": "Standard conversion rate for reward points",
    "conversionRate": 100,
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

## Conversion Rate

The conversion rate determines how many reward points equal 1 unit of currency. For example, if the conversion rate is 100, then 100 reward points equal $1.

When a shop item is marked as purchasable with reward points (`isPurchasableInRewardpoint = true`), the system uses the active conversion rate to calculate the reward point cost based on the item's price.

For example, if an item costs $9.99 and the conversion rate is 100, the reward point cost would be 999 points.

## Integration with Shopping Cart

When a student adds an item to their cart, the system checks if the item is purchasable with reward points. If it is, the system calculates the reward point cost based on the active conversion rate.

During checkout, if the student chooses to pay with reward points, the system deducts the calculated reward points from the student's account.
