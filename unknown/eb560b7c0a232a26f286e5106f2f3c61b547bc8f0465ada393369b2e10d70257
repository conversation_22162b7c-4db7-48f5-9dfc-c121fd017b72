# Application
NODE_ENV=production
PORT=3012
API_URL=http://**************:3012

# Database
DATABASE_HOST=postgres
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres
DATABASE_NAME=hec_db

# JWT
JWT_SECRET=hec_jwt_secret_key_for_authentication_and_authorization

# Mail (Bravo)
MAIL_HOST=smtp-relay.brevo.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=GRD8dnAEfUjW0stT
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=HEC

# Admin User
ADMIN_USER_ID=admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin@123

# Tutor User
TUTOR_USER_ID=tutor
TUTOR_EMAIL=<EMAIL>
TUTOR_PASSWORD=Tutor@123

# Student User
STUDENT_USER_ID=student
STUDENT_EMAIL=<EMAIL>
STUDENT_PASSWORD=Student@123

# File Upload
UPLOAD_DIR=/app/uploads
LOG_PATH=/app/logs
