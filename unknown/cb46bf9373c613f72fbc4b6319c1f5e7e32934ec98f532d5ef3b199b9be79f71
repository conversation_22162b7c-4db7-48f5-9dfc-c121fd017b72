import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, OneToMany, ManyTo<PERSON>ne, Jo<PERSON><PERSON><PERSON>um<PERSON> } from "typeorm";
import { AuditableBaseEntity } from "./base-entity";
import { IsUUID } from "class-validator";
import { EssayMission } from "./essay-mission.entity";
import { EssayTaskSubmissions } from "./essay-task-submissions.entity";

@Entity()
export class EssayMissionTasks extends AuditableBaseEntity {
  @Column({
    name: "title",
    type: "varchar",
    length: 50,
  })
  title: string;

  @Column({
    name: "description",
    type: "text"
  })
  description: string;

  @Column({
    name: "is_active",
    default: true
  })
  isActive?: boolean;

  @Column({
    name: "time_period_unit",
    type: "int",
    default: 1,
    nullable: true
  })
  timePeriodUnit: number;

  @Column({
    name: "word_limit_minumum",
    type: "int",
  })
  wordLimitMinimum: number;

  @Column({
    name: "word_limit_maximum",
    type: "int",
    nullable: true,
  })
  wordLimitMaximum: number;

  @Column({
    name: "deadline",
    type: "int",
    nullable: true
  })
  deadline: number;

  @Column({
    name: "instructions",
    type: "text",
  })
  instructions: string;

  @ManyToOne(() => EssayMission, mission => mission.tasks, { nullable: true })
  @JoinColumn({ name: "mission" })
  mission: EssayMission;

  @Column({
    name: "mission_id",
    type: "uuid",
    nullable: false
  })
  @IsUUID()
  missionId: string;

  @Column({
    name: "meta_data",
    type: "json",
    nullable: true
  })
  metaData: {
    month: string;
    year: string;
    week: string;
  };

  @OneToMany(() => EssayTaskSubmissions, submission => submission.task)
  submissions: EssayTaskSubmissions[];
}