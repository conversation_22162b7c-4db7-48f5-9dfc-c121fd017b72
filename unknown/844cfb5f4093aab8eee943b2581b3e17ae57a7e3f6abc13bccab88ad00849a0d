# Diary Mission Management - Testing Flow

This document outlines the testing flow for the Diary Mission Management API endpoints.

## Prerequisites

Before testing the Diary Mission Management API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (admin, tutor, student)
3. Set up your API testing tool (<PERSON><PERSON> recommended)

## Overview

The Diary Mission Management feature allows tutors to create writing missions with word count targets for students. Students can select missions, write entries to meet the targets, and tutors can provide feedback, corrections, and scores. This testing flow ensures all aspects of the feature work correctly.

## Testing Environment Setup

1. **Database Setup**
   - Ensure the database has the latest migrations applied
   - Verify the following tables exist:
     - `diary_mission`
     - `mission_diary_entry`
     - `mission_diary_entry_feedback`

2. **Test Users**
   - Create test tutor accounts
   - Create test student accounts
   - Ensure tutor-student mappings exist

3. **Test Data**
   - Create sample missions with different word count targets
   - Create mission entries in various states (draft, submitted, reviewed)

## API Testing

### Mission Management (Tutor Side)

#### Create Mission Test
- Endpoint: `POST /tutor/missions`
- Auth: Tutor token required
- Request: Mission details (title, description, targetWordCount, publishDate, score)
- Expected response: 200 OK with mission object

#### Update Mission Test
- Endpoint: `PUT /tutor/missions/{id}`
- Auth: Tutor token required
- Request: Updated mission details
- Expected response: 200 OK with updated mission object

#### Get Mission Test
- Endpoint: `GET /tutor/missions/{id}`
- Auth: Tutor token required
- Expected response: 200 OK with mission object

#### Get Missions Test
- Endpoint: `GET /tutor/missions`
- Auth: Tutor token required
- Expected response: 200 OK with paged list of missions

#### Delete Mission Test
- Endpoint: `DELETE /tutor/missions/{id}`
- Auth: Tutor token required
- Expected response: 200 OK with success message

### Mission Management (Student Side)

#### Get Today's Mission Test
- Endpoint: `GET /student/missions/today`
- Auth: Student token required
- Expected response: 200 OK with mission object

#### Get Mission Test
- Endpoint: `GET /student/missions/{id}`
- Auth: Student token required
- Expected response: 200 OK with mission object

#### Get Available Missions Test
- Endpoint: `GET /student/missions`
- Auth: Student token required
- Expected response: 200 OK with paged list of missions

### Mission Diary Entries (Student Side)

#### Create Entry Test
- Endpoint: `POST /student/missions/entries`
- Auth: Student token required
- Request: Mission ID and entry content
- Expected response: 200 OK with entry object

#### Update Entry Test
- Endpoint: `PUT /student/missions/entries/{id}`
- Auth: Student token required
- Request: Updated entry content
- Expected response: 200 OK with updated entry object

#### Submit Entry Test
- Endpoint: `POST /student/missions/entries/{id}/submit`
- Auth: Student token required
- Expected response: 200 OK with submitted entry object

#### Get Entry Test
- Endpoint: `GET /student/missions/entries/{id}`
- Auth: Student token required
- Expected response: 200 OK with entry object

#### Get Entries Test
- Endpoint: `GET /student/missions/entries`
- Auth: Student token required
- Expected response: 200 OK with paged list of entries

### Mission Diary Entries (Tutor Side)

#### Get Entries Test
- Endpoint: `GET /tutor/missions/entries`
- Auth: Tutor token required
- Expected response: 200 OK with paged list of entries

#### Get Entry Test
- Endpoint: `GET /tutor/missions/entries/{id}`
- Auth: Tutor token required
- Expected response: 200 OK with entry object

#### Add Feedback Test
- Endpoint: `POST /tutor/missions/entries/{id}/feedback`
- Auth: Tutor token required
- Request: Feedback text and optional rating
- Expected response: 200 OK with feedback object

#### Add Correction Test
- Endpoint: `POST /tutor/missions/entries/{id}/correction`
- Auth: Tutor token required
- Request: Correction text
- Expected response: 200 OK with updated entry object

#### Assign Score Test
- Endpoint: `POST /tutor/missions/entries/{id}/score`
- Auth: Tutor token required
- Request: Score value
- Expected response: 200 OK with updated entry object

## Integration Testing

### Tutor Flow

1. **Create Mission**
   - Log in as a tutor
   - Create a new mission with a specific word count target
   - Verify the mission is created successfully

2. **Review Submission**
   - Wait for a student to submit an entry
   - View the submitted entry
   - Add feedback
   - Add correction
   - Assign a score
   - Verify all actions are successful

3. **Notification Reception**
   - Verify receiving notification when a student submits an entry
   - Click on the notification deeplink
   - Verify navigation to the correct entry

### Student Flow

1. **View Missions**
   - Log in as a student
   - View today's mission
   - Browse available missions
   - Verify missions are displayed correctly

2. **Create and Submit Entry**
   - Select a mission
   - Create a new entry
   - Write content that meets the word count target
   - Verify progress calculation
   - Submit the entry
   - Verify submission is successful

3. **Notification Reception**
   - Verify receiving notifications for feedback, correction, and score
   - Click on notification deeplinks
   - Verify navigation to the correct entry

## Edge Case Testing

### Word Count Edge Cases

1. **Exact Word Count**
   - Create an entry with exactly the target word count
   - Verify progress is 100%
   - Verify submission is allowed

2. **Below Word Count**
   - Create an entry with less than the target word count
   - Verify progress is calculated correctly
   - Verify submission is not allowed

3. **Above Word Count**
   - Create an entry with more than the target word count
   - Verify progress is capped at 100%
   - Verify submission is allowed

### Mission Date Edge Cases

1. **Future Publish Date**
   - Create a mission with a future publish date
   - Verify it's not available to students until the publish date

2. **Expired Mission**
   - Create a mission with an expiry date in the past
   - Verify it's not available to students

### Permission Edge Cases

1. **Student Permissions**
   - Attempt to access tutor endpoints as a student
   - Verify proper 403 Forbidden responses

2. **Tutor Permissions**
   - Attempt to access student endpoints as a tutor
   - Verify proper 403 Forbidden responses

3. **Cross-User Access**
   - Attempt to access another student's entries
   - Verify proper 403 Forbidden responses

## Performance Testing

1. **Large Content**
   - Create an entry with a very large content (10,000+ words)
   - Verify word count calculation performance
   - Verify submission and review performance

2. **Multiple Concurrent Users**
   - Simulate multiple students submitting entries simultaneously
   - Simulate multiple tutors reviewing entries simultaneously
   - Verify system stability and response times

## Notification Testing

1. **Email Notifications**
   - Verify email notifications are sent for the following events:
     - Mission submission (to tutor)
     - Mission feedback (to student)
     - Mission correction (to student)
     - Mission review completion (to student)
   - Check email content, formatting, and deeplinks
   - Verify that no notification is sent when a mission is created

2. **In-App Notifications**
   - Verify in-app notifications appear for all relevant events
   - Check notification content and deeplinks
   - Verify deeplinks navigate to the correct screens

3. **Push Notifications**
   - Verify push notifications are sent for all relevant events
   - Check notification content and deeplinks
   - Verify that notification badges are updated correctly

## Deeplink Testing

1. **Web Deeplinks**
   - Test all web deeplinks generated for missions and entries
   - Verify correct navigation to the target content

2. **Mobile Deeplinks**
   - Test all mobile deeplinks generated for missions and entries
   - Verify correct navigation to the target content in the mobile app

## Regression Testing

1. **Existing Diary Features**
   - Verify that existing diary entry functionality still works
   - Ensure no conflicts between regular diary entries and mission entries

2. **Notification System**
   - Verify that other notification types still work
   - Ensure no conflicts with the new notification types



## Conclusion

This testing guide provides a comprehensive approach to testing the Diary Mission Management feature. By following these procedures, you can ensure the feature works correctly, handles edge cases appropriately, and integrates well with the existing system.

For any questions or issues, please contact the development team.
