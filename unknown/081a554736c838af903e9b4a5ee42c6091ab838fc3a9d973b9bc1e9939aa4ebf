# Notification Module

## Overview

The Notification Module provides a comprehensive system for sending and managing notifications across multiple channels (in-app, email, push). It supports various notification types, delivery tracking, and user preferences.

## Entities

### Notification

Represents a notification sent to a user.

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier |
| userId | string | ID of the recipient user |
| type | NotificationType | Type of notification |
| title | string | Notification title |
| message | string | Notification message |
| data | JSON | Additional data related to the notification |
| read | boolean | Whether the notification has been read |
| channels | string[] | Channels through which the notification was sent |
| createdAt | Date | When the notification was created |
| updatedAt | Date | When the notification was last updated |

### NotificationPreference

Represents a user's preferences for receiving notifications.

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier |
| userId | string | ID of the user |
| type | NotificationType | Type of notification |
| email | boolean | Whether to receive via email |
| push | boolean | Whether to receive via push notification |
| inApp | boolean | Whether to receive in-app |

### NotificationDelivery

Tracks the delivery status of notifications.

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier |
| notificationId | string | ID of the notification |
| channel | string | Delivery channel (email, push, in-app) |
| status | string | Delivery status (sent, delivered, failed) |
| error | string | Error message if delivery failed |
| createdAt | Date | When the delivery was attempted |
| updatedAt | Date | When the delivery status was last updated |

## Enums

### NotificationType

```typescript
enum NotificationType {
  DIARY_COMMENT = 'DIARY_COMMENT',
  DIARY_REVIEWED = 'DIARY_REVIEWED',
  TUTOR_ASSIGNED = 'TUTOR_ASSIGNED',
  NEW_MESSAGE = 'NEW_MESSAGE',
  PLAN_PURCHASED = 'PLAN_PURCHASED',
  ACCOUNT_VERIFIED = 'ACCOUNT_VERIFIED',
  PASSWORD_RESET = 'PASSWORD_RESET',
  ESSAY_SUBMITTED = 'ESSAY_SUBMITTED',
  ESSAY_REVIEWED = 'ESSAY_REVIEWED',
  SYSTEM_ANNOUNCEMENT = 'SYSTEM_ANNOUNCEMENT'
}
```

## API Endpoints

### Get Notifications

Retrieves notifications for the authenticated user.

```
GET /api/notifications
```

**Query Parameters:**
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 20)
- `read` (boolean, optional): Filter by read status
- `type` (NotificationType, optional): Filter by notification type

**Response:**
```json
{
  "items": [
    {
      "id": "notif-123",
      "type": "DIARY_COMMENT",
      "title": "New comment on your diary",
      "message": "Tutor John has commented on your diary entry",
      "read": false,
      "data": {
        "diaryId": "diary-456",
        "commentId": "comment-789"
      },
      "createdAt": "2023-01-01T12:00:00Z"
    }
  ],
  "meta": {
    "totalItems": 45,
    "itemsPerPage": 20,
    "currentPage": 1,
    "totalPages": 3
  }
}
```

### Get Notification by ID

Retrieves a specific notification.

```
GET /api/notifications/:id
```

**Response:**
```json
{
  "id": "notif-123",
  "type": "DIARY_COMMENT",
  "title": "New comment on your diary",
  "message": "Tutor John has commented on your diary entry",
  "read": false,
  "data": {
    "diaryId": "diary-456",
    "commentId": "comment-789"
  },
  "createdAt": "2023-01-01T12:00:00Z"
}
```

### Mark Notification as Read

Marks a notification as read.

```
PATCH /api/notifications/:id/read
```

**Response:**
```json
{
  "success": true,
  "notification": {
    "id": "notif-123",
    "read": true,
    "updatedAt": "2023-01-02T12:00:00Z"
  }
}
```

### Mark All Notifications as Read

Marks all notifications as read for the authenticated user.

```
POST /api/notifications/read-all
```

**Response:**
```json
{
  "success": true,
  "count": 5,
  "message": "5 notifications marked as read"
}
```

### Get Unread Count

Gets the count of unread notifications for the authenticated user.

```
GET /api/notifications/unread-count
```

**Response:**
```json
{
  "count": 3
}
```

### Get Notification Preferences

Gets the notification preferences for the authenticated user.

```
GET /api/notifications/preferences
```

**Response:**
```json
{
  "email": {
    "DIARY_COMMENT": true,
    "TUTOR_ASSIGNED": true,
    "NEW_MESSAGE": false
  },
  "push": {
    "DIARY_COMMENT": false,
    "TUTOR_ASSIGNED": true,
    "NEW_MESSAGE": true
  },
  "inApp": {
    "DIARY_COMMENT": true,
    "TUTOR_ASSIGNED": true,
    "NEW_MESSAGE": true
  }
}
```

### Update Notification Preferences

Updates notification preferences for the authenticated user.

```
PATCH /api/notifications/preferences
```

**Request Body:**
```json
{
  "email": {
    "NEW_MESSAGE": true
  },
  "push": {
    "DIARY_COMMENT": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "preferences": {
    "email": {
      "DIARY_COMMENT": true,
      "TUTOR_ASSIGNED": true,
      "NEW_MESSAGE": true
    },
    "push": {
      "DIARY_COMMENT": true,
      "TUTOR_ASSIGNED": true,
      "NEW_MESSAGE": true
    },
    "inApp": {
      "DIARY_COMMENT": true,
      "TUTOR_ASSIGNED": true,
      "NEW_MESSAGE": true
    }
  }
}
```

### Send Notification (Admin Only)

Sends a notification to a user or group of users.

```
POST /api/admin/notifications/send
```

**Request Body:**
```json
{
  "userIds": ["user-123", "user-456"],
  "type": "SYSTEM_ANNOUNCEMENT",
  "title": "System Maintenance",
  "message": "The system will be down for maintenance on Saturday from 2-4 AM.",
  "data": {
    "maintenanceId": "maint-789"
  },
  "channels": ["email", "inApp"]
}
```

**Response:**
```json
{
  "success": true,
  "sent": 2,
  "failed": 0,
  "notifications": [
    {
      "id": "notif-789",
      "userId": "user-123",
      "type": "SYSTEM_ANNOUNCEMENT",
      "title": "System Maintenance",
      "createdAt": "2023-01-01T12:00:00Z"
    },
    {
      "id": "notif-790",
      "userId": "user-456",
      "type": "SYSTEM_ANNOUNCEMENT",
      "title": "System Maintenance",
      "createdAt": "2023-01-01T12:00:00Z"
    }
  ]
}
```

## WebSocket Events

The notification module also provides real-time notifications via WebSocket.

### Receiving Notifications

When a new notification is created, a `notification` event is emitted to the recipient:

```javascript
// Event: notification
{
  "id": "notif-123",
  "type": "DIARY_COMMENT",
  "title": "New comment on your diary",
  "message": "Tutor John has commented on your diary entry",
  "read": false,
  "data": {
    "diaryId": "diary-456",
    "commentId": "comment-789"
  },
  "createdAt": "2023-01-01T12:00:00Z"
}
```

### Notification Read Status

When a notification is marked as read, a `notification_read` event is emitted:

```javascript
// Event: notification_read
{
  "notificationId": "notif-123",
  "read": true,
  "updatedAt": "2023-01-02T12:00:00Z"
}
```

## Service Integration

Other modules can send notifications by injecting the `NotificationService` and calling its methods:

```typescript
@Injectable()
export class DiaryService {
  constructor(
    private readonly notificationService: NotificationService,
  ) {}

  async addComment(diaryId: string, comment: CommentDto, tutorId: string): Promise<Comment> {
    // Create comment logic...

    // Get diary owner
    const diary = await this.diaryRepository.findOne(diaryId, { relations: ['student'] });
    
    // Send notification to student
    await this.notificationService.send({
      userId: diary.student.id,
      type: NotificationType.DIARY_COMMENT,
      title: 'New comment on your diary',
      message: `Tutor ${tutor.name} has commented on your diary entry`,
      data: {
        diaryId,
        commentId: newComment.id
      },
      channels: ['email', 'inApp']
    });

    return newComment;
  }
}
```

## Error Handling

The notification module handles errors gracefully and implements retry mechanisms for failed deliveries:

1. If a notification fails to send via a specific channel, it's marked as failed in the `NotificationDelivery` entity
2. A background job attempts to resend failed notifications periodically
3. After a configurable number of retries, the notification is marked as permanently failed
4. Administrators can view failed notifications and manually trigger retries

## Conclusion

The Notification Module provides a robust system for sending and managing notifications across multiple channels. It supports user preferences, delivery tracking, and real-time updates via WebSocket.
