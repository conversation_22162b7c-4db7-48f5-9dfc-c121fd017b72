import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserNotificationPreference } from '../../database/entities/user-notification-preference.entity';
import { NotificationType } from '../../database/entities/notification.entity';
import { NotificationChannel } from '../../database/entities/notification-delivery.entity';
import { User } from '../../database/entities/user.entity';

@Injectable()
export class NotificationChannelService {
  private readonly logger = new Logger(NotificationChannelService.name);

  constructor(
    @InjectRepository(UserNotificationPreference)
    private readonly userNotificationPreferenceRepository: Repository<UserNotificationPreference>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}

  /**
   * Determine which channels to deliver a notification through based on user preferences
   * @param userId User ID
   * @param notificationType Notification type
   * @param requestedChannels Explicitly requested channels (optional)
   * @returns Array of delivery channels
   */
  async determineChannels(
    userId: string,
    notificationType: NotificationType,
    requestedChannels?: NotificationChannel[]
  ): Promise<NotificationChannel[]> {
    try {
      this.logger.log(`Determining channels for user ${userId}, notification type: ${notificationType}`);

      // If specific channels are requested, use those (subject to user preferences)
      if (requestedChannels && requestedChannels.length > 0) {
        this.logger.log(`Using requested channels: ${requestedChannels.join(', ')}`);
        const channels = await this.filterChannelsByPreferences(userId, notificationType, requestedChannels);
        this.logger.log(`After filtering by preferences: ${channels.join(', ')}`);
        return channels;
      }

      // Get user preferences
      const preferences = await this.userNotificationPreferenceRepository.find({
        where: { userId, notificationType }
      });

      // If no preferences, use defaults
      if (preferences.length === 0) {
        this.logger.log(`No preferences found for user ${userId}, using defaults`);
        const defaultChannels = await this.getDefaultChannels(userId, notificationType);
        this.logger.log(`Default channels: ${defaultChannels.join(', ')}`);
        return defaultChannels;
      }

      // Filter enabled channels
      const enabledChannels = preferences
        .filter(pref => pref.isEnabled)
        .map(pref => pref.channel);

      this.logger.log(`Enabled channels from preferences: ${enabledChannels.join(', ')}`);
      return enabledChannels;
    } catch (error) {
      this.logger.error(`Error determining channels for user ${userId}: ${error.message}`, error.stack);
      // Fall back to in-app only in case of error
      this.logger.log(`Falling back to IN_APP channel due to error`);
      return [NotificationChannel.IN_APP];
    }
  }

  /**
   * Filter requested channels based on user preferences
   * @param userId User ID
   * @param notificationType Notification type
   * @param requestedChannels Requested channels
   * @returns Filtered channels
   */
  private async filterChannelsByPreferences(
    userId: string,
    notificationType: NotificationType,
    requestedChannels: NotificationChannel[]
  ): Promise<NotificationChannel[]> {
    // Get user preferences
    const preferences = await this.userNotificationPreferenceRepository.find({
      where: { userId, notificationType }
    });

    // If no preferences, return all requested channels
    if (preferences.length === 0) {
      return requestedChannels;
    }

    // Create a map of channel to preference
    const preferenceMap = preferences.reduce((map, pref) => {
      map[pref.channel] = pref;
      return map;
    }, {} as Record<string, UserNotificationPreference>);

    // Filter channels based on preferences
    return requestedChannels.filter(channel => {
      const preference = preferenceMap[channel];
      // If no preference for this channel, allow it
      if (!preference) {
        return true;
      }
      // Otherwise, check if it's enabled
      return preference.isEnabled;
    });
  }

  /**
   * Get default delivery channels for a user and notification type
   * @param userId User ID
   * @param notificationType Notification type
   * @returns Array of default delivery channels
   */
  private async getDefaultChannels(
    userId: string,
    notificationType: NotificationType
  ): Promise<NotificationChannel[]> {
    try {
      this.logger.log(`Getting default channels for user ${userId}, notification type: ${notificationType}`);

      // Default to in-app for all notification types
      const channels = [NotificationChannel.IN_APP];
      this.logger.log(`Added default IN_APP channel`);

      // Add push notifications by default for all notification types
      channels.push(NotificationChannel.PUSH);
      this.logger.log(`Added default PUSH channel`);

      // Get user to determine if email and SMS should be included
      const user = await this.userRepository.findOne({ where: { id: userId } });

      if (user) {
        this.logger.log(`Found user ${userId}, email: ${user.email ? 'yes' : 'no'}, phone: ${user.phone ? 'yes' : 'no'}`);

        // If user has email, include email channel for specific notification types
        if (user.email) {
          switch (notificationType) {
            case NotificationType.DIARY_SUBMISSION:
            case NotificationType.DIARY_REVIEW:
            case NotificationType.DIARY_FEEDBACK:
            case NotificationType.TUTOR_ASSIGNMENT:
              channels.push(NotificationChannel.EMAIL);
              this.logger.log(`Added EMAIL channel for ${notificationType}`);
              break;
            default:
              this.logger.log(`EMAIL channel not added for ${notificationType}`);
              break;
          }
        } else {
          this.logger.log(`User has no email, skipping EMAIL channel`);
        }

        // If user has phone, include SMS for high-priority notifications
        if (user.phone) {
          switch (notificationType) {
            case NotificationType.TUTOR_ASSIGNMENT:
              // Only send SMS for important notifications
              channels.push(NotificationChannel.SMS);
              this.logger.log(`Added SMS channel for ${notificationType}`);
              break;
            default:
              this.logger.log(`SMS channel not added for ${notificationType}`);
              break;
          }
        } else {
          this.logger.log(`User has no phone, skipping SMS channel`);
        }

        // Add real-time messaging for chat-related notifications
        // This is a placeholder for future implementation
        switch (notificationType) {
          case NotificationType.DIARY_SUBMISSION:
          case NotificationType.DIARY_REVIEW:
          case NotificationType.DIARY_FEEDBACK:
            // These could be delivered via real-time messaging in the future
            // Uncomment when real-time messaging is implemented
            // channels.push(NotificationChannel.REALTIME_MESSAGE);
            this.logger.log(`REALTIME_MESSAGE channel not implemented yet for ${notificationType}`);
            break;
        }
      } else {
        this.logger.warn(`User ${userId} not found, using basic channels only`);
      }

      this.logger.log(`Final default channels for user ${userId}: ${channels.join(', ')}`);
      return channels;
    } catch (error) {
      this.logger.error(`Error getting default channels for user ${userId}: ${error.message}`, error.stack);
      // Fall back to in-app only in case of error
      this.logger.log(`Falling back to IN_APP channel due to error`);
      return [NotificationChannel.IN_APP];
    }
  }

  /**
   * Check if a notification should be delivered through a specific channel
   * based on time window and days of week preferences
   * @param preference User preference
   * @returns Whether the notification should be delivered
   */
  isDeliveryAllowedByTimePreference(_preference: UserNotificationPreference): boolean {
    // Time-based preferences are not implemented in this version
    return true;
  }
}
