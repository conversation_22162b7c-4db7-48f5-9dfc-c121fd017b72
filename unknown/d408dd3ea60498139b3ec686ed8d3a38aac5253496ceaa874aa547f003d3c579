# HEC Backend API Documentation

This documentation provides a comprehensive overview of all APIs and their main algorithms in the HEC Backend system. The documentation is organized by modules/groups to help you understand the system architecture and functionality.

## Table of Contents

1. [Authentication Module](1-authentication-module.md)
2. [Users Module](2-users-module.md)
3. [Plans Module](3-plans-module.md)
4. [Diary Module](4-diary-module.md)
5. [Shop Module](5-shop-module.md)
6. [Tutor Approval Module](6-tutor-approval-module.md)
7. [Common Services](7-common-services.md)
8. [Backlog Summary](backlog-summary.md)

Each section contains detailed information about the APIs, their endpoints, request/response formats, and the main algorithms used in their implementation.

## System Overview

The HEC Backend is a comprehensive system that provides APIs for:

- User authentication and authorization
- User management (admin, tutor, student)
- Subscription plan management
- Automatic tutor assignment
- Student diary management
- Shop and reward points management
- Shopping cart and checkout process
- Student owned items management
- Tutor approval workflow

For a detailed explanation of the automatic tutor assignment system, see [Automatic Tutor Assignment System](../automatic-tutor-assignment.md).

The system is built using NestJS framework with TypeORM for database operations and follows a modular architecture.

## API Response Format

All APIs in the system follow a standardized response format:

```json
{
  "success": true,
  "message": "Operation successful",
  "data": { ... },
  "errors": null,
  "statusCode": 200
}
```

In case of errors:

```json
{
  "success": false,
  "message": "Operation failed",
  "data": null,
  "errors": [
    {
      "field": "email",
      "message": "Email is already in use"
    }
  ],
  "statusCode": 400
}
```

## Authentication

All protected endpoints require a valid JWT token to be included in the Authorization header:

```
Authorization: Bearer <token>
```

The token is obtained by calling the login API.

## Role-Based Access Control

The system implements role-based access control with three main roles:

- **Admin**: Has access to all endpoints
- **Tutor**: Has access to tutor-specific endpoints
- **Student**: Has access to student-specific endpoints

Some endpoints are accessible by multiple roles, while others are restricted to specific roles.
