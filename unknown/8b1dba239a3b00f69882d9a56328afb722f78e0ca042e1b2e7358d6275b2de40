import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDiaryFriendshipAwardCriteria1748300000000 implements MigrationInterface {
    name = 'AddDiaryFriendshipAwardCriteria1748300000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add the new award criteria to the enum
        await queryRunner.query(`ALTER TYPE "public"."award_criteria_enum" ADD VALUE IF NOT EXISTS 'diary_friendship'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Note: PostgreSQL doesn't support removing enum values directly
        // This would require recreating the enum type, which is complex
        // For now, we'll leave the enum value in place
        console.log('Cannot remove enum values in PostgreSQL. The diary_friendship value will remain.');
    }
}
