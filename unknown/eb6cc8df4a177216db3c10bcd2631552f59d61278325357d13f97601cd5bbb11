import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddBackgroundColorToNovelEntry1748500000000 implements MigrationInterface {
  name = 'AddBackgroundColorToNovelEntry1748500000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add background_color column to novel_entry table
    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      ADD COLUMN "background_color" character varying
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove background_color column from novel_entry table
    await queryRunner.query(`
      ALTER TABLE "novel_entry" 
      DROP COLUMN "background_color"
    `);
  }
}
