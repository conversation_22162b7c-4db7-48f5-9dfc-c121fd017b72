# Authentication API Testing Flow

This document outlines the testing flow for the Authentication API endpoints.

## Prerequisites

Before testing the Authentication API:

1. Ensure the HEC backend is running
2. Have access to test user credentials for different roles
3. Set up your API testing tool (<PERSON><PERSON> recommended)

## Registration Testing Flow

### Test Case 1: Successful Registration

1. Send a POST request to `/api/auth/register` with valid user data
2. Verify HTTP status code is 201 Created
3. Verify response contains user ID and email
4. Verify user is created but not yet confirmed
5. Check if verification email is sent (check logs or email service)

### Test Case 2: Registration Validation

1. Test with missing required fields (userId, email, password, etc.)
2. Test with invalid email format
3. Test with password that doesn't meet complexity requirements
4. Test with password and confirmPassword that don't match
5. Test with agreedToTerms set to false
6. Verify appropriate validation errors are returned for each case

### Test Case 3: Duplicate Registration

1. Register a user with a specific userId and email
2. Attempt to register another user with the same userId
3. Verify appropriate error message about duplicate userId
4. Attempt to register another user with the same email
5. Verify appropriate error message about duplicate email

## Login Testing Flow

### Test Case 1: Successful Login

1. Send a POST request to `/api/auth/login` with valid credentials
2. Verify HTTP status code is 200 OK
3. Verify response contains access_token, refresh_token, and user data
4. Verify token can be used to access protected endpoints

### Test Case 2: Login with Remember Me

1. Send a POST request to `/api/auth/login` with valid credentials and rememberMe=true
2. Verify tokens have extended expiration time
3. Verify refresh token expiry date is correctly set

### Test Case 3: Failed Login Attempts

1. Test with invalid userId
2. Test with valid userId but incorrect password
3. Test with inactive user account
4. Test with unconfirmed user account
5. Verify appropriate error messages for each case

## Email Verification Testing Flow

### Test Case 1: Successful Verification

1. Register a new user and capture the verification token from logs or email service
2. Send a GET request to `/api/auth/verify-email` with the token
3. Verify HTTP status code is 200 OK
4. Verify user account is now confirmed in the database
5. Verify user can now log in

### Test Case 2: Invalid Verification

1. Test with an invalid verification token
2. Test with an expired verification token
3. Test with a token that has already been used
4. Verify appropriate error messages for each case

## Password Reset Testing Flow

### Test Case 1: Request Password Reset

1. Send a POST request to `/api/auth/forgot-password` with a valid email
2. Verify HTTP status code is 200 OK
3. Verify reset email is sent (check logs or email service)
4. Capture the reset token from logs or email service

### Test Case 2: Reset Password

1. Send a POST request to `/api/auth/reset-password` with the token and new password
2. Verify HTTP status code is 200 OK
3. Verify user can log in with the new password
4. Verify user cannot log in with the old password

### Test Case 3: Invalid Reset Attempts

1. Test with an invalid reset token
2. Test with an expired reset token
3. Test with a token that has already been used
4. Test with password that doesn't meet complexity requirements
5. Test with password and confirmPassword that don't match
6. Verify appropriate error messages for each case

## Token Refresh Testing Flow

### Test Case 1: Successful Token Refresh

1. Log in and capture the refresh token
2. Wait until the access token expires or simulate expiration
3. Send a POST request to `/api/auth/refresh-token` with the refresh token
4. Verify HTTP status code is 200 OK
5. Verify response contains new access_token and refresh_token
6. Verify new access token can be used to access protected endpoints

### Test Case 2: Invalid Refresh Attempts

1. Test with an invalid refresh token
2. Test with an expired refresh token
3. Test with a token that has already been used
4. Verify appropriate error messages for each case

## Logout Testing Flow

### Test Case 1: Successful Logout

1. Log in and capture the tokens
2. Send a POST request to `/api/auth/logout` with the refresh token
3. Verify HTTP status code is 200 OK
4. Verify refresh token is invalidated (attempt to use it for token refresh)
5. Verify access token is still valid until it expires

## Edge Cases and Security Testing

### Test Case 1: Brute Force Protection

1. Attempt multiple failed login attempts with the same userId
2. Verify account is temporarily locked after a certain number of attempts
3. Verify appropriate error message is returned

### Test Case 2: Session Management

1. Log in from multiple devices/browsers
2. Verify all sessions are active
3. Test logout from one device and verify other sessions remain active
4. Test password change and verify all sessions are invalidated

### Test Case 3: Token Security

1. Attempt to use an access token after logout
2. Attempt to use a refresh token after logout
3. Attempt to use an access token after password change
4. Verify appropriate security measures are in place
