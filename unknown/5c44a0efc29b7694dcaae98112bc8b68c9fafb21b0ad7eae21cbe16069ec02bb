# Test Environment Configuration
NODE_ENV=test
PORT=3013
API_URL=http://localhost:3013
FRONTEND_URL=http://localhost:3011

# Test Database Configuration (PostgreSQL - using development database)
DATABASE_TYPE=postgres
DATABASE_HOST=**************
DATABASE_PORT=5432
DATABASE_USER=hec_user
DATABASE_PASSWORD=123456_Az
DATABASE_NAME=hec_db
DATABASE_SSL=false

# JWT Configuration for Tests
JWT_SECRET=test_jwt_secret_key_for_testing_only
JWT_EXPIRES_IN=1h

# KCP Test Configuration (Mock values)
KCP_SITE_CD=T0000
KCP_SITE_KEY=test_site_key_for_testing_only
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_TRADE_REG_URL=/std/tradeReg/register
KCP_PAYMENT_URL=/gw/enc/v1/payment
KCP_WEBHOOK_SECRET=test_webhook_secret
KCP_ENVIRONMENT=test
KCP_TEST_MODE=true
MOCK_PAYMENT_GATEWAY=true

# Payment Test Configuration
PAYMENT_PROVIDER=kcp
PAYMENT_CURRENCY=KRW
MOCK_PAYMENT_GATEWAY=true

# Email Configuration (Mock for tests)
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=test_password
SMTP_FROM=<EMAIL>

# File Storage (Local for tests)
STORAGE_PROVIDER=local
UPLOAD_DIR=test-uploads

# Logging Configuration
LOG_LEVEL=error
ENABLE_DEBUG_LOGS=false

# Security Configuration
CORS_ORIGIN=http://localhost:3011
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=1000

# Test-specific settings
ENABLE_SWAGGER=false
MOCK_EXTERNAL_SERVICES=true
