# Plan Promotion Integration

This document outlines how the promotion management system integrates with the plans module in the HEC backend application.

## Overview

The integration between promotions and plans allows for dynamic pricing based on applicable promotions. When a promotion is applied to a plan, the system calculates the final price based on the promotion's discount rules. All plan-related APIs now include promotion information to ensure consistent display across the application.

## Plan-Promotion Integration Flow

### 1. Admin: Apply Promotion to Plans

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  View       │      │  Select     │      │  Choose     │      │  Apply      │
│  Plans      │ ──▶  │  Plans      │ ──▶  │  Promotion  │ ──▶  │  Promotion  │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /plans  │      │ Select Plans│      │ GET /promo- │      │ POST /plans/│
│             │      │ in UI       │      │ tions/admin │      │ admin/apply-│
│             │      │             │      │             │      │ promotion   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /plans` - Get all plans
- `GET /promotions/admin` - Get all promotions (admin)
- `POST /plans/admin/apply-promotion` - Apply promotion to plans

### 2. Admin: Remove Promotion from Plan

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  View Plan  │      │  Select     │      │  Remove     │
│  Details    │ ──▶  │  Plan       │ ──▶  │  Promotion  │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /plans/ │      │ Select Plan │      │ DELETE      │
│ :id         │      │ in UI       │      │ /plans/admin│
│             │      │             │      │ /:id/       │
│             │      │             │      │ promotion   │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /plans/:id` - Get plan details
- `DELETE /plans/admin/:id/promotion` - Remove promotion from plan

### 3. Student: View Plans with Promotions

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Browse     │      │  View Plan  │      │  Subscribe  │
│  Plans      │ ──▶  │  Details    │ ──▶  │  to Plan    │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /plans  │      │ GET /plans/ │      │ POST /plans/│
│             │      │ :id         │      │ subscribe   │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /plans` - Get all plans (includes promotion info)
- `GET /plans/:id` - Get plan details (includes promotion info)
- `POST /plans/subscribe` - Subscribe to plan

## API Response Changes

All plan-related API responses now include promotion information:

### Plan Response DTO

```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Premium Plan",
  "type": "premium",
  "subscriptionType": "monthly",
  "description": "Premium features for serious users",
  "price": 19.99,
  "durationDays": 30,
  "autoRenew": true,
  "isActive": true,
  "isApplicableForPromotion": true,
  "promotionId": "123e4567-e89b-12d3-a456-426614174001",
  "planFeatures": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174002",
      "name": "Advanced Diary",
      "description": "Access to advanced diary features",
      "isActive": true
    }
  ],
  "legacyFeatures": []
}
```

### Simplified Plan DTO (used in UserPlanResponseDto)

```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Premium Plan",
  "type": "premium",
  "subscriptionType": "monthly",
  "description": "Premium features for serious users",
  "price": 19.99,
  "isApplicableForPromotion": true,
  "promotionId": "123e4567-e89b-12d3-a456-426614174001",
  "features": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174002",
      "type": "diary",
      "name": "Advanced Diary",
      "description": "Access to advanced diary features"
    }
  ]
}
```

## API Endpoints

### Apply Promotion to Plans

```
POST /plans/admin/apply-promotion
```

**Request Body:**
```json
{
  "promotionId": "123e4567-e89b-12d3-a456-426614174001",
  "planIds": [
    "123e4567-e89b-12d3-a456-426614174002",
    "123e4567-e89b-12d3-a456-426614174003"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion applied to 2 plans",
  "data": {
    "success": true,
    "message": "Promotion applied to 2 plans. 1 plan was skipped because it is not applicable to this promotion."
  }
}
```

### Remove Promotion from Plan

```
DELETE /plans/admin/:id/promotion
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion removed from plan successfully",
  "data": {
    "success": true,
    "message": "Promotion removed from plan successfully"
  }
}
```

## Implementation Notes

- **Plan Promotion Integration**:
  - All plan-related APIs now include promotion information
  - When a promotion is applied to a plan, the plan's `isApplicableForPromotion` field is set to `true` and the `promotionId` field is set to the promotion's ID
  - When a promotion is removed from a plan, the plan's `isApplicableForPromotion` field is set to `false` and the `promotionId` field is set to `null`
  - Promotions can be restricted to specific plan types using the `applicablePlanIds` field in the promotion
  - When applying a promotion to plans, the system checks if the plans are applicable to the promotion based on the `applicablePlanIds` field

- **Frontend Display Considerations**:
  - When displaying plans, show both the original price and the discounted price if a promotion is applied
  - Include visual indicators for plans with active promotions
  - Show promotion details (name, discount percentage/amount) when displaying plan details
  - When a user subscribes to a plan with a promotion, the subscription should reflect the discounted price
