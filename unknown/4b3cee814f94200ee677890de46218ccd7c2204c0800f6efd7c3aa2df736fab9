import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ShopCategory } from '../../database/entities/shop-category.entity';
import { ShopItem, ShopItemType } from '../../database/entities/shop-item.entity';

@Injectable()
export class ShopSeed {
  private readonly logger = new Logger(ShopSeed.name);

  constructor(
    @InjectRepository(ShopCategory)
    private readonly shopCategoryRepository: Repository<ShopCategory>,
    @InjectRepository(ShopItem)
    private readonly shopItemRepository: Repository<ShopItem>,
  ) {}

  async seed(): Promise<void> {
    this.logger.log('Seeding shop categories and items...');

    // Seed categories
    const categories = await this.seedCategories();

    // Seed shop items
    await this.seedShopItems(categories);

    this.logger.log('Shop seeding completed');
  }

  private async seedCategories(): Promise<Map<string, string>> {
    const categoryMap = new Map<string, string>();

    // Define initial categories
    const initialCategories = [
      {
        name: 'Graphics',
        description: 'Beautiful graphics for your diary',
        isActive: true,
        displayOrder: 1,
        imageUrl: 'uploads/shop-categories/graphics.png',
      },
      {
        name: 'Templates',
        description: 'Ready-to-use templates for your diary',
        isActive: true,
        displayOrder: 2,
        imageUrl: 'uploads/shop-categories/templates.png',
      },
      {
        name: 'Stickers',
        description: 'Fun stickers to decorate your diary',
        isActive: true,
        displayOrder: 3,
        imageUrl: 'uploads/shop-categories/stickers.png',
      },
      {
        name: 'Fonts',
        description: 'Beautiful fonts for your diary',
        isActive: true,
        displayOrder: 4,
        imageUrl: 'uploads/shop-categories/fonts.png',
      },
      {
        name: 'Themes',
        description: 'Complete themes for your diary',
        isActive: true,
        displayOrder: 5,
        imageUrl: 'uploads/shop-categories/themes.png',
      },
    ];

    // Seed each category
    for (const categoryData of initialCategories) {
      // Check if category already exists
      const existingCategory = await this.shopCategoryRepository.findOne({
        where: { name: categoryData.name }
      });

      if (!existingCategory) {
        // Create new category
        const category = this.shopCategoryRepository.create({
          ...categoryData,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        const savedCategory = await this.shopCategoryRepository.save(category);
        categoryMap.set(categoryData.name, savedCategory.id);
        this.logger.log(`Created category: ${savedCategory.name}`);
      } else {
        categoryMap.set(categoryData.name, existingCategory.id);
        this.logger.log(`Category already exists: ${existingCategory.name}`);
      }
    }

    return categoryMap;
  }

  private async seedShopItems(categories: Map<string, string>): Promise<void> {
    // Define initial shop items
    const initialShopItems = [
      // Graphics items
      {
        itemNumber: 'GR-001',
        title: 'Flower Graphics Pack',
        description: 'A beautiful collection of flower graphics for your diary',
        categoryId: categories.get('Graphics'),
        type: ShopItemType.IN_APP_PURCHASE,
        price: 9.99,
        filePath: 'uploads/shop-items/flower-graphics.zip',
        isActive: true,
        isFeatured: true,
        metadata: 'flowers,nature,spring'
      },
      {
        itemNumber: 'GR-002',
        title: 'Animal Graphics Pack',
        description: 'Cute animal graphics for your diary',
        categoryId: categories.get('Graphics'),
        type: ShopItemType.IN_APP_PURCHASE,
        price: 7.99,
        filePath: 'uploads/shop-items/animal-graphics.zip',
        isActive: true,
        isFeatured: false,
        metadata: 'animals,cute,pets'
      },
      {
        itemNumber: 'GR-003',
        title: 'Basic Shapes Pack',
        description: 'Essential shapes for your diary',
        categoryId: categories.get('Graphics'),
        type: ShopItemType.FREE,
        price: 0,
        filePath: 'uploads/shop-items/basic-shapes.zip',
        isActive: true,
        isFeatured: false,
        metadata: 'shapes,basic,essential'
      },

      // Templates items
      {
        itemNumber: 'TP-001',
        title: 'Daily Planner Template',
        description: 'A comprehensive daily planner template',
        categoryId: categories.get('Templates'),
        type: ShopItemType.IN_APP_PURCHASE,
        price: 4.99,
        filePath: 'uploads/shop-items/daily-planner.zip',
        isActive: true,
        isFeatured: true,
        metadata: 'planner,daily,organization'
      },
      {
        itemNumber: 'TP-002',
        title: 'Weekly Schedule Template',
        description: 'Plan your week with this beautiful template',
        categoryId: categories.get('Templates'),
        type: ShopItemType.IN_APP_PURCHASE,
        price: 3.99,
        filePath: 'uploads/shop-items/weekly-schedule.zip',
        isActive: true,
        isFeatured: false,
        metadata: 'schedule,weekly,organization'
      },
      {
        itemNumber: 'TP-003',
        title: 'Simple Notes Template',
        description: 'A simple template for taking notes',
        categoryId: categories.get('Templates'),
        type: ShopItemType.FREE,
        price: 0,
        filePath: 'uploads/shop-items/simple-notes.zip',
        isActive: true,
        isFeatured: false,
        metadata: 'notes,simple,basic'
      },

      // Stickers items
      {
        itemNumber: 'ST-001',
        title: 'Emoji Sticker Pack',
        description: 'Express yourself with these emoji stickers',
        categoryId: categories.get('Stickers'),
        type: ShopItemType.IN_APP_PURCHASE,
        price: 2.99,
        filePath: 'uploads/shop-items/emoji-stickers.zip',
        isActive: true,
        isFeatured: true,
        metadata: 'emoji,emotions,fun'
      },
      {
        itemNumber: 'ST-002',
        title: 'Food Sticker Pack',
        description: 'Delicious food stickers for your diary',
        categoryId: categories.get('Stickers'),
        type: ShopItemType.IN_APP_PURCHASE,
        price: 1.99,
        filePath: 'uploads/shop-items/food-stickers.zip',
        isActive: true,
        isFeatured: false,
        metadata: 'food,snacks,drinks'
      },
      {
        itemNumber: 'ST-003',
        title: 'Basic Sticker Pack',
        description: 'Essential stickers for your diary',
        categoryId: categories.get('Stickers'),
        type: ShopItemType.FREE,
        price: 0,
        filePath: 'uploads/shop-items/basic-stickers.zip',
        isActive: true,
        isFeatured: false,
        metadata: 'basic,essential,simple'
      },
    ];

    // Seed each shop item
    for (const itemData of initialShopItems) {
      // Check if item already exists
      const existingItem = await this.shopItemRepository.findOne({
        where: { itemNumber: itemData.itemNumber }
      });

      if (!existingItem) {
        // Create new shop item
        try {
          const shopItem = this.shopItemRepository.create(itemData);
          const savedShopItem = await this.shopItemRepository.save(shopItem);
          this.logger.log(`Created shop item: ${savedShopItem.title} (${savedShopItem.itemNumber})`);
        } catch (error) {
          this.logger.error(`Error creating shop item ${itemData.itemNumber}: ${error.message}`);
        }
      } else {
        this.logger.log(`Shop item already exists: ${existingItem.title} (${existingItem.itemNumber})`);
      }
    }
  }
}
