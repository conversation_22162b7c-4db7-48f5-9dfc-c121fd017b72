import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID, IsBoolean } from 'class-validator';
import { NotificationType } from '../entities/notification.entity';
import { NotificationChannel, DeliveryStatus } from '../entities/notification-delivery.entity';

export class CreateNotificationDto {
  @ApiProperty({ description: 'User ID to send notification to' })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'Type of notification', enum: NotificationType })
  @IsEnum(NotificationType)
  @IsNotEmpty()
  type: NotificationType;

  @ApiProperty({ description: 'Notification title' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Notification message' })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({ description: 'Related entity ID (optional)', required: false })
  @IsString()
  @IsOptional()
  relatedEntityId?: string;

  @ApiProperty({ description: 'Related entity type (optional)', required: false })
  @IsString()
  @IsOptional()
  relatedEntityType?: string;

  @ApiProperty({ description: 'Channels to deliver notification through', enum: NotificationChannel, isArray: true, required: false })
  @IsEnum(NotificationChannel, { each: true })
  @IsOptional()
  channels?: NotificationChannel[];

  @ApiProperty({ description: 'HTML version of the message for rich notifications (optional)', required: false })
  @IsString()
  @IsOptional()
  htmlContent?: string;
}

export class NotificationResponseDto {
  @ApiProperty({ description: 'Notification ID' })
  id: string;

  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'Notification type', enum: NotificationType })
  type: NotificationType;

  @ApiProperty({ description: 'Notification title' })
  title: string;

  @ApiProperty({ description: 'Notification message' })
  message: string;

  @ApiProperty({ description: 'Related entity ID', required: false })
  relatedEntityId?: string;

  @ApiProperty({ description: 'Related entity type', required: false })
  relatedEntityType?: string;

  @ApiProperty({ description: 'Whether the notification has been read' })
  isRead: boolean;

  @ApiProperty({ description: 'When the notification was read', required: false })
  readAt?: Date;

  @ApiProperty({ description: 'When the notification was created' })
  createdAt: Date;

  @ApiProperty({ description: 'Delivery status for each channel', required: false })
  deliveryStatus?: DeliveryStatusDto[];
}

export class DeliveryStatusDto {
  @ApiProperty({ description: 'Delivery channel', enum: NotificationChannel })
  channel: NotificationChannel;

  @ApiProperty({ description: 'Delivery status', enum: DeliveryStatus })
  status: DeliveryStatus;

  @ApiProperty({ description: 'When the notification was sent', required: false })
  sentAt?: Date;
}

export class MarkNotificationReadDto {
  @ApiProperty({ description: 'Whether to mark as read or unread' })
  @IsBoolean()
  isRead: boolean;
}

export class NotificationPreferenceDto {
  @ApiProperty({ description: 'Notification type', enum: NotificationType })
  @IsEnum(NotificationType)
  @IsNotEmpty()
  notificationType: NotificationType;

  @ApiProperty({ description: 'Notification channel', enum: NotificationChannel })
  @IsEnum(NotificationChannel)
  @IsNotEmpty()
  channel: NotificationChannel;

  @ApiProperty({ description: 'Whether notifications of this type are enabled for this channel' })
  @IsBoolean()
  @IsNotEmpty()
  isEnabled: boolean;
}

export class UpdateNotificationPreferenceDto {
  @ApiProperty({ description: 'Whether notifications of this type are enabled for this channel' })
  @IsBoolean()
  @IsNotEmpty()
  isEnabled: boolean;
}

export class NotificationFilterDto {
  @ApiProperty({ description: 'Filter by notification type', enum: NotificationType, required: false })
  @IsEnum(NotificationType)
  @IsOptional()
  type?: NotificationType;

  @ApiProperty({ description: 'Filter by read status', required: false })
  @IsBoolean()
  @IsOptional()
  isRead?: boolean;
}
