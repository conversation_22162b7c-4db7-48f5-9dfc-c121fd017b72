import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { StoryMakerSubmission } from './story-maker-submission.entity';

@Entity()
export class StoryMakerEvaluation extends AuditableBaseEntity {
  @Column({ name: 'submission_id', type: 'uuid' })
  submissionId: string;

  @Column({ name: 'tutor_id', type: 'uuid' })
  tutorId: string;

  @Column({ name: 'corrections', type: 'text', nullable: true })
  corrections: string;

  @Column({ name: 'feedback', type: 'text', nullable: true })
  feedback: string;

  @Column({ name: 'evaluated_at' })
  evaluatedAt: Date;

  // Relationships
  @ManyToOne(() => StoryMakerSubmission, (submission) => submission.evaluations)
  @JoinColumn({ name: 'submission_id' })
  submission: StoryMakerSubmission;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;
}
