import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Award, AwardModule, AwardCriteria, AwardFrequency } from '../../database/entities/award.entity';

@Injectable()
export class HecDiaryAwardsSeed {
  private readonly logger = new Logger(HecDiaryAwardsSeed.name);

  constructor(
    @InjectRepository(Award)
    private awardRepository: Repository<Award>,
  ) {}

  async seed(): Promise<void> {
    this.logger.log('Seeding HEC Diary Awards...');

    // Define the real HEC Diary Awards as requested by client
    const hecDiaryAwards = [
      // Monthly Awards
      {
        name: 'Best Writer Award',
        description: 'Awarded monthly to the student with exceptional writing quality and consistency in diary entries',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minScore: 80,
          entriesRequired: 10,
          targetEntries: 20,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Designer Award',
        description: 'Awarded monthly to the student with the most creative and engaging diary designs',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_DECORATION],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minStudentLikes: 15,        // Peer appreciation (students liking designs)
          minDesignVariety: 3,        // At least 3 different skins used
          minSkinChanges: 2,          // At least 2 skin changes (experimentation)
          minDecorationScore: 50,     // Minimum overall decoration score
          maxWinners: 1,
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded monthly to the student who excels in all aspects of diary keeping - writing, attendance, and design',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE, AwardCriteria.ATTENDANCE, AwardCriteria.DIARY_DECORATION],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 200,
        isActive: true,
        criteriaConfig: {
          minScore: 75,
          entriesRequired: 15,
          daysRequired: 20,
          minStudentLikes: 10,        // Peer appreciation for design
          minDesignVariety: 2,        // Some design variety
          minDecorationScore: 40,     // Moderate decoration score
          maxWinners: 1,
        },
      },
      {
        name: 'Best Friendship Award',
        description: 'Awarded monthly to the student with the highest social engagement and friendship activities in the diary module',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_FRIENDSHIP],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minFriendships: 3,
          minShares: 5,
          minLikesGiven: 10,
          minLikesReceived: 5,
          minFriendshipScore: 60,
          maxWinners: 1,
        },
      },

      // Annual Awards
      {
        name: 'Best Writer Award',
        description: 'Awarded annually to the student with exceptional writing quality and consistency throughout the year',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 500,
        isActive: true,
        criteriaConfig: {
          minScore: 85,
          entriesRequired: 100,
          targetEntries: 200,
          maxWinners: 1,
        },
      },
      {
        name: 'Best Designer Award',
        description: 'Awarded annually to the student with the most creative and engaging diary designs throughout the year',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_DECORATION],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 500,
        isActive: true,
        criteriaConfig: {
          minStudentLikes: 150,       // Higher peer appreciation for annual
          minDesignVariety: 8,        // More design variety over the year
          minSkinChanges: 10,         // More experimentation over the year
          minDecorationScore: 70,     // Higher overall decoration score
          maxWinners: 1,
        },
      },
      {
        name: 'Best Perfect Award',
        description: 'Awarded annually to the student who excels in all aspects of diary keeping throughout the year',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE, AwardCriteria.ATTENDANCE, AwardCriteria.DIARY_DECORATION],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 750,
        isActive: true,
        criteriaConfig: {
          minScore: 80,
          entriesRequired: 150,
          daysRequired: 250,
          minStudentLikes: 100,       // Peer appreciation for design
          minDesignVariety: 5,        // Good design variety over the year
          minDecorationScore: 60,     // Good overall decoration score
          maxWinners: 1,
        },
      },
      {
        name: 'Best Friendship Award',
        description: 'Awarded annually to the student with the highest social engagement and friendship activities throughout the year',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_FRIENDSHIP],
        frequency: AwardFrequency.YEARLY,
        rewardPoints: 500,
        isActive: true,
        criteriaConfig: {
          minFriendships: 5,
          minShares: 50,
          minLikesGiven: 100,
          minLikesReceived: 50,
          minFriendshipScore: 70,
          maxWinners: 1,
        },
      },
    ];

    // Check and create awards
    for (const awardData of hecDiaryAwards) {
      try {
        // Check if award already exists (by name, module, and frequency)
        const existingAward = await this.awardRepository.findOne({
          where: {
            name: awardData.name,
            module: awardData.module,
            frequency: awardData.frequency,
          },
        });

        if (existingAward) {
          this.logger.log(`Award "${awardData.name}" (${awardData.frequency}) already exists, skipping...`);
          continue;
        }

        // Create new award
        const award = this.awardRepository.create(awardData);
        await this.awardRepository.save(award);

        this.logger.log(`Created award: "${awardData.name}" (${awardData.frequency})`);
      } catch (error) {
        this.logger.error(`Error creating award "${awardData.name}": ${error.message}`);
      }
    }

    this.logger.log('HEC Diary Awards seeding completed');
  }
}
