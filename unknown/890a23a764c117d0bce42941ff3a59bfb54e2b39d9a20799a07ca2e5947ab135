import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, Index, OneToOne } from "typeorm";
import { AuditableBaseEntity } from "./base-entity";
import { IsUUID } from "class-validator";
import { EssayTaskSubmissions } from "./essay-task-submissions.entity";
import type { EssayTaskSubmissionMarking } from "./essay-task-submission-marking.entity";

@Entity()
@Index(["submission", "sequenceNumber"])
@Index(["submissionDate"])
export class EssayTaskSubmissionHistory extends AuditableBaseEntity {
  @Column({
    name: "content",
    type: "text"
  })
  content: string;

  @Column({
    name: "word_count",
    type: "int"
  })
  wordCount: number;

  @Column({
    name: "submission_date",
    type: "timestamp",
  })
  submissionDate: Date;

  @Column({
    name: "sequence_number",
    type: "int",
  })
  sequenceNumber: number;

  @Column({
    name: "meta_data",
    type: "json",
    nullable: true
  })
  metaData?: {
    browserInfo?: string;
    ipAddress?: string;
    submissionAttempts?: number;
    lastDraftSavedAt?: Date;
    timeSpent?: number;
    wordCountDiff?: number;
    contentChanges?: {
      paragraphsAdded?: number,
      paragraphsRemoved?: number,
      significantChanges?: boolean
    };
    reviewerNotes?: string;
    aiDetectionScore?: number;
  };

  @ManyToOne(() => EssayTaskSubmissions, submission => submission.submissionHistory, {nullable: true})
  @JoinColumn({ name: "submission" })
  submission: EssayTaskSubmissions;

  @Column({
    name: "submission_id",
    type: "uuid",
    nullable: false
  })
  @IsUUID()
  submissionId: string;

  @Column({
    name: "previous_revision_id",
    type: "uuid",
    nullable: true
  })
  previousRevisionId?: string;

  @OneToOne('EssayTaskSubmissionMarking', (marking: EssayTaskSubmissionMarking) => marking.submissionHistory)
  @JoinColumn({ name: "submission_mark_id" })
  submissionMark: EssayTaskSubmissionMarking;
}