FROM node:20-alpine

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies and global packages
RUN npm install
RUN npm install -g cross-env
RUN npm install -D tsconfig-paths

# Make sure bcryptjs is installed
RUN npm list bcryptjs || npm install bcryptjs

# Copy the rest of the application
COPY . .

# Create logs and uploads directories
RUN mkdir -p /app/logs /app/uploads /app/uploads/profile-pictures && \
    chmod -R 777 /app/logs /app/uploads

# Expose the port 3010
EXPOSE 3012

# Set environment variables
ENV NODE_ENV=production
ENV NODE_OPTIONS=--max-old-space-size=4096

# Start the application
CMD ["npm", "run", "start"]
