import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus, Inject } from '@nestjs/common';
import { Response } from 'express';
import LoggerService from '../services/logger.service';

// Define Request interface with all required properties
interface Request {
  user?: { id: string; [key: string]: any };
  method: string;
  url: string;
  headers: any;
  body: any;
  params: any;
  query: any;
}

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(@Inject(LoggerService) private readonly logger: LoggerService) {}

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const errorResponse = exception.getResponse();

    // Extract error message and details
    let message = 'An error occurred';
    let error: any = null;
    let errorDetail: string = '';
    let validationErrors: any = null;

    if (typeof errorResponse === 'string') {
      message = errorResponse;
      errorDetail = errorResponse;
    } else if (typeof errorResponse === 'object') {
      const errorObj = errorResponse as any;
      message = errorObj.message || message;

      // Handle conflict errors (409)
      if (status === HttpStatus.CONFLICT) {
        // Handle our custom ConflictException format
        if (errorObj.validationErrors) {
          // Use the validationErrors directly from the exception
          validationErrors = errorObj.validationErrors;
          error = {
            type: 'ConflictException',
            status: HttpStatus.CONFLICT
          };
        } else if (errorObj.error && errorObj.field) {
          // Legacy format support
          validationErrors = {};
          validationErrors[errorObj.field] = [errorObj.error];
          error = {
            type: 'ConflictException',
            field: errorObj.field,
            message: errorObj.error
          };
        } else {
          // Generic conflict error
          error = errorObj.error || (Array.isArray(errorObj.message) ? errorObj.message : null);
        }
      }
      // Handle validation errors specifically
      else if (status === HttpStatus.BAD_REQUEST && errorObj.error) {
        if (errorObj.error.type === 'ValidationError' && errorObj.error.details) {
          // This is a validation error from our ValidationPipe
          validationErrors = errorObj.error.details;
          error = {
            type: 'ValidationError',
            details: validationErrors
          };
        } else if (Array.isArray(errorObj.message) && errorObj.message.length > 0) {
          // This is a validation error from class-validator
          validationErrors = {};
          errorObj.message.forEach((msg: any) => {
            if (typeof msg === 'string' && msg.includes(':')) {
              const [field, errorMsg] = msg.split(':', 2);
              if (!validationErrors[field.trim()]) {
                validationErrors[field.trim()] = [];
              }
              validationErrors[field.trim()].push(errorMsg.trim());
            } else {
              if (!validationErrors['general']) {
                validationErrors['general'] = [];
              }
              validationErrors['general'].push(msg);
            }
          });
          error = {
            type: 'ValidationError',
            details: validationErrors
          };
          message = 'Validation failed';
        } else {
          // Other types of errors
          error = errorObj.error || (Array.isArray(errorObj.message) ? errorObj.message : null);
        }
      } else {
        // Non-validation errors
        error = errorObj.error || (Array.isArray(errorObj.message) ? errorObj.message : null);
      }

      errorDetail = JSON.stringify(errorResponse);
    }

    // Create a unique error reference ID for tracking
    const errorRefId = this.generateErrorRefId();

    // Get user information if available
    const userId = request.user?.id || 'anonymous';

    // Log the error with more context
    this.logger.error(
      `[ERROR-REF:${errorRefId}] [${request.method}] ${request.url} - User: ${userId} - Status: ${status} - ${message}\n${errorDetail}`
    );

    // Format the response according to our API response model
    const responseBody: any = {
      success: false,
      message,
      data: null, // Always include data field, even if null
      error: {
        type: exception.name,
        status,
        refId: errorRefId
      },
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    // Add validation errors if present
    if (validationErrors) {
      responseBody.validationErrors = validationErrors;
    } else if (error) {
      // For conflict errors, include the field in the response
      if (status === HttpStatus.CONFLICT && error.field) {
        responseBody.field = error.field;
      }
      responseBody.error.detail = error;
    }

    response.status(status).json(responseBody);
  }

  /**
   * Generate a unique error reference ID for tracking
   */
  private generateErrorRefId(): string {
    return `ERR-${Date.now().toString(36)}-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`.toUpperCase();
  }
}
