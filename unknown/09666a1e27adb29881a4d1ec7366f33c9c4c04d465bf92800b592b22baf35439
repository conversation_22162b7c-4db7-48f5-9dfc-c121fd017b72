import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtService } from '@nestjs/jwt';
import { CurrentUserService } from '../services/current-user.service';

/**
 * Middleware to extract the user ID from JWT token and set it in the CurrentUserService
 */
@Injectable()
export class CurrentUserMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CurrentUserMiddleware.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly currentUserService: CurrentUserService,
  ) {}

  use(req: Request, res: Response, next: NextFunction) {
    // Extract the token from the Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      // No token, continue without setting user
      return next();
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    try {
      // Verify and decode the token
      const decoded = this.jwtService.verify(token);
      const userId = decoded.id || decoded.sub;

      if (userId) {
        // Run the rest of the request with the current user context
        this.currentUserService.runWithUser(userId, () => {
          next();
        });
      } else {
        next();
      }
    } catch (error) {
      // Token is invalid, continue without setting user
      this.logger.debug(`Invalid JWT token: ${error.message}`);
      next();
    }
  }
}
