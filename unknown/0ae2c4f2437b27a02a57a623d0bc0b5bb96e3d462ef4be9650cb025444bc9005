const { exec } = require('child_process');

// First compile the TypeScript code
exec('npx tsc src/scripts/add-modules-to-plans.ts --outDir dist', (error, stdout, stderr) => {
  if (error) {
    console.error(`Error compiling script: ${error.message}`);
    return;
  }
  
  if (stderr) {
    console.error(`Compilation stderr: ${stderr}`);
  }
  
  console.log(`Compilation stdout: ${stdout}`);
  
  // Then run the compiled JavaScript
  exec('node dist/scripts/add-modules-to-plans.js', (error, stdout, stderr) => {
    if (error) {
      console.error(`Error running script: ${error.message}`);
      return;
    }
    
    if (stderr) {
      console.error(`Execution stderr: ${stderr}`);
    }
    
    console.log(`Execution stdout: ${stdout}`);
  });
});
