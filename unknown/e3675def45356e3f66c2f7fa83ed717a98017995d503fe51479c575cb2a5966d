import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponse } from '../models/api-response.model';

/**
 * Interceptor to standardize all API responses
 */
@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
    const request = context.switchToHttp().getRequest();
    const path = request.url;

    return next.handle().pipe(
      map(data => {
        // If the response is already in the ApiResponse format, return it as is
        if (data && typeof data === 'object' && 'success' in data && 'message' in data) {
          // Ensure timestamp is present
          if (!data.timestamp) {
            data.timestamp = new Date().toISOString();
          }
          
          // Ensure path is present
          if (!data.path) {
            data.path = path;
          }
          
          return data;
        }
        
        // Otherwise, wrap the response in the ApiResponse format
        return {
          success: true,
          message: 'Operation completed successfully',
          data,
          timestamp: new Date().toISOString(),
          path
        };
      }),
    );
  }
}
