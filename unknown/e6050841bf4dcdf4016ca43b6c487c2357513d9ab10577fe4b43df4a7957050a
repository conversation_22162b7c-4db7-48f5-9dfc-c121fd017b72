# Diary Award Calculation Algorithm

## Overview
The diary award system calculates and distributes awards based on student performance across multiple criteria. Awards are generated for different time periods (weekly, monthly, quarterly) and can have multiple winners based on configuration.

## Award Criteria
The system evaluates three main criteria:

### 1. Diary Score (AwardCriteria.DIARY_SCORE)
Combines quality and quantity metrics:

- **Quality Score (60% weight)**
  - Based on average entry score
  - Score range: 0-100
  - Must meet minimum score requirement configured in `minScore`
  - Must have minimum required entries configured in `entriesRequired`

- **Quantity Score (40% weight)**
  - Based on number of entries relative to target
  - Score range: 0-100
  - Bonus points for exceeding target entries (up to 200% of target)
  - Formula: `Math.min(entriesCount / targetEntries * 50, 100)`

- **Combined Score Formula**:
  ```
  combinedScore = (qualityScore * 0.6) + (quantityScore * 0.4)
  ```

### 2. Attendance (AwardCriteria.ATTENDANCE)
Evaluates student attendance:

- Must meet minimum required attendance days (`daysRequired`)
- Score is calculated as ratio of actual attendance to required days
- Formula: `score = Math.min(totalAttendance / daysRequired, 1) * 100`
- Maximum score: 100

### 3. Diary Decoration (AwardCriteria.DIARY_DECORATION)
Based on engagement through likes:

- Must meet minimum required likes (`minLikes`)
- Score is calculated as ratio of actual likes to required likes
- Formula: `score = Math.min(totalLikes / minLikes, 1) * 100`
- Maximum score: 100

## Award Generation Process

1. **Period Determination**
   - Weekly: 7 days
   - Monthly: 31 days
   - Quarterly: 93 days
   - Period is determined automatically based on date range

2. **Eligibility Check**
   - System checks if awards already exist for the period
   - Retrieves all confirmed diary entries within period
   - Filters entries with score >= 1

3. **Scoring Process**
   For each student:
   1. Calculate scores for each criterion
   2. Check minimum requirements for each criterion
   3. If any criterion fails minimum requirements, student is disqualified
   4. Final score is average of all criteria scores

4. **Winner Selection**
   - Qualified students are sorted by total score
   - Top performers are selected based on `maxWinners` configuration
   - Multiple winners possible if configured

## Award Creation

For each winner, the system creates an award record with:
- User ID
- Award ID
- Award date
- Detailed metrics including:
  - Average entry score
  - Number of confirmed entries
  - Attendance days
  - Total likes
  - Combined score
  - Period details

## Error Handling

- System logs all award generation attempts
- Failures are logged with detailed error messages
- Process continues even if individual award creation fails
- Duplicate awards for same period are prevented

## Code Example

Here's a simplified example of how the scoring works:

```typescript
// Quality score (60% weight)
const qualityScore = Math.min(avgEntryScore, 100);

// Quantity score (40% weight)
const quantityRatio = Math.min(entriesCount / targetEntries, 2);
const quantityScore = Math.min(quantityRatio * 50, 100);

// Combined score
const combinedScore = (qualityScore * 0.6) + (quantityScore * 0.4);

// Final score is average of all criteria
finalScore = allCriteriaScores.length > 0 
  ? allCriteriaScores.reduce((sum, score) => sum + score, 0) / allCriteriaScores.length 
  : 0;
```

## Implementation Notes

1. Award generation is idempotent - won't create duplicate awards for same period
2. All scores are normalized to 0-100 range for consistency
3. System supports flexible award criteria configuration through the award entity
4. Performance metrics are stored in award metadata for future reference
