import { <PERSON><PERSON><PERSON>, Column, OneToMany, ManyToOne, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { ShopItem } from './shop-item.entity';

@Entity()
export class ShopCategory extends AuditableBaseEntity {
  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'parent_id', nullable: true })
  parentId: string;

  @ManyToOne(() => ShopCategory, category => category.children)
  @JoinColumn({ name: 'parent_id' })
  parent: ShopCategory;

  @OneToMany(() => ShopCategory, category => category.parent)
  children: ShopCategory[];

  @Column({ name: 'display_order', default: 0 })
  displayOrder: number;

  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @OneToMany(() => ShopItem, shopItem => shopItem.category)
  shopItems: ShopItem[];
}
