import { applyDecorators, Type } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, ApiResponse as SwaggerApiResponse, getSchemaPath } from '@nestjs/swagger';
import { ApiResponse } from '../dto/api-response.dto';
import { PagedListDto } from '../models/paged-list.dto';

/**
 * Decorator for documenting successful API responses with the unified ApiResponse structure
 * @param dataDto The DTO class for the response data
 * @param description Description of the response
 * @returns Decorator
 */
export const ApiOkResponseWithType = <TModel extends Type<any>>(
  dataDto: TModel,
  description = 'Successful operation'
) => {
  return applyDecorators(
    ApiExtraModels(ApiResponse, dataDto),
    ApiOkResponse({
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(ApiResponse) },
          {
            properties: {
              data: { $ref: getSchemaPath(dataDto) },
              success: { example: true },
              statusCode: { example: 200 },
            },
          },
        ],
      },
    }),
  );
};

/**
 * Decorator for documenting successful API responses with array data
 * @param dataDto The DTO class for array items
 * @param description Description of the response
 * @returns Decorator
 */
export const ApiOkResponseWithArrayType = <TModel extends Type<any>>(
  dataDto: TModel,
  description = 'Successful operation'
) => {
  return applyDecorators(
    ApiExtraModels(ApiResponse, dataDto),
    ApiOkResponse({
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(ApiResponse) },
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(dataDto) },
              },
              success: { example: true },
              statusCode: { example: 200 },
            },
          },
        ],
      },
    }),
  );
};

/**
 * Decorator for documenting API responses with paged list data
 * @param dataDto The DTO class for list items
 * @param description Description of the response
 * @returns Decorator
 */
export const ApiOkResponseWithPagedListType = <TModel extends Type<any>>(
  dataDto: TModel,
  description = 'Successful operation'
) => {
  return applyDecorators(
    ApiExtraModels(ApiResponse, PagedListDto, dataDto),
    ApiOkResponse({
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(ApiResponse) },
          {
            properties: {
              data: {
                allOf: [
                  { $ref: getSchemaPath(PagedListDto) },
                  {
                    properties: {
                      items: {
                        type: 'array',
                        items: { $ref: getSchemaPath(dataDto) },
                      }
                    }
                  }
                ]
              },
              success: { example: true },
              statusCode: { example: 200 },
            },
          },
        ],
      },
    }),
  );
};

/**
 * Decorator for documenting error API responses
 * @param status HTTP status code
 * @param description Description of the error
 * @param errorExample Example error message
 * @returns Decorator
 */
export const ApiErrorResponse = (
  status: number,
  description: string,
  errorExample: string | string[] = 'An error occurred'
) => {
  const errors = Array.isArray(errorExample)
    ? errorExample.map(error => ({ error }))
    : [{ error: errorExample }];

  return SwaggerApiResponse({
    status,
    description,
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponse) },
        {
          properties: {
            success: { example: false },
            message: { example: description },
            statusCode: { example: status },
            errors: {
              type: 'array',
              example: errors
            },
          },
        },
      ],
    },
  });
};
