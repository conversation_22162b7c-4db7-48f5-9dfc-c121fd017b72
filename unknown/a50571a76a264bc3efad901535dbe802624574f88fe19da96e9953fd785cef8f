import { Injectable, Logger, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { StoryMaker } from '../../../database/entities/story-maker.entity';
import { StoryMakerParticipation } from '../../../database/entities/story-maker-participation.entity';
import { StoryMakerSubmission } from '../../../database/entities/story-maker-submission.entity';
import { StoryMakerEvaluation } from '../../../database/entities/story-maker-evaluation.entity';
import { User } from '../../../database/entities/user.entity';
import { NotificationHelperService } from '../../../modules/notification/notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';
import { MappingStatus } from '../../../database/entities/student-tutor-mapping.entity';

@Injectable()
export class StoryMakerTutorService {
  private readonly logger = new Logger(StoryMakerTutorService.name);

  constructor(
    @InjectRepository(StoryMaker)
    private readonly storyMakerRepository: Repository<StoryMaker>,
    @InjectRepository(StoryMakerParticipation)
    private readonly participationRepository: Repository<StoryMakerParticipation>,
    @InjectRepository(StoryMakerSubmission)
    private readonly submissionRepository: Repository<StoryMakerSubmission>,
    @InjectRepository(StoryMakerEvaluation)
    private readonly evaluationRepository: Repository<StoryMakerEvaluation>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    private readonly notificationHelper: NotificationHelperService,
  ) {}

  /**
   * Get submissions waiting for evaluation by a specific tutor
   * @param tutorId The ID of the tutor
   * @param query Query parameters for pagination and filtering
   * @returns List of submissions waiting for evaluation
   */
  async getSubmissionsForTutor(tutorId: string, query: { page?: number; limit?: number; search?: string; isEvaluated?: boolean }): Promise<any> {
    try {
      const { page = 1, limit = 10, search, isEvaluated } = query;
      const skip = (page - 1) * limit;

      // Get the student-tutor mappings for this tutor
      const studentTutorMappings = await this.dataSource
        .getRepository('student_tutor_mapping')
        .createQueryBuilder('mapping')
        .where('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
        .getMany();

      if (!studentTutorMappings || studentTutorMappings.length === 0) {
        return {
          submissions: [],
          total_count: 0,
        };
      }

      // Get the student IDs from the mappings
      const studentIds = studentTutorMappings.map((mapping) => mapping.studentId);

      // Create query builder for submissions
      const queryBuilder = this.submissionRepository
        .createQueryBuilder('submission')
        .innerJoinAndSelect('submission.participation', 'participation')
        .innerJoinAndSelect('participation.storyMaker', 'storyMaker')
        .innerJoinAndSelect('participation.student', 'student')
        .leftJoinAndSelect('submission.evaluations', 'evaluation')
        .where('participation.studentId IN (:...studentIds)', { studentIds });

      // Apply filters
      if (isEvaluated !== undefined) {
        queryBuilder.andWhere('submission.isEvaluated = :isEvaluated', { isEvaluated });
      }

      if (search) {
        queryBuilder.andWhere('(student.name ILIKE :search OR student.email ILIKE :search OR storyMaker.title ILIKE :search)', { search: `%${search}%` });
      }

      // Add select fields for additional information
      queryBuilder.addSelect('student.name', 'student_name').addSelect('student.email', 'student_email').addSelect('student.profilePicture', 'student_profile_picture');

      // Order by submission date (newest first)
      queryBuilder.orderBy('submission.submittedAt', 'DESC');

      // Apply pagination
      queryBuilder.skip(skip).take(limit);

      // Execute query
      const [submissions, totalCount] = await queryBuilder.getManyAndCount();

      // Map to DTOs
      const submissionDtos = submissions.map((submission) => {
        const isFirstSubmission = submission.participation.firstSubmittedAt.getTime() === submission.submittedAt.getTime();

        return {
          id: submission.id,
          content: submission.content,
          submitted_at: submission.submittedAt,
          is_evaluated: submission.isEvaluated,
          is_first_submission: isFirstSubmission,
          student: {
            id: submission.participation.studentId,
            name: submission.participation.student.name,
            email: submission.participation.student.email,
            profile_picture: submission.participation.student.profilePicture,
          },
          story_maker: {
            id: submission.participation.storyMakerId,
            title: submission.participation.storyMaker.title,
          },
          participation: {
            id: submission.participation.id,
            first_submitted_at: submission.participation.firstSubmittedAt,
            is_evaluated: submission.participation.isEvaluated,
            score: submission.participation.score,
          },
        };
      });

      return {
        submissions: submissionDtos,
        total_count: totalCount,
      };
    } catch (error) {
      this.logger.error(`Failed to get submissions for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get submissions');
    }
  }

  /**
   * Get a specific submission by ID
   * @param submissionId The ID of the submission to retrieve
   * @param tutorId The ID of the tutor requesting the submission
   * @returns The submission with details
   */
  async getSubmissionById(submissionId: string, tutorId: string): Promise<any> {
    try {
      // Get the submission with its participation, story maker, and student
      const submission = await this.submissionRepository.findOne({
        where: { id: submissionId },
        relations: ['participation', 'participation.storyMaker', 'participation.student', 'evaluations', 'evaluations.tutor'],
      });

      if (!submission) {
        throw new NotFoundException(`Submission with ID ${submissionId} not found`);
      }

      // Verify that the tutor is assigned to this student
      const studentTutorMapping = await this.dataSource
        .getRepository('student_tutor_mapping')
        .createQueryBuilder('mapping')
        .where('mapping.tutorId = :tutorId', { tutorId })
        .andWhere('mapping.studentId = :studentId', { studentId: submission.participation.studentId })
        .andWhere('mapping.status = :status', { status: MappingStatus.ACTIVE })
        .getOne();

      if (!studentTutorMapping) {
        throw new ForbiddenException('You are not authorized to view this submission');
      }

      // Check if this is the first submission for this participation
      const isFirstSubmission = await this.isFirstSubmission(submission);

      // Map to response DTO
      return {
        id: submission.id,
        content: submission.content,
        submitted_at: submission.submittedAt,
        is_evaluated: submission.isEvaluated,
        is_first_submission: isFirstSubmission,
        student: {
          id: submission.participation.studentId,
          name: submission.participation.student.name,
          email: submission.participation.student.email,
          profile_picture: submission.participation.student.profilePicture,
        },
        story_maker: {
          id: submission.participation.storyMakerId,
          title: submission.participation.storyMaker.title,
          instruction: submission.participation.storyMaker.instruction,
          score: submission.participation.storyMaker.score,
        },
        participation: {
          id: submission.participation.id,
          first_submitted_at: submission.participation.firstSubmittedAt,
          is_evaluated: submission.participation.isEvaluated,
          score: submission.participation.score,
        },
        evaluation:
          submission.evaluations && submission.evaluations.length > 0
            ? {
                id: submission.evaluations[0].id,
                corrections: submission.evaluations[0].corrections,
                feedback: submission.evaluations[0].feedback,
                evaluated_at: submission.evaluations[0].evaluatedAt,
                tutor_id: submission.evaluations[0].tutorId,
                tutor_name: submission.evaluations[0].tutor?.name,
                tutor_profile_picture: submission.evaluations[0].tutor?.profilePicture,
              }
            : null,
      };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`Failed to get submission ${submissionId} for tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get submission');
    }
  }

  /**
   * Helper method to check if a submission is the first one for its participation
   * @param submission The submission to check
   * @returns True if this is the first submission, false otherwise
   */
  private async isFirstSubmission(submission: StoryMakerSubmission): Promise<boolean> {
    // Get all submissions for this participation, ordered by submission date
    const submissions = await this.submissionRepository.find({
      where: { participationId: submission.participationId },
      order: { submittedAt: 'ASC' },
    });

    // If there's only one submission or this is the earliest one, it's the first submission
    return submissions.length === 1 || submissions[0].id === submission.id;
  }

  /**
   * Evaluate a story submission
   * @param submissionId The ID of the submission to evaluate
   * @param tutorId The ID of the tutor evaluating the submission
   * @param corrections The corrections to the submission (optional)
   * @param feedback The feedback on the submission (optional)
   * @param score The score for the submission (required for first submission)
   * @returns The updated submission with evaluation
   */
  async evaluateSubmission(submissionId: string, tutorId: string, corrections: string, feedback?: string, score?: number): Promise<any> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the submission with its participation and story maker
      const submission = await this.submissionRepository.findOne({
        where: { id: submissionId },
        relations: ['participation', 'participation.submissions', 'participation.storyMaker'],
      });

      if (!submission) {
        throw new NotFoundException(`Submission with ID ${submissionId} not found`);
      }

      // Check if the submission is already evaluated
      if (submission.isEvaluated) {
        throw new BadRequestException('This submission has already been evaluated');
      }

      // Get the participation
      const participation = submission.participation;

      // Check if this is the first submission for this participation
      const isFirstSubmission = participation.submissions.length === 1 || participation.submissions.every((s) => s.id === submissionId || s.submittedAt > submission.submittedAt);

      // If this is the first submission, score is required
      if (isFirstSubmission && (score === undefined || score === null)) {
        throw new BadRequestException('Score is required for the first submission');
      }

      // Create the evaluation
      const evaluation = queryRunner.manager.create(StoryMakerEvaluation, {
        submissionId,
        tutorId,
        corrections,
        feedback,
        evaluatedAt: new Date(),
      });

      // Save the evaluation
      await queryRunner.manager.save(evaluation);

      // Update the submission
      submission.isEvaluated = true;
      await queryRunner.manager.save(submission);

      // If this is the first submission, update the participation with the score
      if (isFirstSubmission) {
        participation.score = score;
        participation.isEvaluated = true;
        participation.evaluatedAt = new Date();
        participation.evaluatedBy = tutorId;
        await queryRunner.manager.save(participation);
      }

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Send notification to the student
      try {
        // Get student and tutor details
        const student = await this.userRepository.findOne({
          where: { id: participation.studentId },
        });

        const tutor = await this.userRepository.findOne({
          where: { id: tutorId },
        });

        if (student) {
          // Create HTML content for notification
          const htmlContent = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #4a6ee0;">Your Story Has Been Evaluated</h2>
              <p>Your tutor has evaluated your story submission.</p>
              <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <p><strong>Story Title:</strong> ${participation.storyMaker.title}</p>
                <p><strong>Tutor:</strong> ${tutor?.name || 'Your tutor'}</p>
                ${isFirstSubmission ? `<p><strong>Score:</strong> ${score}</p>` : ''}
                ${feedback ? `<p><strong>Feedback:</strong> ${feedback}</p>` : ''}
              </div>
              <p>You can now view the evaluation and ${isFirstSubmission ? 'your score' : 'feedback'} in the story maker section.</p>
              ${isFirstSubmission ? '' : '<p>You can submit a new version of your story if you wish.</p>'}
              <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
                <p>This is an automated message from the HEC system.</p>
                <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
              </div>
            </div>
          `;

          // Notification options
          const notificationOptions = {
            relatedEntityId: submission.id,
            relatedEntityType: 'story_maker_submission',
            htmlContent: htmlContent,
            sendEmail: true,
            sendInApp: true,
            sendPush: true,
            sendMobile: false,
            sendSms: false,
            sendRealtime: true,
          };

          // Send notification to the student
          await this.notificationHelper.notify(
            student.id,
            NotificationType.STORY_REVIEW,
            'Your Story Has Been Evaluated',
            `Your tutor has evaluated your story submission for "${participation.storyMaker.title}".`,
            notificationOptions,
          );

          this.logger.log(`Notification sent to student ${student.id} (${student.name}) for story evaluation ${evaluation.id} of story "${participation.storyMaker.title}"`);
        }
      } catch (error) {
        // Just log the error but don't fail the evaluation
        this.logger.error(`Failed to send notification for story evaluation ${evaluation.id} of submission ${submissionId}: ${error.message}`, error.stack);
      }

      // Get the updated submission with evaluation
      const updatedSubmission = await this.submissionRepository.findOne({
        where: { id: submissionId },
        relations: ['participation', 'evaluations', 'evaluations.tutor'],
      });

      // Map to response DTO
      const tutor = updatedSubmission.evaluations[0]?.tutor;

      return {
        id: updatedSubmission.id,
        content: updatedSubmission.content,
        submitted_at: updatedSubmission.submittedAt,
        is_evaluated: updatedSubmission.isEvaluated,
        evaluation: {
          id: evaluation.id,
          corrections,
          feedback,
          evaluated_at: evaluation.evaluatedAt,
          tutor_id: tutorId,
          tutor_name: tutor?.name,
          tutor_profile_picture: tutor?.profilePicture,
        },
        participation: {
          id: participation.id,
          score: participation.score,
          is_evaluated: participation.isEvaluated,
          evaluated_at: participation.evaluatedAt,
        },
      };
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      this.logger.error(`Failed to evaluate submission ${submissionId} by tutor ${tutorId}: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to evaluate submission');
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }
}
