import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum, IsNumber, IsOptional, IsBoolean, IsDateString, IsArray, Min, Max, IsUUID, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';
import { DiscountType, PromotionType, PromotionApplicableType, PromotionStatus } from '../entities/promotion.entity';

/**
 * DTO for creating a new promotion
 */
export class CreatePromotionDto {
  @ApiProperty({
    example: 'Summer Sale',
    description: 'Name of the promotion'
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: 'Get 20% off on all items during summer',
    description: 'Description of the promotion'
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    example: PromotionType.PERCENTAGE,
    description: 'Type of promotion (percentage or fixed amount)',
    enum: PromotionType
  })
  @IsNotEmpty()
  @IsEnum(PromotionType)
  promotionType: PromotionType;

  @ApiProperty({
    example: DiscountType.PERCENTAGE,
    description: 'Type of discount',
    enum: DiscountType
  })
  @IsNotEmpty()
  @IsEnum(DiscountType)
  discountType: DiscountType;

  @ApiProperty({
    example: 20,
    description: 'Value of the discount (percentage or fixed amount)'
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  discountValue: number;



  @ApiProperty({
    example: PromotionApplicableType.SHOP_ITEM,
    description: 'Type of items the promotion applies to',
    enum: PromotionApplicableType
  })
  @IsNotEmpty()
  @IsEnum(PromotionApplicableType)
  applicableType: PromotionApplicableType;

  @ApiProperty({
    example: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001'],
    description: 'Category IDs the promotion applies to',
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  applicableCategoryIds?: string[];

  @ApiProperty({
    example: ['123e4567-e89b-12d3-a456-426614174002', '123e4567-e89b-12d3-a456-426614174003'],
    description: 'Plan IDs the promotion applies to',
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  applicablePlanIds?: string[];

  @ApiProperty({
    example: true,
    description: 'Whether the promotion is applied to a plan',
    required: false
  })
  @IsOptional()
  @IsBoolean()
  appliedToPlan?: boolean;

  @ApiProperty({
    example: 'SUMMER20',
    description: 'Promotion code for redemption',
    required: false
  })
  @IsOptional()
  @IsString()
  promotionCode?: string;

  @ApiProperty({
    example: '2023-06-01',
    description: 'Start date of the promotion (optional)',
    required: false
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    example: '2023-08-31',
    description: 'End date of the promotion (optional)',
    required: false
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the promotion is active',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    example: 100,
    description: 'Maximum number of times the promotion can be used',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  usageLimit?: number;

  @ApiProperty({
    example: 50,
    description: 'Minimum purchase amount required to apply the promotion',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  minimumPurchaseAmount?: number;

  @ApiProperty({
    example: 1000,
    description: 'Maximum purchase amount eligible for the promotion',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  maximumPurchaseAmount?: number;

  @ApiProperty({
    example: 100,
    description: 'Maximum discount amount that can be applied',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  maximumDiscountAmount?: number;
}

/**
 * DTO for updating a promotion
 */
export class UpdatePromotionDto {
  @ApiProperty({
    example: 'Summer Sale',
    description: 'Name of the promotion',
    required: false
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    example: 'Get 20% off on all items during summer',
    description: 'Description of the promotion',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: PromotionType.PERCENTAGE,
    description: 'Type of promotion (percentage or fixed amount)',
    enum: PromotionType,
    required: false
  })
  @IsOptional()
  @IsEnum(PromotionType)
  promotionType?: PromotionType;

  @ApiProperty({
    example: DiscountType.PERCENTAGE,
    description: 'Type of discount',
    enum: DiscountType,
    required: false
  })
  @IsOptional()
  @IsEnum(DiscountType)
  discountType?: DiscountType;

  @ApiProperty({
    example: 20,
    description: 'Value of the discount (percentage or fixed amount)',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  discountValue?: number;



  @ApiProperty({
    example: PromotionApplicableType.SHOP_ITEM,
    description: 'Type of items the promotion applies to',
    enum: PromotionApplicableType,
    required: false
  })
  @IsOptional()
  @IsEnum(PromotionApplicableType)
  applicableType?: PromotionApplicableType;

  @ApiProperty({
    example: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001'],
    description: 'Category IDs the promotion applies to',
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  applicableCategoryIds?: string[];

  @ApiProperty({
    example: ['123e4567-e89b-12d3-a456-426614174002', '123e4567-e89b-12d3-a456-426614174003'],
    description: 'Plan IDs the promotion applies to',
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  applicablePlanIds?: string[];

  @ApiProperty({
    example: true,
    description: 'Whether the promotion is applied to a plan',
    required: false
  })
  @IsOptional()
  @IsBoolean()
  appliedToPlan?: boolean;

  @ApiProperty({
    example: 'SUMMER20',
    description: 'Promotion code for redemption',
    required: false
  })
  @IsOptional()
  @IsString()
  promotionCode?: string;

  @ApiProperty({
    example: '2023-06-01',
    description: 'Start date of the promotion',
    required: false
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    example: '2023-08-31',
    description: 'End date of the promotion',
    required: false
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the promotion is active',
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    example: 100,
    description: 'Maximum number of times the promotion can be used',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  usageLimit?: number;

  @ApiProperty({
    example: 50,
    description: 'Minimum purchase amount required to apply the promotion',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  minimumPurchaseAmount?: number;

  @ApiProperty({
    example: 1000,
    description: 'Maximum purchase amount eligible for the promotion',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  maximumPurchaseAmount?: number;

  @ApiProperty({
    example: 100,
    description: 'Maximum discount amount that can be applied',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  maximumDiscountAmount?: number;
}

/**
 * DTO for promotion response
 */
export class PromotionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty({ enum: PromotionType })
  promotionType: PromotionType;

  @ApiProperty({ enum: DiscountType })
  discountType: DiscountType;

  @ApiProperty()
  discountValue: number;



  @ApiProperty({ enum: PromotionApplicableType })
  applicableType: PromotionApplicableType;

  @ApiProperty({ type: [String], required: false })
  applicableCategoryIds?: string[];

  @ApiProperty({ type: [String], required: false })
  applicablePlanIds?: string[];

  @ApiProperty({ required: false })
  appliedToPlan?: boolean;

  @ApiProperty({ required: false })
  promotionCode?: string;

  @ApiProperty()
  startDate: Date;

  @ApiProperty()
  endDate: Date;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty({ required: false })
  usageLimit?: number;

  @ApiProperty()
  usageCount: number;

  @ApiProperty({ required: false })
  minimumPurchaseAmount?: number;

  @ApiProperty({ required: false })
  maximumPurchaseAmount?: number;

  @ApiProperty({ required: false })
  maximumDiscountAmount?: number;

  @ApiProperty({ enum: PromotionStatus })
  status: PromotionStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

/**
 * DTO for applying a promotion code
 */
export class ApplyPromotionCodeDto {
  @ApiProperty({
    example: 'SUMMER20',
    description: 'Promotion code to apply'
  })
  @IsNotEmpty()
  @IsString()
  promotionCode: string;

  @ApiProperty({
    example: 100,
    description: 'Original price to calculate discount for'
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  originalPrice: number;

  @ApiProperty({
    example: PromotionApplicableType.SHOP_ITEM,
    description: 'Type of item the promotion is being applied to',
    enum: PromotionApplicableType
  })
  @IsNotEmpty()
  @IsEnum(PromotionApplicableType)
  itemType: PromotionApplicableType;

  @ApiProperty({
    example: 'graphics',
    description: 'Category of the item (for shop items)',
    required: false
  })
  @IsOptional()
  @IsString()
  @ValidateIf(o => o.itemType === PromotionApplicableType.SHOP_ITEM)
  category?: string;

  @ApiProperty({
    example: 'starter',
    description: 'Plan type (for subscription plans)',
    required: false
  })
  @IsOptional()
  @IsString()
  @ValidateIf(o => o.itemType === PromotionApplicableType.PLAN)
  planType?: string;
}

/**
 * DTO for promotion application response
 */
export class PromotionApplicationResponseDto {
  @ApiProperty()
  promotionId: string;

  @ApiProperty()
  promotionName: string;

  @ApiProperty()
  originalPrice: number;

  @ApiProperty()
  discountAmount: number;

  @ApiProperty()
  finalPrice: number;

  @ApiProperty({ enum: DiscountType })
  discountType: DiscountType;

  @ApiProperty()
  discountValue: number;

  @ApiProperty()
  isApplied: boolean;

  @ApiProperty({ required: false })
  message?: string;
}

/**
 * DTO for getting applicable promotions
 */
export class GetApplicablePromotionsDto {
  @ApiProperty({
    example: PromotionApplicableType.SHOP_ITEM,
    description: 'Type of item to get promotions for',
    enum: PromotionApplicableType
  })
  @IsNotEmpty()
  @IsEnum(PromotionApplicableType)
  itemType: PromotionApplicableType;

  @ApiProperty({
    example: 'graphics',
    description: 'Category of the item (for shop items)',
    required: false
  })
  @IsOptional()
  @IsString()
  @ValidateIf(o => o.itemType === PromotionApplicableType.SHOP_ITEM)
  category?: string;

  @ApiProperty({
    example: 'starter',
    description: 'Plan type (for subscription plans)',
    required: false
  })
  @IsOptional()
  @IsString()
  @ValidateIf(o => o.itemType === PromotionApplicableType.PLAN)
  planType?: string;
}
