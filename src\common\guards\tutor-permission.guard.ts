import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger, SetMetadata } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TutorPermission } from '../../database/entities/tutor-permission.entity';
import { UserType } from '../../database/entities/user.entity';
import { Messages } from '../../constants/messages';
import { JwtPayload } from '../../modules/auth/interfaces/jwt-payload.interface';

/**
 * Metadata key for the plan feature ID
 */
export const PLAN_FEATURE_KEY = 'planFeatureId';

/**
 * Decorator to specify which plan feature to check permissions for
 * @param planFeatureId The ID of the plan feature to check permissions for
 */
export const RequireFeaturePermission = (planFeatureId: string) => SetMetadata(PLAN_FEATURE_KEY, planFeatureId);

/**
 * Guard that checks if a user has permission to manage a specific plan feature
 * Allows admins to access all endpoints
 * Allows tutors to access endpoints if they have permission for the specified plan feature
 */
@Injectable()
export class TutorPermissionGuard implements CanActivate {
  private readonly logger = new Logger(TutorPermissionGuard.name);

  constructor(
    private reflector: Reflector,
    @InjectRepository(TutorPermission)
    private tutorPermissionRepository: Repository<TutorPermission>
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const planFeatureId = this.reflector.get<string>(PLAN_FEATURE_KEY, context.getHandler());
    
    if (!planFeatureId) {
      this.logger.warn(`No plan feature ID specified for permission check - ${context.getClass().name}.${context.getHandler().name}`);
      throw new ForbiddenException('No plan feature ID specified for permission check');
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user as JwtPayload;

    // If no user is present in the request, deny access
    if (!user) {
      this.logger.warn(`Permission check failed: No user in request - ${request.method} ${request.url}`);
      throw new ForbiddenException(Messages.FORBIDDEN);
    }

    // Admin users can access all endpoints
    const isAdmin = user.type === UserType.ADMIN ||
      (user.roles && Array.isArray(user.roles) && user.roles.includes('admin'));
    
    if (isAdmin) {
      this.logger.log(`Permission check passed for admin user ${user.id} - ${request.method} ${request.url}`);
      return true;
    }

    // Check if user is a tutor
    const isTutor = user.type === UserType.TUTOR ||
      (user.roles && Array.isArray(user.roles) && user.roles.includes('tutor'));
    
    if (!isTutor) {
      this.logger.warn(`Permission check failed: User ${user.id} is not a tutor - ${request.method} ${request.url}`);
      throw new ForbiddenException(Messages.FORBIDDEN);
    }

    // Check if tutor has permission to manage the specified plan feature
    const permission = await this.tutorPermissionRepository.findOne({
      where: {
        tutorId: user.id,
        planFeatureId,
        isActive: true
      }
    });

    if (!permission) {
      this.logger.warn(`Permission check failed: Tutor ${user.id} does not have permission for plan feature ${planFeatureId} - ${request.method} ${request.url}`);
      throw new ForbiddenException(`You do not have permission to manage this feature. Please contact an administrator.`);
    }

    this.logger.log(`Permission check passed for tutor ${user.id} for plan feature ${planFeatureId} - ${request.method} ${request.url}`);
    return true;
  }
}
