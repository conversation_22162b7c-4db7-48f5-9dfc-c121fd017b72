import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, ManyToOne, <PERSON>in<PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne, BeforeUpdate, BeforeInsert } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { Diary } from './diary.entity';
import { DiarySkin } from './diary-skin.entity';
import { DiaryFeedback } from './diary-feedback.entity';
import { DiaryShare } from './diary-share.entity';
import { DiaryCorrection } from './diary-correction.entity';
import { DiaryEntrySettings } from './diary-entry-settings.entity';
import { DiaryEntryLike } from './diary-entry-like.entity';
import { User } from './user.entity';
import { addDaysUTC } from '../../common/utils/date-utils';
import { DiaryVisibility, ViewAccess } from '../../common/enums/diary-visibility.enum';

/**
 * Status of a diary entry in its lifecycle
 * @enum {string}
 */
export enum DiaryEntryStatus {
  /** Entry is still being edited by the student */
  NEW = 'new',
  /** Entry has been submitted for review */
  SUBMIT = 'submit',
  /** Entry has been reviewed by a tutor but not yet confirmed */
  REVIEWED = 'reviewed',
  /** Entry has been confirmed by tutor and review is completed */
  CONFIRM = 'confirm'
}

@Entity()
export class DiaryEntry extends AuditableBaseEntity {
  @Column({ name: 'diary_id' })
  diaryId: string;

  @ManyToOne(() => Diary, diary => diary.entries)
  @JoinColumn({ name: 'diary_id' })
  diary: Diary;

  @Column({ name: 'entry_date', type: 'date' })
  entryDate: Date;

  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: DiaryEntryStatus,
    default: DiaryEntryStatus.NEW
  })
  status: DiaryEntryStatus;

  @Column({ name: 'skin_id', nullable: true })
  skinId: string;

  @ManyToOne(() => DiarySkin)
  @JoinColumn({ name: 'skin_id' })
  skin: DiarySkin;

  @Column({ name: 'background_color', nullable: true })
  backgroundColor: string;

  @Column({ name: 'is_private', default: false })
  isPrivate: boolean;

  @Column({
    name: 'visibility',
    type: 'enum',
    enum: DiaryVisibility,
    default: DiaryVisibility.PRIVATE
  })
  visibility: DiaryVisibility;

  @Column({
    name: 'view_access',
    type: 'enum',
    enum: ViewAccess,
    default: ViewAccess.FULL
  })
  viewAccess: ViewAccess;

  @Column({ name: 'review_start_time', nullable: true })
  reviewStartTime: Date;

  @Column({ name: 'reviewing_tutor_id', nullable: true })
  reviewingTutorId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'reviewing_tutor_id' })
  reviewingTutor: User;

  @Column({ name: 'review_expiry_time', nullable: true })
  reviewExpiryTime: Date;

  @Column({ name: 'score', type: 'int', nullable: true })
  score: number;

  @Column({ name: 'evaluated_at', nullable: true })
  evaluatedAt: Date;

  @Column({ name: 'evaluated_by', nullable: true })
  evaluatedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'evaluated_by' })
  evaluator: User;

  @BeforeInsert()
  @BeforeUpdate()
  updateReviewExpiryTime() {
    // If status is changed to SUBMIT and reviewStartTime is set
    if (this.status === DiaryEntryStatus.SUBMIT && this.reviewStartTime) {
      // Set review expiry time to 2 hours after review start time using UTC date utility
      this.reviewExpiryTime = addDaysUTC(this.reviewStartTime, 1/12); // 2 hours = 1/12 of a day
    } else if (this.status !== DiaryEntryStatus.SUBMIT) {
      // Clear review expiry time if status is not SUBMIT
      this.reviewExpiryTime = null;
    }
  }

  @OneToMany(() => DiaryFeedback, feedback => feedback.diaryEntry)
  feedbacks: DiaryFeedback[];

  @OneToMany(() => DiaryShare, share => share.diaryEntry)
  shares: DiaryShare[];

  @OneToOne(() => DiaryCorrection, correction => correction.diaryEntry)
  correction: DiaryCorrection;

  @OneToOne(() => DiaryEntrySettings, settings => settings.diaryEntry, { nullable: false })
  settings: DiaryEntrySettings;

  @OneToMany(() => DiaryEntryLike, like => like.diaryEntry)
  likes: DiaryEntryLike[];

  @Column({ name: 'thanks_message', type: 'text', nullable: true })
  thanksMessage: string;
}
