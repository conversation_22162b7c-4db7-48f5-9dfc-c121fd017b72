import { DataSourceOptions } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as dotenv from 'dotenv';
import { DataSource } from 'typeorm';
import { SnakeNamingStrategy } from '../common/strategies/snake-naming.strategy';

dotenv.config();

export const getTypeOrmConfig = (configService: ConfigService): DataSourceOptions => ({
  type: 'postgres' as const,
  host: configService.get<string>('DATABASE_HOST'),
  port: configService.get<number>('DATABASE_PORT'),
  username: configService.get<string>('DATABASE_USER'),
  password: configService.get<string>('DATABASE_PASSWORD'),
  database: configService.get<string>('DATABASE_NAME'),
  entities: [__dirname + '/../database/entities/*.entity{.ts,.js}'],
  migrations: [__dirname + '/../database/migrations/*{.ts,.js}'],
  synchronize: false,
  migrationsRun: true,
  migrationsTableName: 'migrations',
  namingStrategy: new SnakeNamingStrategy(),
  ssl:  { rejectUnauthorized: false }
});

export const AppDataSource = new DataSource(getTypeOrmConfig(new ConfigService()));
