import { Injectable, Logger } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, EntitySubscriberInterface, InsertEvent, UpdateEvent } from 'typeorm';
import { CurrentUserService } from '../../common/services/current-user.service';
import { AuditableBaseEntity } from '../entities/base-entity';
import { getCurrentUTCDate } from '../../common/utils/date-utils';

/**
 * This subscriber automatically handles audit fields (CreatedAt, UpdatedAt)
 * for all entities that extend AuditableBaseEntity
 */
@Injectable()
export class AuditSubscriber implements EntitySubscriberInterface {
  private readonly logger = new Logger(AuditSubscriber.name);

  constructor(
    @InjectDataSource() readonly dataSource: DataSource,
    private readonly currentUserService: CurrentUserService
  ) {
    // Register this subscriber with the data source
    dataSource.subscribers.push(this);
    this.logger.log('Audit subscriber registered');
  }

  /**
   * Indicates that this subscriber only applies to entities
   * that extend AuditableBaseEntity
   */
  listenTo() {
    return AuditableBaseEntity;
  }

  /**
   * Called before entity insertion
   * Sets both createdAt and updatedAt fields
   */
  beforeInsert(event: InsertEvent<AuditableBaseEntity>): void {
    // Set both createdAt and updatedAt to current UTC date
    event.entity.createdAt = getCurrentUTCDate();
    event.entity.updatedAt = getCurrentUTCDate();

    // Set createdBy and updatedBy to current user ID if available
    const currentUserId = this.currentUserService.getCurrentUserId();
    if (currentUserId) {
      event.entity.createdBy = currentUserId;
      event.entity.updatedBy = currentUserId;
      this.logger.debug(`Setting audit user fields for insert on entity: ${event.metadata.name}, user: ${currentUserId}`);
    } else {
      this.logger.debug(`No current user ID available for insert on entity: ${event.metadata.name}`);
    }

    this.logger.debug(`Setting audit fields for insert on entity: ${event.metadata.name}`);
  }

  /**
   * Called before entity update
   * Only sets the updatedAt field
   */
  beforeUpdate(event: UpdateEvent<AuditableBaseEntity>): void {
    // Only update the updatedAt field
    // The entity in event.entity might be partial, so we need to check
    if (event.entity) {
      event.entity.updatedAt = getCurrentUTCDate();

      // Set updatedBy to current user ID if available
      const currentUserId = this.currentUserService.getCurrentUserId();
      if (currentUserId) {
        event.entity.updatedBy = currentUserId;
        this.logger.debug(`Setting updatedBy field for update on entity: ${event.metadata.name}, user: ${currentUserId}`);
      } else {
        this.logger.debug(`No current user ID available for update on entity: ${event.metadata.name}`);
      }

      this.logger.debug(`Setting updatedAt field for update on entity: ${event.metadata.name}`);
    }
  }
}
