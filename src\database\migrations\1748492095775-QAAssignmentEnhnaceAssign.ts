import { MigrationInterface, QueryRunner } from "typeorm";

export class QAAssignmentEnhnaceAssign1748492095775 implements MigrationInterface {
    name = 'QAAssignmentEnhnaceAssign1748492095775'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_submission" DROP CONSTRAINT "FK_064b0390e4c62a2d77970790700"`);
        await queryRunner.query(`ALTER TABLE "qa_submission" ALTER COLUMN "assignment_id" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "qa_submission" ADD CONSTRAINT "FK_064b0390e4c62a2d77970790700" FOREIGN KEY ("assignment_id") REFERENCES "qa_assignment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_submission" DROP CONSTRAINT "FK_064b0390e4c62a2d77970790700"`);
        await queryRunner.query(`ALTER TABLE "qa_submission" ALTER COLUMN "assignment_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "qa_submission" ADD CONSTRAINT "FK_064b0390e4c62a2d77970790700" FOREIGN KEY ("assignment_id") REFERENCES "qa_assignment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }
}
