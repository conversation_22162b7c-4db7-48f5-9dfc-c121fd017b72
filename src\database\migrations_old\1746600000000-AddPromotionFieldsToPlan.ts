import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPromotionFieldsToPlan1746600000000 implements MigrationInterface {
    name = 'AddPromotionFieldsToPlan1746600000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // Add new columns to plan table
            await queryRunner.query(`ALTER TABLE "plan" ADD "is_applicable_for_promotion" boolean NOT NULL DEFAULT false`);
            await queryRunner.query(`ALTER TABLE "plan" ADD "promotion_id" character varying`);
        } catch (error) {
            console.error('Migration error:', error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        try {
            // Remove columns from plan table
            await queryRunner.query(`ALTER TABLE "plan" DROP COLUMN "promotion_id"`);
            await queryRunner.query(`ALTER TABLE "plan" DROP COLUMN "is_applicable_for_promotion"`);
        } catch (error) {
            console.error('Migration error:', error);
            throw error;
        }
    }
}
