import { Injectable, NotFoundException, BadRequestException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { NovelEntry, NovelEntryStatus } from '../../../database/entities/novel-entry.entity';
import { NovelTopic } from '../../../database/entities/novel-topic.entity';
import { Novel } from '../../../database/entities/novel.entity';
import { NovelModuleSkinPreference } from '../../../database/entities/novel-module-skin-preference.entity';
import { StudentTutorMapping, MappingStatus } from '../../../database/entities/student-tutor-mapping.entity';
import { User } from '../../../database/entities/user.entity';

import {
  UpdateNovelEntryDto,
  SubmitNovelEntryDto,
  NovelEntryResponseDto,
  SetNovelSkinPreferenceDto,
  NovelSkinPreferenceResponseDto
} from '../../../database/models/novel.dto';
import { NotificationHelperService } from '../../notification/notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';
import { DiarySkinService } from '../../diary/diary-skin.service';
import { FileRegistryService } from '../../../common/services/file-registry.service';
import { FileEntityType } from '../../../common/enums/file-entity-type.enum';
import { NovelService } from './novel.service';
import {
  NovelTutorGreetingRequiredException,
  NovelDefaultSkinRequiredException
} from '../../../common/exceptions/novel.exceptions';

@Injectable()
export class NovelEntryService {
  private readonly logger = new Logger(NovelEntryService.name);

  constructor(
    @InjectRepository(NovelEntry)
    private readonly novelEntryRepository: Repository<NovelEntry>,
    @InjectRepository(NovelTopic)
    private readonly novelTopicRepository: Repository<NovelTopic>,
    @InjectRepository(Novel)
    private readonly novelRepository: Repository<Novel>,
    @InjectRepository(NovelModuleSkinPreference)
    private readonly skinPreferenceRepository: Repository<NovelModuleSkinPreference>,
    @InjectRepository(StudentTutorMapping)
    private readonly studentTutorMappingRepository: Repository<StudentTutorMapping>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => NotificationHelperService))
    private readonly notificationHelper: NotificationHelperService,
    @Inject(forwardRef(() => DiarySkinService))
    private readonly diarySkinService: DiarySkinService,
    private readonly fileRegistryService: FileRegistryService,
    @Inject(forwardRef(() => NovelService))
    private readonly novelService: NovelService
  ) {}



  async updateEntry(studentId: string, entryId: string, updateEntryDto: UpdateNovelEntryDto): Promise<NovelEntryResponseDto> {
    // Check if student has set tutor greeting before allowing updates
    const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
    if (!hasGreeting) {
      throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before updating novel entries. This is required once for the Novel module.');
    }

    const entry = await this.novelEntryRepository.findOne({
      where: { id: entryId, studentId }
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    if (entry.status === NovelEntryStatus.REVIEWED || entry.status === NovelEntryStatus.CONFIRMED) {
      throw new BadRequestException('Cannot update entry that has been reviewed');
    }

    if (updateEntryDto.content) {
      entry.wordCount = this.calculateWordCount(updateEntryDto.content);
    }

    Object.assign(entry, updateEntryDto);
    const updatedEntry = await this.novelEntryRepository.save(entry);

    this.logger.log(`Updated novel entry ${entryId} for student ${studentId}`);
    return await this.mapEntryToResponseDto(updatedEntry);
  }

  async submitEntry(studentId: string, submitEntryDto: SubmitNovelEntryDto): Promise<NovelEntryResponseDto> {
    // Check if student has set tutor greeting before allowing submission
    const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
    if (!hasGreeting) {
      throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before submitting novel entries. This is required once for the Novel module.');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let isTransactionActive = true;

    try {
      const entry = await this.novelEntryRepository.findOne({
        where: { id: submitEntryDto.entryId, studentId },
        relations: ['topic', 'student']
      });

      if (!entry) {
        await queryRunner.rollbackTransaction();
        isTransactionActive = false;
        throw new NotFoundException('Entry not found');
      }

      if (entry.status === NovelEntryStatus.REVIEWED || entry.status === NovelEntryStatus.CONFIRMED) {
        await queryRunner.rollbackTransaction();
        isTransactionActive = false;
        throw new BadRequestException('Entry has already been reviewed');
      }

      // Update content if provided
      if (submitEntryDto.content !== undefined) {
        entry.content = submitEntryDto.content;
        entry.wordCount = this.calculateWordCount(submitEntryDto.content);
      }

      // Update skin if provided
      if (submitEntryDto.skinId !== undefined) {
        entry.skinId = submitEntryDto.skinId;
      }

      // Update background color if provided
      if (submitEntryDto.backgroundColor !== undefined) {
        entry.backgroundColor = submitEntryDto.backgroundColor;
      }

      // Update status and submission time
      const newStatus = entry.status === NovelEntryStatus.NEW ? NovelEntryStatus.SUBMITTED : NovelEntryStatus.UPDATED;
      entry.status = newStatus;
      entry.submittedAt = new Date();

      const updatedEntry = await queryRunner.manager.save(entry);
      await queryRunner.commitTransaction();
      isTransactionActive = false;

      // Send notification to tutor
      try {
        await this.sendSubmissionNotification(entry, newStatus);
      } catch (notificationError) {
        this.logger.error(`Failed to send notification: ${notificationError.message}`, notificationError.stack);
      }

      this.logger.log(`Submitted novel entry ${submitEntryDto.entryId} for student ${studentId}`);
      return await this.mapEntryToResponseDto(updatedEntry);
    } catch (error) {
      // Only rollback if transaction is still active
      if (isTransactionActive) {
        try {
          await queryRunner.rollbackTransaction();
        } catch (rollbackError) {
          this.logger.error(`Error rolling back transaction: ${rollbackError.message}`, rollbackError.stack);
        }
      }
      this.logger.error(`Error submitting novel entry: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async getStudentEntries(studentId: string, category?: string): Promise<NovelEntryResponseDto[]> {
    // Check if student has set tutor greeting before allowing access to entries
    const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
    if (!hasGreeting) {
      throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before accessing novel entries. This is required once for the Novel module.');
    }

    const queryBuilder = this.novelEntryRepository
      .createQueryBuilder('entry')
      .leftJoinAndSelect('entry.topic', 'topic')
      .leftJoinAndSelect('entry.feedbacks', 'feedbacks')
      .leftJoinAndSelect('entry.correction', 'correction')
      .leftJoinAndSelect('entry.skin', 'skin')
      .where('entry.studentId = :studentId', { studentId });

    if (category) {
      queryBuilder.andWhere('topic.category = :category', { category });
    }

    queryBuilder.orderBy('entry.createdAt', 'DESC');

    const entries = await queryBuilder.getMany();
    return Promise.all(entries.map(entry => this.mapEntryToResponseDto(entry)));
  }

  async getEntryById(entryId: string, studentId?: string): Promise<NovelEntryResponseDto> {
    // If studentId is provided, check if student has set tutor greeting
    if (studentId) {
      const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
      if (!hasGreeting) {
        throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before accessing novel entries. This is required once for the Novel module.');
      }
    }

    const whereCondition: any = { id: entryId };
    if (studentId) {
      whereCondition.studentId = studentId;
    }

    const entry = await this.novelEntryRepository.findOne({
      where: whereCondition,
      relations: ['topic', 'feedbacks', 'correction', 'student', 'skin']
    });

    if (!entry) {
      throw new NotFoundException('Entry not found');
    }

    return await this.mapEntryToResponseDto(entry);
  }

  async getOrCreateEntryByTopicId(studentId: string, topicId: string): Promise<NovelEntryResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    let isTransactionActive = true;

    try {
      // Check if entry already exists for this student and topic
      let entry = await this.novelEntryRepository.findOne({
        where: { topicId, studentId },
        relations: ['topic', 'feedbacks', 'correction', 'skin']
      });

      if (entry) {
        // Entry exists, but still check if student has set tutor greeting
        const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
        if (!hasGreeting) {
          await queryRunner.rollbackTransaction();
          isTransactionActive = false;
          throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before accessing novel entries. This is required once for the Novel module.');
        }

        // Entry exists and greeting is set, return it
        await queryRunner.commitTransaction();
        isTransactionActive = false;
        this.logger.log(`Found existing entry for student ${studentId} on topic ${topicId}`);
        return await this.mapEntryToResponseDto(entry);
      }

      // Before creating a new entry, check if student has set tutor greeting
      // This is required for ALL students before creating any novel entry
      const hasGreeting = await this.novelService.hasSetTutorGreeting(studentId);
      if (!hasGreeting) {
        await queryRunner.rollbackTransaction();
        isTransactionActive = false;
        throw new NovelTutorGreetingRequiredException('Please set a greeting message for your tutor before creating novel entries. This is required once for the Novel module.');
      }

      // Entry doesn't exist, create a new one
      // First verify topic exists and is active
      const topic = await this.novelTopicRepository.findOne({
        where: { id: topicId, isActive: true }
      });

      if (!topic) {
        await queryRunner.rollbackTransaction();
        isTransactionActive = false;
        throw new NotFoundException('Topic not found or inactive');
      }

      // Get user's default novel skin, fall back to old preference system if needed
      let defaultSkinId: string | null = null;

      // First try to get from user entity
      const user = await this.userRepository.findOne({
        where: { id: studentId },
        select: ['defaultNovelSkinId']
      });
      defaultSkinId = user?.defaultNovelSkinId;

      // If no default novel skin in user entity, check old preference system
      if (!defaultSkinId) {
        const skinPreference = await this.skinPreferenceRepository.findOne({
          where: { studentId }
        });
        defaultSkinId = skinPreference?.defaultSkinId;
      }

      if (!defaultSkinId) {
        await queryRunner.rollbackTransaction();
        isTransactionActive = false;
        throw new NovelDefaultSkinRequiredException('Please set a default skin for the Novel module before creating an entry');
      }

      // Create new entry with default content and default skin
      entry = this.novelEntryRepository.create({
        topicId,
        studentId,
        content: '', // Start with empty content
        wordCount: 0,
        skinId: defaultSkinId,
        status: NovelEntryStatus.NEW
      });

      const savedEntry = await queryRunner.manager.save(entry);

      // Load the topic relation for the response
      savedEntry.topic = topic;

      await queryRunner.commitTransaction();
      isTransactionActive = false;

      this.logger.log(`Created new entry for student ${studentId} on topic ${topicId}`);
      return await this.mapEntryToResponseDto(savedEntry);
    } catch (error) {
      // Only rollback if transaction is still active
      if (isTransactionActive) {
        try {
          await queryRunner.rollbackTransaction();
        } catch (rollbackError) {
          this.logger.error(`Error rolling back transaction: ${rollbackError.message}`, rollbackError.stack);
        }
      }
      this.logger.error(`Error getting or creating entry: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async setDefaultSkin(studentId: string, setSkinDto: SetNovelSkinPreferenceDto): Promise<NovelSkinPreferenceResponseDto> {
    let preference = await this.skinPreferenceRepository.findOne({
      where: { studentId }
    });

    if (preference) {
      preference.defaultSkinId = setSkinDto.defaultSkinId;
    } else {
      preference = this.skinPreferenceRepository.create({
        studentId,
        defaultSkinId: setSkinDto.defaultSkinId
      });
    }

    const savedPreference = await this.skinPreferenceRepository.save(preference);
    this.logger.log(`Set default skin for student ${studentId}`);

    return {
      id: savedPreference.id,
      studentId: savedPreference.studentId,
      defaultSkinId: savedPreference.defaultSkinId,
      createdAt: savedPreference.createdAt,
      updatedAt: savedPreference.updatedAt
    };
  }

  async getDefaultSkin(studentId: string): Promise<NovelSkinPreferenceResponseDto | null> {
    const preference = await this.skinPreferenceRepository.findOne({
      where: { studentId }
    });

    if (!preference) {
      return null;
    }

    return {
      id: preference.id,
      studentId: preference.studentId,
      defaultSkinId: preference.defaultSkinId,
      createdAt: preference.createdAt,
      updatedAt: preference.updatedAt
    };
  }

  /**
   * Set default novel skin for a user using the new user entity approach
   * @param userId User ID
   * @param skinId Skin ID
   */
  async setDefaultNovelSkin(userId: string, skinId: string): Promise<void> {
    // Validate that the user has access to this skin (use diary skin service validation)
    await this.diarySkinService.validateStudentSkinAccess(userId, skinId);

    // Update user's default novel skin
    await this.userRepository.update(userId, {
      defaultNovelSkinId: skinId
    });

    this.logger.log(`Updated default novel skin for user ${userId} to ${skinId}`);
  }

  /**
   * Get default novel skin for a user using the new user entity approach
   * @param userId User ID
   * @returns Default novel skin or null if not set
   */
  async getDefaultNovelSkin(userId: string): Promise<any | null> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['defaultNovelSkin']
    });

    if (!user?.defaultNovelSkin) {
      return null;
    }

    // Return a simplified skin response (novel uses diary skins)
    return {
      id: user.defaultNovelSkin.id,
      name: user.defaultNovelSkin.name,
      description: user.defaultNovelSkin.description,
      isActive: user.defaultNovelSkin.isActive,
      isGlobal: true,
      templateContent: user.defaultNovelSkin.templateContent,
      isUserDefaultNovel: true,
      isUserDefaultDiary: false
    };
  }

  private async sendSubmissionNotification(entry: NovelEntry, status: NovelEntryStatus): Promise<void> {
    try {
      // Find assigned tutor
      const tutorMapping = await this.studentTutorMappingRepository.findOne({
        where: {
          studentId: entry.studentId,
          status: MappingStatus.ACTIVE
        },
        relations: ['tutor']
      });

      if (tutorMapping) {
        const notificationType = status === NovelEntryStatus.SUBMITTED
          ? NotificationType.NOVEL_SUBMISSION
          : NotificationType.NOVEL_UPDATE;

        const title = status === NovelEntryStatus.SUBMITTED
          ? 'New Novel Submission'
          : 'Novel Entry Updated';

        const message = `${entry.student.name} has ${status === NovelEntryStatus.SUBMITTED ? 'submitted' : 'updated'} a novel entry for topic "${entry.topic.sequenceTitle}"`;

        await this.notificationHelper.notify(
          tutorMapping.tutorId,
          notificationType,
          title,
          message,
          {
            relatedEntityId: entry.id,
            relatedEntityType: 'novel_entry',
            sendEmail: true,
            sendPush: true,
            sendInApp: true,
            sendMobile: true,
            sendSms: false,
            sendRealtime: false
          }
        );
      }
    } catch (error) {
      this.logger.warn(`Failed to send notification: ${error.message}`);
    }
  }

  private calculateWordCount(content: string): number {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private async mapEntryToResponseDto(entry: NovelEntry): Promise<NovelEntryResponseDto> {
    // Get tutor greeting for the student
    let tutorGreeting: string | undefined = undefined;
    try {
      const novel = await this.novelRepository.findOne({
        where: { userId: entry.studentId },
        select: ['tutorGreeting']
      });
      tutorGreeting = novel?.tutorGreeting;
    } catch (error) {
      this.logger.warn(`Failed to load tutor greeting for student ${entry.studentId}: ${error.message}`);
    }

    // Get skin information if skinId exists
    let skinInfo = undefined;
    if (entry.skinId) {
      try {
        // If skin relation is not loaded, load it separately using DiarySkinService
        let skinDto = null;

        if (entry.skin) {
          // Use the loaded skin relation
          const skinUrl = await this.fileRegistryService.getFileUrlWithFallback(
            FileEntityType.DIARY_SKIN,
            entry.skinId
          );

          skinInfo = {
            id: entry.skin.id,
            name: entry.skin.name,
            description: entry.skin.description,
            previewImagePath: skinUrl,
            isActive: entry.skin.isActive,
            isGlobal: entry.skin.isGlobal,
            createdById: entry.skin.createdById,
            templateContent: entry.skin.templateContent || '<div>{{content}}</div>',
            isUsedIn: true
          };
        } else {
          // Load skin data using DiarySkinService
          skinDto = await this.diarySkinService.getDiarySkinById(entry.skinId);

          skinInfo = {
            id: skinDto.id,
            name: skinDto.name,
            description: skinDto.description,
            previewImagePath: skinDto.previewImagePath, // This already includes the URL
            isActive: skinDto.isActive,
            isGlobal: skinDto.isGlobal,
            createdById: skinDto.createdById,
            templateContent: skinDto.templateContent || '<div>{{content}}</div>',
            isUsedIn: skinDto.isUsedIn || true
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to load skin information for entry ${entry.id}: ${error.message}`);
        // Provide fallback skin info
        const skinUrl = await this.fileRegistryService.getFileUrlWithFallback(
          FileEntityType.DIARY_SKIN,
          entry.skinId
        );
        skinInfo = {
          id: entry.skinId,
          name: 'Default Skin',
          description: 'Skin details could not be loaded',
          previewImagePath: skinUrl,
          isActive: true,
          isGlobal: true,
          createdById: null,
          templateContent: '<div>{{content}}</div>',
          isUsedIn: true
        };
      }
    }

    return {
      id: entry.id,
      topicId: entry.topicId,
      studentId: entry.studentId,
      content: entry.content,
      wordCount: entry.wordCount,
      status: entry.status,
      skinId: entry.skinId,
      backgroundColor: entry.backgroundColor,
      submittedAt: entry.submittedAt,
      reviewedAt: entry.reviewedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      topic: entry.topic ? {
        id: entry.topic.id,
        title: entry.topic.title,
        sequenceTitle: entry.topic.sequenceTitle,
        category: entry.topic.category,
        instruction: entry.topic.instruction,
        isActive: entry.topic.isActive,
        createdAt: entry.topic.createdAt,
        updatedAt: entry.topic.updatedAt
      } : undefined,
      feedbacks: entry.feedbacks?.map(feedback => ({
        id: feedback.id,
        entryId: feedback.entryId,
        tutorId: feedback.tutorId,
        feedback: feedback.feedback,
        createdAt: feedback.createdAt,
        updatedAt: feedback.updatedAt
      })),
      correction: entry.correction ? {
        id: entry.correction.id,
        entryId: entry.correction.entryId,
        tutorId: entry.correction.tutorId,
        correction: entry.correction.correction,
        score: entry.correction.score,
        createdAt: entry.correction.createdAt,
        updatedAt: entry.correction.updatedAt
      } : undefined,
      skin: skinInfo,
      tutorGreeting: tutorGreeting
    };
  }
}
