import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON>o<PERSON>ne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "typeorm";
import { AuditableBaseEntity } from "./base-entity";
import { QAMission } from "./qa-mission.entity";
import { QATaskSubmissions } from "./qa-task-submissions.entity";
import { IsUUID } from "class-validator";

@Entity()
export class QAMissionTasks extends AuditableBaseEntity {
  @Column({
    name: "title",
    type: "varchar",
    length: 50,
  })
  title: string;

  @Column({
    name: "description",
    type: "text"
  })
  description: string;

  @Column({
    name: "is_active",
    default: true
  })
  isActive?: boolean;

  @Column({
    name: "sequence",
    type: "int",
    default: 1,
    nullable: true
  })
  sequence: number;

  @Column({
    name: "word_limit_minumum",
    type: "int",
  })
  wordLimitMinimum: number;

  @Column({
    name: "word_limit_maximum",
    type: "int",
    nullable: true,
  })
  wordLimitMaximum: number;

  @Column({ 
    name: "total_score",
    type: "int",
    nullable: true,
  })
  totalScore: number;

  @Column({
    name: "deadline",
    type: "int",
    nullable: true
  })
  deadline: number;

  @Column({
    name: "instructions",
    type: "text",
  })
  instructions: string;

  @ManyToOne(() => QAMission, mission => mission.tasks, { nullable: true })
  @JoinColumn({ name: "mission_id" })
  mission: QAMission;

  @Column({
    name: "mission_id",
    type: "uuid",
    nullable: false
  })
  @IsUUID()
  missionId: string;

  @OneToMany(() => QATaskSubmissions, submission => submission.task)
  submissions: QATaskSubmissions[];
}