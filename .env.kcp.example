# KCP Payment Gateway Configuration
# Copy this file to .env and update with your actual KCP credentials

# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=your_db_username
DATABASE_PASSWORD=your_db_password
DATABASE_NAME=hec_backend

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# Application Configuration
NODE_ENV=development
PORT=3012
API_URL=http://localhost:3012
FRONTEND_URL=http://localhost:3011

# File Storage Configuration
STORAGE_PROVIDER=local
# For S3 storage (optional)
# STORAGE_PROVIDER=s3
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=ap-northeast-2
# AWS_S3_BUCKET=your-bucket-name

# KCP Payment Gateway Configuration
KCP_SITE_CD=your_kcp_site_code
KCP_SITE_KEY=your_kcp_site_key
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_TRADE_REG_URL=/std/tradeReg/register
KCP_PAYMENT_URL=/gw/enc/v1/payment
KCP_WEBHOOK_SECRET=your_webhook_secret_key

# Payment Configuration
PAYMENT_PROVIDER=kcp
PAYMENT_CURRENCY=KRW
PAYMENT_TIMEOUT=30000
PAYMENT_RETRY_ATTEMPTS=3

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
SMTP_FROM=<EMAIL>

# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Security Configuration
CORS_ORIGIN=http://localhost:3011
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Notification Configuration
NOTIFICATION_EMAIL_ENABLED=true
NOTIFICATION_SMS_ENABLED=false

# Development/Testing Configuration
ENABLE_SWAGGER=true
ENABLE_DEBUG_LOGS=true
MOCK_PAYMENT_GATEWAY=false

# KCP Environment Specific Settings
# For Development/Staging
KCP_ENVIRONMENT=staging
KCP_TEST_MODE=true

# For Production (uncomment and update when deploying to production)
# KCP_ENVIRONMENT=production
# KCP_TEST_MODE=false
# KCP_API_URL=https://spl.kcp.co.kr

# Additional KCP Configuration
KCP_CARD_QUOTA_OPTION=12
KCP_SHOW_CARD_LIST=Y
KCP_ESCROW_USED=N
KCP_TAX_FLAG=TG03
KCP_CURRENCY=410

# Webhook Configuration
WEBHOOK_TIMEOUT=5000
WEBHOOK_RETRY_DELAY=1000
WEBHOOK_MAX_RETRIES=3

# Payment Method Configuration
ENABLE_KCP_CARD=true
ENABLE_KCP_BANK=true
ENABLE_KCP_MOBILE=true
ENABLE_KCP_VIRTUAL_ACCOUNT=false

# Transaction Configuration
TRANSACTION_TIMEOUT_MINUTES=30
PAYMENT_CONFIRMATION_TIMEOUT_MINUTES=10
AUTO_CANCEL_EXPIRED_PAYMENTS=true

# Monitoring and Analytics
ENABLE_PAYMENT_ANALYTICS=true
PAYMENT_LOG_RETENTION_DAYS=90

# Error Handling
PAYMENT_ERROR_NOTIFICATION_EMAIL=<EMAIL>
ENABLE_PAYMENT_ERROR_ALERTS=true

# Performance Configuration
PAYMENT_CACHE_TTL=300
ENABLE_PAYMENT_CACHING=true

# Backup and Recovery
PAYMENT_DATA_BACKUP_ENABLED=true
PAYMENT_DATA_BACKUP_INTERVAL=daily
