import { <PERSON>ti<PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { StudentFriendship } from './student-friendship.entity';

/**
 * Enum representing the status of a diary follow request
 */
export enum FollowRequestStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected'
}

/**
 * Entity representing a request to follow a student's diary
 */
@Entity()
export class DiaryFollowRequest extends AuditableBaseEntity {
  @Column({ name: 'requester_id' })
  requesterId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'requester_id' })
  requester: User;

  @Column({ name: 'diary_owner_id' })
  diaryOwnerId: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'diary_owner_id' })
  diaryOwner: User;

  @Column({ name: 'friendship_id', nullable: true })
  friendshipId: string;

  @ManyToOne(() => StudentFriendship, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'friendship_id' })
  friendship: StudentFriendship;

  @Column({
    type: 'enum',
    enum: FollowRequestStatus,
    default: FollowRequestStatus.PENDING
  })
  status: FollowRequestStatus;

  @Column({ name: 'request_message', nullable: true })
  requestMessage: string;
}
