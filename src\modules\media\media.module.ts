import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MediaController } from './media.controller';
import { CommonModule } from '../../common/common.module';
import { ProfilePictureRegistry } from '../../database/entities/profile-picture-registry.entity';
import { ProfilePicture } from '../../database/entities/profile-picture.entity';
import { ShopItem } from '../../database/entities/shop-item.entity';
import { DiarySkin } from '../../database/entities/diary-skin.entity';
import { StoryMakerRegistry } from '../../database/entities/story-maker-registry.entity';
import { StoryMaker } from '../../database/entities/story-maker.entity';
import { DiaryCoverRegistry } from '../../database/entities/diary-cover-registry.entity';
import { Diary } from '../../database/entities/diary.entity';
// Using FileRegistryService from CommonModule

@Module({
  imports: [TypeOrmModule.forFeature([
    ProfilePictureRegistry,
    ProfilePicture,
    ShopItem,
    DiarySkin,
    StoryMakerRegistry,
    StoryMaker,
    DiaryCoverRegistry,
    Diary
  ]), CommonModule],
  controllers: [MediaController],
  providers: [],
  exports: []
})
export class MediaModule {}
