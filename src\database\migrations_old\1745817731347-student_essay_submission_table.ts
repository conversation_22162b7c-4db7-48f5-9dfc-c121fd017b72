import { MigrationInterface, QueryRunner } from "typeorm";

export class StudentEssaySubmissionTable1745817731347 implements MigrationInterface {
    name = 'StudentEssaySubmissionTable1745817731347'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_9500bf12d147c9a0cde67f37318"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f7d75d2781798b22d628fd65a2"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "task" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "FK_113affd435faf528a6eab27c545"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_af613ccf087d159f7484190092"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ALTER COLUMN "submission" DROP NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_f7d75d2781798b22d628fd65a2" ON "essay_task_submissions" ("task", "created_by") `);
        await queryRunner.query(`CREATE INDEX "IDX_af613ccf087d159f7484190092" ON "essay_task_submission_history" ("submission", "sequence_number") `);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_9500bf12d147c9a0cde67f37318" FOREIGN KEY ("task") REFERENCES "essay_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "FK_113affd435faf528a6eab27c545" FOREIGN KEY ("submission") REFERENCES "essay_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "FK_113affd435faf528a6eab27c545"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_9500bf12d147c9a0cde67f37318"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_af613ccf087d159f7484190092"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f7d75d2781798b22d628fd65a2"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ALTER COLUMN "submission" SET NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_af613ccf087d159f7484190092" ON "essay_task_submission_history" ("sequence_number", "submission") `);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "FK_113affd435faf528a6eab27c545" FOREIGN KEY ("submission") REFERENCES "essay_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "task" SET NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_f7d75d2781798b22d628fd65a2" ON "essay_task_submissions" ("created_by", "task") `);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_9500bf12d147c9a0cde67f37318" FOREIGN KEY ("task") REFERENCES "essay_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
