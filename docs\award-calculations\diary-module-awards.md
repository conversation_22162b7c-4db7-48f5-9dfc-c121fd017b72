# HEC Diary Module - Award Calculations

## Overview

The diary module has 8 awards across 4 categories, each with monthly and annual variants.

## Awards

### 1. Best Writer Award
### 2. Best Designer Award
### 3. Best Perfect Award
### 4. Best Friendship Award

---

## 1. Best Writer Award

Recognizes students with exceptional writing quality and consistency.

### Criteria
- `DIARY_SCORE` (writing quality based on tutor evaluations)

### Calculation Formula

```typescript
// Quality Component (60% weight)
const avgEntryScore = totalScore / confirmedEntries;
const qualityScore = Math.min(avgEntryScore, 100);

// Quantity Component (40% weight)
const quantityRatio = Math.min(confirmedEntries / targetEntries, 2); // Cap at 200%
const quantityScore = Math.min(quantityRatio * 50, 100);

// Final Writer Score
const writerScore = (qualityScore * 0.6) + (quantityScore * 0.4);
```

### Configuration
- **Monthly**: Min score 80, 10+ entries, 150 points
- **Annual**: Min score 85, 100+ entries, 500 points

### Example Calculation

**Student Profile (Monthly)**:
- 18 confirmed diary entries
- Total score: 1,530 points
- Average score: 85 points per entry

**Calculation**:
1. **Quality Score**: `min(85, 100) = 85`
2. **Quantity Ratio**: `min(18/20, 2) = 0.9`
3. **Quantity Score**: `min(0.9 * 50, 100) = 45`
4. **Final Score**: `(85 * 0.6) + (45 * 0.4) = 51 + 18 = 69`

**Result**: 69/100 - Qualifies if above minimum threshold

---

## 2. Best Designer Award

Recognizes students with exceptional visual creativity and design engagement.

### Criteria
- `DIARY_DECORATION` (visual design and peer appreciation)

### Calculation Formula

```typescript
// Peer Appreciation Component (40% weight)
const peerAppreciationRatio = Math.min(studentLikes / minStudentLikes, 2);
const peerAppreciationScore = Math.min(peerAppreciationRatio * 40, 40);

// Design Variety Component (30% weight)
const designVarietyRatio = Math.min(uniqueSkinsUsed / minDesignVariety, 2);
const designVarietyScore = Math.min(designVarietyRatio * 30, 30);

// Design Experimentation Component (20% weight)
const experimentationRatio = Math.min(skinChanges / minSkinChanges, 2);
const experimentationScore = Math.min(experimentationRatio * 20, 20);

// Personalization Component (10% weight)
const personalizationScore = Math.min(customBackgrounds * 2, 10);

// Final Designer Score
const designerScore = peerAppreciationScore + designVarietyScore +
                     experimentationScore + personalizationScore;
```

### Configuration
- **Monthly**: 15+ student likes, 3+ skins, 2+ changes, 150 points
- **Annual**: 150+ student likes, 8+ skins, 10+ changes, 500 points

### Example Calculation

**Student Profile (Monthly)**:
- 25 student likes (peer appreciation)
- 5 unique skins used
- 3 skin changes
- 2 custom backgrounds

**Calculation**:
1. **Peer Appreciation**: `min(25/15, 2) * 40 = min(1.67, 2) * 40 = 40`
2. **Design Variety**: `min(5/3, 2) * 30 = min(1.67, 2) * 30 = 30`
3. **Experimentation**: `min(3/2, 2) * 20 = min(1.5, 2) * 20 = 20`
4. **Personalization**: `min(2 * 2, 10) = 4`
5. **Final Score**: `40 + 30 + 20 + 4 = 94`

**Result**: 94/100 - Excellent designer score

---

## 3. Best Perfect Award

Recognizes students who excel in all aspects of diary keeping.

### Criteria
- `DIARY_SCORE` + `ATTENDANCE` + `DIARY_DECORATION`

### Calculation Formula

```typescript
// Writing Component (40% weight)
const writingScore = calculateWritingScore(entries, config);

// Attendance Component (30% weight)
const attendanceRatio = Math.min(attendanceDays / requiredDays, 1);
const attendanceScore = attendanceRatio * 100;

// Design Component (30% weight)
const designScore = calculateDecorationScore(decorationMetrics, config);

// Final Perfect Score (weighted average)
const perfectScore = (writingScore * 0.4) + (attendanceScore * 0.3) + (designScore * 0.3);
```

### Configuration
- **Monthly**: Score 75+, 15+ entries, 20+ days, 10+ likes, 200 points
- **Annual**: Score 80+, 150+ entries, 250+ days, 100+ likes, 750 points

### Example Calculation

**Student Profile (Monthly)**:
- Writing: 82 average score, 16 entries
- Attendance: 22 days out of 30
- Design: 15 student likes, 3 unique skins, 2 skin changes, 1 custom background

**Calculation**:
1. **Writing Score**: `82` (meets minimum 75)
2. **Attendance Score**: `min(22/20, 1) * 100 = 100`
3. **Design Score**:
   - Peer: `min(15/10, 2) * 40 = 40`
   - Variety: `min(3/2, 2) * 30 = 30`
   - Experimentation: `min(2/2, 2) * 20 = 20`
   - Personalization: `1 * 2 = 2`
   - Total: `40 + 30 + 20 + 2 = 92`
4. **Final Score**: `(82 * 0.4) + (100 * 0.3) + (92 * 0.3) = 32.8 + 30 + 27.6 = 90.4`

**Result**: 90.4/100 - Excellent all-around performance

---

## 4. Best Friendship Award

Recognizes students with exceptional social engagement and friendship activities.

### Criteria
- `DIARY_FRIENDSHIP` (social engagement metrics)

### Calculation Formula

```typescript
// Active Friendships Component (20% weight)
const friendshipRatio = Math.min(activeFriendships / minFriendships, 1);
const friendshipScore = friendshipRatio * 20;

// Sharing Component (30% weight)
const sharingRatio = Math.min(sharedEntries / minShares, 1);
const sharingScore = sharingRatio * 30;

// Likes Given Component (25% weight)
const likesGivenRatio = Math.min(likesGiven / minLikesGiven, 1);
const likesGivenScore = likesGivenRatio * 25;

// Likes Received Component (25% weight)
const likesReceivedRatio = Math.min(likesReceived / minLikesReceived, 1);
const likesReceivedScore = likesReceivedRatio * 25;

// Final Friendship Score
const totalFriendshipScore = friendshipScore + sharingScore +
                            likesGivenScore + likesReceivedScore;
```

### Configuration
- **Monthly**: 3+ friends, 5+ shares, 10+ given, 5+ received, 150 points
- **Annual**: 5+ friends, 50+ shares, 100+ given, 50+ received, 500 points

### Example Calculation

**Student Profile (Monthly)**:
- 4 active friendships with diary viewing permission
- 8 diary entries shared with friends
- 15 likes given to friends' entries
- 7 likes received from friends

**Calculation**:
1. **Friendships**: `min(4/3, 1) * 20 = 1 * 20 = 20`
2. **Sharing**: `min(8/5, 1) * 30 = 1 * 30 = 30`
3. **Likes Given**: `min(15/10, 1) * 25 = 1 * 25 = 25`
4. **Likes Received**: `min(7/5, 1) * 25 = 1 * 25 = 25`
5. **Final Score**: `20 + 30 + 25 + 25 = 100`

**Result**: 100/100 - Perfect friendship engagement

---

## Award Filtering

### Client Requirements Only
The system only processes awards that match the exact client requirements:
- **Best Writer Award**
- **Best Designer Award**
- **Best Perfect Award**
- **Best Friendship Award**

Any additional awards created outside the seeder are automatically filtered out during calculation to ensure only client-specified awards are considered.

## Scheduler

### Automated Generation
- **Monthly Awards**: 1st of each month at 02:00 UTC
- **Annual Awards**: January 1st at 03:00 UTC

### Implementation
```typescript
@Injectable()
export class SimplifiedAwardScheduler {
  // Monthly awards: 1st of each month at 2:00 AM UTC
  @Cron('0 2 1 * *')
  async generateMonthlyAwards() {
    const lastMonth = addMonthsUTC(getCurrentUTCDate(), -1);
    const startDate = getStartOfMonthUTC(lastMonth);
    const endDate = getEndOfMonthUTC(lastMonth);
    await this.diaryAwardService.generateAwardsForRange(startDate, endDate);
  }

  // Annual awards: January 1st at 3:00 AM UTC
  @Cron('0 3 1 1 *')
  async generateAnnualAwards() {
    const lastYear = getCurrentUTCDate().getUTCFullYear() - 1;
    const startDate = new Date(Date.UTC(lastYear, 0, 1));
    const endDate = new Date(Date.UTC(lastYear, 11, 31, 23, 59, 59, 999));
    await this.diaryAwardService.generateAwardsForRange(startDate, endDate);
  }
}
```

### Manual Triggers
```bash
# Check status
GET /api/award-scheduler/status

# Generate monthly awards for December 2023
POST /api/award-scheduler/trigger/monthly?year=2023&month=12

# Generate annual awards for 2023
POST /api/award-scheduler/trigger/annual?year=2023
```


