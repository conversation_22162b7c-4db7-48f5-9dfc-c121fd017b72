import { <PERSON>, Get, Post, Body, UseGuards, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { StudentGuard } from '../../../common/guards/student.guard';
import { GetUser } from '../../../common/decorators/get-user.decorator';
import { BlockGameService } from './block-game.service';
import { BlockGameDetailDto, SubmitBlockGameDto, BlockGameAttemptResultDto } from '../../../database/models/block-game/block-game-student.dto';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../../common/decorators/api-response.decorator';

@ApiTags('Play-Block')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, StudentGuard)
@Controller('play/block')
export class BlockGameController {
  constructor(private readonly blockGameService: BlockGameService) {}

  @Get('play')
  @ApiOperation({
    summary: 'Get a random block game to play',
    description: 'Retrieves a random active block game with randomized word blocks for student to play',
  })
  @ApiOkResponseWithType(BlockGameDetailDto, 'Random block game retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'No games available at the moment')
  async getRandomBlockGame(@GetUser() user: any): Promise<ApiResponse<BlockGameDetailDto>> {
    const studentId = user.sub || user.id;
    const result = await this.blockGameService.getRandomBlockGame(studentId);
    return ApiResponse.success(result, 'Random block game retrieved successfully');
  }

  @Get('games/:id')
  @ApiOperation({
    summary: "Get a block game for playing. (It's kept for future if replay option is needed)",
    description: 'Retrieves a specific block game with randomized word blocks for student to play',
  })
  @ApiParam({
    name: 'id',
    type: 'string',
    description: 'Block game ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponseWithType(BlockGameDetailDto, 'Block game retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found or not available')
  async getBlockGameForPlay(@Param('id', ParseUUIDPipe) id: string, @GetUser() user: any): Promise<ApiResponse<BlockGameDetailDto>> {
    const studentId = user.sub || user.id;
    const result = await this.blockGameService.getBlockGameForPlay(id, studentId);
    return ApiResponse.success(result, 'Block game retrieved successfully');
  }

  @Post('submit')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({
    summary: 'Submit block game attempt',
    description: "Submit student's sentence constructions and get detailed results with scoring",
  })
  @ApiOkResponseWithType(BlockGameAttemptResultDto, 'Block game submitted successfully')
  @ApiErrorResponse(400, 'Invalid submission data')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found or not available')
  async submitBlockGame(@Body() submitDto: SubmitBlockGameDto, @GetUser() user: any): Promise<ApiResponse<BlockGameAttemptResultDto>> {
    const studentId = user.sub || user.id;
    const result = await this.blockGameService.submitAttempt(studentId, submitDto);
    return ApiResponse.success(result, 'You completed successfully');
  }
}
