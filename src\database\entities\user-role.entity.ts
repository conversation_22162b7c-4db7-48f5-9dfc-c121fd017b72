import { En<PERSON>ty, PrimaryColumn, ManyToOne, CreateDateColumn, UpdateDateColumn } from "typeorm";
import { User } from "./user.entity";
import { Role } from "./role.entity";

@Entity()
export class UserRole {

    @PrimaryColumn({ name: 'user_id' })
    userId: string;

    @PrimaryColumn({ name: 'role_id' })
    roleId: string;

    @ManyToOne(() => User, user => user.userRoles)
    user: User;

    @ManyToOne(() => Role, role => role.userRoles)
    role: Role;

    @CreateDateColumn({ name: 'created_at' })
    createdAt: Date;

    @UpdateDateColumn({ name: 'updated_at' })
    updatedAt: Date;

    toDto(): string {
      return this.role.name;
    }
}