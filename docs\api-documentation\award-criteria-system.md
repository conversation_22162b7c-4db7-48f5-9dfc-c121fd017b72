# Award Criteria System Documentation

## Overview

The award criteria system ensures that each module (diary, novel, essay) can only use its own specific criteria when creating awards. This maintains data integrity and prevents cross-module contamination of award logic.

## Available Modules

### 1. Diary Module (`diary`)
**Available Criteria:**
- `diary_score` - Evaluation based on diary entry scores
- `attendance` - Evaluation based on diary entry submission frequency
- `diary_decoration` - Evaluation based on diary decoration and customization

### 2. Novel Module (`novel`)
**Available Criteria:**
- `novel_performance` - Overall performance in the novel module

### 3. <PERSON><PERSON><PERSON> (`essay`)
**Available Criteria:**
- `essay_performance` - Overall performance in the essay module

## API Endpoints

### Get Award Criteria Information

#### Get All Criteria (Organized by <PERSON><PERSON><PERSON>)
```
GET /awards/criteria
```

**Response:**
```json
{
  "success": true,
  "message": "Award criteria retrieved successfully",
  "data": {
    "diary": [
      {
        "id": "diary_score",
        "name": "Diary Score",
        "description": "Evaluation based on diary entry scores",
        "module": "diary"
      },
      {
        "id": "attendance",
        "name": "Attendance",
        "description": "Evaluation based on diary entry submission frequency",
        "module": "diary"
      },
      {
        "id": "diary_decoration",
        "name": "Diary Decoration",
        "description": "Evaluation based on diary decoration and customization",
        "module": "diary"
      }
    ],
    "novel": [
      {
        "id": "novel_performance",
        "name": "Novel Performance",
        "description": "Overall performance in the novel module",
        "module": "novel"
      }
    ],
    "essay": [
      {
        "id": "essay_performance",
        "name": "Essay Performance",
        "description": "Overall performance in the essay module",
        "module": "essay"
      }
    ]
  }
}
```

#### Get Criteria for Specific Module
```
GET /awards/criteria?module=diary
```

**Response:**
```json
{
  "success": true,
  "message": "Award criteria for diary module retrieved successfully",
  "data": [
    {
      "id": "diary_score",
      "name": "Diary Score",
      "description": "Evaluation based on diary entry scores",
      "module": "diary"
    },
    {
      "id": "attendance",
      "name": "Attendance",
      "description": "Evaluation based on diary entry submission frequency",
      "module": "diary"
    },
    {
      "id": "diary_decoration",
      "name": "Diary Decoration",
      "description": "Evaluation based on diary decoration and customization",
      "module": "diary"
    }
  ]
}
```

#### Get Criteria for Novel Module
```
GET /awards/criteria?module=novel
```

**Response:**
```json
{
  "success": true,
  "message": "Award criteria for novel module retrieved successfully",
  "data": [
    {
      "id": "novel_performance",
      "name": "Novel Performance",
      "description": "Overall performance in the novel module",
      "module": "novel"
    }
  ]
}
```

#### Get Criteria for Essay Module
```
GET /awards/criteria?module=essay
```

**Response:**
```json
{
  "success": true,
  "message": "Award criteria for essay module retrieved successfully",
  "data": [
    {
      "id": "essay_performance",
      "name": "Essay Performance",
      "description": "Overall performance in the essay module",
      "module": "essay"
    }
  ]
}
```

## Creating Awards

### Valid Award Examples

#### Diary Module Award
```json
{
  "name": "Gold Star Diarist",
  "description": "Awarded to the student with the highest diary scores for the month",
  "module": "diary",
  "criteria": ["diary_score"],
  "frequency": "monthly",
  "rewardPoints": 100,
  "isActive": true,
  "criteriaConfig": {
    "minScore": 90,
    "entriesRequired": 5
  }
}
```

#### Novel Module Award
```json
{
  "name": "Novel Excellence",
  "description": "Awarded to students with exceptional performance in the Novel module",
  "module": "novel",
  "criteria": ["novel_performance"],
  "frequency": "weekly",
  "rewardPoints": 100,
  "isActive": true,
  "criteriaConfig": {
    "minScore": 85
  }
}
```

#### Essay Module Award
```json
{
  "name": "Essay Master",
  "description": "Awarded to students with outstanding essay performance",
  "module": "essay",
  "criteria": ["essay_performance"],
  "frequency": "monthly",
  "rewardPoints": 120,
  "isActive": true,
  "criteriaConfig": {
    "minScore": 88
  }
}
```

## Validation Rules

### 1. Module-Criteria Matching
- Each criterion can only be used with its designated module
- Cross-module criteria usage is strictly prohibited

### 2. Error Messages

#### Invalid Criterion for Module
**Request:**
```json
{
  "name": "Test Award",
  "description": "Test description",
  "module": "novel",
  "criteria": ["diary_score"],
  "frequency": "monthly",
  "rewardPoints": 100
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Validation failed",
  "statusCode": 400,
  "validationErrors": {
    "criteria": [
      "Invalid criteria for 'novel' module: diary_score. Valid criteria for 'novel' module are: novel_performance"
    ]
  }
}
```

#### Invalid Criterion
**Request:**
```json
{
  "name": "Test Award",
  "description": "Test description",
  "module": "diary",
  "criteria": ["invalid_criterion"],
  "frequency": "monthly",
  "rewardPoints": 100
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Validation failed",
  "statusCode": 400,
  "validationErrors": {
    "criteria": [
      "Invalid criteria for 'diary' module: invalid_criterion. Valid criteria for 'diary' module are: diary_score, attendance, diary_decoration"
    ]
  }
}
```

#### Multiple Invalid Criteria
**Request:**
```json
{
  "name": "Test Award",
  "description": "Test description",
  "module": "diary",
  "criteria": ["invalid_criterion", "novel_performance"],
  "frequency": "monthly",
  "rewardPoints": 100
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Validation failed",
  "statusCode": 400,
  "validationErrors": {
    "criteria": [
      "Invalid criteria for 'diary' module: invalid_criterion, novel_performance. Valid criteria for 'diary' module are: diary_score, attendance, diary_decoration"
    ]
  }
}
```

#### Missing Criteria
**Request:**
```json
{
  "name": "Test Award",
  "description": "Test description",
  "module": "diary",
  "criteria": [],
  "frequency": "monthly",
  "rewardPoints": 100
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Validation failed",
  "statusCode": 400,
  "validationErrors": {
    "criteria": [
      "At least one criterion must be specified"
    ]
  }
}
```

## Best Practices

### 1. Use Appropriate Criteria
- Always use criteria that belong to the same module as your award
- Check available criteria using the `/awards/criteria` endpoint
- Use module filtering to get specific criteria: `/awards/criteria?module=diary`

### 2. Multiple Criteria
- You can combine multiple criteria from the same module
- Example: `["diary_score", "attendance", "diary_decoration"]` for diary module

### 3. Criteria Configuration
- Provide appropriate `criteriaConfig` based on your criteria
- Common config fields: `minScore`, `entriesRequired`, `daysRequired`, `minSkins`

### 4. API Usage Examples
- Get all criteria: `GET /awards/criteria`
- Get diary criteria only: `GET /awards/criteria?module=diary`
- Get novel criteria only: `GET /awards/criteria?module=novel`
- Get essay criteria only: `GET /awards/criteria?module=essay`
- Get available awards: `GET /awards/available` (Student endpoint)

### 5. Testing
- Use the criteria endpoint to verify available options
- Test with invalid combinations to understand error messages
- Use module filtering to focus on specific module requirements

## Implementation Details

### Validation Flow
1. Check if criteria array is provided and not empty
2. Validate each criterion exists in the system
3. Verify each criterion belongs to the specified module
4. Check for duplicate criteria
5. Validate criteria configuration format

### Error Handling
- Detailed error messages specify which criteria are invalid
- Suggestions for valid alternatives are provided
- Clear indication of which module each criterion belongs to

This system ensures data integrity and provides clear guidance for award creation across all modules.
