# Promotion and Shopping Cart Integration

This document outlines how the promotion management system integrates with the shopping cart functionality in the HEC backend application.

## Overview

The integration between promotions and the shopping cart allows for dynamic pricing based on applicable promotions. When items are added to the cart, the system calculates the final price based on any applied promotions. During checkout, the system preserves promotion information in purchase records.

## Shopping Cart Flow with Promotions

### 1. Adding Items to Cart

When a user adds an item to the cart, the system:

1. Retrieves the shop item details, including any applied promotion
2. Calculates the final price based on the promotion (if any)
3. Stores the item in the cart with the original price, final price, and promotion information
4. Returns the updated cart with all items and their prices

```mermaid
sequenceDiagram
    participant User
    participant CartController
    participant CartService
    participant ShopItemService
    participant PromotionService

    User->>CartController: Add item to cart
    CartController->>CartService: addToCart(userId, itemId, quantity)
    CartService->>ShopItemService: getShopItem(itemId)
    ShopItemService-->>CartService: Shop item with promotion info

    alt Item has promotion
        CartService->>PromotionService: validatePromotion(promotionId)
        PromotionService-->>CartService: Promotion validation result
        CartService->>CartService: calculateFinalPrice(item, promotion)
    else No promotion
        CartService->>CartService: Use original price
    end

    CartService->>CartService: Add item to cart with prices
    CartService-->>CartController: Updated cart
    CartController-->>User: Cart response with items and prices
```

### 2. Updating Cart Items

When a user updates the quantity of an item in the cart, the system:

1. Retrieves the cart item
2. Updates the quantity
3. Recalculates the total price based on the final price and new quantity
4. Returns the updated cart

### 3. Applying Promotion Codes

When a user applies a promotion code, the system:

1. Validates the promotion code
2. Checks if the promotion is applicable to the items in the cart
3. Calculates the discount for each applicable item
4. Updates the cart items with the new prices
5. Returns the updated cart with the applied promotion

```mermaid
sequenceDiagram
    participant User
    participant CartController
    participant CartService
    participant PromotionService

    User->>CartController: Apply promotion code
    CartController->>PromotionService: applyPromotionCode(code, cartItems)

    PromotionService->>PromotionService: Validate promotion
    PromotionService->>PromotionService: Check applicability to items
    PromotionService->>PromotionService: Calculate discounts

    PromotionService-->>CartController: Discount information
    CartController->>CartService: updateCartWithPromotion(discountInfo)
    CartService-->>CartController: Updated cart
    CartController-->>User: Cart response with applied promotion
```

### 4. Checkout Process

During checkout, the system:

1. Validates the cart items and their prices
2. Creates purchase records with promotion information
3. Stores the original price, final price, promotion ID, and discount amount
4. Clears the cart or marks it as checked out
5. Returns the checkout response with all purchase information

```mermaid
sequenceDiagram
    participant User
    participant CheckoutController
    participant CartService
    participant PurchaseService

    User->>CheckoutController: Checkout cart
    CheckoutController->>CartService: getCart(userId)
    CartService-->>CheckoutController: Cart with items and prices

    CheckoutController->>PurchaseService: createPurchases(cartItems)

    loop For each cart item
        PurchaseService->>PurchaseService: Create purchase record with promotion info
    end

    PurchaseService-->>CheckoutController: Purchase records
    CheckoutController->>CartService: clearCart(userId)
    CartService-->>CheckoutController: Cart cleared
    CheckoutController-->>User: Checkout response with purchases
```

## Data Flow

### Cart Item Data Structure

When an item is added to the cart, the following promotion-related information is stored:

```json
{
  "id": "cart-item-id",
  "shopItemId": "shop-item-id",
  "title": "Item Title",
  "originalPrice": 100,
  "price": 80,
  "discountPercentage": 20,
  "isOnSale": true,
  "promotionId": "promotion-id",
  "quantity": 1,
  "totalPrice": 80,
  "totalRewardPoints": 80,
  "categoryId": "category-id",
  "categoryName": "Category Name"
}
```

The cart item includes category information (`categoryId` and `categoryName`) to support category-based promotions. This information is retrieved when the cart is loaded:

```typescript
// Get cart items with a direct query, including category information
const cartItemsQuery = await this.dataSource.query(
  `SELECT ci.id, ci.shop_item_id as "shopItemId", ci.quantity, ci.price, ci.reward_points as "rewardPoints",
          si.title, si.price as "originalPrice", si.promotion_id as "promotionId", si.file_path as "filePath",
          si.type, si.price_equivalent_to_reward_point as "priceEquivalentToRewardPoint", si.discounted_price as "discountedPrice",
          si.category_id as "categoryId", sc.name as "categoryName"
   FROM shopping_cart_item ci
   JOIN shop_item si ON ci.shop_item_id = si.id
   LEFT JOIN shop_category sc ON si.category_id = sc.id
   WHERE ci.cart_id = $1`,
  [cart.id]
);
```

### Purchase Record Data Structure

When a purchase is created during checkout, the following promotion-related information is stored:

```json
{
  "id": "purchase-id",
  "userId": "user-id",
  "shopItemId": "shop-item-id",
  "quantity": 1,
  "originalPrice": 100,
  "finalPrice": 80,
  "promotionId": "promotion-id",
  "discountAmount": 20,
  "paymentMethod": "CREDIT_CARD",
  "status": "COMPLETED",
  "categoryId": "category-id",
  "notes": "Purchase from category: Category Name"
}
```

The purchase record includes category information (`categoryId` and notes with the category name) to support reporting and analytics. This information is stored during checkout:

```typescript
// Get the shop item details including category information
const shopItemQuery = await queryRunner.query(
  `SELECT si.id, si.title, si.price, si.promotion_id, si.discounted_price,
          si.category_id, sc.name as category_name
   FROM shop_item si
   LEFT JOIN shop_category sc ON si.category_id = sc.id
   WHERE si.id = $1`,
  [item.shopItemId]
);

// Create purchase record with category information
const purchaseResult = await queryRunner.query(
  `INSERT INTO shop_item_purchase (
     user_id, shop_item_id, quantity, original_price, final_price,
     promotion_id, discount_amount, reward_points_used, payment_method, status,
     category_id, notes, created_at, updated_at
   )
   VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
   RETURNING id`,
  [
    userId, item.shopItemId, item.quantity, originalPrice, finalPrice,
    shopItem.promotion_id, discountAmount, itemRewardPointsUsed, paymentMethod, PurchaseStatus.COMPLETED,
    shopItem.category_id, `Purchase from category: ${shopItem.category_name || 'Unknown'}`, now, now
  ]
);
```

## Promotion Validation in Cart

When validating promotions in the cart, the system checks:

1. **Active Status**: The promotion must be active.
2. **Date Range**: If start and end dates are specified, the current date must be within that range.
3. **Usage Limit**: If a usage limit is specified, the promotion must not have exceeded that limit.
4. **Applicable Type**: The promotion must be applicable to shop items.
5. **Applicable Categories**: If applicable category IDs are specified, the item's category must be in that list.
6. **Purchase Amount**: If minimum or maximum purchase amounts are specified, the purchase amount must be within that range.

## Category-Based Promotion Application

The system supports category-based promotions through the `applicableCategoryIds` field. When applying promotions:

1. If a promotion has `applicableCategoryIds` specified, it will only be applied to items in those categories.
2. When applying a promotion to shop items, the system filters out items that are not in applicable categories.
3. During checkout, the system includes category information in purchase records for better tracking.

### Implementation Details

The category validation is implemented in the following ways:

1. **When Applying Promotions to Shop Items**:
   ```typescript
   // Check if promotion has applicable category restrictions
   const hasApplicableCategoryRestrictions =
     promotion.applicableCategoryIds &&
     promotion.applicableCategoryIds.length > 0;

   // Filter items that are in applicable categories
   const applicableItems = hasApplicableCategoryRestrictions
     ? shopItems.filter(item => promotion.applicableCategoryIds.includes(item.categoryId))
     : shopItems;

   // Apply promotion only to applicable items
   for (const shopItem of applicableItems) {
     shopItem.promotionId = promotionId;
   }
   ```

2. **When Validating Promotions in Cart**:
   ```typescript
   // Check if promotion is applicable to the category
   if (category &&
       promotion.applicableCategoryIds &&
       promotion.applicableCategoryIds.length > 0 &&
       !promotion.applicableCategoryIds.includes(category)) {
     return {
       isApplied: false,
       message: `Promotion is not applicable to ${category} category`
     };
   }
   ```

3. **When Creating Purchase Records**:
   ```typescript
   // Create purchase record with category information
   const purchaseResult = await queryRunner.query(
     `INSERT INTO shop_item_purchase (
        user_id, shop_item_id, quantity, original_price, final_price,
        promotion_id, discount_amount, reward_points_used, payment_method, status,
        category_id, notes, created_at, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING id`,
     [
       userId, item.shopItemId, item.quantity, originalPrice, finalPrice,
       shopItem.promotion_id, discountAmount, itemRewardPointsUsed, paymentMethod, PurchaseStatus.COMPLETED,
       shopItem.category_id, `Purchase from category: ${shopItem.category_name || 'Unknown'}`, now, now
     ]
   );
   ```

## API Endpoints

### Add to Cart

- **Endpoint**: `POST /shop/cart/add`
- **Description**: Adds an item to the cart with promotion pricing
- **Request Body**:
  ```json
  {
    "shopItemId": "shop-item-id",
    "quantity": 1
  }
  ```
- **Response**:
  ```json
  {
    "id": "cart-id",
    "status": "ACTIVE",
    "lastActivity": "2023-05-15T10:30:00.000Z",
    "items": [
      {
        "id": "cart-item-id",
        "shopItemId": "shop-item-id",
        "title": "Item Title",
        "originalPrice": 100,
        "price": 80,
        "discountPercentage": 20,
        "isOnSale": true,
        "promotionId": "promotion-id",
        "quantity": 1,
        "totalPrice": 80,
        "totalRewardPoints": 80,
        "categoryId": "category-id",
        "categoryName": "Category Name"
      }
    ],
    "totalPrice": 80,
    "totalRewardPoints": 80,
    "itemCount": 1
  }
  ```

### Update Cart Item

- **Endpoint**: `PUT /shop/cart/items/{id}`
- **Description**: Updates the quantity of an item in the cart
- **Request Body**:
  ```json
  {
    "quantity": 2
  }
  ```
- **Response**:
  ```json
  {
    "id": "cart-id",
    "status": "ACTIVE",
    "lastActivity": "2023-05-15T10:35:00.000Z",
    "items": [
      {
        "id": "cart-item-id",
        "shopItemId": "shop-item-id",
        "title": "Item Title",
        "originalPrice": 100,
        "price": 80,
        "discountPercentage": 20,
        "isOnSale": true,
        "promotionId": "promotion-id",
        "quantity": 2,
        "totalPrice": 160,
        "totalRewardPoints": 160,
        "categoryId": "category-id",
        "categoryName": "Category Name"
      }
    ],
    "totalPrice": 160,
    "totalRewardPoints": 160,
    "itemCount": 1
  }
  ```

### Remove Cart Item

- **Endpoint**: `DELETE /shop/cart/items/{id}`
- **Description**: Removes an item from the cart
- **Response**:
  ```json
  {
    "id": "cart-id",
    "status": "ACTIVE",
    "lastActivity": "2023-05-15T10:40:00.000Z",
    "items": [],
    "totalPrice": 0,
    "totalRewardPoints": 0,
    "itemCount": 0
  }
  ```

### Clear Cart

- **Endpoint**: `DELETE /shop/cart`
- **Description**: Clears all items from the cart
- **Response**:
  ```json
  {
    "id": "cart-id",
    "status": "ACTIVE",
    "lastActivity": "2023-05-15T10:45:00.000Z",
    "items": [],
    "totalPrice": 0,
    "totalRewardPoints": 0,
    "itemCount": 0
  }
  ```

### Checkout

- **Endpoint**: `POST /shop/cart/checkout`
- **Description**: Processes the checkout with promotion information
- **Request Body**:
  ```json
  {
    "paymentMethod": "CREDIT_CARD",
    "useRewardPoints": false,
    "paymentDetails": {
      "cardNumber": "****************",
      "expiryMonth": "12",
      "expiryYear": "2025",
      "cvv": "123"
    }
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "orderId": "order-id",
    "totalAmount": 160,
    "rewardPointsUsed": 0,
    "remainingRewardPoints": 1000,
    "paymentMethod": "CREDIT_CARD",
    "purchaseDate": "2023-05-15T10:50:00.000Z",
    "items": [
      {
        "id": "cart-item-id",
        "shopItemId": "shop-item-id",
        "title": "Item Title",
        "originalPrice": 100,
        "price": 80,
        "discountPercentage": 20,
        "isOnSale": true,
        "promotionId": "promotion-id",
        "quantity": 2,
        "totalPrice": 160,
        "totalRewardPoints": 160,
        "categoryId": "category-id",
        "categoryName": "Category Name"
      }
    ]
  }
  ```

## Best Practices

1. **Category Management**: Ensure that shop items are properly categorized to take advantage of category-based promotions.

2. **Promotion Application**: When applying promotions to shop items, consider the categories they belong to.

3. **Checkout Validation**: Always validate promotion applicability during checkout to ensure the correct prices are used.

4. **Purchase Records**: Include promotion and category information in purchase records for better tracking and reporting.

5. **Error Handling**: Handle promotion validation errors gracefully and provide clear messages to users.

## Conclusion

The integration between promotions and the shopping cart provides a flexible way to offer discounts to users. By properly categorizing shop items and applying promotions based on categories, you can create targeted marketing campaigns and increase sales.
