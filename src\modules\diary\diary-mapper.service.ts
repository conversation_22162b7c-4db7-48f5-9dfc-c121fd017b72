import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DiaryEntry, DiaryEntryStatus } from '../../database/entities/diary-entry.entity';
import { Diary } from '../../database/entities/diary.entity';
import { DiaryEntryResponseDto, DiaryDetailsDto, DiarySkinResponseDto, DiaryFeedbackResponseDto } from '../../database/models/diary.dto';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { DiaryShare } from '../../database/entities/diary-share.entity';

@Injectable()
export class DiaryMapperService {
  private readonly logger = new Logger(DiaryMapperService.name);

  constructor(
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    private readonly deeplinkService: DeeplinkService,
    private readonly fileRegistryService: FileRegistryService,
    @InjectRepository(DiaryShare)
    private readonly diaryShareRepository: Repository<DiaryShare>
  ) {}

  // Helper method to map Diary entity to DiaryDetailsDto
  async mapDiaryToDetailsDto(diary: Diary): Promise<DiaryDetailsDto> {
    // Get total entries count
    const totalEntries = await this.diaryEntryRepository.count({
      where: { diaryId: diary.id }
    });

    // Get confirmed entries count
    const reviewedEntries = await this.diaryEntryRepository.count({
      where: { diaryId: diary.id, status: DiaryEntryStatus.CONFIRM }
    });

    // Get average score
    const scoreResult = await this.diaryEntryRepository
      .createQueryBuilder('entry')
      .select('AVG(entry.score)', 'averageScore')
      .where('entry.diaryId = :diaryId', { diaryId: diary.id })
      .andWhere('entry.score IS NOT NULL')
      .getRawOne();

    const averageScore = scoreResult?.averageScore ? parseFloat(scoreResult.averageScore) : null;

    // Get cover photo URL
    const coverPhotoUrl = await this.fileRegistryService.getFileUrlWithFallback(
      FileEntityType.DIARY_COVER,
      diary.id
    );

    return {
      id: diary.id,
      userId: diary.userId,
      userName: diary.user?.name,
      defaultSkinId: diary.defaultSkinId,
      totalEntries,
      reviewedEntries,
      averageScore,
      coverPhotoUrl,
      createdAt: diary.createdAt,
      updatedAt: diary.updatedAt
    };
  }

  private mapSkinToResponseDto(skin: any): DiarySkinResponseDto | undefined {
    if (!skin) return undefined;
    return {
      ...skin,
      isUsedIn: skin.isUsedIn ?? []
    };
  }

  private mapFeedbackToResponseDto(feedback: any): DiaryFeedbackResponseDto {
    return {
      ...feedback,
      tutorName: feedback.tutor?.name ?? 'Unknown Tutor'
    };
  }
  // Helper method to map DiaryEntry entity to DiaryEntryResponseDto
  // Made public so it can be used by other services
  async mapEntryToResponseDto(entry: DiaryEntry): Promise<DiaryEntryResponseDto> {
    return {
      id: entry.id,
      diary: entry.diary,
      entryDate: entry.entryDate,
      title: entry.title,
      content: entry.content,
      status: entry.status,
      skin: entry.skin ? this.mapSkinToResponseDto(entry.skin) : undefined,
      backgroundColor: entry.backgroundColor,
      isPrivate: entry.isPrivate,
      score: entry.score,
      evaluatedAt: entry.evaluatedAt,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      hasGreeting: !!entry.diary?.tutorGreeting,
      feedbacks: entry.feedbacks?.map(feedback => this.mapFeedbackToResponseDto(feedback)) || [],
      likeCount: entry.likes ? entry.likes.length : 0,
      hasLiked: false, // This will be updated by the DiaryEntryService if a userId is provided
      settings: entry.settings,
      correction: entry.correction,
      thanksMessage: entry.thanksMessage,
      // Add share URLs only if entry is not private
      shareUrl: !entry.isPrivate ? this.deeplinkService.getWebLink(DeeplinkType.DIARY_SHARE, { id: entry.id }) : undefined,
      // Add cache-busting timestamp to get the latest QR code if regenerated
      qrCodeUrl: !entry.isPrivate ? `${this.fileRegistryService.getFileUrl(FileEntityType.DIARY_QR, entry.id)}?t=${Date.now()}` : undefined
    };
  }
}
