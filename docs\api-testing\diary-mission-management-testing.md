# Diary Mission Management API Testing Flow

This document outlines the testing flow for the Diary Mission Management API endpoints.

## Prerequisites

Before testing the Diary Mission Management API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (tutor, student)
3. Set up your API testing tool (<PERSON><PERSON> recommended)

## Tutor Mission Management Testing Flow

### Test Case 1: Create Mission

1. Authenticate with a tutor token
2. Send a POST request to `/api/diary/tutor/missions` with valid mission data:
   ```json
   {
     "title": "Weekly Writing Challenge",
     "description": "Write about your favorite book and why you enjoyed it.",
     "targetWordCount": 200,
     "targetMaxWordCount": 300,
     "publishDate": "2023-08-15T00:00:00Z",
     "expiryDate": "2023-08-30T23:59:59Z",
     "score": 100
   }
   ```
3. Verify HTTP status code is 201 Created
4. Verify response contains the created mission details
5. Verify mission is created in the database
6. Verify mission is associated with the authenticated tutor

### Test Case 2: Mission Creation Validation

1. Test with missing required fields (title, description, targetWordCount, publishDate, score)
2. Test with invalid data types (non-string title, non-numeric targetWordCount)
3. Test with invalid values (negative targetWordCount, past publishDate)
4. Verify appropriate error messages for each case

### Test Case 3: Get Tutor Missions

1. Authenticate with a tutor token
2. Send a GET request to `/api/diary/tutor/missions` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains a paged list of missions
5. Verify only missions created by the authenticated tutor are returned
6. Test filtering by isActive, publishDateFrom, publishDateTo
7. Test sorting by different fields

### Test Case 4: Update Mission

1. Authenticate with a tutor token
2. Send a PUT request to `/api/diary/tutor/missions/{id}` with updated mission data
3. Verify HTTP status code is 200 OK
4. Verify response contains the updated mission details
5. Verify mission is updated in the database
6. Test updating individual fields (title, description, targetWordCount, etc.)

### Test Case 5: Delete Mission

1. Authenticate with a tutor token
2. Send a DELETE request to `/api/diary/tutor/missions/{id}`
3. Verify HTTP status code is 200 OK
4. Verify mission is deleted from the database
5. Test deleting a mission that has entries (should fail or cascade delete)

## Student Mission Management Testing Flow

### Test Case 1: Get Today's Mission

1. Authenticate with a student token
2. Send a GET request to `/api/diary/missions/today`
3. Verify HTTP status code is 200 OK
4. Verify response contains a mission object
5. Verify the mission is active and available to the student
6. Verify the mission is either published today or is the most recent active mission

### Test Case 2: Get Available Missions

1. Authenticate with a student token
2. Send a GET request to `/api/diary/missions` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains a paged list of missions
5. Verify only missions available to the student are returned (created by assigned tutors)
6. Verify only active missions with valid publish/expiry dates are returned
7. Test filtering by publishDateFrom, publishDateTo, createdBy

### Test Case 3: Get Specific Mission

1. Authenticate with a student token
2. Send a GET request to `/api/diary/missions/{id}`
3. Verify HTTP status code is 200 OK
4. Verify response contains the mission details
5. Test with a mission not assigned to the student (should return 403 Forbidden)
6. Test with an inactive mission (should return 403 Forbidden)
7. Test with a mission with future publish date (should return 403 Forbidden)
8. Test with an expired mission (should return 403 Forbidden)

## Mission Entry Management Testing Flow

### Test Case 1: Create Mission Entry

1. Authenticate with a student token
2. Send a POST request to `/api/diary/missions/entries` with valid entry data:
   ```json
   {
     "missionId": "123e4567-e89b-12d3-a456-426614174000",
     "content": "My favorite book is..."
   }
   ```
3. Verify HTTP status code is 201 Created
4. Verify response contains the created entry details
5. Verify entry is created in the database with status "NEW"
6. Verify entry is associated with the authenticated student and specified mission
7. Verify word count and progress are calculated correctly

### Test Case 2: Entry Creation Validation

1. Test with missing required fields (missionId, content)
2. Test with invalid mission ID (non-existent, not available to student)
3. Test with empty content
4. Test creating multiple entries for the same mission (should fail)
5. Verify appropriate error messages for each case

### Test Case 3: Update Mission Entry

1. Authenticate with a student token
2. Send a PUT request to `/api/diary/missions/entries/{id}` with updated content
3. Verify HTTP status code is 200 OK
4. Verify response contains the updated entry details
5. Verify entry is updated in the database
6. Verify word count and progress are recalculated
7. Test updating an entry that is not in "NEW" status (should fail)
8. Test updating an entry that belongs to another student (should return 403 Forbidden)

### Test Case 4: Submit Mission Entry

1. Authenticate with a student token
2. Create an entry with content that meets the target word count
3. Send a POST request to `/api/diary/missions/entries/{id}/submit` with an empty body
4. Verify HTTP status code is 200 OK
5. Verify response contains the submitted entry details
6. Verify entry status is changed to "SUBMITTED"
7. Test submitting an entry that doesn't meet the target word count (should fail)
8. Test submitting an entry that is already submitted (should fail)
9. Test submitting an entry that belongs to another student (should return 403 Forbidden)

### Test Case 5: Get Student Mission Entries

1. Authenticate with a student token
2. Send a GET request to `/api/diary/missions/entries` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains a paged list of entries
5. Verify only entries created by the authenticated student are returned
6. Test filtering by missionId, status, createdAtFrom, createdAtTo
7. Test sorting by different fields

## Tutor Review Testing Flow

### Test Case 1: Get Tutor Mission Entries

1. Authenticate with a tutor token
2. Send a GET request to `/api/diary/tutor/missions/entries` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains a paged list of entries
5. Verify only entries from students assigned to the tutor are returned
6. Test filtering by missionId, studentId, status, createdAtFrom, createdAtTo
7. Test sorting by different fields

### Test Case 2: Add Feedback

1. Authenticate with a tutor token
2. Send a POST request to `/api/diary/tutor/missions/entries/{id}/feedback` with feedback data:
   ```json
   {
     "feedback": "Great job on your analysis of the book!",
     "rating": 4
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains the feedback details
5. Verify feedback is added to the entry in the database
6. Test adding feedback to an entry that is not submitted (should fail)
7. Test adding feedback to an entry from a student not assigned to the tutor (should return 403 Forbidden)

### Test Case 3: Add Correction with Score (Combined)

1. Authenticate with a tutor token
2. Send a POST request to `/api/diary/tutor/missions/entries/{id}/correction` with correction and score data:
   ```json
   {
     "correction": "Here are some grammar corrections...",
     "score": 85
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains the updated entry with both correction and score
5. Verify entry status is changed to "CONFIRMED"
6. Verify both correction and score are added to the entry in the database
7. Test adding correction and score to an entry that is not submitted (should fail)
8. Test adding correction and score to an entry that already has a correction (should fail)
9. Test adding correction and score to an entry that already has a score (should fail)
10. Test with missing correction field (should fail with validation error)
11. Test with missing score field (should fail with validation error)
12. Test assigning a score outside the valid range (negative or greater than mission score)
13. Test adding correction and score to an entry from a student not assigned to the tutor (should return 403 Forbidden)

### Test Case 4: Add Correction Only (DEPRECATED)

1. Authenticate with a tutor token
2. Send a POST request to `/api/diary/tutor/missions/entries/{id}/correction-only` with correction data:
   ```json
   {
     "correction": "Here are some grammar corrections..."
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains the updated entry with correction
5. Verify entry status is changed to "REVIEWED"
6. Verify correction is added to the entry in the database
7. Test adding correction to an entry that is not submitted (should fail)
8. Test adding correction to an entry that already has a correction (should fail)
9. Test adding correction to an entry from a student not assigned to the tutor (should return 403 Forbidden)

### Test Case 5: Assign Score Only (DEPRECATED)

1. Authenticate with a tutor token
2. Send a POST request to `/api/diary/tutor/missions/entries/{id}/score` with score data:
   ```json
   {
     "score": 85
   }
   ```
3. Verify HTTP status code is 200 OK
4. Verify response contains the updated entry with score
5. Verify entry status is changed to "CONFIRMED"
6. Verify score is added to the entry in the database
7. Test assigning a score to an entry that is not submitted (should fail)
8. Test assigning a score to an entry that already has a score (should fail)
9. Test assigning a score outside the valid range (negative or greater than mission score)
10. Test assigning a score to an entry from a student not assigned to the tutor (should return 403 Forbidden)

## Notification Testing Flow

### Primary Flow (Using Combined API)

1. Create a mission as a tutor
2. Create and submit an entry as a student
3. Verify the tutor receives a notification about the submission
4. Add feedback as a tutor (optional)
5. Verify the student receives a notification about the feedback (if provided)
6. Add correction with score as a tutor (combined operation)
7. Verify the student receives a notification about the completed review with both correction and score

### Legacy Flow (Using Deprecated APIs)

1. Create a mission as a tutor
2. Create and submit an entry as a student
3. Verify the tutor receives a notification about the submission
4. Add feedback as a tutor
5. Verify the student receives a notification about the feedback
6. Add correction as a tutor (using deprecated endpoint)
7. Verify the student receives a notification about the correction
8. Assign score as a tutor (using deprecated endpoint)
9. Verify the student receives a notification about the score

## Edge Cases and Error Handling

1. Test with expired authentication tokens
2. Test with users who don't have the required roles
3. Test with non-existent resource IDs
4. Test with malformed request bodies
5. Test with extremely large content values
6. Test with special characters in text fields
7. Test with boundary values for numeric fields

## Conclusion

This testing flow covers the main functionality of the Diary Mission Management API. By following these test cases, you can ensure that the API works correctly and handles edge cases appropriately.
