version: "3.9" # Use the latest version of Docker Compose

services: # Define the services to be started
  stg-api: # Define the API service
    container_name: hec-api-stg # Name the container for easy identification
    build: # Build the development image from the Dockerfile
      context: . # Use the current directory as the build context
      dockerfile: Dockerfile.stg # Use the development Dockerfile
    ports:
      - "3012:3012" # Map container port 3012 to host port 3012
    restart: unless-stopped # Restart the container automatically on failure
    volumes:
      - ./logs:/app/logs # Mount logs directory for persistent logs
      - ./uploads:/app/uploads # Mount uploads directory for persistent file storage
      - ./.env:/app/.env # Mount .env file for environment variables
      - ./tsconfig.json:/app/tsconfig.json # Mount TypeScript configuration
      - ./nest-cli.json:/app/nest-cli.json # Mount NestJS CLI configuration
      - ./src:/app/src # Mount source code for development
      - ./package.json:/app/package.json # Mount package.json for development
      - ./package-lock.json:/app/package-lock.json # Mount package-lock.json for development
    networks:
      - hec-network-stg # Connect the container to the hec-network network
    environment:
      NODE_ENV: staging # Set the environment to development
      UPLOAD_DIR: /app/uploads # Define the upload directory
      LOG_PATH: /app/logs # Define the log directory
    # Use a different command for development to watch for changes
    command: npm run start # Start the application in development mode with watch

networks:
  hec-network-stg: # Define the hec-network network
    driver: bridge # Use the bridge driver for the network
    
