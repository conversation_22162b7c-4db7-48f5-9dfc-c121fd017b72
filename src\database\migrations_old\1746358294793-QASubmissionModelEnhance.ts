import { MigrationInterface, QueryRunner } from "typeorm";

export class QASubmissionModelEnhance1746358294793 implements MigrationInterface {
    name = 'QASubmissionModelEnhance1746358294793'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."qa_mission_goal_time_frequency_enum" AS ENUM('weekly', 'monthly')`);
        await queryRunner.query(`CREATE TABLE "qa_mission_goal" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "time_frequency" "public"."qa_mission_goal_time_frequency_enum" NOT NULL DEFAULT 'weekly', "is_active" boolean NOT NULL DEFAULT true, "sequence_number" integer NOT NULL DEFAULT '1', CONSTRAINT "PK_cd70982a1224cc072b198e82699" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "qa_task_missions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying(50) NOT NULL, "description" text NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "time_period_unit" integer DEFAULT '1', "word_limit_minumum" integer NOT NULL, "word_limit_maximum" integer, "deadline" integer, "instructions" text NOT NULL, "mission_id" uuid NOT NULL, "meta_data" json, "mission" uuid, CONSTRAINT "PK_04c6e81802d294d6b5dddc2908c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "qa_task_missions" ADD CONSTRAINT "FK_4b179e99b9d286e8eb715182721" FOREIGN KEY ("mission") REFERENCES "qa_mission_goal"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_task_missions" DROP CONSTRAINT "FK_4b179e99b9d286e8eb715182721"`);
        await queryRunner.query(`DROP TABLE "qa_task_missions"`);
        await queryRunner.query(`DROP TABLE "qa_mission_goal"`);
        await queryRunner.query(`DROP TYPE "public"."qa_mission_goal_time_frequency_enum"`);
    }
}
