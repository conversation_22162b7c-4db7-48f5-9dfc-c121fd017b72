import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { WaterfallSet } from '../../../database/entities/waterfall-set.entity';
import { WaterfallQuestion } from '../../../database/entities/waterfall-question.entity';
import { WaterfallParticipation } from '../../../database/entities/waterfall-participation.entity';
import { WaterfallAnswer } from '../../../database/entities/waterfall-answer.entity';
import { SubmitWaterfallGameDto, WaterfallParticipationResponseDto, WaterfallAnswerResponseDto } from '../../../database/models/waterfall/waterfall-submission.dto';
import { WaterfallGameResponseDto } from '../../../database/models/waterfall/waterfall-response.dto';

@Injectable()
export class WaterfallService {
  private readonly logger = new Logger(WaterfallService.name);

  constructor(
    @InjectRepository(WaterfallSet)
    private readonly setRepository: Repository<WaterfallSet>,
    @InjectRepository(WaterfallQuestion)
    private readonly questionRepository: Repository<WaterfallQuestion>,
    @InjectRepository(WaterfallParticipation)
    private readonly participationRepository: Repository<WaterfallParticipation>,
    @InjectRepository(WaterfallAnswer)
    private readonly answerRepository: Repository<WaterfallAnswer>,
    private readonly dataSource: DataSource,
  ) {}

  // Get all waterfall sets
  async getAllSets(): Promise<WaterfallSet[]> {
    return this.setRepository.find({
      order: {
        createdAt: 'DESC',
      },
    });
  }

  // Get a specific waterfall set by ID with its questions
  async getSetById(setId: string): Promise<WaterfallSet | null> {
    return this.setRepository.findOne({
      where: { id: setId },
      relations: ['questions'],
    });
  }

  // Get questions for a specific set
  async getQuestionsBySetId(setId: string): Promise<WaterfallQuestion[]> {
    return this.questionRepository.find({
      where: { setId },
      order: {
        id: 'ASC',
      },
    });
  }

  /**
   * Get a random waterfall set with questions for a student to play
   * @param studentId The ID of the student
   * @returns A random waterfall set with questions
   */
  async getRandomSet(studentId: string): Promise<WaterfallGameResponseDto> {
    try {
      // Get all sets that have questions
      const queryBuilder = this.setRepository.createQueryBuilder('set').leftJoinAndSelect('set.questions', 'questions').where('set.totalQuestions > 0').orderBy('RANDOM()'); // PostgreSQL random ordering

      // Get a random set
      const set = await queryBuilder.getOne();

      if (!set) {
        this.logger.warn('No waterfall sets with questions found');
        throw new NotFoundException('No games available at the moment. Please try again later.');
      }

      const response: WaterfallGameResponseDto = {
        id: set.id,
        title: set.title,
        total_score: set.totalScore,
        total_questions: set.totalQuestions,
        questions: set.questions.map((question) => ({
          id: question.id,
          question_text: question.questionText,
          question_text_plain: question.questionTextPlain,
          options: question.options,
          correct_answers: question.correctAnswers,
        })),
      };

      return response;
    } catch (error) {
      this.logger.error(`Failed to get random waterfall set: ${error.message}`, error.stack);

      // Rethrow the error if it's already a NestJS exception
      if (error instanceof NotFoundException) {
        throw error;
      }

      // Otherwise, wrap it in a BadRequestException with a user-friendly message
      throw new BadRequestException("We couldn't find a game for you right now. Please try again later.");
    }
  }

  // This method has been replaced by submitGame

  // Helper method to check if the student's answers match the correct answers
  private checkAnswers(correctAnswers: string[], studentAnswers: string[]): boolean {
    if (correctAnswers.length !== studentAnswers.length) {
      return false;
    }

    for (let i = 0; i < correctAnswers.length; i++) {
      // Case-insensitive comparison
      if (correctAnswers[i].toLowerCase() !== studentAnswers[i].toLowerCase()) {
        return false;
      }
    }

    return true;
  }

  /**
   * Submit a waterfall game result
   * @param userId The ID of the user submitting the game
   * @param setId The ID of the set being played
   * @param dto The submission data
   * @returns The participation record with detailed results
   */
  async submitGame(userId: string, setId: string, dto: SubmitWaterfallGameDto): Promise<WaterfallParticipationResponseDto> {
    // Ensure setId from parameter matches the one in the DTO
    if (setId !== dto.set_id) {
      this.logger.warn(`Set ID mismatch: parameter ${setId} vs DTO ${dto.set_id}`);
      setId = dto.set_id; // Use the one from the DTO
    }
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the set
      const set = await this.setRepository.findOne({
        where: { id: setId },
      });

      if (!set) {
        this.logger.warn(`Waterfall set with ID ${setId} not found`);
        throw new NotFoundException(`Game not found. Please try a different game.`);
      }

      // Get all questions for the set
      const questions = await this.questionRepository.find({
        where: { setId },
      });

      if (questions.length === 0) {
        this.logger.warn(`No questions found for set with ID ${setId}`);
        throw new BadRequestException(`This game has no questions. Please try a different game.`);
      }

      // Create a map of question IDs to questions for easy lookup
      const questionMap = new Map<string, WaterfallQuestion>();
      questions.forEach((question) => {
        questionMap.set(question.id, question);
      });

      // Validate that all submitted questions belong to the set
      for (const answer of dto.answers) {
        if (!questionMap.has(answer.question_id)) {
          this.logger.warn(`Question with ID ${answer.question_id} does not belong to the set ${setId}`);
          throw new BadRequestException(`There seems to be a problem with your submission. Please refresh the page and try again.`);
        }
      }

      // Log if not all questions are answered, but don't throw an error
      if (dto.answers.length !== questions.length) {
        this.logger.warn(`Incomplete submission: ${dto.answers.length} answers provided for ${questions.length} questions`);
      }

      // Check if at least one question is answered
      if (dto.answers.length === 0) {
        this.logger.warn(`No answers provided in submission`);
        throw new BadRequestException(`Please answer at least one question.`);
      }

      // Check for duplicate question IDs in the submission - only log, don't throw error
      const submittedQuestionIds = dto.answers.map((a) => a.question_id);
      const uniqueQuestionIds = new Set(submittedQuestionIds);
      if (submittedQuestionIds.length !== uniqueQuestionIds.size) {
        this.logger.warn(`Duplicate question IDs found in submission`);
        // We'll process the submission anyway, but we'll only count each question once
      }

      // Calculate the score and create the result
      let correctCount = 0;
      const answerResults: WaterfallAnswerResponseDto[] = [];
      const answerEntities: WaterfallAnswer[] = [];

      // Create the participation record
      const participation = new WaterfallParticipation();
      participation.studentId = userId;
      participation.setId = setId;
      participation.totalQuestions = questions.length; // This is the total questions in the set
      participation.totalCorrectAnswers = 0; // Initialize to 0, will update after processing answers
      participation.score = 0; // Initialize to 0, will update after processing answers
      participation.createdBy = userId;
      participation.updatedBy = userId;

      // Save the participation record to get an ID
      const savedParticipation = await queryRunner.manager.save(participation);

      // Process each answer
      for (const answerDto of dto.answers) {
        const question = questionMap.get(answerDto.question_id);

        // Log if the number of submitted answers doesn't match the number of correct answers
        // This is just for logging purposes - we'll still try to evaluate the answer
        if (question.correctAnswers.length !== answerDto.answers.length) {
          this.logger.warn(`Answer format mismatch for question ${answerDto.question_id}: expected ${question.correctAnswers.length} answers, got ${answerDto.answers.length}`);
        }

        // Check if the answers match
        const isCorrect = this.checkAnswers(question.correctAnswers, answerDto.answers);

        if (isCorrect) {
          correctCount++;
        }

        // Create the answer entity
        const answer = new WaterfallAnswer();
        answer.participationId = savedParticipation.id;
        answer.questionId = answerDto.question_id;
        answer.submittedAnswers = answerDto.answers;
        answer.isCorrect = isCorrect;
        answer.createdBy = userId;
        answer.updatedBy = userId;

        answerEntities.push(answer);

        // Add to the response
        answerResults.push({
          question_id: answerDto.question_id,
          submitted_answers: answerDto.answers,
          is_correct: isCorrect,
          correct_answers: question.correctAnswers,
        });
      }

      // Calculate the score based on the set's total score and the number of correct answers
      let score = 0;
      if (questions.length > 0 && set.totalScore > 0) {
        // Calculate the points per question
        const pointsPerQuestion = set.totalScore / questions.length;

        // Calculate score based on the number of correct answers
        score = Math.round(correctCount * pointsPerQuestion);

        // Log the submission details
        if (dto.answers.length < questions.length) {
          this.logger.log(`Partial submission score: ${score} (${correctCount} correct answers out of ${dto.answers.length} answered questions, ${questions.length} total questions)`);
        } else {
          this.logger.log(`Full submission score: ${score} (${correctCount} correct answers out of ${questions.length} total questions)`);
        }
      } else {
        this.logger.warn(`Invalid score calculation parameters: questions.length=${questions.length}, set.totalScore=${set.totalScore}`);
      }

      // Update the participation record with the score
      savedParticipation.totalCorrectAnswers = correctCount;
      savedParticipation.score = score;

      // Save the updated participation record
      await queryRunner.manager.save(savedParticipation);

      // Save all answer records
      await queryRunner.manager.save(answerEntities);

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Return the response
      return {
        id: savedParticipation.id,
        set_id: setId,
        set_title: set.title,
        total_correct_answers: correctCount,
        total_questions: questions.length,
        score: score,
        submitted_at: savedParticipation.createdAt,
        answers: answerResults,
      };
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      this.logger.error(`Failed to submit waterfall game for set ${setId}: ${error.message}`, error.stack);

      // Rethrow NestJS exceptions as they already have appropriate status codes and messages
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }

      // For unexpected errors, provide a generic user-friendly message
      throw new BadRequestException("We couldn't process your answers. Please check your submission and try again.");
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }
}
