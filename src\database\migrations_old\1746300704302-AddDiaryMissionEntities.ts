import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDiaryMissionEntities1746300704302 implements MigrationInterface {
    name = 'AddDiaryMissionEntities1746300704302'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_student_owned_item_purchase"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_student_owned_item_shop_item"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_student_owned_item_student"`);
        await queryRunner.query(`ALTER TABLE "shopping_cart" DROP CONSTRAINT "FK_shopping_cart_user"`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" DROP CONSTRAINT "FK_shopping_cart_item_shop_item"`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" DROP CONSTRAINT "FK_shopping_cart_item_cart"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_student_owned_item_student_shop_item"`);
        await queryRunner.query(`CREATE TABLE "diary_mission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying NOT NULL, "description" text NOT NULL, "target_word_count" integer NOT NULL, "publish_date" TIMESTAMP NOT NULL, "expiry_date" TIMESTAMP, "tutor_id" uuid NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "score" integer NOT NULL, CONSTRAINT "PK_c04698dc3a0fa7f33193684fd5e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "mission_diary_entry_feedback" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "mission_entry_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "feedback" text NOT NULL, "rating" integer, CONSTRAINT "PK_e496f231dbe9ee4e06489a16070" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."mission_diary_entry_status_enum" AS ENUM('new', 'submit', 'reviewed', 'confirm')`);
        await queryRunner.query(`CREATE TABLE "mission_diary_entry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "mission_id" uuid NOT NULL, "student_id" uuid NOT NULL, "content" text NOT NULL, "word_count" integer NOT NULL, "progress" double precision NOT NULL, "status" "public"."mission_diary_entry_status_enum" NOT NULL DEFAULT 'new', "gained_score" integer, "reviewed_by" uuid, "reviewed_at" TIMESTAMP, "correction" text, "correction_provided_at" TIMESTAMP, "correction_provided_by" uuid, CONSTRAINT "PK_f3eba44dee5ab5ad5ba5c4ee5cc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "shop_category" DROP COLUMN "parent_id"`);
        await queryRunner.query(`ALTER TABLE "shop_category" ADD "parent_id" uuid`);
        await queryRunner.query(`ALTER TYPE "public"."shop_item_category_enum" RENAME TO "shop_item_category_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."shop_item_shop_item_category_enum" AS ENUM('skin', 'emoticon')`);
        await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "shop_item_category" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "shop_item_category" TYPE "public"."shop_item_shop_item_category_enum" USING "shop_item_category"::"text"::"public"."shop_item_shop_item_category_enum"`);
        await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "shop_item_category" SET DEFAULT 'skin'`);
        await queryRunner.query(`DROP TYPE "public"."shop_item_category_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."owned_item_status_enum" RENAME TO "owned_item_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."student_owned_item_status_enum" AS ENUM('available', 'in_use', 'expired')`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ALTER COLUMN "status" TYPE "public"."student_owned_item_status_enum" USING "status"::"text"::"public"."student_owned_item_status_enum"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ALTER COLUMN "status" SET DEFAULT 'available'`);
        await queryRunner.query(`DROP TYPE "public"."owned_item_status_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" RENAME TO "notification_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."notification_type_enum" AS ENUM('diary_submission', 'diary_update', 'diary_review', 'diary_feedback', 'mission_created', 'mission_submission', 'mission_feedback', 'mission_correction', 'mission_review_complete', 'tutor_greeting', 'tutor_assignment', 'tutor_verification', 'chat_message', 'system')`);
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum" USING "type"::"text"::"public"."notification_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."notification_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" DROP CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2"`);
        await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" RENAME TO "user_notification_preference_notification_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."user_notification_preference_notification_type_enum" AS ENUM('diary_submission', 'diary_update', 'diary_review', 'diary_feedback', 'mission_created', 'mission_submission', 'mission_feedback', 'mission_correction', 'mission_review_complete', 'tutor_greeting', 'tutor_assignment', 'tutor_verification', 'chat_message', 'system')`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" ALTER COLUMN "notification_type" TYPE "public"."user_notification_preference_notification_type_enum" USING "notification_type"::"text"::"public"."user_notification_preference_notification_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."user_notification_preference_notification_type_enum_old"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_a3e9a158d886e27427535a1f0c" ON "student_owned_item" ("student_id", "shop_item_id") `);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" ADD CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2" UNIQUE ("user_id", "notification_type", "channel")`);
        await queryRunner.query(`ALTER TABLE "shop_category" ADD CONSTRAINT "FK_a2995f26fcc34c7a1f4513760fd" FOREIGN KEY ("parent_id") REFERENCES "shop_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ADD CONSTRAINT "FK_5ec730ca7ae3840095734dd8dae" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ADD CONSTRAINT "FK_afd6408684da50921602e404057" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ADD CONSTRAINT "FK_503b746c1e584b2ef199093e62b" FOREIGN KEY ("purchase_id") REFERENCES "shop_item_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shopping_cart" ADD CONSTRAINT "FK_2486032b4fc81da82629c53f955" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" ADD CONSTRAINT "FK_33e6e53bd90f5f35dabf8c509a6" FOREIGN KEY ("cart_id") REFERENCES "shopping_cart"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" ADD CONSTRAINT "FK_f06d406b33e3bacdc465d5baeaa" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_mission" ADD CONSTRAINT "FK_4e9b74e8605cd1c89ab282bc81a" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry_feedback" ADD CONSTRAINT "FK_5cb69c9da1e75c8e8ca70f2fa0e" FOREIGN KEY ("mission_entry_id") REFERENCES "mission_diary_entry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry_feedback" ADD CONSTRAINT "FK_5cb791d97eacc59d2110a9d795e" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ADD CONSTRAINT "FK_a3b7d2760ae08ffe65191d711f4" FOREIGN KEY ("mission_id") REFERENCES "diary_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ADD CONSTRAINT "FK_438585b4eb3e9eb83307641aee0" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ADD CONSTRAINT "FK_f3970b9186ce11f3a8a97691329" FOREIGN KEY ("reviewed_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" DROP CONSTRAINT "FK_f3970b9186ce11f3a8a97691329"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" DROP CONSTRAINT "FK_438585b4eb3e9eb83307641aee0"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" DROP CONSTRAINT "FK_a3b7d2760ae08ffe65191d711f4"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry_feedback" DROP CONSTRAINT "FK_5cb791d97eacc59d2110a9d795e"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry_feedback" DROP CONSTRAINT "FK_5cb69c9da1e75c8e8ca70f2fa0e"`);
        await queryRunner.query(`ALTER TABLE "diary_mission" DROP CONSTRAINT "FK_4e9b74e8605cd1c89ab282bc81a"`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" DROP CONSTRAINT "FK_f06d406b33e3bacdc465d5baeaa"`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" DROP CONSTRAINT "FK_33e6e53bd90f5f35dabf8c509a6"`);
        await queryRunner.query(`ALTER TABLE "shopping_cart" DROP CONSTRAINT "FK_2486032b4fc81da82629c53f955"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_503b746c1e584b2ef199093e62b"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_afd6408684da50921602e404057"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_5ec730ca7ae3840095734dd8dae"`);
        await queryRunner.query(`ALTER TABLE "shop_category" DROP CONSTRAINT "FK_a2995f26fcc34c7a1f4513760fd"`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" DROP CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a3e9a158d886e27427535a1f0c"`);
        await queryRunner.query(`CREATE TYPE "public"."user_notification_preference_notification_type_enum_old" AS ENUM('chat_message', 'diary_feedback', 'diary_review', 'diary_submission', 'diary_update', 'system', 'tutor_assignment', 'tutor_greeting', 'tutor_verification')`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" ALTER COLUMN "notification_type" TYPE "public"."user_notification_preference_notification_type_enum_old" USING "notification_type"::"text"::"public"."user_notification_preference_notification_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."user_notification_preference_notification_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum_old" RENAME TO "user_notification_preference_notification_type_enum"`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" ADD CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2" UNIQUE ("user_id", "notification_type", "channel")`);
        await queryRunner.query(`CREATE TYPE "public"."notification_type_enum_old" AS ENUM('chat_message', 'diary_feedback', 'diary_review', 'diary_submission', 'diary_update', 'system', 'tutor_assignment', 'tutor_greeting', 'tutor_verification')`);
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum_old" USING "type"::"text"::"public"."notification_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."notification_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."notification_type_enum_old" RENAME TO "notification_type_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."owned_item_status_enum_old" AS ENUM('available', 'expired', 'in_use')`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ALTER COLUMN "status" TYPE "public"."owned_item_status_enum_old" USING "status"::"text"::"public"."owned_item_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ALTER COLUMN "status" SET DEFAULT 'available'`);
        await queryRunner.query(`DROP TYPE "public"."student_owned_item_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."owned_item_status_enum_old" RENAME TO "owned_item_status_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."shop_item_category_enum_old" AS ENUM('emoticon', 'skin')`);
        await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "shop_item_category" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "shop_item_category" TYPE "public"."shop_item_category_enum_old" USING "shop_item_category"::"text"::"public"."shop_item_category_enum_old"`);
        await queryRunner.query(`ALTER TABLE "shop_item" ALTER COLUMN "shop_item_category" SET DEFAULT 'skin'`);
        await queryRunner.query(`DROP TYPE "public"."shop_item_shop_item_category_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."shop_item_category_enum_old" RENAME TO "shop_item_category_enum"`);
        await queryRunner.query(`ALTER TABLE "shop_category" DROP COLUMN "parent_id"`);
        await queryRunner.query(`ALTER TABLE "shop_category" ADD "parent_id" character varying`);
        await queryRunner.query(`DROP TABLE "mission_diary_entry"`);
        await queryRunner.query(`DROP TYPE "public"."mission_diary_entry_status_enum"`);
        await queryRunner.query(`DROP TABLE "mission_diary_entry_feedback"`);
        await queryRunner.query(`DROP TABLE "diary_mission"`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_student_owned_item_student_shop_item" ON "student_owned_item" ("student_id", "shop_item_id") `);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" ADD CONSTRAINT "FK_shopping_cart_item_cart" FOREIGN KEY ("cart_id") REFERENCES "shopping_cart"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" ADD CONSTRAINT "FK_shopping_cart_item_shop_item" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shopping_cart" ADD CONSTRAINT "FK_shopping_cart_user" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ADD CONSTRAINT "FK_student_owned_item_student" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ADD CONSTRAINT "FK_student_owned_item_shop_item" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ADD CONSTRAINT "FK_student_owned_item_purchase" FOREIGN KEY ("purchase_id") REFERENCES "shop_item_purchase"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
    }

}
