import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { ProfileViewController } from './profile-view.controller';
import { TutorEducationService } from './tutor-education.service';
import { TutorEducationController } from './tutor-education.controller';
import { AuthModule } from '../auth/auth.module';
import { JwtService } from '@nestjs/jwt';
import { User } from 'src/database/entities/user.entity';
import { ProfilePicture } from 'src/database/entities/profile-picture.entity';
import { Role } from 'src/database/entities/role.entity';
import { UserRole } from 'src/database/entities/user-role.entity';
import { UserPlan } from 'src/database/entities/user-plan.entity';
import { Plan } from 'src/database/entities/plan.entity';
import { StudentTutorMapping } from 'src/database/entities/student-tutor-mapping.entity';
import { TutorEducation } from 'src/database/entities/tutor-education.entity';
import { TutorApproval } from 'src/database/entities/tutor-approval.entity';
import { CommonModule } from 'src/common/common.module';
import { MulterModule } from '@nestjs/platform-express';
import { EmailModule } from '../email/email.module';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    TypeOrmModule.forFeature([
      User,
      Role,
      UserRole,
      Plan,
      UserPlan,
      ProfilePicture,
      StudentTutorMapping,
      TutorEducation,
      TutorApproval
    ]),
    CommonModule,
    EmailModule,
    MulterModule.register({
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  ],
  providers: [
    UsersService,
    JwtService,
    TutorEducationService
  ],
  controllers: [
    UsersController,
    ProfileViewController,
    TutorEducationController
  ],
  exports: [UsersService, TutorEducationService],
})
export class UsersModule {}
