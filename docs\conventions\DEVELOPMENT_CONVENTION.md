# HEC Backend Development Conventions

This document outlines the development conventions for the HEC Backend project. All developers should follow these guidelines to ensure consistency, maintainability, and high code quality.

## Table of Contents

1. [Project Structure](#project-structure)
2. [Naming Conventions](#naming-conventions)
3. [API Design](#api-design)
4. [Error Handling](#error-handling)
5. [Authentication](#authentication)
6. [Documentation](#documentation)
7. [Testing](#testing)
8. [Code Style](#code-style)
9. [Database](#database)
10. [Logging](#logging)

## Project Structure

The project follows a modular structure based on NestJS framework:

```
src/
├── common/              # Shared utilities, decorators, guards, etc.
│   ├── decorators/      # Custom decorators
│   ├── dto/             # Common DTOs
│   ├── filters/         # Exception filters
│   ├── guards/          # Authentication guards
│   ├── interceptors/    # Request/response interceptors
│   ├── models/          # Common models
│   └── services/        # Common services
├── database/            # Database-related files
│   ├── decorators/      # Database-related decorators
│   ├── entities/        # TypeORM entities
│   ├── interfaces/      # Database interfaces
│   └── models/          # Database DTOs
├── modules/             # Feature modules
│   ├── auth/            # Authentication module
│   ├── diary/           # Diary module
│   ├── plans/           # Subscription plans module
│   ├── tutor-approval/  # Tutor approval module
│   └── users/           # User management module
└── main.ts              # Application entry point
```

### Module Structure

Each feature module should follow this structure:

```
module-name/
├── module-name.controller.ts  # API endpoints
├── module-name.module.ts      # Module definition
├── module-name.service.ts     # Business logic
└── module-name.spec.ts        # Tests
```

## Naming Conventions

### Files

- Use kebab-case for file names: `user-profile.service.ts`
- Use descriptive suffixes: `.controller.ts`, `.service.ts`, `.dto.ts`, `.entity.ts`, etc.
- Test files should end with `.spec.ts`

### Classes

- Use PascalCase for class names: `UserProfileService`
- Controllers should end with `Controller`: `AuthController`
- Services should end with `Service`: `UserService`
- DTOs should end with `Dto`: `CreateUserDto`
- Use camelCase for all DTO properties to maintain consistency with entity properties
- Entities should use singular nouns: `User`, not `Users`
- Interfaces should start with `I` or describe their purpose: `IUser` or `UserInterface`

### Methods

- Use camelCase for method names: `findUserById`
- HTTP methods in controllers should use descriptive verbs:
  - `findAll()`, `findOne()`, `create()`, `update()`, `remove()`
- Service methods should be descriptive of their business function

### Variables and Properties

- Use camelCase for variable names: `userProfile`
- Use camelCase for entity properties: `user.firstName`, `diaryEntry.content`
- Use descriptive names that indicate purpose and type
- Boolean variables and properties should use prefixes like `is`, `has`, `should`: `isActive`, `hasPermission`

### Entity Property Naming

- Always use camelCase for entity properties (e.g., `userId`, `createdAt`, `isActive`)
- The database column names will be automatically converted to snake_case by the naming strategy (e.g., `user_id`, `created_at`, `is_active`)
- When defining columns, always include the `name` property to explicitly map to the snake_case database column:

```typescript
@Column({ name: 'first_name' })
firstName: string;

@Column({ name: 'is_active', default: true })
isActive: boolean;
```

### Consistency in Naming

- Maintain consistency in naming across the entire codebase
- All entity properties, DTO properties, and variables should use camelCase
- This consistency makes it easier to map between entities, DTOs, and JSON responses
- When accessing properties in code, always use the camelCase version (e.g., `user.firstName`, not `user.FirstName`)
- When querying the database using TypeORM, use camelCase in the query conditions (TypeORM will handle the conversion):

```typescript
// Correct
const user = await this.userRepository.findOne({ where: { userId: 'john123' } });

// Incorrect
const user = await this.userRepository.findOne({ where: { UserId: 'john123' } });
```

## API Design

### Response Structure

All API responses must follow the unified `ApiResponse<T>` structure:

```typescript
{
  success: boolean;       // Indicates if the request was successful
  message: string;        // Human-readable message
  statusCode: number;     // HTTP status code
  data?: T;               // Response data (optional)
  errors?: ErrorDetail[]; // Error details if success is false (optional)
}
```

### Swagger Documentation

- All endpoints must be documented using Swagger decorators
- Use the latest convention with `ApiOkResponseWithType` or `ApiOkResponseWithArrayType`
- Document all possible responses, including errors
- Provide examples for request bodies and parameters

Example:

```typescript
@ApiOkResponseWithType(UserResponseDto, 'User retrieved successfully')
@ApiErrorResponse(404, 'User not found')
async findOne(@Param('id') id: string): Promise<ApiResponse<UserResponseDto>> {
  // Implementation
}
```

### HTTP Status Codes

Use appropriate HTTP status codes:

- 200: OK (successful GET, PUT, PATCH)
- 201: Created (successful POST that creates a resource)
- 204: No Content (successful DELETE)
- 400: Bad Request (validation errors)
- 401: Unauthorized (missing or invalid authentication)
- 403: Forbidden (authenticated but not authorized)
- 404: Not Found (resource not found)
- 409: Conflict (resource already exists)
- 500: Internal Server Error (server-side errors)

## Error Handling

### Global Exception Filter

All exceptions should be handled by the global exception filter, which transforms them into the unified API response format.

### Custom Exceptions

Use NestJS built-in exceptions or create custom exceptions that extend `HttpException`:

```typescript
throw new BadRequestException('Invalid input data');
throw new NotFoundException('User not found');
```

### Validation

- Use class-validator decorators for DTO validation
- Provide descriptive error messages in validation decorators

Example:

```typescript
@IsEmail({}, { message: 'Please provide a valid email address' })
@IsNotEmpty({ message: 'Email is required' })
email: string;
```

## Authentication

### JWT Authentication

- Use JWT for authentication
- Include user ID, roles, and other essential information in the token payload
- Set appropriate token expiration times
- Use refresh tokens for extended sessions

### Guards

- Use `JwtAuthGuard` for protected routes
- Use role-specific guards (`AdminGuard`, `TutorGuard`, `StudentGuard`) for role-based access control
- Use the `@Public()` decorator for public endpoints

Example:

```typescript
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
@Get('admin-only')
adminOnlyEndpoint() {
  // Implementation
}
```

## Documentation

### Code Comments

- Use JSDoc-style comments for classes, methods, and complex logic
- Document parameters, return types, and thrown exceptions
- Explain the purpose and behavior of complex code

Example:

```typescript
/**
 * Retrieves a user by their ID
 * @param id The user ID
 * @returns The user entity
 * @throws NotFoundException if the user is not found
 */
async findById(id: string): Promise<User> {
  // Implementation
}
```

### Swagger Documentation

- Use `@ApiTags` to categorize endpoints
- Use `@ApiOperation` to describe endpoint purpose
- Use `@ApiParam`, `@ApiQuery`, and `@ApiBody` to document parameters
- Use `@ApiOkResponseWithType` and `@ApiErrorResponse` to document responses

## Testing

### Unit Tests

- Write unit tests for services and other business logic
- Use Jest as the testing framework
- Mock dependencies using Jest mocks or NestJS testing utilities

### Integration Tests

- Write integration tests for API endpoints
- Test the complete request-response cycle
- Use an in-memory database for testing

### Test Naming

- Test files should be named the same as the file they test, with a `.spec.ts` suffix
- Test descriptions should clearly state what is being tested and the expected outcome

Example:

```typescript
describe('AuthService', () => {
  describe('login', () => {
    it('should return a JWT token when credentials are valid', async () => {
      // Test implementation
    });

    it('should throw UnauthorizedException when credentials are invalid', async () => {
      // Test implementation
    });
  });
});
```

## Code Style

### Formatting

- Use Prettier for code formatting
- Use 2 spaces for indentation
- Maximum line length: 100 characters
- Use single quotes for strings
- Add semicolons at the end of statements

### Linting

- Use ESLint for code linting
- Fix all linting errors before committing code
- Follow the configured ESLint rules

### Imports

- Group imports in the following order:
  1. External libraries (NestJS, TypeORM, etc.)
  2. Internal modules (relative imports)
  3. Models, DTOs, and interfaces
- Sort imports alphabetically within each group
- Use absolute imports for modules and relative imports for files in the same module

Example:

```typescript
// External libraries
import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation } from '@nestjs/swagger';

// Internal modules
import { AuthService } from './auth.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';

// Models, DTOs, and interfaces
import { LoginUserDto, RegisterDto } from '../../database/models/users.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
```

## Database

### Entities

- Use TypeORM entities to define database schema
- Use appropriate column types and constraints
- Define relationships between entities
- Use UUIDs for primary keys
- Use camelCase for entity properties (e.g., `userId`, `createdAt`, `isActive`)

### File Registry System

- Use the FileRegistryService for all file operations
- Follow the conventions in [FILE_UPLOAD_CONVENTION.md](./FILE_UPLOAD_CONVENTION.md)
- Use entity-specific registry tables for file metadata
- Use the getFileUrlWithFallback method for URL generation
- Implement proper error handling and fallback mechanisms

Example:

```typescript
@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @OneToMany(() => UserRole, userRole => userRole.user)
  userRoles: UserRole[];
}
```

### Migrations

- Use TypeORM migrations for database schema changes
- Generate migrations automatically or write them manually
- Run migrations during application startup

### Repositories

- Use TypeORM repositories for database operations
- Inject repositories into services using `@InjectRepository()`
- Use repository methods for CRUD operations

## Logging

### Logger

- Use NestJS built-in logger or a custom logger
- Log important events, errors, and warnings
- Include relevant context information in log messages
- Use appropriate log levels (log, error, warn, debug, verbose)

Example:

```typescript
private readonly logger = new Logger(AuthService.name);

async login(loginDto: LoginUserDto) {
  try {
    // Implementation
    this.logger.log(`User ${user.name} logged in successfully`);
  } catch (error) {
    this.logger.error(`Login failed for user ${loginDto.userId}: ${error.message}`, error.stack);
    throw error;
  }
}
```

### Audit Logging

- Use audit logging for security-sensitive operations
- Log user actions, IP addresses, and timestamps
- Store audit logs in a separate table or file

---

This document is a living guide and should be updated as the project evolves. All team members are encouraged to suggest improvements to these conventions.
