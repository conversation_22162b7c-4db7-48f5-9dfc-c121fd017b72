import { Injectable, Logger, NotFoundException, BadRequestException, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { StoryMaker } from '../../../database/entities/story-maker.entity';
import { StoryMakerParticipation } from '../../../database/entities/story-maker-participation.entity';
import { StoryMakerSubmission } from '../../../database/entities/story-maker-submission.entity';
import { StoryMakerEvaluation } from '../../../database/entities/story-maker-evaluation.entity';
import { StoryMakerGameListResponseDto, StoryMakerGameListItemDto, StoryMakerGameDetailDto } from '../../../database/models/story-maker/story-maker-student.dto';
import { StoryMakerSubmissionDto } from '../../../database/models/story-maker/story-maker-student.dto';
import { PaginationDto } from '../../../common/models/pagination.dto';
import { FileRegistryService } from '../../../common/services/file-registry.service';
import { FileEntityType } from '../../../common/enums/file-entity-type.enum';
import { CurrentUserService } from '../../../common/services/current-user.service';
import { NotificationHelperService } from '../../../modules/notification/notification-helper.service';
import { NotificationType } from '../../../database/entities/notification.entity';
import { UsersService } from '../../../modules/users/users.service';
import { TutorMatchingService } from '../../../modules/tutor-matching/tutor-matching.service';
import { PlanFeature, FeatureType } from '../../../database/entities/plan-feature.entity';
import { UserType } from '../../../database/entities/user.entity';

@Injectable()
export class StoryMakerService {
  private readonly logger = new Logger(StoryMakerService.name);

  constructor(
    @InjectRepository(StoryMaker)
    private readonly storyMakerRepository: Repository<StoryMaker>,
    @InjectRepository(StoryMakerParticipation)
    private readonly participationRepository: Repository<StoryMakerParticipation>,
    @InjectRepository(StoryMakerSubmission)
    private readonly submissionRepository: Repository<StoryMakerSubmission>,
    @InjectRepository(StoryMakerEvaluation)
    private readonly evaluationRepository: Repository<StoryMakerEvaluation>,
    private readonly fileRegistryService: FileRegistryService,
    private readonly currentUserService: CurrentUserService,
    private readonly dataSource: DataSource,
    private readonly notificationHelper: NotificationHelperService,
    private readonly usersService: UsersService,
    private readonly tutorMatchingService: TutorMatchingService,
  ) {}

  /**
   * Get the plan feature ID for the story maker module
   * This is used to identify the story maker module in the tutor matching system
   * @returns The story maker module feature ID or null if not found
   */
  private async getStoryMakerModuleFeatureId(): Promise<string | null> {
    try {
      // Get the repository for PlanFeature
      const planFeatureRepository = this.dataSource.getRepository(PlanFeature);

      // Find the story maker module feature (part of HEC_PLAY)
      const playFeature = await planFeatureRepository.findOne({
        where: { type: FeatureType.HEC_PLAY },
      });

      if (!playFeature) {
        this.logger.warn('Story maker module feature (HEC_PLAY) not found');
        return null;
      }

      this.logger.log(`Found story maker module feature with ID: ${playFeature.id}`);
      return playFeature.id;
    } catch (error) {
      this.logger.error(`Error getting story maker module feature ID: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Get available story maker games for the current student
   * @param paginationDto Pagination parameters
   * @returns List of available games with played status
   */
  async getAvailableGames(paginationDto: PaginationDto): Promise<StoryMakerGameListResponseDto> {
    try {
      const { page = 1, limit = 10 } = paginationDto;
      const skip = (page - 1) * limit;

      const studentId = this.currentUserService.getCurrentUserId();
      if (!studentId) {
        this.logger.warn('Attempted to get available games without a valid student ID');
        return { games: [], total_count: 0 };
      }

      const [storyMakers, totalCount] = await this.storyMakerRepository.findAndCount({
        where: { isActive: true },
        skip,
        take: limit,
        order: { createdAt: 'DESC' },
      });

      const participations = await this.participationRepository.find({
        where: { studentId },
        select: ['storyMakerId'],
      });

      // Create a set of story maker IDs that the student has already played
      const playedStoryMakerIds = new Set(participations.map((p) => p.storyMakerId));

      const games: StoryMakerGameListItemDto[] = [];

      for (const storyMaker of storyMakers) {
        try {
          const pictureUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STORY_MAKER, storyMaker.id);

          games.push({
            id: storyMaker.id,
            title: storyMaker.title,
            instruction: storyMaker.instruction,
            picture: pictureUrl || '',
            score: storyMaker.score,
            is_played: playedStoryMakerIds.has(storyMaker.id),
          });
        } catch (error) {
          // Log the error but continue processing other games
          this.logger.error(`Failed to process story maker with ID ${storyMaker.id} (${storyMaker.title}): ${error.message}`, error.stack);
        }
      }

      return {
        games,
        total_count: totalCount,
      };
    } catch (error) {
      this.logger.error(`Failed to retrieve available story maker games: ${error.message}`, error.stack);
      // Return empty result instead of throwing technical error to the student
      return { games: [], total_count: 0 };
    }
  }

  /**
   * Get a specific story maker game by ID with enhanced details for students
   *
   * This endpoint returns story details with different information based on the student's interaction:
   *
   * Scenario 1: Student hasn't played the game yet
   * - Returns basic story details with is_played: false
   * - No submission content, evaluation, or score is returned
   *
   * Scenario 2: Student submitted but tutor hasn't evaluated yet
   * - Returns story details with is_played: true
   * - Returns the student's latest submission content
   * - No evaluation details or score yet
   *
   * Scenario 3: Student submitted and tutor evaluated with score
   * - Returns story details with is_played: true
   * - Returns the student's submission content
   * - Returns evaluation details (corrections, feedback)
   * - Returns the score assigned by the tutor
   *
   * Scenario 4: Student submitted again after evaluation
   * - Returns story details with is_played: true
   * - Returns the student's latest submission content (the new one)
   * - No evaluation details for the latest submission
   * - Still returns the score from the first submission
   *
   * @param id The ID of the story maker game
   * @returns Enhanced game details including submission, evaluation, and score when available
   */
  async getGameById(id: string): Promise<StoryMakerGameDetailDto> {
    try {
      // Get current student ID
      const studentId = this.currentUserService.getCurrentUserId();
      if (!studentId) {
        this.logger.warn('Attempted to get game details without a valid student ID');
        throw new NotFoundException('Game not found');
      }

      // Get the story maker
      const storyMaker = await this.storyMakerRepository.findOne({
        where: { id, isActive: true },
      });

      if (!storyMaker) {
        this.logger.warn(`Story maker with ID ${id} not found or not active`);
        throw new NotFoundException('Game not found');
      }

      // Check if the student has already played this game and get participation details
      const participation = await this.participationRepository.findOne({
        where: { studentId, storyMakerId: id },
        relations: ['submissions', 'submissions.evaluations', 'submissions.evaluations.tutor'],
      });

      // Get the picture URL
      const pictureUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STORY_MAKER, storyMaker.id);

      // Prepare the response
      const response: any = {
        id: storyMaker.id,
        title: storyMaker.title,
        instruction: storyMaker.instruction,
        picture: pictureUrl || '',
        score: storyMaker.score,
        word_limit: storyMaker.wordLimit,
        deadline: storyMaker.deadline,
        is_played: !!participation,
      };

      // If the student has played this game, add their submission and evaluation details
      if (participation) {
        // Add the participation score to the response
        response.participation_score = participation.score;

        // Sort submissions by date (newest first)
        const sortedSubmissions = participation.submissions.sort((a, b) => b.submittedAt.getTime() - a.submittedAt.getTime());

        // If there are submissions, add the latest one to the response
        if (sortedSubmissions.length > 0) {
          const latestSubmission = sortedSubmissions[0];

          // Add the latest submission content
          response.latest_submission = {
            id: latestSubmission.id,
            content: latestSubmission.content,
            submitted_at: latestSubmission.submittedAt,
            is_evaluated: latestSubmission.isEvaluated,
          };

          // If the latest submission has been evaluated, add the evaluation details
          if (latestSubmission.isEvaluated && latestSubmission.evaluations && latestSubmission.evaluations.length > 0) {
            const evaluation = latestSubmission.evaluations[0];

            response.latest_submission.evaluation = {
              id: evaluation.id,
              corrections: evaluation.corrections,
              feedback: evaluation.feedback,
              evaluated_at: evaluation.evaluatedAt,
              tutor_id: evaluation.tutorId,
              tutor_name: evaluation.tutor?.name,
              tutor_profile_picture: evaluation.tutor?.profilePicture,
            };
          }
        }
      }

      return response;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error; // Rethrow NotFoundException for proper 404 response
      }

      this.logger.error(`Failed to retrieve story maker game with ID ${id}: ${error.message}`, error.stack);
      throw new NotFoundException('Game not found'); // Convert all other errors to 404 for student-facing API
    }
  }

  /**
   * Get all submissions made by the current student for a specific story maker game
   * @param id The ID of the story maker game
   * @returns List of submissions with evaluations
   */
  async getStudentSubmissions(id: string): Promise<any> {
    try {
      // Get current student ID
      const studentId = this.currentUserService.getCurrentUserId();
      if (!studentId) {
        this.logger.warn('Attempted to get submissions without a valid student ID');
        throw new NotFoundException("We couldn't find your information. Please try logging in again.");
      }

      // Get the story maker
      const storyMaker = await this.storyMakerRepository.findOne({
        where: { id, isActive: true },
      });

      if (!storyMaker) {
        this.logger.warn(`Story maker with ID ${id} not found or not active`);
        throw new NotFoundException('Game not found');
      }

      // Get the participation with submissions and evaluations
      const participation = await this.participationRepository.findOne({
        where: { studentId, storyMakerId: id },
        relations: ['submissions', 'submissions.evaluations', 'submissions.evaluations.tutor'],
      });

      if (!participation) {
        return {
          story_maker: {
            id: storyMaker.id,
            title: storyMaker.title,
          },
          participation: null,
          submissions: [],
        };
      }

      // Sort submissions by date (newest first)
      const sortedSubmissions = participation.submissions.sort((a, b) => b.submittedAt.getTime() - a.submittedAt.getTime());

      // Map submissions to DTOs
      const submissionDtos = sortedSubmissions.map((submission) => {
        const evaluation = submission.evaluations && submission.evaluations.length > 0 ? submission.evaluations[0] : null;

        const submissionDto: any = {
          id: submission.id,
          content: submission.content,
          submitted_at: submission.submittedAt,
          is_evaluated: submission.isEvaluated,
          created_at: submission.createdAt,
        };

        if (evaluation) {
          submissionDto.evaluation = {
            id: evaluation.id,
            tutor_id: evaluation.tutorId,
            tutor_name: evaluation.tutor?.name,
            tutor_profile_picture: evaluation.tutor?.profilePicture,
            corrections: evaluation.corrections,
            feedback: evaluation.feedback,
            evaluated_at: evaluation.evaluatedAt,
          };
        }

        return submissionDto;
      });

      // Get the picture URL
      const pictureUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.STORY_MAKER, storyMaker.id);

      // Return the result
      return {
        story_maker: {
          id: storyMaker.id,
          title: storyMaker.title,
          instruction: storyMaker.instruction,
          picture: pictureUrl || '',
          score: storyMaker.score,
          word_limit: storyMaker.wordLimit,
          deadline: storyMaker.deadline,
        },
        participation: {
          id: participation.id,
          first_submitted_at: participation.firstSubmittedAt,
          is_evaluated: participation.isEvaluated,
          score: participation.score,
          evaluated_at: participation.evaluatedAt,
        },
        submissions: submissionDtos,
        can_submit: submissionDtos.length === 0 || submissionDtos[0].is_evaluated,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to get student submissions: ${error.message}`, error.stack);
      throw new NotFoundException('Failed to get submissions');
    }
  }

  /**
   * Submit a story for a specific story maker game
   * @param id The ID of the story maker game
   * @param content The content of the story. Max length is 50,000 characters.
   * @returns Success message
   */
  async submitStory(id: string, content: string): Promise<{ message: string }> {
    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Validate content
      if (!content || content.trim() === '') {
        this.logger.warn(`Student attempted to submit empty story for story maker ${id}`);
        throw new BadRequestException("Don't forget to write your story before submitting!");
      }

      // Get current student ID
      const studentId = this.currentUserService.getCurrentUserId();
      if (!studentId) {
        this.logger.warn('Attempted to submit story without a valid student ID');
        throw new UnauthorizedException("We couldn't find your information. Please try logging in again.");
      }

      // Verify the user is a student by checking their type
      const student = await this.usersService.findById(studentId);
      if (!student) {
        this.logger.warn(`User with ID ${studentId} not found during story submission`);
        throw new NotFoundException('User not found');
      }

      if (student.type !== UserType.STUDENT) {
        this.logger.warn(`User with ID ${studentId} and name ${student.name} is not a student`);
        throw new ForbiddenException('Only students can submit stories');
      }

      // Get the story maker within the transaction
      const storyMaker = await queryRunner.manager.findOne(StoryMaker, {
        where: { id, isActive: true },
      });

      if (!storyMaker) {
        this.logger.warn(`Story maker with ID ${id} not found or not active during submission attempt`);
        throw new NotFoundException('This game is not available right now. Please try another one!');
      }

      // Check word limit if it's set
      if (storyMaker.wordLimit) {
        // Extract text content from HTML by removing all HTML tags
        const textContent = content
          .replace(/<[^>]*>/g, ' ')
          .replace(/\s+/g, ' ')
          .trim();

        // Count words in the text content
        const wordCount = textContent.split(/\s+/).length;

        if (wordCount > storyMaker.wordLimit) {
          this.logger.warn(`Student attempted to submit story with ${wordCount} words, exceeding limit of ${storyMaker.wordLimit} for story maker ${id} (${storyMaker.title})`);
          throw new BadRequestException(`Your story has ${wordCount} words, but the limit is ${storyMaker.wordLimit} words. Please make it shorter.`);
        }

        // Log the word count for debugging
        this.logger.log(`Story submission for story maker ${id} (${storyMaker.title}) has ${wordCount} words (limit: ${storyMaker.wordLimit})`);
      }

      // Check if the student has already participated in this story maker
      // First get the participation with a lock but without relations to avoid the "FOR UPDATE cannot be applied to the nullable side of an outer join" error
      let participation = await queryRunner.manager.findOne(StoryMakerParticipation, {
        where: { studentId, storyMakerId: id },
        lock: { mode: 'pessimistic_write' }, // Prevent race conditions
      });

      // If participation exists, fetch the submissions separately
      if (participation) {
        const submissions = await queryRunner.manager.find(StoryMakerSubmission, {
          where: { participationId: participation.id },
          order: { submittedAt: 'DESC' },
        });
        participation.submissions = submissions;
      }

      const now = new Date();

      // If no participation exists, create one (first time submission)
      if (!participation) {
        participation = queryRunner.manager.create(StoryMakerParticipation, {
          studentId,
          storyMakerId: id,
          isEvaluated: false,
          firstSubmittedAt: now,
        });

        await queryRunner.manager.save(participation);
      } else {
        // Check if there's a deadline and if it has passed
        if (storyMaker.deadline) {
          const deadlineDate = new Date(participation.firstSubmittedAt);
          deadlineDate.setDate(deadlineDate.getDate() + storyMaker.deadline);

          if (now > deadlineDate) {
            this.logger.warn(`Student ${studentId} attempted to submit after deadline for story maker ${id} (${storyMaker.title})`);
            throw new BadRequestException('The deadline for this story has passed. You can no longer submit.');
          }
        }

        // Check if the student has any submissions
        if (participation.submissions && participation.submissions.length > 0) {
          // Get the latest submission
          const latestSubmission = participation.submissions.sort((a, b) => b.submittedAt.getTime() - a.submittedAt.getTime())[0];

          // Check if the latest submission has been evaluated
          if (!latestSubmission.isEvaluated) {
            this.logger.warn(`Student ${studentId} attempted to resubmit before evaluation for story maker ${id} (${storyMaker.title})`);
            throw new BadRequestException("Your previous submission is still waiting for evaluation. You can submit again after it's evaluated.");
          }
        }
      }

      // Create a new submission record
      const submission = queryRunner.manager.create(StoryMakerSubmission, {
        participationId: participation.id,
        content,
        submittedAt: now,
        isEvaluated: false,
      });

      await queryRunner.manager.save(submission);

      // Commit the transaction
      await queryRunner.commitTransaction();

      this.logger.log(`Student ${studentId} successfully submitted story for story maker ${id} (${storyMaker.title})`);

      // Send notification to the assigned tutor or all admin users
      try {
        // Get the story maker module feature ID
        const storyMakerModuleId = await this.getStoryMakerModuleFeatureId();

        // Create HTML content for the notification
        const htmlContent = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #4a6ee0;">New Story Submission</h2>
            <p>A student has submitted a story that needs your evaluation.</p>
            <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0;">
              <p><strong>Student:</strong> ${student.name}</p>
              <p><strong>Story Title:</strong> ${storyMaker.title}</p>
              <p><strong>Submission Time:</strong> ${new Date().toLocaleString()}</p>
            </div>
            <p>Please review and evaluate this submission at your earliest convenience.</p>
            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
              <p>This is an automated message from the HEC system.</p>
              <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
            </div>
          </div>
        `;

        // Notification options
        const notificationOptions = {
          relatedEntityId: submission.id,
          relatedEntityType: 'story_maker_submission',
          htmlContent: htmlContent,
          sendEmail: true,
          sendInApp: true,
          sendPush: true,
          sendMobile: false,
          sendSms: false,
          sendRealtime: true,
        };

        if (storyMakerModuleId) {
          // Find the assigned tutor for this student and the story maker module
          const studentTutorMapping = await this.tutorMatchingService.getStudentTutorForModule(studentId, storyMakerModuleId);

          if (studentTutorMapping) {
            // Send notification to the assigned tutor
            await this.notificationHelper.notify(
              studentTutorMapping.tutorId,
              NotificationType.STORY_SUBMISSION,
              'New Story Submission Requires Evaluation',
              `Student ${student.name} has submitted a story "${storyMaker.title}" that needs evaluation.`,
              notificationOptions,
            );

            this.logger.log(`Sent story submission notification to tutor ${studentTutorMapping.tutorId} for story maker ${id} (${storyMaker.title})`);
            return { message: 'Great job! Your story has been sent to your tutor for review.' };
          } else {
            this.logger.warn(`No tutor assigned for student ${studentId} and story maker module ${storyMakerModuleId} (${storyMaker.title})`);

            // Try to get any available tutor for the story maker module
            const availableTutors = await this.tutorMatchingService.getAvailableTutorsForModule(storyMakerModuleId);

            if (availableTutors && availableTutors.length > 0) {
              // Auto-assign the first available tutor
              const assignResult = await this.tutorMatchingService.assignTutor({
                studentId: studentId,
                tutorId: availableTutors[0].id,
                planFeatureId: storyMakerModuleId,
                notes: 'Auto-assigned during story maker submission',
              });

              if (assignResult) {
                // Send notification to the newly assigned tutor
                await this.notificationHelper.notify(
                  availableTutors[0].id,
                  NotificationType.STORY_SUBMISSION,
                  'New Story Submission Requires Evaluation',
                  `Student ${student.name} has submitted a story "${storyMaker.title}" that needs evaluation.`,
                  notificationOptions,
                );

                this.logger.log(`Auto-assigned and notified tutor ${availableTutors[0].id} for student ${studentId} and story maker ${id} (${storyMaker.title})`);
                return { message: 'Great job! Your story has been sent to your tutor for review.' };
              }
            }
          }
        }

        // If we get here, either there's no module ID, no assigned tutor, or auto-assignment failed
        // Fall back to notifying all admins
        const adminUsers = await this.usersService.getAllAdminUsers();

        if (adminUsers && adminUsers.length > 0) {
          // Get admin user IDs
          const adminUserIds = adminUsers.map((admin) => admin.id);

          // Send notification to all admins
          await this.notificationHelper.notifyMany(
            adminUserIds,
            NotificationType.STORY_SUBMISSION,
            'New Story Submission Requires Evaluation',
            `Student ${student.name} has submitted a story "${storyMaker.title}" that needs evaluation.`,
            notificationOptions,
          );

          this.logger.log(`Sent story submission notifications to ${adminUserIds.length} admin users`);
        } else {
          this.logger.warn('No admin users found to notify about story submission');
        }
      } catch (error) {
        // Just log the error but don't fail the submission
        this.logger.error(`Failed to send notification for story submission: ${error.message}`, error.stack);
      }

      return { message: 'Great job! Your story has been sent to your tutor for review.' };
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();

      // If it's already a NestJS exception, just rethrow it
      if (error instanceof BadRequestException || error instanceof UnauthorizedException || error instanceof ForbiddenException || error instanceof NotFoundException) {
        throw error;
      }

      if (error.code === '23505') {
        // PostgreSQL unique constraint violation
        this.logger.warn(`Concurrent submission detected for story maker ${id}`);
        throw new BadRequestException("You've already sent your story for this game. Try another game!");
      }

      this.logger.error(`Failed to submit story for story maker ${id}: ${error.message}`, error.stack);
      throw new BadRequestException('Oops! Something went wrong. Please try again.');
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }
}
