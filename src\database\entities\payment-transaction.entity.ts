import { Enti<PERSON>, Column, <PERSON>To<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

/**
 * Payment transaction status
 * @enum {string}
 */
export enum PaymentTransactionStatus {
  /** Transaction initiated */
  INITIATED = 'initiated',
  /** Payment pending */
  PENDING = 'pending',
  /** Payment processing */
  PROCESSING = 'processing',
  /** Payment completed successfully */
  COMPLETED = 'completed',
  /** Payment failed */
  FAILED = 'failed',
  /** Payment cancelled */
  CANCELLED = 'cancelled',
  /** Payment refunded */
  REFUNDED = 'refunded'
}

/**
 * Purchase type for payment transactions
 * @enum {string}
 */
export enum PurchaseType {
  /** Shop item purchase */
  SHOP_ITEM = 'shop_item',
  /** Plan subscription */
  PLAN = 'plan'
}

/**
 * KCP Payment method types
 * @enum {string}
 */
export enum KcpPaymentMethod {
  /** Credit card */
  CARD = 'card',
  /** Bank transfer */
  BANK = 'bank',
  /** Mobile payment */
  MOBILE = 'mobile',
  /** Virtual account */
  VACCT = 'vacct'
}

@Entity()
export class PaymentTransaction extends AuditableBaseEntity {
  @Column({ name: 'transaction_id', unique: true })
  transactionId: string;

  @Column({ name: 'kcp_transaction_id', nullable: true })
  kcpTransactionId: string;

  @Column({ name: 'order_id' })
  orderId: string;

  @Column({ name: 'amount', type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ name: 'currency', default: 'KRW' })
  currency: string;

  @Column({
    name: 'payment_method',
    type: 'enum',
    enum: KcpPaymentMethod
  })
  paymentMethod: KcpPaymentMethod;

  @Column({
    name: 'status',
    type: 'enum',
    enum: PaymentTransactionStatus,
    default: PaymentTransactionStatus.INITIATED
  })
  status: PaymentTransactionStatus;

  @Column({ name: 'payment_status', nullable: true })
  paymentStatus: string;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({
    name: 'purchase_type',
    type: 'enum',
    enum: PurchaseType
  })
  purchaseType: PurchaseType;

  @Column({ name: 'reference_id' })
  referenceId: string;

  // KCP specific fields
  @Column({ name: 'kcp_trade_key', nullable: true })
  kcpTradeKey: string;

  @Column({ name: 'kcp_approval_key', nullable: true })
  kcpApprovalKey: string;

  @Column({ name: 'kcp_approval_time', nullable: true })
  kcpApprovalTime: Date;

  @Column({ name: 'kcp_card_code', nullable: true })
  kcpCardCode: string;

  @Column({ name: 'kcp_card_name', nullable: true })
  kcpCardName: string;

  @Column({ name: 'kcp_card_no', nullable: true })
  kcpCardNo: string;

  @Column({ name: 'request_data', type: 'jsonb', nullable: true })
  requestData: any;

  @Column({ name: 'response_data', type: 'jsonb', nullable: true })
  responseData: any;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;

  @Column({ name: 'processed_at', nullable: true })
  processedAt: Date;

  @Column({ name: 'expires_at', nullable: true })
  expiresAt: Date;

  /**
   * Check if transaction is expired
   */
  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() > this.expiresAt;
  }

  /**
   * Check if transaction is in a final state
   */
  isFinalState(): boolean {
    return [
      PaymentTransactionStatus.COMPLETED,
      PaymentTransactionStatus.FAILED,
      PaymentTransactionStatus.CANCELLED,
      PaymentTransactionStatus.REFUNDED
    ].includes(this.status);
  }

  /**
   * Check if transaction is successful
   */
  isSuccessful(): boolean {
    return this.status === PaymentTransactionStatus.COMPLETED;
  }
}
