import { applyDecorators, Type } from '@nestjs/common';
import { ApiResponse, getSchemaPath } from '@nestjs/swagger';
import { 
  ApiResponse as ApiResponseModel, 
  PaginatedResponse, 
  ValidationErrorResponse, 
  ConflictErrorResponse, 
  ErrorResponse 
} from '../models/api-response.model';

/**
 * Decorator factory for standard API success response
 * @param status HTTP status code
 * @param description Response description
 * @param model Response data model (optional)
 * @returns Decorator
 */
export const ApiSuccessResponse = <TModel extends Type<any>>(
  status: number,
  description: string,
  model?: TModel
) => {
  return applyDecorators(
    ApiResponse({
      status,
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(ApiResponseModel) },
          {
            properties: {
              data: model ? { $ref: getSchemaPath(model) } : { type: 'object' }
            }
          }
        ]
      }
    })
  );
};

/**
 * Decorator factory for paginated API success response
 * @param description Response description
 * @param model Response data model
 * @returns Decorator
 */
export const ApiPaginatedResponse = <TModel extends Type<any>>(
  description: string,
  model: TModel
) => {
  return applyDecorators(
    ApiResponse({
      status: 200,
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(PaginatedResponse) },
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(model) }
              }
            }
          }
        ]
      }
    })
  );
};

/**
 * Decorator for validation error response (400)
 * @param description Response description
 * @returns Decorator
 */
export const ApiValidationErrorResponse = (description = 'Validation failed') => {
  return ApiResponse({
    status: 400,
    description,
    type: ValidationErrorResponse
  });
};

/**
 * Decorator for unauthorized error response (401)
 * @param description Response description
 * @returns Decorator
 */
export const ApiUnauthorizedResponse = (description = 'Unauthorized') => {
  return ApiResponse({
    status: 401,
    description,
    schema: {
      allOf: [
        { $ref: getSchemaPath(ErrorResponse) },
        {
          properties: {
            message: { example: 'Unauthorized' },
            error: {
              example: {
                type: 'UnauthorizedException',
                status: 401,
                refId: 'ERR-M9BF5NQQ-31028'
              }
            }
          }
        }
      ]
    }
  });
};

/**
 * Decorator for forbidden error response (403)
 * @param description Response description
 * @returns Decorator
 */
export const ApiForbiddenResponse = (description = 'Forbidden') => {
  return ApiResponse({
    status: 403,
    description,
    schema: {
      allOf: [
        { $ref: getSchemaPath(ErrorResponse) },
        {
          properties: {
            message: { example: 'Forbidden' },
            error: {
              example: {
                type: 'ForbiddenException',
                status: 403,
                refId: 'ERR-M9BF5NQQ-31028'
              }
            }
          }
        }
      ]
    }
  });
};

/**
 * Decorator for not found error response (404)
 * @param description Response description
 * @param entity Entity name
 * @returns Decorator
 */
export const ApiNotFoundResponse = (description = 'Not found', entity = 'Resource') => {
  return ApiResponse({
    status: 404,
    description,
    schema: {
      allOf: [
        { $ref: getSchemaPath(ErrorResponse) },
        {
          properties: {
            message: { example: `${entity} not found` },
            error: {
              example: {
                type: 'NotFoundException',
                status: 404,
                refId: 'ERR-M9BF5NQQ-31028'
              }
            }
          }
        }
      ]
    }
  });
};

/**
 * Decorator for conflict error response (409)
 * @param description Response description
 * @returns Decorator
 */
export const ApiConflictResponse = (description = 'Resource already exists') => {
  return ApiResponse({
    status: 409,
    description,
    type: ConflictErrorResponse
  });
};

/**
 * Decorator for internal server error response (500)
 * @param description Response description
 * @returns Decorator
 */
export const ApiInternalServerErrorResponse = (description = 'Internal server error') => {
  return ApiResponse({
    status: 500,
    description,
    schema: {
      allOf: [
        { $ref: getSchemaPath(ErrorResponse) },
        {
          properties: {
            message: { example: 'An error occurred while processing your request' },
            error: {
              example: {
                type: 'InternalServerErrorException',
                status: 500,
                refId: 'ERR-M9BF5NQQ-31028'
              }
            }
          }
        }
      ]
    }
  });
};
