import { createDatabase } from 'typeorm-extension';
import { DataSource } from 'typeorm';
import { AppDataSource } from './database.config';
import { Client } from 'pg';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { SeedService } from './seed.service';

async function testConnection() {
    const client = new Client({
        host: process.env.DATABASE_HOST,
        port: parseInt(process.env.DATABASE_PORT||'5432'),
        user: process.env.DATABASE_USER,
        password: process.env.DATABASE_PASSWORD,
        database: 'postgres', // Connect to default database first
        ssl:  { rejectUnauthorized: false }
    });

    try {
        await client.connect();
        console.log('Successfully connected to PostgreSQL server');

        // Check if our database exists
        const result = await client.query(
            "SELECT datname FROM pg_database WHERE datname = $1",
            [process.env.DATABASE_NAME]
        );

        if (result.rows.length === 0) {
            console.log(`Database ${process.env.DATABASE_NAME} does not exist`);
            return false;
        } else {
            console.log(`Database ${process.env.DATABASE_NAME} exists`);
            return true;
        }
    } catch (error) {
        console.error('Database connection test failed:', error);
        throw error;
    } finally {
        try {
            await client.end();
        } catch (endError) {
            // Ignore end connection errors
        }
    }
}

export async function initializeDatabase() {
    try {
        // Test connection first
        const dbExists = await testConnection();

        if (!dbExists) {
            const dataSourceOptions = AppDataSource.options;

            // Try to create database if it doesn't exist
            try {
                await createDatabase({
                    options: dataSourceOptions,
                    ifNotExist: true,
                });
                console.log('Database created successfully');
            } catch (error) {
                console.warn('Database creation attempt:', error.message);
                // Continue even if database creation fails (it might already exist)
            }
        }

        // Initialize the data source
        if (!AppDataSource.isInitialized) {
            await AppDataSource.initialize();
            console.log('Data source has been initialized');
        }

        // Run essential seeding after database initialization
        await seedEssentialData();

    } catch (error) {
        console.error('Database initialization failed:', error);
        throw error;
    }
}

async function seedEssentialData() {
    try {
        console.log('Starting essential data seeding process...');

        // Create a temporary NestJS application context to access the SeedService
        const app = await NestFactory.createApplicationContext(AppModule);
        const seedService = app.get(SeedService);

        // Run essential seeding (users, plan features, and plans)
        const result = await seedService.seedEssentialData();

        await app.close();

        if (result.success) {
            console.log('Essential data seeding completed successfully');
        } else {
            console.error('Essential data seeding failed:', result.message);
        }
    } catch (error) {
        console.error('Essential data seeding failed:', error);
        // Don't throw error to prevent application startup failure
    }
}
