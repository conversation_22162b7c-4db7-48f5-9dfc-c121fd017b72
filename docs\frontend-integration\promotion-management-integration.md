# Promotion Management System - Frontend Integration Flow

This document provides a comprehensive integration flow for frontend developers to implement the Promotion Management System with minimal verbal communication needed. It focuses exclusively on API endpoints and data flows without prescribing specific frontend implementation approaches.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Integration Flows](#integration-flows)
   - [Admin Promotion Management Flow](#admin-promotion-management-flow)
   - [Promotion Application to Items Flow](#promotion-application-to-items-flow)
   - [Category-Based Promotion Flow](#category-based-promotion-flow)
   - [Plan-Based Promotion Flow](#plan-based-promotion-flow)
   - [Student Promotion Code Flow](#student-promotion-code-flow)
3. [API Endpoints](#api-endpoints)
   - [Admin Promotion Management](#admin-promotion-management)
   - [Promotion Application](#promotion-application)
   - [Plan Promotion Management](#plan-promotion-management)
   - [Student Promotion Usage](#student-promotion-usage)

## Architecture Overview

The Promotion Management System is built with a modular architecture that integrates with both the shop system and plans system:

- **PromotionsService**: Manages promotion CRUD operations and validation
- **ShopItemService**: Handles applying promotions to shop items
- **PlansService**: <PERSON>les applying promotions to subscription plans
- **ShoppingCartService**: Processes promotion codes during checkout
- **PromotionValidationService**: Validates promotion applicability based on various criteria

The system supports category-based promotions through the `applicableCategoryIds` field, allowing for targeted discounts to specific product categories. It also supports plan-based promotions through the `applicablePlanIds` field, allowing for targeted discounts to specific subscription plans.

## Integration Flows

### Admin Promotion Management Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  List       │      │  Create     │      │  View       │      │  Update     │
│  Promotions │ ──▶  │  Promotion  │ ──▶  │  Details    │ ──▶  │  Promotion  │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /promo- │      │ POST /promo-│      │ GET /promo- │      │ PATCH /pro- │
│ tions/admin │      │ tions       │      │ tions/{id}  │      │ motions/{id}│
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
                                                                      │
                                                                      ▼
                                                              ┌─────────────┐
                                                              │ DELETE /pro-│
                                                              │ motions/{id}│
                                                              └─────────────┘
```

### Promotion Application to Items Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  View Shop  │      │  Select     │      │  Choose     │      │  Apply      │
│  Items      │ ──▶  │  Items      │ ──▶  │  Promotion  │ ──▶  │  Promotion  │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /shop/  │      │ Select Items│      │ GET /promo- │      │ POST /shop/ │
│ admin/items │      │ in UI       │      │ tions/admin │      │ admin/apply-│
│             │      │             │      │             │      │ promotion   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

### Category-Based Promotion Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Create     │      │  Set        │      │  Apply to   │      │  View       │
│  Promotion  │ ──▶  │  Category   │ ──▶  │  Items      │ ──▶  │  Results    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /promo-│      │ Include     │      │ POST /shop/ │      │ View Applied│
│ tions       │      │ applicable  │      │ admin/apply-│      │ Items and   │
│             │      │ CategoryIds │      │ promotion   │      │ Skipped     │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

### Plan-Based Promotion Flow

For detailed information about plan promotion integration, please refer to the [Plan Promotion Integration](plan-promotion-integration.md) document.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Create     │      │  Set        │      │  Apply to   │      │  View       │
│  Promotion  │ ──▶  │  Plan IDs   │ ──▶  │  Plans      │ ──▶  │  Results    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /promo-│      │ Include     │      │ POST /plans/│      │ View Applied│
│ tions       │      │ applicable  │      │ admin/apply-│      │ Plans and   │
│             │      │ PlanIds     │      │ promotion   │      │ Skipped     │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

### Student Promotion Code Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  View Cart  │      │  Enter      │      │  Apply      │      │  Checkout   │
│  Items      │ ──▶  │  Code       │ ──▶  │  Code       │ ──▶  │  with Code  │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /shop/  │      │ Enter Code  │      │ POST /promo-│      │ POST /shop/ │
│ cart        │      │ in UI       │      │ tions/apply-│      │ cart/       │
│             │      │             │      │ code        │      │ checkout    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

## API Endpoints

### Admin Promotion Management

#### Create Promotion

```
POST /promotions
```

**Request Body:**
```json
{
  "name": "Summer Sale",
  "description": "20% off all items",
  "promotionType": "percentage",
  "discountType": "percentage",
  "discountValue": 20,
  "applicableType": "SHOP_ITEM",
  "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
  "applicablePlanIds": [],
  "promotionCode": "SUMMER20",
  "startDate": "2023-06-01",
  "endDate": "2023-08-31",
  "isActive": true,
  "usageLimit": 1000,
  "minimumPurchaseAmount": 50,
  "maximumPurchaseAmount": 1000,
  "maximumDiscountAmount": 100
}
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion created successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174001",
    "name": "Summer Sale",
    "description": "20% off all items",
    "promotionType": "percentage",
    "discountType": "percentage",
    "discountValue": 20,
    "applicableType": "SHOP_ITEM",
    "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
    "applicablePlanIds": [],
    "promotionCode": "SUMMER20",
    "startDate": "2023-06-01T00:00:00.000Z",
    "endDate": "2023-08-31T23:59:59.999Z",
    "isActive": true,
    "usageLimit": 1000,
    "usageCount": 0,
    "minimumPurchaseAmount": 50,
    "maximumPurchaseAmount": 1000,
    "maximumDiscountAmount": 100,
    "status": "ACTIVE",
    "createdAt": "2023-05-15T10:30:00.000Z",
    "updatedAt": "2023-05-15T10:30:00.000Z"
  }
}
```

#### Get All Promotions

```
GET /promotions/admin
```

**Query Parameters:**
- `status`: Filter by promotion status (ACTIVE/INACTIVE/SCHEDULED/EXPIRED)
- `applicableType`: Filter by applicable type (ALL/SHOP_ITEM/PLAN)
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by
- `sortDirection`: Sort direction (ASC/DESC)

**Response:**
```json
{
  "success": true,
  "message": "Promotions retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174001",
        "name": "Summer Sale",
        "description": "20% off all items",
        "promotionType": "percentage",
        "discountType": "percentage",
        "discountValue": 20,
        "applicableType": "SHOP_ITEM",
        "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
        "promotionCode": "SUMMER20",
        "startDate": "2023-06-01T00:00:00.000Z",
        "endDate": "2023-08-31T23:59:59.999Z",
        "isActive": true,
        "usageLimit": 1000,
        "usageCount": 0,
        "minimumPurchaseAmount": 50,
        "maximumPurchaseAmount": 1000,
        "maximumDiscountAmount": 100,
        "status": "ACTIVE",
        "createdAt": "2023-05-15T10:30:00.000Z",
        "updatedAt": "2023-05-15T10:30:00.000Z"
      }
    ],
    "totalItems": 1,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  }
}
```

#### Get Promotion by ID

```
GET /promotions/{id}
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174001",
    "name": "Summer Sale",
    "description": "20% off all items",
    "promotionType": "percentage",
    "discountType": "percentage",
    "discountValue": 20,
    "applicableType": "SHOP_ITEM",
    "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
    "promotionCode": "SUMMER20",
    "startDate": "2023-06-01T00:00:00.000Z",
    "endDate": "2023-08-31T23:59:59.999Z",
    "isActive": true,
    "usageLimit": 1000,
    "usageCount": 0,
    "minimumPurchaseAmount": 50,
    "maximumPurchaseAmount": 1000,
    "maximumDiscountAmount": 100,
    "status": "ACTIVE",
    "createdAt": "2023-05-15T10:30:00.000Z",
    "updatedAt": "2023-05-15T10:30:00.000Z"
  }
}
```

#### Update Promotion

```
PATCH /promotions/{id}
```

**Request Body:**
```json
{
  "name": "Updated Summer Sale",
  "description": "25% off all items",
  "discountValue": 25,
  "isActive": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion updated successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174001",
    "name": "Updated Summer Sale",
    "description": "25% off all items",
    "promotionType": "percentage",
    "discountType": "percentage",
    "discountValue": 25,
    "applicableType": "SHOP_ITEM",
    "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
    "promotionCode": "SUMMER20",
    "startDate": "2023-06-01T00:00:00.000Z",
    "endDate": "2023-08-31T23:59:59.999Z",
    "isActive": true,
    "usageLimit": 1000,
    "usageCount": 0,
    "minimumPurchaseAmount": 50,
    "maximumPurchaseAmount": 1000,
    "maximumDiscountAmount": 100,
    "status": "ACTIVE",
    "createdAt": "2023-05-15T10:30:00.000Z",
    "updatedAt": "2023-05-15T11:15:00.000Z"
  }
}
```

#### Delete Promotion

```
DELETE /promotions/{id}
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion deleted successfully",
  "data": null
}
```

#### Generate Promotion Code

```
POST /promotions/generate-code
```

**Request Body:**
```json
{
  "prefix": "SUMMER"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion code generated successfully",
  "data": {
    "promotionCode": "SUMMER123ABC"
  }
}
```

### Promotion Application

#### Apply Promotion to Shop Items

```
POST /shop/admin/apply-promotion
```

**Request Body:**
```json
{
  "promotionId": "123e4567-e89b-12d3-a456-426614174001",
  "itemIds": [
    "123e4567-e89b-12d3-a456-426614174002",
    "123e4567-e89b-12d3-a456-426614174003"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion applied to 2 shop items. 1 item was skipped because it is not in applicable categories.",
  "data": {
    "success": true,
    "message": "Promotion applied to 2 shop items"
  }
}
```

### Plan Promotion Management

For detailed information about plan promotion integration, please refer to the [Plan Promotion Integration](plan-promotion-integration.md) document.

#### Apply Promotion to Plans

```
POST /plans/admin/apply-promotion
```

**Request Body:**
```json
{
  "promotionId": "123e4567-e89b-12d3-a456-426614174001",
  "planIds": [
    "123e4567-e89b-12d3-a456-426614174002",
    "123e4567-e89b-12d3-a456-426614174003"
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion applied to 2 plans",
  "data": {
    "success": true,
    "message": "Promotion applied to 2 plans. 1 plan was skipped because it is not applicable to this promotion."
  }
}
```

#### Remove Promotion from Plan

```
DELETE /plans/admin/:id/promotion
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion removed from plan successfully",
  "data": {
    "success": true,
    "message": "Promotion removed from plan successfully"
  }
}
```

### Student Promotion Usage

#### Apply Promotion Code

```
POST /promotions/apply-code
```

**Request Body:**
```json
{
  "promotionCode": "SUMMER20",
  "originalPrice": 100,
  "itemType": "SHOP_ITEM",
  "category": "123e4567-e89b-12d3-a456-426614174000"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Promotion code applied successfully",
  "data": {
    "promotionId": "123e4567-e89b-12d3-a456-426614174001",
    "promotionName": "Summer Sale",
    "originalPrice": 100,
    "discountAmount": 20,
    "finalPrice": 80,
    "discountType": "percentage",
    "discountValue": 20,
    "isApplied": true,
    "message": "Promotion applied successfully"
  }
}
```

#### Get Applicable Promotions

```
GET /promotions/applicable
```

**Query Parameters:**
- `itemType`: Type of item (SHOP_ITEM/PLAN)
- `category`: Category ID (for shop items)
- `planType`: Plan type (for plans, e.g., "starter", "standard", "pro", "ultimate")
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "message": "Applicable promotions retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174001",
        "name": "Summer Sale",
        "description": "20% off all items",
        "promotionType": "percentage",
        "discountType": "percentage",
        "discountValue": 20,
        "applicableType": "SHOP_ITEM",
        "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
        "promotionCode": "SUMMER20",
        "startDate": "2023-06-01T00:00:00.000Z",
        "endDate": "2023-08-31T23:59:59.999Z",
        "isActive": true,
        "usageLimit": 1000,
        "usageCount": 0,
        "minimumPurchaseAmount": 50,
        "maximumPurchaseAmount": 1000,
        "maximumDiscountAmount": 100,
        "status": "ACTIVE",
        "createdAt": "2023-05-15T10:30:00.000Z",
        "updatedAt": "2023-05-15T10:30:00.000Z"
      }
    ],
    "totalItems": 1,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  }
}
```


