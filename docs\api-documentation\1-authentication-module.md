# Authentication Module

The Authentication Module handles user registration, login, email verification, password reset, and role management.

## Epics

1. **User Authentication**
2. **User Registration**
3. **Account Recovery**
4. **Email Verification**

## APIs

### 1. Register User

**Endpoint:** `POST /auth/register`

**Description:** Registers a new student or tutor user. A verification link will be sent to the provided email. For tutor registration, the account will require admin approval after email verification.

**Request Body:**
```json
{
  "userId": "john123",
  "email": "<EMAIL>",
  "phone": "**********",
  "gender": "male",
  "password": "Password123!",
  "confirmPassword": "Password123!",
  "type": "student",
  "agreeToTerms": true,
  "dateOfBirth": "1990-01-01"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Registration successful. Please check your email for verification link.",
  "data": {
    "userId": "john123"
  },
  "errors": null,
  "statusCode": 201
}
```

**Algorithm:**
1. Validate input data
2. Check if user with same email or userId already exists
3. Create new user with provided data
4. Hash the password
5. Assign appropriate role based on user type
6. Generate email verification token
7. Send verification email
8. Return success response

### 2. Login User

**Endpoint:** `POST /auth/login`

**Description:** Unified login endpoint for all user types. If selectedRole is not provided, the system will automatically use the highest role available to the user. The rememberMe option extends the session duration to 30 days.

**Request Body:**
```json
{
  "userId": "john123",
  "password": "Password123!",
  "selectedRole": "student",
  "rememberMe": true,
  "returnUrl": "/dashboard"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "John Doe",
      "userId": "john123",
      "email": "<EMAIL>",
      "type": "student",
      "isActive": true,
      "isConfirmed": true,
      "roles": ["student"],
      "activePlan": "Pro",
      "selectedRole": "student"
    },
    "returnUrl": "/dashboard"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Find user by userId
2. Validate password
3. Check if user is confirmed
4. Check if user is active
5. Validate selected role
6. For tutors, check if approved
7. For students, check subscription plan status
8. Generate JWT token with user info and plan details
9. Return token and user info

### 3. Verify Email

**Endpoint:** `GET /auth/verify-email`

**Description:** Verifies a user's email using the token sent during registration.

**Query Parameters:**
- `token`: The verification token

**Response:**
```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "John Doe",
      "userId": "john123",
      "email": "<EMAIL>",
      "type": "student",
      "isActive": true,
      "isConfirmed": true,
      "roles": ["student"]
    },
    "requiresApproval": false,
    "approvalStatus": null
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate token
2. Check if token is expired
3. Find user associated with token
4. Mark user as confirmed
5. For tutors, create approval request
6. Mark token as used
7. Generate JWT token
8. Return success response with appropriate message

### 4. Forgot Password

**Endpoint:** `POST /auth/forgot-password`

**Description:** Sends a password reset link to the user's email. The link is valid for 5 minutes.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset link sent to your email",
  "data": null,
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Find user by email
2. Generate password reset token
3. Set token expiry to 5 minutes from now
4. Save token to database
5. Send password reset email with token
6. Return success response

### 5. Reset Password

**Endpoint:** `POST /auth/reset-password`

**Description:** Resets a user's password using the token sent in the forgot password email.

**Request Body:**
```json
{
  "token": "abc123def456",
  "password": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset successful",
  "data": null,
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate token
2. Check if token is expired
3. Find user associated with token
4. Validate new password
5. Update user's password
6. Mark token as used
7. Return success response

### 6. Forgot User ID

**Endpoint:** `POST /auth/forgot-userid`

**Description:** Sends the user's ID to their registered email address.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User ID sent to your email",
  "data": null,
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Find user by email
2. Send email with user ID
3. Return success response

### 7. Resend Verification Email

**Endpoint:** `POST /auth/resend-verification`

**Description:** Resends the verification email to the user.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Verification email resent",
  "data": null,
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Find user by email
2. Check if user is already confirmed
3. Generate new verification token
4. Send verification email
5. Return success response

### 8. Get All Roles

**Endpoint:** `GET /auth/roles`

**Description:** Gets a list of all available roles in the system.

**Response:**
```json
{
  "success": true,
  "message": "Roles retrieved successfully",
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "admin"
    },
    {
      "id": "223e4567-e89b-12d3-a456-************",
      "name": "tutor"
    },
    {
      "id": "323e4567-e89b-12d3-a456-************",
      "name": "student"
    }
  ],
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Retrieve all roles from database
2. Return roles list

## Features

1. **User Registration**
   - Register as student or tutor
   - Email verification
   - Tutor approval workflow

2. **User Login**
   - Unified login for all user types
   - Role selection
   - Remember me functionality
   - Return URL support

3. **Account Recovery**
   - Forgot password
   - Forgot user ID
   - Password reset

4. **Email Verification**
   - Email verification on registration
   - Resend verification email

## Tasks

1. **Implement User Registration API**
   - Create registration endpoint
   - Validate input data
   - Handle email verification
   - Implement role assignment

2. **Implement User Login API**
   - Create login endpoint
   - Implement JWT token generation
   - Handle role selection
   - Implement remember me functionality

3. **Implement Account Recovery APIs**
   - Create forgot password endpoint
   - Create reset password endpoint
   - Create forgot user ID endpoint

4. **Implement Email Verification**
   - Create verify email endpoint
   - Create resend verification endpoint
   - Implement token generation and validation
