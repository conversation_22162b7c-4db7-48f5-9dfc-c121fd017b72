import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { ConfigModule } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";

import { QAMissionGoal } from "src/database/entities/qa-mission-goal.entity";
import { QATaskMissions } from "src/database/entities/qa-task-missions.entity";
import { QAMissionMonth } from '../../database/entities/qa-mission-month.entity';
import { QAMissionWeek } from '../../database/entities/qa-mission-week.entity';
import { QATaskSubmissions } from "src/database/entities/qa-task-submissions.entity";
import { QATaskSubmissionHistory } from "src/database/entities/qa-task-submission-history.entity";
import { QATaskSubmissionMarking } from "src/database/entities/qa-task-submission-marking.entity";

import { AdminQAMissionController } from "./admin-qa-mission.controller";
import { QAMissionTimeController } from './qa-mission-time.controller';
import { TutorQAMissionController } from './tutor-qa-mission.controller';
import { StudentQAMissionController } from './student-qa-mission.controller';

import { QAMissionService } from "./qa-mission.service";
import { QAMissionTimeService } from './qa-mission-time.service';
import { TutorQAMissionService } from './tutor-qa-mission.service';
import { QASubmissionService } from "./student-qa-mission.service";
import { CurrentUserService } from "src/common/services/current-user.service";
import { NotificationHelperService } from '../notification/notification-helper.service';
import { DeeplinkService } from '../../common/utils/deeplink.service';
import { NotificationModule } from '../notification/notification.module';
import { DeeplinkModule } from '../../common/utils/deeplink.module';
import { QAMission } from "src/database/entities/qa-mission.entity";
import { QAWeeklyMissionTasks } from "src/database/entities/qa-weekly-mission-tasks.entity";
import { QAMonthlyMissionTasks } from "src/database/entities/qa-monthly-mission-tasks.entity";
import { QAMissionTasks } from "src/database/entities/qa-mission-tasks.entity";
import { TutorQAMissionAdministrationController } from "./tutor-qa-mission-admin-task.controller";
import { TutorPermission } from "src/database/entities/tutor-permission.entity";
import { CommonModule } from "src/common/common.module";
import { PermissionsModule } from "../permissions/permissions.module";
import { StudentTutorMapping } from "src/database/entities/student-tutor-mapping.entity";
import { User } from "src/database/entities/user.entity";
import { PlanFeature } from "src/database/entities/plan-feature.entity";
import { TutorQAMissionTimeController } from "./tutor-qa-mission-time.controller";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      QAMissionGoal,
      QATaskMissions,
      QAMissionTasks,
      QATaskSubmissionHistory,
      QATaskSubmissions,
      QAMissionMonth,
      QAMissionWeek,
      QATaskSubmissionMarking,
      QAMission,
      QAWeeklyMissionTasks,
      QAMonthlyMissionTasks,
      QAMissionMonth,
      QAMissionWeek,
      TutorPermission,
      StudentTutorMapping,
      User,
      PlanFeature
    ]),
    ConfigModule,
    NotificationModule,
    DeeplinkModule,
    CommonModule,
    PermissionsModule
  ],
  controllers: [
    AdminQAMissionController,
    QAMissionTimeController,
    TutorQAMissionController,
    TutorQAMissionAdministrationController,
    StudentQAMissionController,
    TutorQAMissionTimeController
  ],
  providers: [
    QAMissionService,
    JwtService,
    QAMissionTimeService,
    TutorQAMissionService,
    QASubmissionService,
    CurrentUserService,
    NotificationHelperService,
    DeeplinkService
  ],
  exports: [
    QAMissionService,
    QAMissionTimeService,
    TutorQAMissionService,
    QASubmissionService
  ]
})
export class QAMissionModule {}
