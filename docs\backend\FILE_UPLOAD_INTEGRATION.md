# File Upload Integration Guide

## Overview

This document provides a comprehensive guide for integrating the HEC file upload system into the frontend application. It covers uploading files, managing file metadata, and displaying uploaded files.

## File Upload Flow

The file upload flow in the HEC system follows these steps:

1. User selects a file through the UI
2. Frontend validates file type and size
3. Frontend uploads file to the file upload API
4. Backend processes the file and stores it
5. Backend returns file metadata and URL
6. Frontend displays the file or file preview

## API Endpoints

### Upload File

```
POST /api/media/upload
```

**Request:**
- Must be a `multipart/form-data` request
- File should be sent in the `file` field
- Additional metadata can be included as form fields

**Response:**
```json
{
  "id": "file-123",
  "originalName": "document.pdf",
  "mimeType": "application/pdf",
  "size": 1024000,
  "url": "/uploads/file-123.pdf",
  "thumbnailUrl": "/uploads/thumbnails/file-123.jpg",
  "createdAt": "2023-01-01T12:00:00Z"
}
```

### Get File

```
GET /api/media/:fileId
```

**Response:**
- The file content with appropriate Content-Type header

### Delete File

```
DELETE /api/media/:fileId
```

**Response:**
```json
{
  "success": true,
  "message": "File deleted successfully"
}
```

## Frontend Implementation

### File Selection

```javascript
// HTML
<input type="file" id="fileInput" accept=".pdf,.jpg,.png,.doc,.docx" />

// JavaScript
const fileInput = document.getElementById('fileInput');
fileInput.addEventListener('change', handleFileSelect);

function handleFileSelect(event) {
  const file = event.target.files[0];
  if (file) {
    // Validate file
    if (validateFile(file)) {
      // Proceed with upload
      uploadFile(file);
    }
  }
}
```

### File Validation

```javascript
function validateFile(file) {
  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB in bytes
  if (file.size > maxSize) {
    alert('File is too large. Maximum size is 10MB.');
    return false;
  }
  
  // Check file type
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];
  
  if (!allowedTypes.includes(file.type)) {
    alert('Invalid file type. Allowed types: PDF, JPG, PNG, DOC, DOCX');
    return false;
  }
  
  return true;
}
```

### File Upload

```javascript
async function uploadFile(file) {
  try {
    // Show upload progress
    showUploadProgress(0);
    
    const formData = new FormData();
    formData.append('file', file);
    
    // Add any additional metadata
    formData.append('entityType', 'user_profile');
    formData.append('entityId', '123');
    
    const response = await fetch('/api/media/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAccessToken()}`
      },
      body: formData,
      // Track upload progress
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        showUploadProgress(percentCompleted);
      }
    });
    
    if (!response.ok) {
      throw new Error('Upload failed');
    }
    
    const fileData = await response.json();
    
    // Handle successful upload
    handleUploadSuccess(fileData);
    
  } catch (error) {
    console.error('File upload error:', error);
    showUploadError(error.message);
  } finally {
    hideUploadProgress();
  }
}
```

### Display Uploaded File

```javascript
function handleUploadSuccess(fileData) {
  // Create file preview based on file type
  let filePreview;
  
  if (fileData.mimeType.startsWith('image/')) {
    // Image preview
    filePreview = `
      <div class="file-preview">
        <img src="${fileData.url}" alt="${fileData.originalName}" />
        <div class="file-info">
          <span>${fileData.originalName}</span>
          <button onclick="deleteFile('${fileData.id}')">Delete</button>
        </div>
      </div>
    `;
  } else {
    // Generic file preview
    filePreview = `
      <div class="file-preview">
        <div class="file-icon">${getFileIcon(fileData.mimeType)}</div>
        <div class="file-info">
          <span>${fileData.originalName}</span>
          <a href="${fileData.url}" target="_blank">View</a>
          <button onclick="deleteFile('${fileData.id}')">Delete</button>
        </div>
      </div>
    `;
  }
  
  // Add to the UI
  document.getElementById('filePreviewContainer').innerHTML = filePreview;
}
```

### Delete File

```javascript
async function deleteFile(fileId) {
  if (!confirm('Are you sure you want to delete this file?')) {
    return;
  }
  
  try {
    const response = await fetch(`/api/media/${fileId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${getAccessToken()}`
      }
    });
    
    if (!response.ok) {
      throw new Error('Delete failed');
    }
    
    // Remove file preview from UI
    document.getElementById('filePreviewContainer').innerHTML = '';
    
  } catch (error) {
    console.error('File delete error:', error);
    alert('Failed to delete file: ' + error.message);
  }
}
```

## File URL Handling

### Cache Busting

To prevent caching issues with updated files, append a timestamp to the URL:

```javascript
function getFileUrlWithTimestamp(url) {
  const timestamp = new Date().getTime();
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}t=${timestamp}`;
}
```

### Fallback Images

Handle missing images with fallbacks:

```javascript
function getFileUrlWithFallback(fileData) {
  if (!fileData || !fileData.url) {
    return '/assets/images/default-placeholder.png';
  }
  return getFileUrlWithTimestamp(fileData.url);
}
```

## Multiple File Upload

For multiple file uploads, use this approach:

```javascript
// HTML
<input type="file" id="multipleFileInput" multiple accept=".pdf,.jpg,.png" />

// JavaScript
async function uploadMultipleFiles(files) {
  const uploadPromises = Array.from(files).map(file => {
    return uploadFile(file);
  });
  
  try {
    // Upload all files in parallel
    const results = await Promise.all(uploadPromises);
    return results;
  } catch (error) {
    console.error('Multiple file upload error:', error);
    throw error;
  }
}
```

## Drag and Drop Upload

Implement drag and drop file upload:

```javascript
const dropZone = document.getElementById('dropZone');

// Prevent default drag behaviors
['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
  dropZone.addEventListener(eventName, preventDefaults, false);
});

function preventDefaults(e) {
  e.preventDefault();
  e.stopPropagation();
}

// Highlight drop zone when item is dragged over it
['dragenter', 'dragover'].forEach(eventName => {
  dropZone.addEventListener(eventName, highlight, false);
});

['dragleave', 'drop'].forEach(eventName => {
  dropZone.addEventListener(eventName, unhighlight, false);
});

function highlight() {
  dropZone.classList.add('highlight');
}

function unhighlight() {
  dropZone.classList.remove('highlight');
}

// Handle dropped files
dropZone.addEventListener('drop', handleDrop, false);

function handleDrop(e) {
  const dt = e.dataTransfer;
  const files = dt.files;
  
  if (files.length > 0) {
    handleFiles(files);
  }
}

function handleFiles(files) {
  if (files.length > 1) {
    uploadMultipleFiles(files);
  } else {
    uploadFile(files[0]);
  }
}
```

## Conclusion

This integration guide provides the foundation for implementing file uploads in your frontend application. Follow these patterns to ensure reliable file handling and display.

For any questions or issues, please contact the backend team.
