# React Components Library - KCP Payment Integration

This document provides a complete set of React components for implementing the KCP payment gateway integration in your NextJS frontend.

## Component Architecture

```
components/payment/
├── PaymentProvider.tsx          # Context provider for payment state
├── PaymentMethodSelector.tsx    # Payment method selection component
├── CheckoutForm.tsx            # Shop checkout form
├── SubscriptionForm.tsx        # Plan subscription form
├── PaymentStatus.tsx           # Payment status display
├── PaymentProgress.tsx         # Payment progress indicator
├── PaymentError.tsx            # Error handling component
└── PaymentSuccess.tsx          # Success confirmation component
```

## 1. Payment Context Provider

```typescript
// components/payment/PaymentProvider.tsx
import React, { createContext, useContext, useReducer, ReactNode } from 'react';

interface PaymentState {
  selectedMethod: string | null;
  loading: boolean;
  error: string | null;
  transactionId: string | null;
  paymentUrl: string | null;
  status: 'idle' | 'processing' | 'success' | 'error';
}

type PaymentAction =
  | { type: 'SET_METHOD'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_TRANSACTION'; payload: { id: string; url?: string } }
  | { type: 'SET_STATUS'; payload: PaymentState['status'] }
  | { type: 'RESET' };

const initialState: PaymentState = {
  selectedMethod: null,
  loading: false,
  error: null,
  transactionId: null,
  paymentUrl: null,
  status: 'idle',
};

const paymentReducer = (state: PaymentState, action: PaymentAction): PaymentState => {
  switch (action.type) {
    case 'SET_METHOD':
      return { ...state, selectedMethod: action.payload, error: null };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false, status: 'error' };
    case 'SET_TRANSACTION':
      return {
        ...state,
        transactionId: action.payload.id,
        paymentUrl: action.payload.url || null,
        status: 'processing',
      };
    case 'SET_STATUS':
      return { ...state, status: action.payload };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
};

const PaymentContext = createContext<{
  state: PaymentState;
  dispatch: React.Dispatch<PaymentAction>;
} | null>(null);

export const PaymentProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(paymentReducer, initialState);

  return (
    <PaymentContext.Provider value={{ state, dispatch }}>
      {children}
    </PaymentContext.Provider>
  );
};

export const usePayment = () => {
  const context = useContext(PaymentContext);
  if (!context) {
    throw new Error('usePayment must be used within a PaymentProvider');
  }
  return context;
};
```

## 2. Payment Method Selector

```typescript
// components/payment/PaymentMethodSelector.tsx
import React from 'react';
import { usePayment } from './PaymentProvider';
import styles from './PaymentMethodSelector.module.css';

interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  description: string;
  enabled: boolean;
}

const paymentMethods: PaymentMethod[] = [
  {
    id: 'reward_points',
    name: 'Reward Points',
    icon: '🎯',
    description: 'Use your earned reward points',
    enabled: true,
  },
  {
    id: 'kcp_card',
    name: 'Credit/Debit Card',
    icon: '💳',
    description: 'Visa, MasterCard, American Express',
    enabled: true,
  },
  {
    id: 'kcp_bank',
    name: 'Bank Transfer',
    icon: '🏦',
    description: 'Direct bank account transfer',
    enabled: true,
  },
  {
    id: 'kcp_mobile',
    name: 'Mobile Payment',
    icon: '📱',
    description: 'Mobile carrier billing',
    enabled: true,
  },
];

interface PaymentMethodSelectorProps {
  userRewardPoints?: number;
  requiredPoints?: number;
  onMethodChange?: (method: string) => void;
}

export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  userRewardPoints = 0,
  requiredPoints = 0,
  onMethodChange,
}) => {
  const { state, dispatch } = usePayment();

  const handleMethodSelect = (methodId: string) => {
    // Check if reward points are sufficient
    if (methodId === 'reward_points' && userRewardPoints < requiredPoints) {
      dispatch({
        type: 'SET_ERROR',
        payload: `Insufficient reward points. Required: ${requiredPoints}, Available: ${userRewardPoints}`,
      });
      return;
    }

    dispatch({ type: 'SET_METHOD', payload: methodId });
    onMethodChange?.(methodId);
  };

  return (
    <div className={styles.container}>
      <h3 className={styles.title}>Select Payment Method</h3>
      
      {state.error && (
        <div className={styles.error}>
          {state.error}
        </div>
      )}

      <div className={styles.methods}>
        {paymentMethods.map((method) => {
          const isDisabled = !method.enabled || 
            (method.id === 'reward_points' && userRewardPoints < requiredPoints);
          
          return (
            <div
              key={method.id}
              className={`${styles.method} ${
                state.selectedMethod === method.id ? styles.selected : ''
              } ${isDisabled ? styles.disabled : ''}`}
              onClick={() => !isDisabled && handleMethodSelect(method.id)}
            >
              <div className={styles.methodIcon}>{method.icon}</div>
              <div className={styles.methodInfo}>
                <div className={styles.methodName}>{method.name}</div>
                <div className={styles.methodDescription}>
                  {method.id === 'reward_points' 
                    ? `Available: ${userRewardPoints} points`
                    : method.description
                  }
                </div>
              </div>
              {state.selectedMethod === method.id && (
                <div className={styles.checkmark}>✓</div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
```

## 3. Checkout Form Component

```typescript
// components/payment/CheckoutForm.tsx
import React, { useState } from 'react';
import { usePayment } from './PaymentProvider';
import { PaymentMethodSelector } from './PaymentMethodSelector';
import { useShopCheckout } from '../../hooks/useShopCheckout';
import styles from './CheckoutForm.module.css';

interface CheckoutFormProps {
  cartTotal: number;
  cartItems: any[];
  userRewardPoints: number;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const CheckoutForm: React.FC<CheckoutFormProps> = ({
  cartTotal,
  cartItems,
  userRewardPoints,
  onSuccess,
  onError,
}) => {
  const { state, dispatch } = usePayment();
  const { processCheckout } = useShopCheckout();
  const [useRewardPoints, setUseRewardPoints] = useState(false);
  const [promoCode, setPromoCode] = useState('');

  const requiredPoints = cartItems.reduce((total, item) => 
    total + (item.rewardPoints * item.quantity), 0
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!state.selectedMethod) {
      dispatch({ type: 'SET_ERROR', payload: 'Please select a payment method' });
      return;
    }

    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const checkoutData = {
        paymentMethod: state.selectedMethod,
        useRewardPoints: useRewardPoints && state.selectedMethod !== 'reward_points',
        promoCode: promoCode.trim() || undefined,
        returnUrl: `${window.location.origin}/payment/success?type=shop`,
        cancelUrl: `${window.location.origin}/payment/cancel`,
      };

      await processCheckout(checkoutData);
      onSuccess?.();
    } catch (error: any) {
      const errorMessage = error.message || 'Checkout failed';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      onError?.(errorMessage);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={styles.form}>
      <div className={styles.section}>
        <h2>Order Summary</h2>
        <div className={styles.orderSummary}>
          {cartItems.map((item) => (
            <div key={item.id} className={styles.orderItem}>
              <span>{item.title} x {item.quantity}</span>
              <span>{item.totalPrice} KRW</span>
            </div>
          ))}
          <div className={styles.orderTotal}>
            <strong>Total: {cartTotal} KRW</strong>
          </div>
        </div>
      </div>

      <div className={styles.section}>
        <PaymentMethodSelector
          userRewardPoints={userRewardPoints}
          requiredPoints={requiredPoints}
        />
      </div>

      {state.selectedMethod && state.selectedMethod !== 'reward_points' && (
        <div className={styles.section}>
          <label className={styles.checkbox}>
            <input
              type="checkbox"
              checked={useRewardPoints}
              onChange={(e) => setUseRewardPoints(e.target.checked)}
              disabled={userRewardPoints < requiredPoints}
            />
            Use reward points ({userRewardPoints} available)
          </label>
        </div>
      )}

      <div className={styles.section}>
        <label className={styles.label}>
          Promo Code (Optional)
          <input
            type="text"
            value={promoCode}
            onChange={(e) => setPromoCode(e.target.value)}
            placeholder="Enter promo code"
            className={styles.input}
          />
        </label>
      </div>

      <button
        type="submit"
        disabled={!state.selectedMethod || state.loading}
        className={styles.submitButton}
      >
        {state.loading ? 'Processing...' : `Pay ${cartTotal} KRW`}
      </button>
    </form>
  );
};
```

## 4. Subscription Form Component

```typescript
// components/payment/SubscriptionForm.tsx
import React, { useState } from 'react';
import { usePayment } from './PaymentProvider';
import { PaymentMethodSelector } from './PaymentMethodSelector';
import { usePlanSubscription } from '../../hooks/usePlanSubscription';
import styles from './SubscriptionForm.module.css';

interface Plan {
  id: string;
  name: string;
  price: number;
  subscriptionType: 'monthly' | 'yearly';
  features: string[];
}

interface SubscriptionFormProps {
  plan: Plan;
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const SubscriptionForm: React.FC<SubscriptionFormProps> = ({
  plan,
  onSuccess,
  onError,
}) => {
  const { state, dispatch } = usePayment();
  const { subscribeToPlan } = usePlanSubscription();
  const [autoRenew, setAutoRenew] = useState(true);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!state.selectedMethod) {
      dispatch({ type: 'SET_ERROR', payload: 'Please select a payment method' });
      return;
    }

    if (plan.price === 0 && state.selectedMethod !== 'free') {
      dispatch({ type: 'SET_ERROR', payload: 'This is a free plan' });
      return;
    }

    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const subscriptionData = {
        planId: plan.id,
        paymentMethod: state.selectedMethod,
        autoRenew,
        returnUrl: `${window.location.origin}/payment/success?type=plan`,
        cancelUrl: `${window.location.origin}/payment/cancel`,
      };

      await subscribeToPlan(subscriptionData);
      onSuccess?.();
    } catch (error: any) {
      const errorMessage = error.message || 'Subscription failed';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      onError?.(errorMessage);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={styles.form}>
      <div className={styles.section}>
        <h2>Plan Details</h2>
        <div className={styles.planInfo}>
          <h3>{plan.name}</h3>
          <div className={styles.price}>
            {plan.price === 0 ? 'Free' : `${plan.price} KRW/${plan.subscriptionType}`}
          </div>
          <ul className={styles.features}>
            {plan.features.map((feature, index) => (
              <li key={index}>{feature}</li>
            ))}
          </ul>
        </div>
      </div>

      {plan.price > 0 && (
        <div className={styles.section}>
          <PaymentMethodSelector />
        </div>
      )}

      <div className={styles.section}>
        <label className={styles.checkbox}>
          <input
            type="checkbox"
            checked={autoRenew}
            onChange={(e) => setAutoRenew(e.target.checked)}
          />
          Enable auto-renewal
        </label>
        <p className={styles.note}>
          Your subscription will automatically renew at the end of each billing period.
          You can cancel anytime from your account settings.
        </p>
      </div>

      <button
        type="submit"
        disabled={(!state.selectedMethod && plan.price > 0) || state.loading}
        className={styles.submitButton}
      >
        {state.loading 
          ? 'Processing...' 
          : plan.price === 0 
            ? 'Subscribe for Free' 
            : `Subscribe for ${plan.price} KRW`
        }
      </button>
    </form>
  );
};
```

## 5. Payment Status Component

```typescript
// components/payment/PaymentStatus.tsx
import React, { useEffect } from 'react';
import { usePaymentVerification } from '../../hooks/usePaymentVerification';
import { PaymentProgress } from './PaymentProgress';
import { PaymentSuccess } from './PaymentSuccess';
import { PaymentError } from './PaymentError';
import styles from './PaymentStatus.module.css';

interface PaymentStatusProps {
  transactionId: string;
  onStatusChange?: (status: string) => void;
}

export const PaymentStatus: React.FC<PaymentStatusProps> = ({
  transactionId,
  onStatusChange,
}) => {
  const { status, loading, error, verifyPayment } = usePaymentVerification(transactionId);

  useEffect(() => {
    if (status) {
      onStatusChange?.(status.status);
    }
  }, [status, onStatusChange]);

  if (loading) {
    return <PaymentProgress message="Verifying payment..." />;
  }

  if (error) {
    return (
      <PaymentError
        message={error}
        onRetry={() => verifyPayment(transactionId)}
      />
    );
  }

  if (status) {
    switch (status.status) {
      case 'completed':
        return <PaymentSuccess transaction={status} />;
      case 'failed':
        return (
          <PaymentError
            message={status.errorMessage || 'Payment failed'}
            onRetry={() => verifyPayment(transactionId)}
          />
        );
      case 'processing':
      case 'pending':
        return <PaymentProgress message="Processing payment..." />;
      default:
        return <PaymentProgress message="Checking payment status..." />;
    }
  }

  return <PaymentProgress message="Loading..." />;
};
```

## 6. CSS Modules for Styling

```css
/* components/payment/PaymentMethodSelector.module.css */
.container {
  margin: 1rem 0;
}

.title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
}

.error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 0.75rem;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.methods {
  display: grid;
  gap: 0.75rem;
}

.method {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.method:hover:not(.disabled) {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.method.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.method.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f9fafb;
}

.methodIcon {
  font-size: 1.5rem;
  margin-right: 0.75rem;
}

.methodInfo {
  flex: 1;
}

.methodName {
  font-weight: 600;
  color: #1f2937;
}

.methodDescription {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.checkmark {
  color: #3b82f6;
  font-weight: bold;
  font-size: 1.25rem;
}
```

```css
/* components/payment/CheckoutForm.module.css */
.form {
  max-width: 600px;
  margin: 0 auto;
}

.section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #ffffff;
}

.orderSummary {
  space-y: 0.5rem;
}

.orderItem {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.orderTotal {
  padding-top: 1rem;
  border-top: 2px solid #e5e7eb;
  font-size: 1.125rem;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.label {
  display: block;
  font-weight: 500;
  color: #374151;
}

.input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  margin-top: 0.5rem;
  font-size: 1rem;
}

.input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.submitButton {
  width: 100%;
  background-color: #3b82f6;
  color: white;
  padding: 1rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submitButton:hover:not(:disabled) {
  background-color: #2563eb;
}

.submitButton:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}
```

This React components library provides a complete, production-ready set of components for implementing the KCP payment gateway integration in your NextJS frontend application.
