import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, Unique, Index } from 'typeorm';
import { DiaryEntry } from './diary-entry.entity';
import { User } from './user.entity';

export enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
}

@Entity('diary_entry_attendance')
@Unique(['diaryEntryId'])
@Index(['studentId', 'entryDate'])
export class DiaryEntryAttendance {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  diaryEntryId: string;

  @Column({ type: 'uuid' })
  studentId: string;

  @Column({ type: 'date' })
  entryDate: Date;

  @Column({ type: 'enum', enum: AttendanceStatus })
  status: AttendanceStatus;

  @Column({ type: 'int', nullable: true })
  wordCount: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => DiaryEntry, { onDelete: 'CASCADE' })
  diaryEntry: DiaryEntry;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  student: User;
}
