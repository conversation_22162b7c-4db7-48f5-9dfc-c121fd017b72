import { MigrationInterface, QueryRunner } from "typeorm";

export class StudentEssaySubmissionTable1745750417185 implements MigrationInterface {
    name = 'StudentEssaySubmissionTable1745750417185'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."essay_task_submissions_status_enum" RENAME TO "essay_task_submissions_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."essay_task_submissions_status_enum" AS ENUM('draft', 'submitted', 'reviewed', 'discarded')`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" TYPE "public"."essay_task_submissions_status_enum" USING "status"::"text"::"public"."essay_task_submissions_status_enum"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" SET DEFAULT 'draft'`);
        await queryRunner.query(`DROP TYPE "public"."essay_task_submissions_status_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."essay_task_submissions_status_enum_old" AS ENUM('draft', 'submitted', 'reviewed')`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" TYPE "public"."essay_task_submissions_status_enum_old" USING "status"::"text"::"public"."essay_task_submissions_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ALTER COLUMN "status" SET DEFAULT 'draft'`);
        await queryRunner.query(`DROP TYPE "public"."essay_task_submissions_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."essay_task_submissions_status_enum_old" RENAME TO "essay_task_submissions_status_enum"`);
    }

}
