import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, UseInterceptors, UploadedFile, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { ShopService } from './shop.service';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { Inject } from '@nestjs/common';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import {
  CreateShopCategoryDto,
  UpdateShopCategoryDto,
  ShopCategoryResponseDto,
  CreateShopItemDto,
  UpdateShopItemDto,
  ShopItemResponseDto,
  ShopItemWithPromotionDto,
  BulkPriceUpdateDto,
  BulkDiscountUpdateDto,
  ApplyPromotionDto,
  ShopItemPurchaseResponseDto,
  GenerateItemNumberDto,
  FileUploadResponseDto,
  GroupedShopItemsResponseDto,
  PublishDiarySkinToShopDto,
  DiarySkinDraftShopItemResponseDto
} from '../../database/models/shop.dto';
import { ShopItemType } from '../../database/entities/shop-item.entity';
import { PurchaseStatus } from '../../database/entities/shop-item-purchase.entity';
import { DiaryService } from '../diary/diary.service';
import { ShopSkinService } from './shop-skin.service';
import { ApiOkResponseWithEmptyData } from 'src/common/decorators/api-empty-response.decorator';
import { UpdatePromotionApplicationStatusDto } from '../../database/models/update-promotion-activation.dto';
import { PromotionResponseDto } from 'src/database/models/promotion.dto';
// These imports are no longer needed as we're using FileUploadService
// import { diskStorage } from 'multer';
// import { extname } from 'path';
// import { v4 as uuidv4 } from 'uuid';

@ApiTags('admin-shop')
@Controller('admin/shop')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
export class AdminShopController {
  private readonly logger = new Logger(AdminShopController.name);

  constructor(
    private readonly shopService: ShopService,
    @Inject(FileRegistryService) private readonly fileRegistryService: FileRegistryService,
    @Inject(DiaryService) private readonly diaryService: DiaryService,
    @Inject(ShopSkinService) private readonly shopSkinService: ShopSkinService
  ) {}

  // Category management endpoints

  @Post('categories')
  @ApiOperation({
    summary: 'Create a new shop category (Admin only)',
    description: 'Creates a new shop category with the specified details.'
  })
  @ApiBody({
    type: CreateShopCategoryDto,
    description: 'Category creation data'
  })
  @ApiOkResponseWithType(ShopCategoryResponseDto, 'Shop category created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(409, 'Category with the same name already exists')
  async createCategory(@Body() createShopCategoryDto: CreateShopCategoryDto): Promise<ApiResponse<ShopCategoryResponseDto>> {
    const category = await this.shopService.createShopCategory(createShopCategoryDto);
    return ApiResponse.success(category, 'Shop category created successfully', 201);
  }

  @Get('categories')
  @ApiOperation({
    summary: 'Get all shop categories (Admin only)',
    description: 'Get a list of all shop categories, including inactive ones.'
  })
  @ApiQuery({
    name: 'includeInactive',
    required: false,
    type: String,
    description: 'Whether to include inactive categories (pass "true" to include inactive categories)'
  })
  @ApiQuery({
    name: 'parentId',
    required: false,
    type: String,
    description: 'Filter categories by parent ID. Use "null" to get only top-level categories.'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiOkResponseWithPagedListType(ShopCategoryResponseDto, 'Shop categories retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllCategories(
    @Query('includeInactive') includeInactive?: string,
    @Query('parentId') parentId?: string,
    @Query() paginationDto?: PaginationDto
  ): Promise<ApiResponse<PagedListDto<ShopCategoryResponseDto>>> {
    const categories = await this.shopService.getAllShopCategories(includeInactive === 'true', parentId, paginationDto);
    return ApiResponse.success(categories, 'Shop categories retrieved successfully');
  }

  @Get('categories/:id')
  @ApiOperation({
    summary: 'Get shop category by ID (Admin only)',
    description: 'Get details of a specific shop category by ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'Category ID',
    type: String
  })
  @ApiOkResponseWithType(ShopCategoryResponseDto, 'Shop category retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Shop category not found')
  async getCategoryById(@Param('id') id: string): Promise<ApiResponse<ShopCategoryResponseDto>> {
    const category = await this.shopService.getShopCategoryById(id);
    return ApiResponse.success(category, 'Shop category retrieved successfully');
  }

  @Patch('categories/:id')
  @ApiOperation({
    summary: 'Update a shop category (Admin only)',
    description: 'Update an existing shop category with the specified details.'
  })
  @ApiParam({
    name: 'id',
    description: 'Category ID',
    type: String
  })
  @ApiBody({
    type: UpdateShopCategoryDto,
    description: 'Category update data'
  })
  @ApiOkResponseWithType(ShopCategoryResponseDto, 'Shop category updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Shop category not found')
  @ApiErrorResponse(409, 'Category with the same name already exists')
  async updateCategory(@Param('id') id: string, @Body() updateShopCategoryDto: UpdateShopCategoryDto): Promise<ApiResponse<ShopCategoryResponseDto>> {
    const category = await this.shopService.updateShopCategory(id, updateShopCategoryDto);
    return ApiResponse.success(category, 'Shop category updated successfully');
  }

  @Delete('categories/:id')
  @ApiOperation({
    summary: 'Delete a shop category (Admin only)',
    description: 'Delete an existing shop category.'
  })
  @ApiParam({
    name: 'id',
    description: 'Category ID',
    type: String
  })
  @ApiOkResponseWithType(Object, 'Shop category deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Shop category not found')
  async deleteCategory(@Param('id') id: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    const result = await this.shopService.deleteShopCategory(id);
    return ApiResponse.success(result, result.message);
  }

  @Get('emoticon-category-id')
  @ApiOperation({
    summary: 'Get emoticon category ID (Admin only)',
    description: 'Get the ID of the emoticon category. The emoticon category is automatically created if it does not exist.'
  })
  @ApiOkResponseWithType(String, 'Emoticon category ID retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(500, 'Internal server error')
  async getEmotIconCategoryId(): Promise<ApiResponse<string>> {
    const emoticonCategory = await this.shopService.getOrCreateEmoticonCategory();
    return ApiResponse.success(emoticonCategory.id, 'Emoticon category ID retrieved successfully');
  }

  // Shop item management endpoints

  @Post('items/generate-number')
  @ApiOperation({
    summary: 'Generate a unique item number (Admin only)',
    description: 'Generates a unique item number based on the category ID. The prefix is derived from the first 2 letters of the category name.'
  })
  @ApiBody({
    type: GenerateItemNumberDto,
    description: 'Item number generation data (requires categoryId)'
  })
  @ApiOkResponseWithType(String, 'Item number generated successfully')
  @ApiErrorResponse(400, 'Bad Request - Missing or invalid category ID')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Category not found')
  async generateItemNumber(@Body() generateItemNumberDto: GenerateItemNumberDto): Promise<ApiResponse<string>> {
    const itemNumber = await this.shopService.generateItemNumber(generateItemNumberDto);
    return ApiResponse.success(itemNumber, 'Item number generated successfully');
  }

  @Post('items')
  @ApiOperation({
    summary: 'Create a new shop item with file upload (Admin only)',
    description: 'Creates a new shop item with the specified details and uploads the file in one step.'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The file to upload'
        },
        itemNumber: {
          type: 'string',
          description: 'Optional item number (will be generated if not provided)'
        },
        title: {
          type: 'string',
          description: 'Title of the shop item'
        },
        description: {
          type: 'string',
          description: 'Description of the shop item'
        },
        categoryId: {
          type: 'string',
          description: 'Category ID for the shop item'
        },
        type: {
          type: 'string',
          enum: Object.values(ShopItemType),
          description: 'Type of shop item'
        },
        price: {
          type: 'number',
          description: 'Price of the shop item'
        },
        isPurchasableInRewardpoint: {
          type: 'boolean',
          description: 'Whether the item is purchasable with reward points. For FREE items, this will be automatically set to false.'
        },
        isActive: {
          type: 'boolean',
          description: 'Whether the shop item is active'
        },
        isFeatured: {
          type: 'boolean',
          description: 'Whether the shop item is featured'
        },
        metadata: {
          type: 'string',
          description: 'Additional metadata for the shop item as JSON string'
        }
      },
      required: ['title', 'description', 'categoryId']
    }
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
      }
    })
  )
  @ApiOkResponseWithType(ShopItemResponseDto, 'Shop item created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(409, 'Shop item with the same item number already exists')
  async createItem(@UploadedFile() file: any, @Body() createShopItemDto: CreateShopItemDto): Promise<ApiResponse<ShopItemResponseDto>> {
    try {
      // Convert string values to appropriate types
      if (createShopItemDto.price && typeof createShopItemDto.price === 'string') {
        createShopItemDto.price = parseFloat(createShopItemDto.price);
      }

      if (createShopItemDto.discountedPrice && typeof createShopItemDto.discountedPrice === 'string') {
        createShopItemDto.discountedPrice = parseFloat(createShopItemDto.discountedPrice);
      }

      // priceEquivalentToRewardPoint has been removed

      if (createShopItemDto.isPurchasableInRewardpoint !== undefined && typeof createShopItemDto.isPurchasableInRewardpoint === 'string') {
        createShopItemDto.isPurchasableInRewardpoint = createShopItemDto.isPurchasableInRewardpoint === 'true';
      }

      if (createShopItemDto.isActive && typeof createShopItemDto.isActive === 'string') {
        createShopItemDto.isActive = createShopItemDto.isActive === 'true';
      }

      if (createShopItemDto.isFeatured && typeof createShopItemDto.isFeatured === 'string') {
        createShopItemDto.isFeatured = createShopItemDto.isFeatured === 'true';
      }

      // No processing for metadata - use it exactly as it comes in the request

      // Process the file upload and shop item creation in one step
      const item = await this.shopService.createShopItemWithFile(file, createShopItemDto);

      // The file URL is already set by the service using getFileUrlWithFallback
      return ApiResponse.success(item, 'Shop item created successfully', 201);
    } catch (error) {
      console.error('Error creating shop item:', error);
      if (error.response) {
        console.error('Error response:', error.response);
      }
      throw error;
    }
  }

  @Post('items/upload')
  @ApiOperation({
    summary: 'Upload a file for a shop item (Admin only)',
    description: 'Uploads a file for a shop item and returns the file path.'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The file to upload'
        },
        itemNumber: {
          type: 'string',
          description: 'Optional item number (will be generated if not provided)'
        },
        categoryId: {
          type: 'string',
          description: 'Optional category ID for organizing files and generating item numbers'
        }
      },
      required: ['file']
    }
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
      }
    })
  )
  @ApiOkResponseWithType(FileUploadResponseDto, 'File uploaded successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async uploadFile(@UploadedFile() file: any, @Body('itemNumber') itemNumber: string, @Body('categoryId') categoryId: string): Promise<ApiResponse<FileUploadResponseDto>> {
    // If no item number is provided, generate one
    let finalItemNumber = itemNumber;
    if (!finalItemNumber) {
      try {
        finalItemNumber = await this.shopService.generateItemNumber({ categoryId });
      } catch (error) {
        finalItemNumber = 'TEMP';
      }
    }

    const result = await this.fileRegistryService.uploadFile(FileEntityType.SHOP_ITEM, file, finalItemNumber, { categoryId });
    return ApiResponse.success(
      {
        filePath: result.filePath,
        itemNumber: finalItemNumber
      },
      'File uploaded successfully'
    );
  }

  @Get('items')
  @ApiOperation({
    summary: 'Get all shop items (Admin only)',
    description: 'Get a list of all shop items, including inactive ones.'
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    type: String,
    description: 'Filter items by category ID'
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ShopItemType,
    description: 'Filter items by type'
  })
  @ApiQuery({
    name: 'includeInactive',
    required: false,
    type: String,
    description: 'Whether to include inactive items (pass "true" to include inactive items)'
  })
  @ApiQuery({
    name: 'itemNumber',
    required: false,
    type: String,
    description: 'Filter items by item number (partial match if 3+ characters)'
  })
  @ApiQuery({
    name: 'title',
    required: false,
    type: String,
    description: 'Filter items by title (partial match if 3+ characters)'
  })
  @ApiQuery({
    name: 'promotionId',
    required: false,
    type: String,
    description: 'Filter items by promotion ID'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(ShopItemResponseDto, 'Shop items retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllItems(
    @Query('categoryId') categoryId?: string,
    @Query('type') type?: ShopItemType,
    @Query('includeInactive') includeInactive?: string,
    @Query('itemNumber') itemNumber?: string,
    @Query('title') title?: string,
    @Query('promotionId') promotionId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC'
  ): Promise<ApiResponse<PagedListDto<ShopItemResponseDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC'
    };

    const items = await this.shopService.getAllShopItems(
      categoryId,
      type,
      includeInactive === 'true',
      false, // featuredOnly
      itemNumber,
      title,
      promotionId,
      paginationDto
    );

    // File URLs are already set by the service using getFileUrlWithFallback

    return ApiResponse.success(items, 'Shop items retrieved successfully');
  }

  @Get('items/with-promotions')
  @ApiOperation({
    summary: 'Get shop items with promotion details (Admin only)',
    description: 'Get a list of shop items with human-readable discount display.'
  })
  @ApiQuery({
    name: 'searchTerm',
    required: false,
    type: String,
    description: 'Search term for filtering across item number, title, category name, and promotion name (minimum 3 characters for partial matching, except for item numbers with hyphens)'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(ShopItemWithPromotionDto, 'Shop items with promotions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getItemsWithPromotions(
    @Query('searchTerm') searchTerm?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC'
  ): Promise<ApiResponse<PagedListDto<ShopItemWithPromotionDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC'
    };

    const pagedItems = await this.shopService.getShopItemsWithPromotions(
      null, // categoryId
      null, // type
      false, // includeInactive
      false, // featuredOnly
      searchTerm,
      null, // promotionId
      paginationDto
    );

    return ApiResponse.success(pagedItems, 'Shop items with promotions retrieved successfully');
  }

  @Get('items/grouped-by-category')
  @ApiOperation({
    summary: 'Get all shop items grouped by category (Admin only)',
    description: 'Get a list of all shop items grouped by their categories.'
  })
  @ApiQuery({
    name: 'includeInactive',
    required: false,
    type: String,
    description: 'Whether to include inactive items (pass "true" to include inactive items)'
  })
  @ApiQuery({
    name: 'title',
    required: false,
    type: String,
    description: 'Filter items by title (partial match if 3+ characters)'
  })
  // Note: These filters are no longer used in the service but kept in the API for backward compatibility
  @ApiQuery({
    name: 'itemNumber',
    required: false,
    type: String,
    description: 'Filter items by item number (not currently used)'
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    type: String,
    description: 'Filter items by category ID (not currently used)'
  })
  @ApiQuery({
    name: 'promotionId',
    required: false,
    type: String,
    description: 'Filter items by promotion ID (not currently used)'
  })
  @ApiOkResponseWithType(GroupedShopItemsResponseDto, 'Shop items grouped by category retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllItemsGroupedByCategory(
    @Query('includeInactive') includeInactive?: string,
    @Query('title') title?: string,
    // These parameters are kept for backward compatibility but not used
    @Query('itemNumber') _itemNumber?: string,
    @Query('categoryId') _categoryId?: string,
    @Query('promotionId') _promotionId?: string
  ): Promise<ApiResponse<GroupedShopItemsResponseDto>> {
    // The service method now accepts includeInactive and title parameters
    const groupedItems = await this.shopService.getAllShopItemsGroupedByCategory(includeInactive === 'true', title);

    return ApiResponse.success(groupedItems, 'Shop items grouped by category retrieved successfully');
  }

  @Get('categories/:categoryId/items')
  @ApiOperation({
    summary: 'Get shop items by category (Admin only)',
    description: 'Get a list of shop items for a specific category with filtering and sorting options.'
  })
  @ApiParam({
    name: 'categoryId',
    description: 'Category ID',
    type: String
  })
  @ApiQuery({
    name: 'includeInactive',
    required: false,
    type: String,
    description: 'Whether to include inactive items (pass "true" to include inactive items)'
  })
  @ApiQuery({
    name: 'itemNumber',
    required: false,
    type: String,
    description: 'Filter items by item number (partial match if 3+ characters)'
  })
  @ApiQuery({
    name: 'title',
    required: false,
    type: String,
    description: 'Filter items by title (partial match if 3+ characters)'
  })
  @ApiQuery({
    name: 'promotionId',
    required: false,
    type: String,
    description: 'Filter items by promotion ID'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Sort field (price, title, createdAt, etc.)'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction'
  })
  @ApiOkResponseWithPagedListType(ShopItemResponseDto, 'Shop items retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Category not found')
  async getItemsByCategory(
    @Param('categoryId') categoryId: string,
    @Query('includeInactive') includeInactive?: string,
    @Query('itemNumber') itemNumber?: string,
    @Query('title') title?: string,
    @Query('promotionId') promotionId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC'
  ): Promise<ApiResponse<PagedListDto<ShopItemResponseDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC'
    };

    const items = await this.shopService.getShopItemsByCategory(categoryId, includeInactive === 'true', itemNumber, title, promotionId, paginationDto);

    return ApiResponse.success(items, 'Shop items retrieved successfully');
  }



  @Patch('items/:id')
  @ApiOperation({
    summary: 'Update a shop item with optional file upload (Admin only)',
    description: 'Update an existing shop item with the specified details and optionally upload a new file.'
  })
  @ApiParam({
    name: 'id',
    description: 'Item ID',
    type: String
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Optional new file to upload'
        },
        itemNumber: {
          type: 'string',
          description: 'Item number'
        },
        title: {
          type: 'string',
          description: 'Title of the shop item'
        },
        description: {
          type: 'string',
          description: 'Description of the shop item'
        },
        categoryId: {
          type: 'string',
          description: 'Category ID for the shop item'
        },
        type: {
          type: 'string',
          enum: Object.values(ShopItemType),
          description: 'Type of shop item'
        },
        price: {
          type: 'number',
          description: 'Price of the shop item'
        },
        isPurchasableInRewardpoint: {
          type: 'boolean',
          description: 'Whether the item is purchasable with reward points. For FREE items, this will be automatically set to false.'
        },
        isActive: {
          type: 'boolean',
          description: 'Whether the shop item is active'
        },
        isFeatured: {
          type: 'boolean',
          description: 'Whether the shop item is featured'
        },
        metadata: {
          type: 'string',
          description: 'Additional metadata for the shop item as JSON string'
        }
      }
    }
  })
  @UseInterceptors(
    FileInterceptor('file', {
      limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
      }
    })
  )
  @ApiOkResponseWithType(ShopItemResponseDto, 'Shop item updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Shop item not found')
  @ApiErrorResponse(409, 'Shop item with the same item number already exists')
  async updateItem(@Param('id') id: string, @UploadedFile() file: any, @Body() updateShopItemDto: UpdateShopItemDto): Promise<ApiResponse<ShopItemResponseDto>> {
    try {
      // Convert string values to appropriate types
      if (updateShopItemDto.hasOwnProperty('price')) {
        if (typeof updateShopItemDto.price === 'string') {
          updateShopItemDto.price = parseFloat(updateShopItemDto.price);
        }
      }

      if (updateShopItemDto.hasOwnProperty('discountedPrice')) {
        if (typeof updateShopItemDto.discountedPrice === 'string') {
          updateShopItemDto.discountedPrice = parseFloat(updateShopItemDto.discountedPrice);
        }
      }

      // priceEquivalentToRewardPoint has been removed

      if (updateShopItemDto.isPurchasableInRewardpoint !== undefined && typeof updateShopItemDto.isPurchasableInRewardpoint === 'string') {
        updateShopItemDto.isPurchasableInRewardpoint = updateShopItemDto.isPurchasableInRewardpoint === 'true';
      }

      if (updateShopItemDto.isActive !== undefined && typeof updateShopItemDto.isActive === 'string') {
        updateShopItemDto.isActive = updateShopItemDto.isActive === 'true';
      }

      if (updateShopItemDto.isFeatured !== undefined && typeof updateShopItemDto.isFeatured === 'string') {
        updateShopItemDto.isFeatured = updateShopItemDto.isFeatured === 'true';
      }

      // No processing for metadata - use it exactly as it comes in the request

      console.log('Processed update DTO:', JSON.stringify(updateShopItemDto));

      // Process the file upload and shop item update in one step
      const item = await this.shopService.updateShopItemWithFile(id, file, updateShopItemDto);

      // The file URL is already set by the service using getFileUrlWithFallback
      return ApiResponse.success(item, 'Shop item updated successfully');
    } catch (error) {
      console.error('Error updating shop item:', error);
      if (error.response) {
        console.error('Error response:', error.response);
      }
      throw error;
    }
  }
  @Patch('items/:id/promotion-activation')
  @ApiOperation({
    summary: 'Update promotion activation status for a shop item (Admin only)',
    description: 'Enable or disable promotion for a shop item. This controls whether the promotion applies to the item even when a promotionId is set.'
  })
  @ApiParam({
    name: 'id',
    description: 'Item ID',
    type: String
  })
  @ApiBody({
    type: UpdatePromotionApplicationStatusDto
  })
  @ApiOkResponseWithType(ShopItemResponseDto, 'Promotion activation status updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Shop item not found')
  @ApiErrorResponse(409, 'Shop item has no promotion applied')
  async updatePromotionActivation(
    @Param('id') id: string,
    @Body() updateDto: UpdatePromotionApplicationStatusDto
  ): Promise<ApiResponse<ShopItemResponseDto>> {
    const item = await this.shopService.updatePromotionActivation(id, updateDto.isPromotionActive);
    return ApiResponse.success(item, `Promotion ${updateDto.isPromotionActive ? 'activated' : 'deactivated'} successfully`);
  }


  @Delete('items/:id')
  @ApiOperation({
    summary: 'Delete a shop item (Admin only)',
    description: 'Delete an existing shop item.'
  })
  @ApiParam({
    name: 'id',
    description: 'Item ID',
    type: String
  })
  @ApiOkResponseWithType(Object, 'Shop item deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Shop item not found')
  async deleteItem(@Param('id') id: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    const result = await this.shopService.deleteShopItem(id);
    return ApiResponse.success(result, result.message);
  }

  @Post('items/bulk-update-prices')
  @ApiOperation({
    summary: 'Update prices for multiple shop items (Admin only)',
    description: 'Updates prices for multiple shop items at once.'
  })
  @ApiBody({
    type: BulkPriceUpdateDto,
    description: 'Bulk price update data'
  })
  @ApiOkResponseWithType(Object, 'Prices updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'No shop items found with the provided IDs')
  async bulkUpdatePrices(@Body() bulkPriceUpdateDto: BulkPriceUpdateDto): Promise<ApiResponse<{ success: boolean; message: string }>> {
    const result = await this.shopService.bulkUpdatePrices(bulkPriceUpdateDto);
    return ApiResponse.success(result, result.message);
  }

  @Post('items/bulk-update-discounts')
  @ApiOperation({
    summary: 'Update discounted prices for multiple shop items (Admin only)',
    description: 'Updates discounted prices for multiple shop items at once.'
  })
  @ApiBody({
    type: BulkDiscountUpdateDto,
    description: 'Bulk discount update data'
  })
  @ApiOkResponseWithType(Object, 'Discounted prices updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'No shop items found with the provided IDs')
  async bulkUpdateDiscounts(@Body() bulkDiscountUpdateDto: BulkDiscountUpdateDto): Promise<ApiResponse<{ success: boolean; message: string }>> {
    const result = await this.shopService.bulkUpdateDiscounts(bulkDiscountUpdateDto);
    return ApiResponse.success(result, result.message);
  }

  @Post('items/apply-promotion')
  @ApiOperation({
    summary: 'Apply a promotion to multiple targets (Admin only)',
    description: 'Applies a promotion to multiple shop items, categories, or plans. ' +
      'You must provide at least one of: itemIds, categoryIds, or planIds. ' +
      'When applying to categories, the promotion will be applied to all items within those categories.'
  })
  @ApiBody({
    type: ApplyPromotionDto,
    description: 'Promotion application data',
    examples: {
      items: {
        summary: 'Apply to specific items',
        value: {
          promotionId: '123e4567-e89b-12d3-a456-426614174001',
          itemIds: ['123e4567-e89b-12d3-a456-426614174002']
        }
      },
      categories: {
        summary: 'Apply to all items in categories',
        value: {
          promotionId: '123e4567-e89b-12d3-a456-426614174001',
          categoryIds: ['123e4567-e89b-12d3-a456-426614174003']
        }
      },
      plans: {
        summary: 'Apply to plans',
        value: {
          promotionId: '123e4567-e89b-12d3-a456-426614174001',
          planIds: ['123e4567-e89b-12d3-a456-426614174004']
        }
      },
      mixed: {
        summary: 'Apply to multiple target types',
        value: {
          promotionId: '123e4567-e89b-12d3-a456-426614174001',
          itemIds: ['123e4567-e89b-12d3-a456-426614174002'],
          categoryIds: ['123e4567-e89b-12d3-a456-426614174003'],
          planIds: ['123e4567-e89b-12d3-a456-426614174004']
        }
      }
    }
  })
  @ApiOkResponseWithType(Object, 'Promotion applied successfully')
  @ApiErrorResponse(400, 'Invalid input - At least one target type must be provided')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Promotion or target items/categories/plans not found')
  async applyPromotion(@Body() applyPromotionDto: ApplyPromotionDto): Promise<ApiResponse<{
    itemsUpdated?: number;
    itemsSkipped?: number;
    categoriesProcessed?: number;
    plansUpdated?: number;
    plansSkipped?: number;
    totalItemsAffected: number;
  }>> {
    const result = await this.shopService.applyPromotion(applyPromotionDto);
    return ApiResponse.success(result, 'Promotion applied successfully');
  }

  @Delete('items/:id/promotion')
  @ApiOperation({
    summary: 'Remove promotion from a shop item (Admin only)',
    description: 'Remove the promotion from a specific shop item.'
  })
  @ApiParam({
    name: 'id',
    description: 'Shop item ID',
    type: String
  })
  @ApiOkResponseWithType(Object, 'Promotion removed successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Shop item not found')
  async removePromotionFromItem(@Param('id') id: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
    const result = await this.shopService.removePromotionFromItem(id);
    return ApiResponse.success(result, result.message);
  }

  @Get('diary-skins/:id/draft')
  @ApiOperation({
    summary: 'Get a draft shop item from a diary skin (Admin only)',
    description:
      'Returns a draft shop item with suggested values based on the diary skin. This endpoint provides default values for all fields that can be modified in the publish endpoint. The image file is automatically taken from the diary skin and cannot be changed. This does not create anything in the database.'
  })
  @ApiParam({
    name: 'id',
    description: 'Diary skin ID',
    type: String
  })
  @ApiOkResponseWithType(DiarySkinDraftShopItemResponseDto, 'Draft shop item created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Diary skin not found')
  async getDraftShopItemFromDiarySkin(@Param('id') id: string): Promise<ApiResponse<DiarySkinDraftShopItemResponseDto>> {
    // Get a draft shop item from the diary skin
    const draftShopItem = await this.shopSkinService.createDraftShopItemFromDiarySkin(id);

    return ApiResponse.success(draftShopItem, 'Draft shop item created successfully');
  }

  @Post('diary-skins/:id/publish')
  @ApiOperation({
    summary: 'Publish a diary skin to the shop (Admin only)',
    description:
      'Publishes an existing diary skin to the shop as a purchasable item. This endpoint is similar to the create shop item API, but with two key differences: 1) The category is fixed to "Skin" if not provided, and 2) The image file is automatically taken from the diary skin and cannot be changed - the file upload part is skipped and the diary skin\'s preview image path is used directly. All other fields match the standard shop item creation. The response will include the created shop item with filePath set to the diary skin\'s preview image path.'
  })
  @ApiParam({
    name: 'id',
    description: 'Diary skin ID',
    type: String
  })
  @ApiBody({
    type: PublishDiarySkinToShopDto,
    description: 'Shop item details for the diary skin'
  })
  @ApiOkResponseWithType(ShopItemResponseDto, "Diary skin published to shop successfully. The response includes the shop item with filePath set to the diary skin's preview image path.")
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Diary skin not found')
  async publishDiarySkinToShop(@Param('id') id: string, @Body() publishDto: PublishDiarySkinToShopDto): Promise<ApiResponse<ShopItemResponseDto>> {
    try {
      // Verify the diary skin exists (will throw NotFoundException if not found)
      await this.diaryService.getDiarySkinById(id);

      // If no category ID is provided, get or create the Skin category
      let categoryId = publishDto.categoryId;
      if (!categoryId) {
        const skinCategory = await this.shopSkinService.getOrCreateSkinCategory();
        categoryId = skinCategory.id;
      }

      // Determine if the item is free based on the type
      const isFree = publishDto.type === ShopItemType.FREE;

      // Create shop item data
      const createShopItemDto: CreateShopItemDto = {
        title: publishDto.title,
        description: publishDto.description,
        categoryId: categoryId,
        itemNumber: publishDto.itemNumber,
        type: publishDto.type || ShopItemType.IN_APP_PURCHASE,
        // If the item is free, set price to 0, otherwise use the provided price or default to 0
        price: isFree ? 0 : publishDto.price || 0,
        // If the item is free, set isPurchasableInRewardpoint to false, otherwise use the provided value
        isPurchasableInRewardpoint: isFree ? false : publishDto.isPurchasableInRewardpoint || false,
        isActive: publishDto.isActive !== undefined ? publishDto.isActive : true,
        isFeatured: publishDto.isFeatured || false,
        promotionId: publishDto.promotionId,
        metadata: publishDto.metadata
      };

      // Create the shop item from the diary skin
      const shopItem = await this.shopService.createShopItemFromDiarySkin(id, createShopItemDto);

      return ApiResponse.success(shopItem, 'Diary skin published to shop successfully');
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(`Error publishing diary skin to shop: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to publish diary skin to shop: ${error.message}`);
    }
  }

  // Purchase management endpoints

  @Get('purchases')
  @ApiOperation({
    summary: 'Get all purchases (Admin only)',
    description: 'Get a list of all purchases, optionally filtered by status.'
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: PurchaseStatus,
    description: 'Filter purchases by status'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(ShopItemPurchaseResponseDto, 'Purchases retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllPurchases(@Query('status') status?: PurchaseStatus, @Query() paginationDto?: PaginationDto): Promise<ApiResponse<PagedListDto<ShopItemPurchaseResponseDto>>> {
    const pagedPurchases = await this.shopService.getAllPurchases(status, paginationDto);

    // Add secure URLs for purchased items with file paths
    for (const purchase of pagedPurchases.items) {
      try {
        // Get the shop item details to access the file path
        const item = await this.shopService.getShopItemById(purchase.shopItemId);
        if (item && item.filePath) {
          // Generate a secure URL for the file
          const secureUrl = await this.shopService.getSecureFileUrl(purchase.shopItemId);
          // Add the secure URL to the purchase response
          purchase['secureFileUrl'] = secureUrl;
        }
      } catch (error) {
        // If there's an error getting the item, just continue without the secure URL
        console.warn(`Could not get secure URL for purchased item ${purchase.shopItemId}: ${error.message}`);
      }
    }

    return ApiResponse.success(pagedPurchases, 'Purchases retrieved successfully');
  }

  @Get('purchases/:id')
  @ApiOperation({
    summary: 'Get purchase by ID (Admin only)',
    description: 'Get details of a specific purchase by ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'Purchase ID',
    type: String
  })
  @ApiOkResponseWithType(ShopItemPurchaseResponseDto, 'Purchase retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Purchase not found')
  async getPurchaseById(@Param('id') id: string): Promise<ApiResponse<ShopItemPurchaseResponseDto>> {
    const purchase = await this.shopService.getPurchaseById(id);

    // Add secure URL for the purchased item if it has a file path
    try {
      // Get the shop item details to access the file path
      const item = await this.shopService.getShopItemById(purchase.shopItemId);
      if (item && item.filePath) {
        // Generate a secure URL for the file
        const secureUrl = await this.shopService.getSecureFileUrl(purchase.shopItemId);
        // Add the secure URL to the purchase response
        purchase['secureFileUrl'] = secureUrl;
      }
    } catch (error) {
      // If there's an error getting the item, just continue without the secure URL
      console.warn(`Could not get secure URL for purchased item ${purchase.shopItemId}: ${error.message}`);
    }

    return ApiResponse.success(purchase, 'Purchase retrieved successfully');
  }

  @Patch('purchases/:id/status')
  @ApiOperation({
    summary: 'Update purchase status (Admin only)',
    description: 'Update the status of a purchase.'
  })
  @ApiParam({
    name: 'id',
    description: 'Purchase ID',
    type: String
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          enum: Object.values(PurchaseStatus),
          description: 'New status for the purchase'
        },
        notes: {
          type: 'string',
          description: 'Optional notes about the status change'
        }
      },
      required: ['status']
    }
  })
  @ApiOkResponseWithType(ShopItemPurchaseResponseDto, 'Purchase status updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Purchase not found')
  async updatePurchaseStatus(@Param('id') id: string, @Body() body: { status: PurchaseStatus; notes?: string }): Promise<ApiResponse<ShopItemPurchaseResponseDto>> {
    const purchase = await this.shopService.updatePurchaseStatus(id, body.status, body.notes);

    // Add secure URL for the purchased item if it has a file path
    try {
      // Get the shop item details to access the file path
      const item = await this.shopService.getShopItemById(purchase.shopItemId);
      if (item && item.filePath) {
        // Generate a secure URL for the file
        const secureUrl = await this.shopService.getSecureFileUrl(purchase.shopItemId);
        // Add the secure URL to the purchase response
        purchase['secureFileUrl'] = secureUrl;
      }
    } catch (error) {
      // If there's an error getting the item, just continue without the secure URL
      console.warn(`Could not get secure URL for purchased item ${purchase.shopItemId}: ${error.message}`);
    }

    return ApiResponse.success(purchase, 'Purchase status updated successfully');
  }

  @Get('items/:id')
  @ApiOperation({
    summary: 'Get shop item by ID (Admin only)',
    description: 'Get details of a specific shop item by ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'Item ID',
    type: String
  })
  @ApiOkResponseWithType(ShopItemResponseDto, 'Shop item retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Shop item not found')
  async getItemById(@Param('id') id: string): Promise<ApiResponse<ShopItemResponseDto>> {
    const item = await this.shopService.getShopItemById(id);

    // The file URL is already set by the service using getFileUrlWithFallback

    return ApiResponse.success(item, 'Shop item retrieved successfully');
  }
}
