import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { QAQuestion } from '../../database/entities/qa-question.entity';
import { QAAssignment } from '../../database/entities/qa-assignment.entity';
import { QASubmission } from '../../database/entities/qa-submission.entity';
import { QASubscription } from '../../database/entities/qa-subscription.entity';
import { QAAssignmentStatus } from '../../database/entities/qa-assignment.entity';
import { QASubmissionStatus } from '../../database/entities/qa-submission.entity';
import { 
  CreateQAQuestionDto, 
  CreateQAAssignmentDto, 
  CreateQASubmissionDto,
  QAQuestionResponseDto,
  QAAssignmentResponseDto,
  QASubmissionResponseDto,
  ReviewSubmissionDto,
  UpdateQAQuestionDto,
  QAQuestionPaginationDto,
  QAAssignmentPaginationDto,
  CreateQAAssignmentItemsDto,
  QAAssignmentItemsResponseDto
} from '../../database/models/qa.dto';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { PaginationService } from '../../common/services/pagination.service';
import { User, UserType } from 'src/database/entities/user.entity';
import { QAAssignmentSets } from 'src/database/entities/qa-assignment-sets.entity';
import { QAAssignmentItems } from 'src/database/entities/qa-assignment-items.entity';

@Injectable()
export class TutorQAService {
  private readonly logger = new Logger(TutorQAService.name);

  constructor(
    private readonly paginationService: PaginationService,
    @InjectRepository(QAQuestion)
    private readonly qaQuestionRepository: Repository<QAQuestion>,
    @InjectRepository(QAAssignment)
    private qaAssignmentRepository: Repository<QAAssignment>,
    @InjectRepository(QAAssignmentItems)
    private qaAssignmentItemsRepository: Repository<QAAssignmentItems>,
    @InjectRepository(QAAssignmentSets)
    private qaAssignmentSetRepository: Repository<QAAssignmentSets>,
    @InjectRepository(QASubmission)
    private qaSubmissionRepository: Repository<QASubmission>,
    @InjectRepository(QASubscription)
    private qaSubscriptionRepository: Repository<QASubscription>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource
  ) {}

  async getStudentDropdownList(searchQuery?: string): Promise<any[]> {
    try {
      // Create query builder
      const queryBuilder = this.userRepository
        .createQueryBuilder('user')
        .select([
          'user.id as id',
          'user.name as name',
          'user.userId as userId',
          'user.email as email'
        ])
        .where('user.isActive = :isActive', { isActive: true })
        .andWhere('user.type = :type', { type: UserType.STUDENT });

      // Add search filter if provided
      if (searchQuery && searchQuery.trim().length > 0) {
        // Use partial matching if search query has at least 3 characters
        if (searchQuery.length >= 3) {
          queryBuilder.andWhere(
            '(LOWER(user.name) LIKE LOWER(:search) OR ' +
            'LOWER(user.userId) LIKE LOWER(:search) OR ' +
            'LOWER(user.email) LIKE LOWER(:search))',
            { search: `%${searchQuery}%` }
          );
        } else {
          // Use exact matching for short queries
          queryBuilder.andWhere(
            '(LOWER(user.name) = LOWER(:search) OR ' +
            'LOWER(user.userId) = LOWER(:search) OR ' +
            'LOWER(user.email) = LOWER(:search))',
            { search: searchQuery }
          );
        }
      }

      // Order by name for better usability
      queryBuilder.orderBy('user.name', 'ASC');

      // Limit results for performance
      queryBuilder.limit(50);

      // Execute raw query to get the exact fields we need
      const students = await queryBuilder.getRawMany();

      return students;
    } catch (error) {
      this.logger.error(`Failed to fetch student dropdown list: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch student dropdown list');
    }
  }

  async getPendingSubmissions(
    paginationDto: PaginationDto
  ): Promise<ApiResponse<PagedListDto<QASubmissionResponseDto>>> {
    const { skip, take } = this.paginationService.getPaginationParameters(paginationDto);

    const [submissions, totalCount] = await this.qaSubmissionRepository.findAndCount({
      where: { status: QASubmissionStatus.SUBMITTED },
      skip,
      take,
      relations: ['assignment', 'assignment.question']
    });

    const pagedList = this.paginationService.createPagedList(
      submissions.map(s => this.transformToSubmissionResponse(s)),
      totalCount,
      paginationDto
    );

    return ApiResponse.success(
      pagedList,
      'Pending submissions retrieved successfully'
    );
  }

  async getReviewedSubmissions(
    tutorId: string, 
    paginationDto: PaginationDto
  ): Promise<ApiResponse<PagedListDto<QASubmissionResponseDto>>> {
    const { skip, take } = this.paginationService.getPaginationParameters(paginationDto);
    
    const [submissions, totalCount] = await this.qaSubmissionRepository.findAndCount({
      where: { 
        reviewedBy: tutorId,
        status: QASubmissionStatus.REVIEWED 
      },
      skip,
      take,
      relations: ['assignment', 'assignment.question']
    });

    const pagedList = this.paginationService.createPagedList(
      submissions.map(s => this.transformToSubmissionResponse(s)),
      totalCount,
      paginationDto
    );

    return ApiResponse.success(
      pagedList,
      'Reviewed submissions retrieved successfully'
    );
  }

  private transformToSubmissionResponse(submission: QASubmission): QASubmissionResponseDto {
    return {
      id: submission.id,
      score: submission.points,
      answer: submission.answer,
      status: submission.status,
      submissionDate: submission.submissionDate,
      feedback: submission.feedback,
      corrections: submission.corrections,
      createdAt: submission.createdAt,
      updatedAt: submission.updatedAt
    };
  }

  async createAssignments(dto: CreateQAAssignmentItemsDto): Promise<QAAssignmentItemsResponseDto[]> {
    // 1. Get the latest set sequence for the student
    const lastSet = await this.qaAssignmentSetRepository.findOne({
      where: { },
      order: { setSequence: 'DESC' }
    });
    const nextSequence = lastSet ? lastSet.setSequence + 1 : 1;

    // 2. Create the assignment set
    const assignmentSet = this.qaAssignmentSetRepository.create({
      setSequence: nextSequence,
      instructions: dto.instructions,
    });
    await this.qaAssignmentSetRepository.save(assignmentSet);

    // 3. Create assignments for each question
    const assignments = this.qaAssignmentItemsRepository.create(
      dto.questionIds.map(qid => ({
        questionId: qid,
        studentId: dto.studentId,
        setSequence: nextSequence,
        assignedDate: new Date(),
        status: QAAssignmentStatus.ASSIGNED
      }))
    );

    const savedAssignments = await this.qaAssignmentItemsRepository.save(assignments);

    // 4. Return response DTOs
    // return savedAssignments.map(a => ({
    //   id: a.id,
    //   questionId: a.questionId,
    //   studentId: a.studentId,
    //   score: a.score, // map score to points if needed
    //   deadline: a.deadline,
    //   setSequence: a.setSequence,
    //   assignmentSet: {
    //     setSequence: a.assignmentSet.setSequence,
    //     instructions: a.assignmentSet.instructions,
    //   },
    //   status: a.status,
    //   assignedDate: a.assignedDate,
    //   createdAt: a.createdAt,
    //   updatedAt: a.updatedAt,
    // }));
    return savedAssignments.map(a => ({
      id: a.id,
      questionIds: [a.questionId],
      studentId: a.studentId,
      points: a.score,
      deadline: a.deadline,
      status: a.status,
      assignedDate: a.assignedDate,
      createdAt: a.createdAt,
      updatedAt: a.updatedAt,
    }));
  }
}