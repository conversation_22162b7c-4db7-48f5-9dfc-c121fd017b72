import { Injectable, Logger, BadRequestException, NotFoundException, Inject, forwardRef, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, <PERSON><PERSON>han, <PERSON>ThanOrEqual, MoreThanOrEqual, Repository } from 'typeorm';
import { AwardSchedule, ScheduleStatus } from '../../database/entities/award-schedule.entity';
import { DiaryAwardService } from '../diary/diary-award.service';
import { AwardModule, AwardFrequency } from '../../database/entities/award.entity';
import { CreateAwardScheduleDto, AwardScheduleResponseDto } from '../../database/models/award-schedule.dto';
import {
  getCurrentUTCDate,
  addDaysUTC,
  addMonthsUTC,
  addYearsUTC,
  getStartOfDayUTC,
  getEndOfDayUTC,
  getStartOfMonthUTC,
  getEndOfMonthUTC,
} from '../../common/utils/date-utils';

@Injectable()
export class AwardScheduleService implements OnModuleInit {
  private readonly logger = new Logger(AwardScheduleService.name);
  private initialized = false;

  constructor(
    @InjectRepository(AwardSchedule)
    private readonly awardScheduleRepository: Repository<AwardSchedule>,
    @Inject(forwardRef(() => DiaryAwardService))
    private readonly diaryAwardService: DiaryAwardService,
  ) {}

  async onModuleInit() {
    await this.ensureInitialized();
  }

  private async ensureInitialized() {
    if (!this.initialized) {
      try {
        // Verify repository connection
        await this.awardScheduleRepository.count();
        this.initialized = true;
        this.logger.log('AwardScheduleService initialized successfully');
      } catch (error) {
        this.logger.error(`Failed to initialize AwardScheduleService: ${error.message}`, error.stack);
        throw new Error('AwardScheduleService failed to initialize');
      }
    }
  }
  /**
   * Create award schedules for all modules up to the end of the current year
   * This should be run at the start of each day
   */
  async createDailySchedules(): Promise<void> {
    await this.ensureInitialized();
    const today = getCurrentUTCDate();
    const currentYear = today.getUTCFullYear();
    const yearEnd = new Date(Date.UTC(currentYear, 11, 31)); // December 31st

    try {
      this.logger.log('Creating new award schedules');

      const createScheduleDto = {
        module: AwardModule.DIARY,
        frequency: AwardFrequency.MONTHLY,
        isActive: true
      };

      await this.createManualSchedule(createScheduleDto);
      this.logger.log('Successfully created daily schedules');
    } catch (error) {
      if (!error.message?.includes('overlap')) {
        this.logger.error(`Error creating award schedules: ${error.message}`, error.stack);
        throw error;
      } else {
        this.logger.debug('Skipping schedule creation due to overlap');
      }
    }
  }

  /**
   * Create a new award schedule
   */
  private async createSchedule(data: {
    module: AwardModule;
    scheduleDate: Date;
    periodStartDate: Date;
    periodEndDate: Date;
  }): Promise<void> {
    // Check if schedule already exists
    const existingSchedule = await this.awardScheduleRepository.findOne({
      where: {
        module: data.module,
        scheduleDate: data.scheduleDate,
      },
    });

    if (!existingSchedule) {
      const schedule = this.awardScheduleRepository.create({
        ...data,
        status: ScheduleStatus.PENDING,
        retryCount: 0
      });
      await this.awardScheduleRepository.save(schedule);
      this.logger.log(`Created award schedule for ${data.module} on ${data.scheduleDate}`);
    }
  }

  /**
   * Process pending award schedules
   * This includes both today's schedules and any missed schedules from previous days
   */
  async processPendingSchedules(): Promise<void> {
    await this.ensureInitialized();
    const today = getCurrentUTCDate();

    try {
      // Get all pending or failed schedules up to today
      const pendingSchedules = await this.awardScheduleRepository.find({
        where: [
          { status: ScheduleStatus.PENDING, scheduleDate: LessThan(today), isActive: true },
          { status: ScheduleStatus.FAILED, scheduleDate: LessThan(today), retryCount: LessThan(3), isActive: true },
        ],
        order: {
          scheduleDate: 'ASC',
        },
      });

      if (pendingSchedules.length === 0) {
        this.logger.log('No pending award schedules to process');
        return;
      }

      this.logger.log(`Processing ${pendingSchedules.length} pending award schedules`);

      for (const schedule of pendingSchedules) {
        try {
          schedule.processingStartedAt = getCurrentUTCDate();
          await this.awardScheduleRepository.save(schedule);

          switch (schedule.module) {
            case AwardModule.DIARY:
              await this.diaryAwardService.generateAwardsForRange(schedule.periodStartDate, schedule.periodEndDate);
              break;
            // Add other modules here when needed
            default:
              this.logger.warn(`Unsupported module ${schedule.module} for award schedule ${schedule.id}`);
              break;
          }

          schedule.status = ScheduleStatus.COMPLETED;
          schedule.processingCompletedAt = getCurrentUTCDate();
          await this.awardScheduleRepository.save(schedule);

          this.logger.log(`Successfully processed award schedule ${schedule.id}`);
        } catch (error) {
          schedule.status = ScheduleStatus.FAILED;
          schedule.errorMessage = error.message;
          schedule.retryCount += 1;
          schedule.lastRetryDate = getCurrentUTCDate();
          await this.awardScheduleRepository.save(schedule);

          this.logger.error(
            `Error processing award schedule ${schedule.id}: ${error.message}`,
            error.stack
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error processing pending schedules: ${error.message}`, error.stack);
      throw error;
    }
  }
  /**
   * Create a manual award schedule
   * @param createScheduleDto The schedule details
   * @returns Created award schedule
   */
  async createManualSchedule(createScheduleDto: CreateAwardScheduleDto): Promise<AwardScheduleResponseDto> {
    await this.ensureInitialized();
    try {
      const today = getCurrentUTCDate();
      let scheduleDate: Date;
      let periodStartDate: Date;
      let periodEndDate: Date;

      // Calculate dates based on frequency
      switch (createScheduleDto.frequency) {

        case AwardFrequency.WEEKLY:
          scheduleDate = addDaysUTC(today, 7 - today.getUTCDay());
          periodStartDate = addDaysUTC(scheduleDate, -6);
          periodEndDate = getEndOfDayUTC(scheduleDate);
          break;

        case AwardFrequency.MONTHLY:
          scheduleDate = getStartOfDayUTC(addMonthsUTC(getStartOfMonthUTC(today), 1));
          periodStartDate = getStartOfMonthUTC(today);
          periodEndDate = getEndOfMonthUTC(today);
          break;

        case AwardFrequency.QUARTERLY:
          const monthsToNextQuarter = 3 - (today.getUTCMonth() % 3);
          scheduleDate = getStartOfDayUTC(addMonthsUTC(getStartOfMonthUTC(today), monthsToNextQuarter));
          const quarterStartMonth = Math.floor(today.getUTCMonth() / 3) * 3;
          periodStartDate = new Date(Date.UTC(today.getUTCFullYear(), quarterStartMonth, 1));
          periodEndDate = getEndOfMonthUTC(addMonthsUTC(periodStartDate, 2));
          break;

        case AwardFrequency.YEARLY:
          scheduleDate = new Date(Date.UTC(today.getUTCFullYear() + 1, 0, 1));
          periodStartDate = new Date(Date.UTC(today.getUTCFullYear(), 0, 1));
          periodEndDate = new Date(Date.UTC(today.getUTCFullYear(), 11, 31, 23, 59, 59, 999));
          break;

        default:
          throw new BadRequestException('Invalid award frequency');
      }

      // First validate initial schedule
      await this.validateScheduleOverlap(
        createScheduleDto.module,
        scheduleDate,
        periodStartDate,
        periodEndDate
      );

      // Create all remaining schedules
      const schedules = await this.createMultipleSchedules(
        createScheduleDto.module,
        createScheduleDto.frequency,
        periodStartDate,
        periodEndDate,
        createScheduleDto.isActive ?? true
      );

      this.logger.log(
        `Created ${schedules.length} ${createScheduleDto.frequency} award schedules for ${createScheduleDto.module}` +
        ` (${periodStartDate.toISOString()} to ${periodEndDate.toISOString()})`
      );

      // Return the first schedule as response
      return this.mapScheduleToDto(schedules[0]);
    } catch (error) {
      this.logger.error(`Error creating manual award schedule: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get an award schedule by ID
   * @param id The schedule ID
   * @returns The award schedule
   */
  async getScheduleById(id: string): Promise<AwardScheduleResponseDto> {
    await this.ensureInitialized();
    const schedule = await this.awardScheduleRepository.findOne({
      where: { id }
    });

    if (!schedule) {
      throw new NotFoundException(`Award schedule with ID ${id} not found`);
    }

    return this.mapScheduleToDto(schedule);
  }

  /**
   * Get all award schedules with optional filters
   * @returns List of award schedules
   */
  async getAllSchedules(
    module?: AwardModule,
    status?: ScheduleStatus,
    startDate?: Date,
    endDate?: Date
  ): Promise<AwardScheduleResponseDto[]> {
    await this.ensureInitialized();
    try {
      const where: any = {};

      if (module) {
        where.module = module;
      }

      if (status) {
        where.status = status;
      }

      if (startDate && endDate) {
        where.scheduleDate = Between(startDate, endDate);
      }

      const schedules = await this.awardScheduleRepository.find({
        where,
        order: { scheduleDate: 'DESC' }
      });

      return schedules.map(schedule => this.mapScheduleToDto(schedule));
    } catch (error) {
      this.logger.error(`Error getting award schedules: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update the active status of an award schedule
   * @param id The schedule ID
   * @param isActive Whether the schedule should be active
   * @returns The updated award schedule
   */
  async updateActiveStatus(id: string, isActive: boolean): Promise<AwardScheduleResponseDto> {
    await this.ensureInitialized();
    const schedule = await this.awardScheduleRepository.findOne({
      where: { id }
    });

    if (!schedule) {
      throw new NotFoundException(`Award schedule with ID ${id} not found`);
    }

    schedule.isActive = isActive;
    const savedSchedule = await this.awardScheduleRepository.save(schedule);

    return this.mapScheduleToDto(savedSchedule);
  }

  /**
   * Check for overlapping schedules
   * @param module Award module
   * @param scheduleDate Schedule date
   * @param periodStartDate Period start date
   * @param periodEndDate Period end date
   * @throws BadRequestException if overlapping schedule exists
   */  private async validateScheduleOverlap(
    module: AwardModule,
    scheduleDate: Date,
    periodStartDate: Date,
    periodEndDate: Date
  ): Promise<void> {
    // Check for existing schedules in the same period
    const existingSchedule = await this.awardScheduleRepository.findOne({
      where: [
        {
          module,
          scheduleDate: Between(periodStartDate, periodEndDate),
          isActive: true
        },
        {
          module,
          periodStartDate: LessThanOrEqual(periodEndDate),
          periodEndDate: MoreThanOrEqual(periodStartDate),
          isActive: true
        }
      ]
    });

    if (existingSchedule) {
      throw new BadRequestException(
        `An active schedule already exists for ${module} during this period ` +
        `(${existingSchedule.periodStartDate.toISOString()} to ${existingSchedule.periodEndDate.toISOString()})`
      );
    }
  }

  /**
   * Calculate remaining schedule dates for the year based on frequency
   * @param frequency Award frequency
   * @param startDate Start date to calculate from
   * @returns Array of schedule dates
   */
  private calculateRemainingScheduleDates(frequency: AwardFrequency, startDate: Date): Date[] {
    const dates: Date[] = [];
    const currentYear = startDate.getUTCFullYear();
    const yearEnd = new Date(Date.UTC(currentYear, 11, 31)); // December 31st

    switch (frequency) {

      case AwardFrequency.WEEKLY:
        // Calculate remaining Sundays in the year
        let nextSunday = addDaysUTC(startDate, 7 - startDate.getUTCDay());
        while (nextSunday <= yearEnd) {
          dates.push(nextSunday);
          nextSunday = addDaysUTC(nextSunday, 7);
        }
        break;

      case AwardFrequency.MONTHLY:
        // Calculate remaining first days of months in the year
        let nextMonth = getStartOfDayUTC(addMonthsUTC(getStartOfMonthUTC(startDate), 1));
        while (nextMonth <= yearEnd) {
          dates.push(nextMonth);
          nextMonth = addMonthsUTC(nextMonth, 1);
        }
        break;

      case AwardFrequency.QUARTERLY:
        // Calculate remaining quarter starts in the year
        const currentQuarter = Math.floor(startDate.getUTCMonth() / 3);
        const remainingQuarters = 4 - currentQuarter;
        for (let i = 1; i <= remainingQuarters; i++) {
          const monthsToAdd = i * 3;
          const quarterStart = new Date(Date.UTC(
            startDate.getUTCFullYear(),
            currentQuarter * 3 + monthsToAdd,
            1
          ));
          if (quarterStart <= yearEnd) {
            dates.push(quarterStart);
          }
        }
        break;

      case AwardFrequency.YEARLY:
        // Only add next year's January 1st
        dates.push(new Date(Date.UTC(currentYear + 1, 0, 1)));
        break;
    }

    return dates;
  }

  /**
   * Create multiple schedules based on frequency for the remaining period
   * @param createScheduleDto Schedule creation data
   * @returns Array of created schedules
   */
  private async createMultipleSchedules(
    module: AwardModule,
    frequency: AwardFrequency,
    periodStartDate: Date,
    periodEndDate: Date,
    isActive: boolean = true
  ): Promise<AwardSchedule[]> {
    const schedules: AwardSchedule[] = [];
    const today = getCurrentUTCDate();
    const futureDates = this.calculateRemainingScheduleDates(frequency, today);

    for (let scheduleDate of futureDates) {
      const schedule = this.awardScheduleRepository.create({
        module,
        scheduleDate,
        periodStartDate,
        periodEndDate,
        status: ScheduleStatus.PENDING,
        retryCount: 0,
        isActive
      });
      schedules.push(schedule);
    }

    return this.awardScheduleRepository.save(schedules);
  }

  /**
   * Map award schedule entity to DTO
   */
  private mapScheduleToDto(schedule: AwardSchedule): AwardScheduleResponseDto {
    return {
      id: schedule.id,
      module: schedule.module,
      scheduleDate: schedule.scheduleDate,
      periodStartDate: schedule.periodStartDate,
      periodEndDate: schedule.periodEndDate,
      status: schedule.status,
      errorMessage: schedule.errorMessage,
      retryCount: schedule.retryCount,
      lastRetryDate: schedule.lastRetryDate,
      createdAt: schedule.createdAt,
      updatedAt: schedule.updatedAt
    };
  }
}

