import { Controller, Get, Post, Body, Param, UseGuards, Req, NotFoundException, BadRequestException, Patch, UseInterceptors, UploadedFile, Query, Res } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiTags, ApiConsumes, ApiResponse as SwaggerApiResponse } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import * as fs from 'fs';
import * as path from 'path';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { UsersService } from './users.service';
import { ConfigService } from '@nestjs/config';
import { ProfilePictureService } from '../../common/services/profile-picture.service';
import { UserResponseDto, UpdateProfileDto, CalculateAgeDto, CalculateAgeResponseDto, CreateAdminUserByAdminDto, CreateTutorUserByAdminDto, CreateStudentUserByAdminDto, UserCreationResponseDto } from '../../database/models/users.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { ApiResponse } from 'src/common/dto/api-response.dto';
import { PagedListDto } from 'src/common/models/paged-list.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from 'src/common/decorators/api-response.decorator';
import { UserFilterDto } from './dto/user-filter.dto';
@ApiTags('Users')
@ApiBearerAuth('JWT-auth')
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
    private readonly profilePictureService: ProfilePictureService
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all users (Admin only)',
    description: 'Retrieves all users with filtering and sorting options. Filter by userId, email, phone, and gender. Sort by userId, email, gender, phone, name, or createdAt.'
  })
  @ApiOkResponseWithPagedListType(UserResponseDto, 'Returns all users')
  @ApiErrorResponse(401, 'Unauthorized', 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden', 'Forbidden - Admin access required')
  @UseGuards(AdminGuard)
  async findAll(@Query() filterDto: UserFilterDto): Promise<ApiResponse<PagedListDto<UserResponseDto>>> {
    const pagedList = await this.usersService.findAllPaginated(filterDto);
    return ApiResponse.success(pagedList, 'Users retrieved successfully');
  }

  @Get('admin')
  @ApiOperation({
    summary: 'Get all admin users (Admin only)',
    description: 'Retrieves all admin users with filtering and sorting options. Filter by userId, email, phone, and gender. Sort by userId, email, gender, phone, name, or createdAt.'
  })
  @ApiOkResponseWithPagedListType(UserResponseDto, 'Returns all admin users')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @UseGuards(AdminGuard)
  async getAdminUsers(@Query() filterDto: UserFilterDto): Promise<ApiResponse<PagedListDto<UserResponseDto>>> {
    const pagedList = await this.usersService.findAllAdminUsers(filterDto);
    return ApiResponse.success(pagedList, 'Admin users retrieved successfully');
  }

  @Get('tutor')
  @ApiOperation({
    summary: 'Get all tutor users (Admin only)',
    description: 'Retrieves all tutor users with filtering and sorting options. Filter by userId, email, phone, and gender. Sort by userId, email, gender, phone, name, or createdAt.'
  })
  @ApiOkResponseWithPagedListType(UserResponseDto, 'Returns all tutor users')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @UseGuards(AdminGuard)
  async getTutorUsers(@Query() filterDto: UserFilterDto): Promise<ApiResponse<PagedListDto<UserResponseDto>>> {
    const pagedList = await this.usersService.findAllTutorUsers(filterDto);
    return ApiResponse.success(pagedList, 'Tutor users retrieved successfully');
  }

  @Get('student')
  @ApiOperation({
    summary: 'Get all student users (Admin only)',
    description: 'Retrieves all student users with filtering and sorting options. Filter by userId, email, phone, and gender. Sort by userId, email, gender, phone, name, or createdAt.'
  })
  @ApiOkResponseWithPagedListType(UserResponseDto, 'Returns all student users')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @UseGuards(AdminGuard)
  async getStudentUsers(@Query() filterDto: UserFilterDto): Promise<ApiResponse<PagedListDto<UserResponseDto>>> {
    const pagedList = await this.usersService.findAllStudentUsers(filterDto);
    return ApiResponse.success(pagedList, 'Student users retrieved successfully');
  }

  @Get('profile')
  @ApiOperation({
    summary: 'Get current user profile',
    description: 'Retrieves the profile information of the currently authenticated user. For tutor users, this includes their education information.'
  })
  @ApiOkResponseWithType(UserResponseDto, 'Returns the user profile')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(404, 'User not found')
  async getProfile(@Req() req: any): Promise<ApiResponse<UserResponseDto>> {
    const userId = req.user.sub;
    // Explicitly set loadRelations to true to ensure all relations, including education information, are loaded
    const user = await this.usersService.findById(userId, true);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    const userDto = user.toDto(req.user.selectedRole);

    // Add profile picture URL if the user has a profile picture
    // Otherwise, set profilePictureUrl to null
    const hasProfilePicture = await this.profilePictureService.hasProfilePicture(userId);
    if (hasProfilePicture) {
      // Use the profile picture registry service to get the URL
      userDto.profilePictureUrl =await this.profilePictureService.getProfilePictureUrl(userId);
    } else {
      userDto.profilePictureUrl = null;
    }

    return ApiResponse.success(userDto, 'User profile retrieved successfully');
  }

  @Patch('profile')
  @ApiOperation({
    summary: 'Update current user profile',
    description: 'Updates the profile information of the currently authenticated user. Note: The name field is for display purposes only and does not affect login. Users must continue to use their userId for login.'
  })
  @ApiBody({
    type: UpdateProfileDto,
    description: 'User profile data to update',
    examples: {
      example1: {
        summary: 'Update Profile Example',
        description: 'Example of updating user profile with all fields',
        value: {
          name: 'John Doe',
          phoneNumber: '+1234567890',
          address: '123 Main St',
          city: 'New York',
          state: 'NY',
          country: 'USA',
          postalCode: '10001',
          bio: 'I am a software developer with 5 years of experience.',
          dateOfBirth: '1990-01-01',  // YYYY-MM-DD format is required
          gender: 'male',
          socialLinks: {
            linkedin: 'https://linkedin.com/in/johndoe',
            twitter: 'https://twitter.com/johndoe'
          }
        }
      }
    }
  })
  @ApiOkResponseWithType(UserResponseDto, 'Returns the updated user profile')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(404, 'User not found')
  async updateProfile(@Req() req: any, @Body() updateProfileDto: UpdateProfileDto): Promise<ApiResponse<UserResponseDto>> {
    const userId = req.user.sub;
    const updatedUser = await this.usersService.updateProfile(userId, updateProfileDto);
    return ApiResponse.success(
      updatedUser,
      'Profile updated successfully. Remember to use your userId for login, not your display name.'
    );
  }

  @Post('calculate-age')
  @ApiOperation({
    summary: 'Calculate age from date of birth',
    description: 'Calculates age based on the provided date of birth in ISO 8601 format.'
  })
  @ApiBody({ type: CalculateAgeDto })
  @ApiOkResponseWithType(CalculateAgeResponseDto, 'Returns the calculated age')
  @ApiErrorResponse(400, 'Invalid date format')
  async calculateAge(@Body() calculateAgeDto: CalculateAgeDto): Promise<ApiResponse<CalculateAgeResponseDto>> {
    const result = this.usersService.calculateAge(calculateAgeDto.dateOfBirth);
    return ApiResponse.success(result, 'Age calculated successfully');
  }

  @Post('profile/picture')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload profile picture',
    description: 'Uploads a profile picture for the currently authenticated user.'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Profile picture file (JPEG, PNG, or GIF)'
        }
      }
    }
  })
  @ApiOkResponseWithType(UserResponseDto, 'Returns the updated user profile with the new profile picture URL')
  @ApiErrorResponse(400, 'Invalid file format or size')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(404, 'User not found')
  async uploadProfilePicture(
    @Req() req: any,
    @UploadedFile() file: any
  ): Promise<ApiResponse<UserResponseDto>> {
    const userId = req.user.sub;
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }
    const updatedUser = await this.usersService.updateProfilePicture(userId, file);
    return ApiResponse.success(updatedUser, 'Profile picture updated successfully');
  }

  @Post('admin/create')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Create a new admin user with auto-generated password (Admin only)',
    description: 'Creates a new admin user with auto-generated password and sends credentials via email. Only accessible by admins.'
  })
  @ApiBody({
    type: CreateAdminUserByAdminDto,
    description: 'Admin user creation data (simplified)',
    examples: {
      admin: {
        summary: 'Create Admin User',
        description: 'Create a new admin user with auto-generated password',
        value: {
          userId: 'admin123',
          email: '<EMAIL>'
        }
      }
    }
  })
  @ApiOkResponseWithType(UserCreationResponseDto, 'Admin user created successfully and email sent')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(409, 'User already exists', 'User with this email or userId already exists')
  async createAdmin(@Req() req: any, @Body() createAdminUserDto: CreateAdminUserByAdminDto): Promise<ApiResponse<UserCreationResponseDto>> {
    const adminId = req.user.sub; // Get admin ID from JWT token
    const user = await this.usersService.createAdminUserByAdmin(createAdminUserDto, adminId);
    return ApiResponse.success(user, 'Admin user created successfully and email sent', 201);
  }

  @Post('admin/create-tutor')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Create a new tutor user with auto-generated password (Admin only)',
    description: 'Creates a new tutor user with auto-generated password, auto-approval, and sends credentials via email. Only accessible by admins.'
  })
  @ApiBody({
    type: CreateTutorUserByAdminDto,
    description: 'Tutor user creation data (simplified)',
    examples: {
      tutor: {
        summary: 'Create Tutor User',
        description: 'Create a new tutor user with auto-generated password',
        value: {
          userId: 'tutor123',
          email: '<EMAIL>'
        }
      }
    }
  })
  @ApiOkResponseWithType(UserCreationResponseDto, 'Tutor user created successfully and email sent')
  @ApiErrorResponse(400, 'Invalid input', 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized', 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden', 'Forbidden - Admin access required')
  @ApiErrorResponse(409, 'User already exists', 'User with this email or userId already exists')
  async createTutor(@Req() req: any, @Body() createTutorUserDto: CreateTutorUserByAdminDto): Promise<ApiResponse<UserCreationResponseDto>> {
    const adminId = req.user.sub; // Get admin ID from JWT token
    const user = await this.usersService.createTutorUserByAdmin(createTutorUserDto, adminId);
    return ApiResponse.success(user, 'Tutor user created successfully and email sent', 201);
  }

  @Post('admin/create-student')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Create a new student user with auto-generated password (Admin only)',
    description: 'Creates a new student user with auto-generated password, auto-verification, and sends credentials via email. Only accessible by admins.'
  })
  @ApiBody({
    type: CreateStudentUserByAdminDto,
    description: 'Student user creation data (simplified)',
    examples: {
      student: {
        summary: 'Create Student User',
        description: 'Create a new student user with auto-generated password',
        value: {
          userId: 'student123',
          email: '<EMAIL>'
        }
      }
    }
  })
  @ApiOkResponseWithType(UserCreationResponseDto, 'Student user created successfully and email sent')
  @ApiErrorResponse(400, 'Invalid input', 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized', 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden', 'Forbidden - Admin access required')
  @ApiErrorResponse(409, 'User already exists', 'User with this email or userId already exists')
  async createStudent(@Req() req: any, @Body() createStudentUserDto: CreateStudentUserByAdminDto): Promise<ApiResponse<UserCreationResponseDto>> {
    const adminId = req.user.sub; // Get admin ID from JWT token
    const user = await this.usersService.createStudentUserByAdmin(createStudentUserDto, adminId);
    return ApiResponse.success(user, 'Student user created successfully and email sent', 201);
  }

  @Post('admin/assign-role/:userId/:roleName')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Assign a role to a user (Admin only)',
    description: 'Assigns a specific role to a user. Only accessible by admins.'
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user to assign the role to',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiParam({
    name: 'roleName',
    description: 'The name of the role to assign',
    example: 'admin',
    schema: { enum: ['admin', 'tutor', 'student'] }
  })
  @ApiOkResponseWithType(UserResponseDto, 'Role assigned successfully')
  @ApiErrorResponse(400, 'Invalid input or user already has role', 'User already has this role')
  @ApiErrorResponse(401, 'Unauthorized', 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden', 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'User or role not found', 'User or role not found')
  async assignRole(
    @Param('userId') userId: string,
    @Param('roleName') roleName: string
  ): Promise<ApiResponse<UserResponseDto>> {
    const user = await this.usersService.assignRole(userId, roleName);
    return ApiResponse.success(user, 'Role assigned successfully');
  }

  @Post('admin/remove-role/:userId/:roleName')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Remove a role from a user (Admin only)',
    description: 'Removes a specific role from a user. Only accessible by admins.'
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user to remove the role from',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiParam({
    name: 'roleName',
    description: 'The name of the role to remove',
    example: 'tutor',
    schema: { enum: ['admin', 'tutor', 'student'] }
  })
  @ApiOkResponseWithType(UserResponseDto, 'Role removed successfully')
  @ApiErrorResponse(400, 'Invalid input or user does not have role', 'User does not have this role')
  @ApiErrorResponse(401, 'Unauthorized', 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden', 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'User or role not found', 'User or role not found')
  async removeRole(
    @Param('userId') userId: string,
    @Param('roleName') roleName: string
  ): Promise<ApiResponse<UserResponseDto>> {
    const user = await this.usersService.removeRole(userId, roleName);
    return ApiResponse.success(user, 'Role removed successfully');
  }

  @Get('profile-picture/:userId')
  @ApiOperation({
    summary: 'Get user profile picture',
    description: 'Retrieves the profile picture of a user by their ID. This endpoint is accessible to all authenticated users.'
  })
  @SwaggerApiResponse({
    status: 200,
    description: 'Profile picture retrieved successfully',
    content: {
      'image/jpeg': {},
      'image/png': {},
      'image/gif': {}
    }
  })
  @ApiErrorResponse(404, 'Profile picture not found')
  async getProfilePicture(
    @Param('userId') userId: string,
    @Res() res: Response
  ): Promise<void> {
    try {
      // Get the profile picture from the database
      const profilePicture = await this.profilePictureService.getProfilePicture(userId);

      // If no profile picture is found, return 404
      if (!profilePicture) {
        console.log(`No profile picture found for user ${userId}`);
        throw new NotFoundException('Profile picture not found');
      }

      // Construct the full path to the profile picture
      const uploadDir = this.configService.get<string>('UPLOAD_DIR') || 'uploads';
      const profilePicturePath = path.resolve(process.cwd(), uploadDir, profilePicture.filePath);

      // Log the path for debugging
      console.log(`Looking for profile picture at: ${profilePicturePath}`);

      // Check if the file exists
      if (!fs.existsSync(profilePicturePath)) {
        console.log('Profile picture file not found on disk');
        throw new NotFoundException('Profile picture file not found on disk');
      }

      // Set the content type based on the mime type stored in the database
      res.setHeader('Content-Type', profilePicture.mimeType);

      // Send the file
      res.sendFile(profilePicturePath);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error serving profile picture:', error);
      throw new NotFoundException(`Profile picture not found: ${error.message}`);
    }
  }

  @Get('profile/picture')
  @ApiOperation({
    summary: 'Get current user profile picture',
    description: 'Retrieves the profile picture of the currently authenticated user.'
  })
  @SwaggerApiResponse({
    status: 200,
    description: 'Profile picture retrieved successfully',
    content: {
      'image/jpeg': {},
      'image/png': {},
      'image/gif': {}
    }
  })
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(404, 'Profile picture not found')
  async getCurrentUserProfilePicture(
    @Req() req: any,
    @Res() res: Response
  ): Promise<void> {
    try {
      const userId = req.user.sub;

      // Get the profile picture from the database
      const profilePicture = await this.profilePictureService.getProfilePicture(userId);

      // If no profile picture is found, return 404
      if (!profilePicture) {
        console.log(`No profile picture found for current user ${userId}`);
        throw new NotFoundException('Profile picture not found');
      }

      // Construct the full path to the profile picture
      const uploadDir = this.configService.get<string>('UPLOAD_DIR') || 'uploads';
      const profilePicturePath = path.resolve(process.cwd(), uploadDir, profilePicture.filePath);

      // Log the path for debugging
      console.log(`Looking for profile picture at: ${profilePicturePath}`);

      // Check if the file exists
      if (!fs.existsSync(profilePicturePath)) {
        console.log('Profile picture file not found on disk');
        throw new NotFoundException('Profile picture file not found on disk');
      }

      // Set the content type based on the mime type stored in the database
      res.setHeader('Content-Type', profilePicture.mimeType);

      // Send the file
      res.sendFile(profilePicturePath);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error('Error serving profile picture:', error);
      throw new NotFoundException(`Profile picture not found: ${error.message}`);
    }
  }
}
