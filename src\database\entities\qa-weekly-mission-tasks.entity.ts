import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, OneToMany } from "typeorm";
import { AuditableBaseEntity } from "./base-entity";
import { QAMissionGoal } from "./qa-mission-goal.entity";
import { IsUUID } from "class-validator";
import { QATaskSubmissions } from "./qa-task-submissions.entity";
import { QAMission } from "./qa-mission.entity";

@Entity()
export class QAWeeklyMissionTasks extends AuditableBaseEntity {
      @Column({
        name: "title",
        type: "varchar",
        length: 50,
      })
      title: string;
    
      @Column({
        name: "description",
        type: "text"
      })
      description: string;
    
      @Column({
        name: "is_active",
        default: true
      })
      isActive?: boolean;
    
      @Column({
        name: "sequence",
        type: "int",
        default: 1,
        nullable: true
      })
      sequence: number;
    
      @Column({
        name: "word_limit_minumum",
        type: "int",
      })
      wordLimitMinimum: number;
    
      @Column({
        name: "word_limit_maximum",
        type: "int",
        nullable: true,
      })
      wordLimitMaximum: number;
    
      @Column({
        name: "total_score",
        type: "int",
        nullable: true,
      })
      totalScore: number;
    
      // @Column({
      //   name: "deadline",
      //   type: "date",
      //   nullable: true
      // })
      // deadline: Date;

      @Column({
        name: "deadline",
        type: "int",
        nullable: true
      })
      deadline: number;
    
      @Column({
        name: "instructions",
        type: "text",
      })
      instructions: string;
    
      // @ManyToOne(() => QAMissionGoal, mission => mission.tasks, { nullable: true })
      // @JoinColumn({ name: "mission" })
      // mission: QAMissionGoal;

      @ManyToOne(() => QAMission, mission => mission.monthlyTasks, { nullable: false })
      @JoinColumn({ name: "mission_id" })
      mission: QAMission;
    
      @Column({
        name: "mission_id",
        type: "uuid",
        nullable: false
      })
      @IsUUID()
      missionId: string;
    
      @OneToMany(() => QATaskSubmissions, submission => submission.task)
      submissions: QATaskSubmissions[];
}