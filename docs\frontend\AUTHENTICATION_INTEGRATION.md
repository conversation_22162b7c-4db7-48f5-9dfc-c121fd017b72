# Authentication Integration Guide

## Overview

This document provides a comprehensive guide for integrating the HEC authentication system into the frontend application. It covers user registration, login, token management, and session handling.

## Authentication Flow

The authentication flow in the HEC system follows these steps:

1. User enters credentials (userId/password)
2. Frontend sends credentials to the authentication API
3. Backend validates credentials and returns JWT access token and refresh token
4. Frontend stores tokens in secure storage
5. Access token is included in subsequent API requests as a Bearer token
6. When access token expires, refresh token is used to obtain a new access token
7. "Remember Me" functionality extends token lifetimes (30 days vs 1 day)

## API Endpoints

### Login

```
POST /api/auth/login
```

**Request Body:**
```json
{
  "userId": "user123",
  "password": "password123",
  "rememberMe": true,
  "returnUrl": "/dashboard"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "a1b2c3d4e5f6g7h8i9j0...",
    "refresh_token_expires": "2023-08-24T12:00:00.000Z",
    "user": {
      "id": "123",
      "userId": "user123",
      "email": "<EMAIL>",
      "type": "student",
      "name": "John Doe",
      "isActive": true,
      "isConfirmed": true,
      "roles": ["student"],
      "selectedRole": "student",
      "defaultSkinId": "skin-123",
      "activePlan": "STANDARD",
      "activePlanDetails": {
        "id": "plan-456",
        "name": "STANDARD",
        "startDate": "2023-07-24T12:00:00.000Z",
        "endDate": "2023-08-24T12:00:00.000Z",
        "isActive": true
      },
      "profilePictureUrl": "https://example.com/profile-pictures/user123.jpg"
    },
    "returnUrl": "/dashboard"
  },
  "errors": null,
  "statusCode": 200
}
```

### Register

```
POST /api/auth/register
```

**Request Body:**
```json
{
  "userId": "newuser123",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "name": "New User",
  "type": "student",
  "phoneNumber": "+**********",
  "gender": "male",
  "agreedToTerms": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Registration successful. Please check your email to verify your account.",
  "data": {
    "id": "123",
    "userId": "newuser123",
    "email": "<EMAIL>",
    "name": "New User",
    "type": "student",
    "isConfirmed": false,
    "isActive": false
  },
  "errors": null,
  "statusCode": 201
}
```

### Forgot Password

```
POST /api/auth/forgot-password
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### Reset Password

```
POST /api/auth/reset-password
```

**Request Body:**
```json
{
  "token": "reset-token-from-email",
  "password": "newpassword123",
  "confirmPassword": "newpassword123"
}
```

## Frontend Implementation

### Token Storage

Store authentication tokens securely:

```javascript
// Store tokens
const storeTokens = (accessToken, refreshToken, refreshTokenExpiry) => {
  localStorage.setItem('accessToken', accessToken);

  // Only store refresh token if provided
  if (refreshToken) {
    localStorage.setItem('refreshToken', refreshToken);
  }

  // Store refresh token expiry if provided
  if (refreshTokenExpiry) {
    localStorage.setItem('refreshTokenExpiry', refreshTokenExpiry);
  }

  // Store user data from decoded token
  try {
    const payload = JSON.parse(atob(accessToken.split('.')[1]));
    localStorage.setItem('userData', JSON.stringify({
      id: payload.sub,
      userId: payload.username,
      name: payload.name,
      type: payload.type,
      selectedRole: payload.selectedRole,
      roles: payload.roles,
      defaultSkinId: payload.defaultSkinId,
      activePlan: payload.activePlan
    }));
  } catch (e) {
    console.error('Failed to decode token:', e);
  }
};

// Get access token
const getAccessToken = () => {
  return localStorage.getItem('accessToken');
};

// Get user data from stored token
const getUserData = () => {
  const userData = localStorage.getItem('userData');
  return userData ? JSON.parse(userData) : null;
};

// Clear tokens on logout
const clearTokens = () => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('refreshTokenExpiry');
  localStorage.removeItem('userData');
};
```

### API Request Authentication

Include the token in API requests:

```javascript
const apiRequest = async (url, options = {}) => {
  const token = getAccessToken();

  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const response = await fetch(url, {
    ...options,
    headers,
  });

  // Handle 401 Unauthorized errors
  if (response.status === 401) {
    // Attempt token refresh or redirect to login
    await refreshTokenOrLogout();
    return;
  }

  return response.json();
};
```

### Token Refresh

Implement token refresh to maintain user sessions:

```javascript
const refreshTokenOrLogout = async () => {
  const refreshToken = localStorage.getItem('refreshToken');

  if (!refreshToken) {
    // No refresh token, redirect to login
    window.location.href = '/login';
    return;
  }

  try {
    const response = await fetch('/api/auth/refresh-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    });

    if (response.ok) {
      const data = await response.json();
      // Store the new tokens - note that both access_token and refresh_token are updated
      storeTokens(data.data.access_token, data.data.refresh_token);
      // Also store the refresh token expiry if needed
      if (data.data.refresh_token_expires) {
        localStorage.setItem('refreshTokenExpiry', data.data.refresh_token_expires);
      }
      return true;
    } else {
      // Refresh failed, redirect to login
      clearTokens();
      window.location.href = '/login';
      return false;
    }
  } catch (error) {
    console.error('Token refresh failed:', error);
    clearTokens();
    window.location.href = '/login';
    return false;
  }
};
```

## User Session Management

### Check Authentication Status

```javascript
const isAuthenticated = () => {
  const token = getAccessToken();
  if (!token) return false;

  // Optional: Check if token is expired
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expiry = payload.exp * 1000; // Convert to milliseconds
    return Date.now() < expiry;
  } catch (e) {
    return false;
  }
};
```

### Protected Routes

Implement protected routes in your frontend framework:

```javascript
// React example with react-router
const ProtectedRoute = ({ children }) => {
  const authenticated = isAuthenticated();

  if (!authenticated) {
    // Redirect to login
    return <Navigate to="/login" />;
  }

  return children;
};

// Usage
<Route
  path="/dashboard"
  element={
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  }
/>
```

## Error Handling

Handle common authentication errors:

- Invalid credentials
- Account locked
- Email not verified
- Password reset required
- Session expired

Example error handling:

```javascript
const login = async (email, password, rememberMe) => {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password, rememberMe }),
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle specific error cases
      switch (data.error) {
        case 'EMAIL_NOT_VERIFIED':
          return { success: false, message: 'Please verify your email before logging in.' };
        case 'INVALID_CREDENTIALS':
          return { success: false, message: 'Invalid email or password.' };
        case 'ACCOUNT_LOCKED':
          return { success: false, message: 'Your account has been locked. Please contact support.' };
        default:
          return { success: false, message: 'Login failed. Please try again.' };
      }
    }

    // Success - store tokens and return user
    storeTokens(data.accessToken, data.refreshToken);
    return { success: true, user: data.user };

  } catch (error) {
    console.error('Login error:', error);
    return { success: false, message: 'Network error. Please try again.' };
  }
};
```

## Conclusion

This integration guide provides the foundation for implementing authentication in your frontend application. Follow these patterns to ensure secure and reliable user authentication.

For any questions or issues, please contact the backend team.
