import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { PlanFeature } from './plan-feature.entity';

/**
 * Entity representing permissions for tutors to manage specific plan features
 * This allows admins to assign specific tutors to manage different features like Q&A, Diary, etc.
 */
@Entity()
export class TutorPermission extends AuditableBaseEntity {
  @Column({ name: 'tutor_id' })
  tutorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @Column({ name: 'plan_feature_id' })
  planFeatureId: string;

  @ManyToOne(() => PlanFeature)
  @JoinColumn({ name: 'plan_feature_id' })
  planFeature: PlanFeature;

  @Column({ name: 'granted_by' })
  grantedBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'granted_by' })
  admin: User;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'notes', nullable: true, type: 'text' })
  notes: string;
}
