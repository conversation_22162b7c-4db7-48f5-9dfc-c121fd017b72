import { Injectable, LoggerService as NestLoggerService, LogLevel } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import { existsSync, mkdirSync } from 'fs';

@Injectable()
class LoggerService implements NestLoggerService {
    private logDirectory: string;
    private applicationLogPath: string;
    private errorLogPath: string;
    private auditLogPath: string;
    private operationLogPath: string;

    constructor() {
        this.logDirectory = process.env.LOG_PATH || './logs';

        // Create log directory if it doesn't exist
        if (!existsSync(this.logDirectory)) {
            mkdirSync(this.logDirectory, { recursive: true });
        }

        // Initialize log file paths
        this.applicationLogPath = path.join(this.logDirectory, 'application.log');
        this.errorLogPath = path.join(this.logDirectory, 'error.log');
        this.auditLogPath = path.join(this.logDirectory, 'audit.log');
        this.operationLogPath = path.join(this.logDirectory, 'operation.log');
    }

    private formatMessage(message: any, params: any[] = []): string {
        return [message, ...params].map(item =>
            typeof item === 'object' ? JSON.stringify(item) : item
        ).join(' ');
    }

    private writeLog(filePath: string, level: string, message: string, meta?: any): void {
        let logMessage = `${new Date().toISOString()} [${level}]: ${message}`;

        // Add metadata if provided
        if (meta) {
            const metaString = typeof meta === 'object' ? JSON.stringify(meta) : meta;
            logMessage += ` - ${metaString}`;
        }

        logMessage += '\n';

        try {
            fs.appendFileSync(filePath, logMessage, 'utf8');

            // Also write to application log if it's not already going there
            if (filePath !== this.applicationLogPath) {
                fs.appendFileSync(this.applicationLogPath, logMessage, 'utf8');
            }
        } catch (error) {
            console.error(`Failed to write to log file ${filePath}:`, error);
        }
    }

    // Implement NestJS LoggerService interface methods
    log(message: any, ...optionalParams: any[]): void {
        this.writeLog(this.applicationLogPath, 'INFO', this.formatMessage(message, optionalParams));
    }

    error(message: any, stack?: string, context?: string): void {
        const formattedMessage = this.formatMessage(message, [context, stack].filter(Boolean));
        this.writeLog(this.errorLogPath, 'ERROR', formattedMessage);
    }

    warn(message: any, ...optionalParams: any[]): void {
        this.writeLog(this.applicationLogPath, 'WARN', this.formatMessage(message, optionalParams));
    }

    debug(message: any, ...optionalParams: any[]): void {
        if (process.env.NODE_ENV !== 'production') {
            this.writeLog(this.applicationLogPath, 'DEBUG', this.formatMessage(message, optionalParams));
        }
    }

    verbose(message: any, ...optionalParams: any[]): void {
        if (process.env.NODE_ENV !== 'production') {
            this.writeLog(this.applicationLogPath, 'VERBOSE', this.formatMessage(message, optionalParams));
        }
    }

    // Custom methods
    info(message: string, meta?: any): void {
        this.writeLog(this.applicationLogPath, 'INFO', message, meta);
    }

    public audit(message: string, meta?: any): void {
        this.writeLog(this.auditLogPath, 'AUDIT', message, meta);
    }

    public operation(message: string, meta?: any): void {
        this.writeLog(this.operationLogPath, 'OPERATION', message, meta);
    }

    public request(method: string, url: string, userId?: string, meta?: any): void {
        const message = `${method.toUpperCase()} ${url} - User: ${userId || 'anonymous'}`;
        this.operation(message, meta);
    }

    public response(method: string, url: string, statusCode: number, responseTime: number, userId?: string, requestId?: string, meta?: any): void {
        const message = `${requestId ? `[REQ-ID:${requestId}] ` : ''}${method.toUpperCase()} ${url} - ${statusCode} - ${responseTime}ms - User: ${userId || 'anonymous'}`;

        // Log to different levels based on status code
        if (statusCode >= 500) {
            this.error(message, undefined, JSON.stringify(meta || {}));
        } else if (statusCode >= 400) {
            this.warn(message, meta);
        } else {
            this.operation(message, meta);
        }

        // Always log to operation log for API request tracking
        if (statusCode !== 200) {
            this.operation(message, meta);
        }
    }
}

export default LoggerService;