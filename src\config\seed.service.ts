import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserType } from '../database/entities/user.entity';
import { Role } from '../database/entities/role.entity';
import { UserRole } from '../database/entities/user-role.entity';
import { Plan, PlanType, SubscriptionType } from '../database/entities/plan.entity';
import { UserPlan } from '../database/entities/user-plan.entity';
import { DiarySkin } from '../database/entities/diary-skin.entity';
import { PlanFeature } from '../database/entities/plan-feature.entity';
import { TutorApproval, TutorApprovalStatus } from '../database/entities/tutor-approval.entity';
import { ConfigService } from '@nestjs/config';
import { seedPlanFeatures, seedPlans } from './seed-plan-features';
import { getCurrentUTCDate, addMonthsUTC, addYearsUTC } from '../common/utils/date-utils';
import { trimUserId } from '../common/utils/user-utils';
import { AwardSeed } from './seeds/award.seed';
import { PromotionSeed } from './seeds/promotion.seed';
import { HecDiaryAwardsSeed } from './seeds/hec-diary-awards.seed';
import { EssayAwardsSeed } from './seeds/essay-awards.seed';
import { NovelAwardsSeed } from './seeds/novel-awards.seed';
import { ShopSeed } from './seeds/shop.seed';
import { WeekSeed } from './seeds/week.seed';
import { MonthSeed } from './seeds/month.seed';
import { ShopCategory } from '../database/entities/shop-category.entity';
import { SHOP_CONSTANTS } from '../modules/shop/constants/shop-constants';

@Injectable()
export class SeedService implements OnModuleInit {
  private readonly logger = new Logger(SeedService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(UserRole)
    private readonly userRoleRepository: Repository<UserRole>,
    @InjectRepository(Plan)
    private readonly planRepository: Repository<Plan>,
    @InjectRepository(UserPlan)
    private readonly userPlanRepository: Repository<UserPlan>,
    @InjectRepository(DiarySkin)
    private readonly diarySkinRepository: Repository<DiarySkin>,
    @InjectRepository(PlanFeature)
    private readonly planFeatureRepository: Repository<PlanFeature>,
    @InjectRepository(TutorApproval)
    private readonly tutorApprovalRepository: Repository<TutorApproval>,
    @InjectRepository(ShopCategory)
    private readonly shopCategoryRepository: Repository<ShopCategory>,
    private readonly configService: ConfigService,
    private readonly awardSeed: AwardSeed,
    private readonly promotionSeed: PromotionSeed,
    private readonly hecDiaryAwardsSeed: HecDiaryAwardsSeed,
    private readonly essayAwardsSeed: EssayAwardsSeed,
    private readonly novelAwardsSeed: NovelAwardsSeed,
    private readonly shopSeed: ShopSeed,
    private readonly weekSeed: WeekSeed,
    private readonly monthSeed: MonthSeed,
  ) {}

  async onModuleInit() {
    // Seeding is disabled to prevent automatic execution
    this.logger.log('Database seeding is disabled. Use manual seeding if needed.');
  }

  /**
   * Seed only users (admin, tutor, student) from environment variables
   * This method is called during database initialization
   */
  async seedUsersOnly() {
    try {
      this.logger.log('Starting user-only seeding process...');

      // Seed roles first (required for users)
      try {
        await this.seedRoles();
      } catch (error) {
        this.logger.error(`Error seeding roles: ${error.message}`, error.stack);
      }

      // Seed users from environment variables
      try {
        await this.seedAdminUser();
      } catch (error) {
        this.logger.error(`Error seeding admin user: ${error.message}`, error.stack);
      }

      try {
        await this.seedTutorUser();
      } catch (error) {
        this.logger.error(`Error seeding tutor user: ${error.message}`, error.stack);
      }

      try {
        await this.seedTutorApproval();
      } catch (error) {
        this.logger.error(`Error seeding tutor approval: ${error.message}`, error.stack);
      }

      try {
        await this.seedStudentUser();
      } catch (error) {
        this.logger.error(`Error seeding student user: ${error.message}`, error.stack);
      }

      this.logger.log('User-only seeding process completed!');
      return { success: true, message: 'User seeding completed successfully' };
    } catch (error) {
      this.logger.error(`User seeding failed: ${error.message}`, error.stack);
      return { success: false, message: `User seeding failed: ${error.message}` };
    }
  }

  /**
   * Seed essential data (users, plan features, and plans) for database initialization
   * This method is called during database initialization
   */
  async seedEssentialData() {
    try {
      this.logger.log('Starting essential data seeding process...');

      // Seed roles first (required for users)
      try {
        await this.seedRoles();
      } catch (error) {
        this.logger.error(`Error seeding roles: ${error.message}`, error.stack);
      }

      // Seed users from environment variables
      try {
        await this.seedAdminUser();
      } catch (error) {
        this.logger.error(`Error seeding admin user: ${error.message}`, error.stack);
      }

      try {
        await this.seedTutorUser();
      } catch (error) {
        this.logger.error(`Error seeding tutor user: ${error.message}`, error.stack);
      }

      try {
        await this.seedTutorApproval();
      } catch (error) {
        this.logger.error(`Error seeding tutor approval: ${error.message}`, error.stack);
      }

      try {
        await this.seedStudentUser();
      } catch (error) {
        this.logger.error(`Error seeding student user: ${error.message}`, error.stack);
      }

      // Seed plan features and plans
      try {
        await this.seedPlanFeatures();
      } catch (error) {
        this.logger.error(`Error seeding plan features: ${error.message}`, error.stack);
      }

      try {
        await this.seedPlans();
      } catch (error) {
        this.logger.error(`Error seeding plans: ${error.message}`, error.stack);
      }


            try {
        await this.weekSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding week: ${error.message}`, error.stack);
      }

      try {
        await this.monthSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding month: ${error.message}`, error.stack);
      }

      try {
        await this.hecDiaryAwardsSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding HEC diary awards: ${error.message}`, error.stack);
      }

      try {
        await this.essayAwardsSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding essay awards: ${error.message}`, error.stack);
      }

      try {
        await this.novelAwardsSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding novel awards: ${error.message}`, error.stack);
      }

      // Seed emoticon category
      try {
        await this.seedEmoticonCategory();
      } catch (error) {
        this.logger.error(`Error seeding emoticon category: ${error.message}`, error.stack);
      }

      this.logger.log('Essential data seeding process completed!');
      return { success: true, message: 'Essential data seeding completed successfully' };
    } catch (error) {
      this.logger.error(`Essential data seeding failed: ${error.message}`, error.stack);
      return { success: false, message: `Essential data seeding failed: ${error.message}` };
    }
  }

  async manuallyFixStudentRoles() {
    return await this.fixStudentRoles();
  }

  /**
   * Run the database seeding process manually
   * This method can be called from a controller or command line script
   */
  async runSeeding() {
    try {
      this.logger.log('Starting database seeding process...');

      // Then seed new data
      try {
        await this.seedRoles();
        await this.seedDiarySkins();
      } catch (error) {
        this.logger.error(`Error seeding roles: ${error.message}`, error.stack);
      }

      try {
        await this.seedAdminUser();
      } catch (error) {
        this.logger.error(`Error seeding admin user: ${error.message}`, error.stack);
      }

      try {
        await this.seedTutorUser();
      } catch (error) {
        this.logger.error(`Error seeding tutor user: ${error.message}`, error.stack);
      }

      try {
        await this.seedTutorApproval();
      } catch (error) {
        this.logger.error(`Error seeding tutor approval: ${error.message}`, error.stack);
      }

      try {
        await this.seedStudentUser();
      } catch (error) {
        this.logger.error(`Error seeding student user: ${error.message}`, error.stack);
      }

      try {
        await this.seedPlanFeatures();
      } catch (error) {
        this.logger.error(`Error seeding plan features: ${error.message}`, error.stack);
      }

      try {
        await this.seedPlans();
      } catch (error) {
        this.logger.error(`Error seeding plans: ${error.message}`, error.stack);
      }

      try {
        await this.seedAdditionalStudents();
      } catch (error) {
        this.logger.error(`Error seeding additional students: ${error.message}`, error.stack);
      }

      try {
        await this.assignPlansToStudents();
      } catch (error) {
        this.logger.error(`Error assigning plans to students: ${error.message}`, error.stack);
      }

      try {
        await this.awardSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding awards: ${error.message}`, error.stack);
      }

      try {
        await this.hecDiaryAwardsSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding HEC diary awards: ${error.message}`, error.stack);
      }

      try {
        await this.essayAwardsSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding essay awards: ${error.message}`, error.stack);
      }

      try {
        await this.novelAwardsSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding novel awards: ${error.message}`, error.stack);
      }

      try {
        await this.promotionSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding promotions: ${error.message}`, error.stack);
      }

      try {
        await this.shopSeed.seed();
      } catch (error) {
        this.logger.error(`Error seeding shop: ${error.message}`, error.stack);
      }

      try {
        await this.monthSeed.seed();
        this.logger.log('Month seeding completed');
      } catch (error) {
        this.logger.error(`Error seeding months: ${error.message}`, error.stack);
      }

      try {
        await this.weekSeed.seed();
        this.logger.log('Week seeding completed');
      } catch (error) {
        this.logger.error(`Error seeding weeks: ${error.message}`, error.stack);
      }

      this.logger.log('Database seeding process completed!');
      return { success: true, message: 'Database seeding completed successfully' };
    } catch (error) {
      this.logger.error(`Database seeding failed: ${error.message}`, error.stack);
      return { success: false, message: `Database seeding failed: ${error.message}` };
    }
  }

  // Removed clearExistingData method as we now check for existing records before seeding

  private async seedRoles() {
    this.logger.log('Seeding roles...');

    const roles = ['admin', 'tutor', 'student'];

    for (const roleName of roles) {
      // Check if role already exists
      const existingRole = await this.roleRepository.findOne({ where: { name: roleName } });

      if (existingRole) {
        this.logger.log(`Role ${roleName} already exists, skipping...`);
        continue;
      }

      try {
        const role = new Role();
        role.name = roleName;

        await this.roleRepository.save(role);
        this.logger.log(`Created role: ${roleName}`);
      } catch (error) {
        this.logger.error(`Error creating role ${roleName}: ${error.message}`, error.stack);
        // Continue with other roles even if one fails
      }
    }

    // Fix any students without roles
    await this.fixStudentRoles();
  }

  private async assignRoleToUser(userId: string, roleName: string): Promise<void> {
    const role = await this.roleRepository.findOne({ where: { name: roleName } });
    if (!role) return;

    const existingUserRole = await this.userRoleRepository.findOne({
      where: { userId: userId, roleId: role.id }
    });

    if (!existingUserRole) {
      const userRole = new UserRole();
      userRole.userId = userId;
      userRole.roleId = role.id;
      await this.userRoleRepository.save(userRole);
    }
  }

  /**
   * Fix students who are missing their student role
   * This method checks all users with type 'student' and ensures they have the student role
   */
  async fixStudentRoles() {
    const students = await this.userRepository.find({
      where: { type: UserType.STUDENT },
      relations: ['userRoles', 'userRoles.role']
    });

    const studentsWithoutRoles = students.filter(student =>
      !student.userRoles || student.userRoles.length === 0
    );

    for (const student of studentsWithoutRoles) {
      await this.assignRoleToUser(student.id, 'student');
    }

    return {
      totalStudents: students.length,
      studentsFixed: studentsWithoutRoles.length,
      success: true
    };
  }

  private async seedAdminUser() {
    this.logger.log('Seeding admin user...');

    // Get admin credentials from config
    const adminEmail = this.configService.get<string>('ADMIN_EMAIL') || '<EMAIL>';
    let adminUserId = this.configService.get<string>('ADMIN_USER_ID') || 'admin';
    // Trim the userId to remove any whitespace
    adminUserId = trimUserId(adminUserId);
    const adminPassword = this.configService.get<string>('ADMIN_PASSWORD') || '123456_Az';

    // Check if admin user already exists
    const existingAdmin = await this.userRepository.findOne({
      where: [
        { email: adminEmail },
        { userId: adminUserId }
      ],
      relations: ['userRoles', 'userRoles.role']
    });

    if (existingAdmin) {
      const hasAdminRole = existingAdmin.userRoles?.some(ur => ur.role?.name === 'admin');
      if (!hasAdminRole) {
        await this.assignRoleToUser(existingAdmin.id, 'admin');
      }
      return;
    }

    // Get admin role
    const adminRole = await this.roleRepository.findOne({ where: { name: 'admin' } });

    if (!adminRole) {
      this.logger.error('Admin role not found. Cannot create admin user.');
      throw new Error('Admin role not found. Database seeding failed.');
    }

    // Create admin user
    const adminUser = new User();
    adminUser.name = 'Administrator';
    adminUser.email = adminEmail;
    adminUser.userId = adminUserId;
    // Use the entity's method to set and hash the password
    adminUser.setPassword(adminPassword);
    adminUser.phoneNumber = '+1234567890'; // Default phone number
    adminUser.gender = 'male'; // Default gender
    adminUser.agreedToTerms = true; // Default agreement to terms
    adminUser.type = UserType.ADMIN;
    adminUser.isActive = true;
    adminUser.isConfirmed = true;


    this.logger.log(`Admin password hashed: ${adminUser.password.substring(0, 10)}...`);

    const savedAdmin = await this.userRepository.save(adminUser);

    // Check if user already has the admin role
    const existingUserRole = await this.userRoleRepository.findOne({
      where: {
        userId: savedAdmin.id,
        roleId: adminRole.id
      }
    });

    if (existingUserRole) {
      this.logger.log(`User ${savedAdmin.name} already has the admin role, skipping role assignment...`);
    } else {
      try {
        // Assign admin role to user
        const userRole = new UserRole();
        userRole.userId = savedAdmin.id;
        userRole.roleId = adminRole.id;
        userRole.role = adminRole; // Set the role relation
        userRole.user = savedAdmin; // Set the user relation

        await this.userRoleRepository.save(userRole);
        this.logger.log(`Assigned admin role to user ${savedAdmin.name}`);
      } catch (error) {
        this.logger.error(`Error assigning admin role to user ${savedAdmin.name}: ${error.message}`, error.stack);
        // Continue execution even if role assignment fails
      }
    }

    // Verify the admin user has the correct role
    const adminWithRoles = await this.userRepository.findOne({
      where: { id: savedAdmin.id },
      relations: ['userRoles', 'userRoles.role']
    });

    if (adminWithRoles && adminWithRoles.userRoles) {
      const roles = adminWithRoles.userRoles.map(ur => ur.role.name);
      this.logger.log(`Admin user ${adminWithRoles.name} has roles: ${roles.join(', ')}`);
    } else {
      this.logger.warn(`Admin user ${savedAdmin.name} has no roles or could not be loaded with roles`);
    }

    this.logger.log(`Created admin user with userId: ${adminUserId} and email: ${adminEmail}`);
    this.logger.log('Default admin password is set. Please change it after first login.');
  }

  private async seedTutorUser() {
    this.logger.log('Seeding tutor user...');

    // Get tutor credentials from config
    const tutorEmail = this.configService.get<string>('TUTOR_EMAIL') || '<EMAIL>';
    let tutorUserId = this.configService.get<string>('TUTOR_USER_ID') || 'tutor';
    // Trim the userId to remove any whitespace
    tutorUserId = trimUserId(tutorUserId);
    const tutorPassword = this.configService.get<string>('TUTOR_PASSWORD') || 'Tutor@123';

    // Check if tutor user already exists
    const existingTutor = await this.userRepository.findOne({
      where: [
        { email: tutorEmail },
        { userId: tutorUserId }
      ],
      relations: ['userRoles', 'userRoles.role']
    });

    if (existingTutor) {
      const hasTutorRole = existingTutor.userRoles?.some(ur => ur.role?.name === 'tutor');
      if (!hasTutorRole) {
        await this.assignRoleToUser(existingTutor.id, 'tutor');
      }
      return;
    }

    // Get tutor role
    const tutorRole = await this.roleRepository.findOne({ where: { name: 'tutor' } });

    if (!tutorRole) {
      this.logger.error('Tutor role not found. Cannot create tutor user.');
      throw new Error('Tutor role not found. Database seeding failed.');
    }

    // Create tutor user
    const tutorUser = new User();
    tutorUser.name = 'Tutor User';
    tutorUser.email = tutorEmail;
    tutorUser.userId = tutorUserId;
    // Use the entity's method to set and hash the password
    tutorUser.setPassword(tutorPassword);
    tutorUser.phoneNumber = '+1234567890'; // Default phone number
    tutorUser.gender = 'male'; // Default gender
    tutorUser.agreedToTerms = true; // Default agreement to terms
    tutorUser.type = UserType.TUTOR;
    tutorUser.isActive = true;
    tutorUser.isConfirmed = true;


    this.logger.log(`Tutor password hashed: ${tutorUser.password.substring(0, 10)}...`);

    const savedTutor = await this.userRepository.save(tutorUser);

    // Check if user already has the tutor role
    const existingUserRole = await this.userRoleRepository.findOne({
      where: {
        userId: savedTutor.id,
        roleId: tutorRole.id
      }
    });

    if (existingUserRole) {
      this.logger.log(`User ${savedTutor.name} already has the tutor role, skipping role assignment...`);
    } else {
      try {
        // Assign tutor role to user
        const userRole = new UserRole();
        userRole.userId = savedTutor.id;
        userRole.roleId = tutorRole.id;
        userRole.role = tutorRole; // Set the role relation
        userRole.user = savedTutor; // Set the user relation

        await this.userRoleRepository.save(userRole);
        this.logger.log(`Assigned tutor role to user ${savedTutor.name}`);
      } catch (error) {
        this.logger.warn(`Error assigning tutor role to user ${savedTutor.name}: ${error.message}`);
        // Continue execution even if role assignment fails
      }
    }

    this.logger.log(`Created tutor user with userId: ${tutorUserId} and email: ${tutorEmail}`);
    this.logger.log('Default tutor password is set. Please change it after first login.');
  }

  private async seedStudentUser() {
    this.logger.log('Seeding student user...');

    // Get student credentials from config
    const studentEmail = this.configService.get<string>('STUDENT_EMAIL') || '<EMAIL>';
    let studentUserId = this.configService.get<string>('STUDENT_USER_ID') || 'student';
    // Trim the userId to remove any whitespace
    studentUserId = trimUserId(studentUserId);
    const studentPassword = this.configService.get<string>('STUDENT_PASSWORD') || 'Student@123';

    // Check if student user already exists
    const existingStudent = await this.userRepository.findOne({
      where: [
        { email: studentEmail },
        { userId: studentUserId }
      ],
      relations: ['userRoles', 'userRoles.role']
    });

    if (existingStudent) {
      const hasStudentRole = existingStudent.userRoles?.some(ur => ur.role?.name === 'student');
      if (!hasStudentRole) {
        await this.assignRoleToUser(existingStudent.id, 'student');
      }
      return;
    }

    // Get student role
    const studentRole = await this.roleRepository.findOne({ where: { name: 'student' } });

    if (!studentRole) {
      this.logger.error('Student role not found. Cannot create student user.');
      throw new Error('Student role not found. Database seeding failed.');
    }

    // Create student user
    const studentUser = new User();
    studentUser.name = 'Student User';
    studentUser.email = studentEmail;
    studentUser.userId = studentUserId;
    // Use the entity's method to set and hash the password
    studentUser.setPassword(studentPassword);
    studentUser.phoneNumber = '+1234567890'; // Default phone number
    studentUser.gender = 'male'; // Default gender
    studentUser.agreedToTerms = true; // Default agreement to terms
    studentUser.type = UserType.STUDENT;
    studentUser.isActive = true;
    studentUser.isConfirmed = true;


    this.logger.log(`Student password hashed: ${studentUser.password.substring(0, 10)}...`);

    const savedStudent = await this.userRepository.save(studentUser);

    // Check if user already has the student role
    const existingUserRole = await this.userRoleRepository.findOne({
      where: {
        userId: savedStudent.id,
        roleId: studentRole.id
      }
    });

    if (existingUserRole) {
      this.logger.log(`User ${savedStudent.name} already has the student role, skipping role assignment...`);
    } else {
      try {
        // Assign student role to user
        const userRole = new UserRole();
        userRole.userId = savedStudent.id;
        userRole.roleId = studentRole.id;
        userRole.role = studentRole; // Set the role relation
        userRole.user = savedStudent; // Set the user relation

        await this.userRoleRepository.save(userRole);
        this.logger.log(`Assigned student role to user ${savedStudent.name}`);
      } catch (error) {
        this.logger.warn(`Error assigning student role to user ${savedStudent.name}: ${error.message}`);
        // Continue execution even if role assignment fails
      }
    }

    this.logger.log(`Created student user with userId: ${studentUserId} and email: ${studentEmail}`);
    this.logger.log('Default student password is set. Please change it after first login.');
  }

  private async seedPlanFeatures() {
    await seedPlanFeatures(this.logger, this.planFeatureRepository);
  }

  private async seedPlans() {
    await seedPlans(this.logger, this.planRepository, this.planFeatureRepository);
  }

  // The seedPlans and seedPlanFeatures methods now use the functions from seed-plan-features.ts

  private async seedAdditionalStudents() {
    this.logger.log('Seeding additional student users...');

    // Get student role
    const studentRole = await this.roleRepository.findOne({ where: { name: 'student' } });

    if (!studentRole) {
      this.logger.error('Student role not found. Cannot create additional student users.');
      throw new Error('Student role not found. Database seeding failed.');
    }

    // Define additional students
    const additionalStudents = [
      {
        name: 'John Doe',
        email: '<EMAIL>',
        userId: 'john_doe',
        password: 'Student@123',
        phoneNumber: '+1234567891',
        gender: 'male',
      },
      {
        name: 'Jane Smith',
        email: '<EMAIL>',
        userId: 'jane_smith',
        password: 'Student@123',
        phoneNumber: '+1234567892',
        gender: 'female',
      },
      {
        name: 'Alex Johnson',
        email: '<EMAIL>',
        userId: 'alex_johnson',
        password: 'Student@123',
        phoneNumber: '+1234567893',
        gender: 'N/A',
      },
      {
        name: 'Sam Wilson',
        email: '<EMAIL>',
        userId: 'sam_wilson',
        password: 'Student@123',
        phoneNumber: '+1234567894',
        gender: 'male',
      }
    ];

    // Create each student
    for (const studentData of additionalStudents) {
      // Check if student already exists
      const existingStudent = await this.userRepository.findOne({
        where: [
          { email: studentData.email },
          { userId: studentData.userId }
        ],
        relations: ['userRoles', 'userRoles.role']
      });

      if (existingStudent) {
        const hasStudentRole = existingStudent.userRoles?.some(ur => ur.role?.name === 'student');
        if (!hasStudentRole) {
          await this.assignRoleToUser(existingStudent.id, 'student');
        }
        continue;
      }

      try {
        // Trim the userId to remove any whitespace
        const trimmedUserId = trimUserId(studentData.userId);

        // Create student user
        const studentUser = new User();
        studentUser.name = studentData.name;
        studentUser.email = studentData.email;
        studentUser.userId = trimmedUserId;
        // Use the entity's method to set and hash the password
        studentUser.setPassword(studentData.password);
        studentUser.phoneNumber = studentData.phoneNumber;
        studentUser.gender = studentData.gender;
        studentUser.agreedToTerms = true;
        studentUser.type = UserType.STUDENT;
        studentUser.isActive = true;
        studentUser.isConfirmed = true;


        this.logger.log(`Additional student password hashed for ${studentData.name}: ${studentUser.password.substring(0, 10)}...`);

        const savedStudent = await this.userRepository.save(studentUser);
        this.logger.log(`Created student user: ${studentData.name} with userId: ${studentData.userId}`);

        // Check if user already has the student role
        const existingUserRole = await this.userRoleRepository.findOne({
          where: {
            userId: savedStudent.id,
            roleId: studentRole.id
          }
        });

        if (existingUserRole) {
          this.logger.log(`User ${savedStudent.name} already has the student role, skipping role assignment...`);
        } else {
          try {
            // Assign student role to user
            const userRole = new UserRole();
            userRole.userId = savedStudent.id;
            userRole.roleId = studentRole.id;
            userRole.role = studentRole; // Set the role relation
            userRole.user = savedStudent; // Set the user relation

            await this.userRoleRepository.save(userRole);
            this.logger.log(`Assigned student role to user ${savedStudent.name}`);
          } catch (error) {
            this.logger.warn(`Error assigning student role to user ${savedStudent.name}: ${error.message}`);
            // Continue execution even if role assignment fails
          }
        }
      } catch (error) {
        // Log the error but continue with the next student
        this.logger.warn(`Error creating student ${studentData.name}: ${error.message}`);
        continue;
      }
    }

    this.logger.log('Additional students seeding completed.');
  }

  private async assignPlansToStudents() {
    this.logger.log('Assigning plans to students...');

    // Define plan assignments
    const planAssignments = [
      { userIdOrEmail: 'john_doe', planType: PlanType.STARTER, subscriptionType: SubscriptionType.MONTHLY },
      { userIdOrEmail: 'jane_smith', planType: PlanType.STANDARD, subscriptionType: SubscriptionType.YEARLY },
      { userIdOrEmail: 'alex_johnson', planType: PlanType.PRO, subscriptionType: SubscriptionType.MONTHLY },
      { userIdOrEmail: 'sam_wilson', planType: PlanType.ULTIMATE, subscriptionType: SubscriptionType.YEARLY }
    ];

    for (const assignment of planAssignments) {
      // Find the user
      const user = await this.userRepository.findOne({
        where: [
          { userId: assignment.userIdOrEmail },
          { email: assignment.userIdOrEmail }
        ]
      });

      if (!user) {
        this.logger.error(`User ${assignment.userIdOrEmail} not found. Skipping plan assignment.`);
        // We'll continue instead of throwing an error since this is not critical
        continue;
      }

      // Find the plan
      const plan = await this.planRepository.findOne({
        where: {
          type: assignment.planType,
          subscriptionType: assignment.subscriptionType
        }
      });

      if (!plan) {
        this.logger.error(`Plan with type ${assignment.planType} and subscription type ${assignment.subscriptionType} not found. Skipping assignment.`);
        // We'll continue instead of throwing an error since this is not critical
        continue;
      }

      // Check if user already has this plan
      const existingUserPlan = await this.userPlanRepository.findOne({
        where: {
          userId: user.id,
          planId: plan.id,
          isActive: true
        }
      });

      if (existingUserPlan) {
        this.logger.log(`User ${user.name} already has plan ${plan.name}, skipping...`);
        continue;
      }

      // Deactivate any existing active plans for this user
      const activeUserPlans = await this.userPlanRepository.find({
        where: {
          userId: user.id,
          isActive: true
        }
      });

      for (const activePlan of activeUserPlans) {
        activePlan.isActive = false;
        await this.userPlanRepository.save(activePlan);
        this.logger.log(`Deactivated existing plan for user ${user.name}`);
      }

      // Calculate start and end dates in UTC
      const startDate = getCurrentUTCDate();
      let endDate: Date;

      if (assignment.subscriptionType === SubscriptionType.MONTHLY) {
        endDate = addMonthsUTC(startDate, 1);
      } else {
        endDate = addYearsUTC(startDate, 1);
      }

      // Create user plan
      const userPlan = new UserPlan();
      userPlan.userId = user.id;
      userPlan.planId = plan.id;
      userPlan.user = user; // Set the user relation
      userPlan.plan = plan; // Set the plan relation
      userPlan.startDate = startDate;
      userPlan.endDate = endDate;
      userPlan.isActive = true;
      userPlan.isPaid = true;
      userPlan.autoRenew = true;
      userPlan.nextRenewalDate = new Date(endDate.getTime());


      await this.userPlanRepository.save(userPlan);

      this.logger.log(`Assigned plan ${plan.name} to user ${user.name}`);
    }

    this.logger.log('Plan assignments completed.');
  }

  private async seedDiarySkins() {
    this.logger.log('Seeding diary skins...');

    // Define the diary skins to seed
    const skins = [
      {
        name: 'Classic',
        description: 'A clean, classic diary template with a simple layout.',
        templateContent: '<div class="classic-template">Classic template content</div>',
        previewImagePath: '/images/diary/classic-preview.jpg',
        isActive: true,
        isGlobal: true
      },
      {
        name: 'Modern',
        description: 'A modern, sleek design with minimalist aesthetics.',
        templateContent: '<div class="modern-template">Modern template content</div>',
        previewImagePath: '/images/diary/modern-preview.jpg',
        isActive: true,
        isGlobal: true
      },
      {
        name: 'Vintage',
        description: 'An old-fashioned diary template with a nostalgic feel.',
        templateContent: '<div class="vintage-template">Vintage template content</div>',
        previewImagePath: '/images/diary/vintage-preview.jpg',
        isActive: true,
        isGlobal: true
      },
      {
        name: 'Nature',
        description: 'A nature-inspired template with floral and leaf motifs.',
        templateContent: '<div class="nature-template">Nature template content</div>',
        previewImagePath: '/images/diary/nature-preview.jpg',
        isActive: true,
        isGlobal: true
      },
      {
        name: 'Academic',
        description: 'A structured template designed for academic reflections.',
        templateContent: '<div class="academic-template">Academic template content</div>',
        previewImagePath: '/images/diary/academic-preview.jpg',
        isActive: true,
        isGlobal: true
      },
      // Additional 4 diary skins
      {
        name: 'Minimalist',
        description: 'An ultra-clean, minimalist design with focus on content.',
        templateContent: '<div class="minimalist-template">Minimalist template content</div>',
        previewImagePath: '/images/diary/minimalist-preview.jpg',
        isActive: true,
        isGlobal: true
      },
      {
        name: 'Creative',
        description: 'A colorful, artistic template for creative writing and expression.',
        templateContent: '<div class="creative-template">Creative template content</div>',
        previewImagePath: '/images/diary/creative-preview.jpg',
        isActive: true,
        isGlobal: true
      },
      {
        name: 'Professional',
        description: 'A business-oriented template ideal for work reflections and professional development.',
        templateContent: '<div class="professional-template">Professional template content</div>',
        previewImagePath: '/images/diary/professional-preview.jpg',
        isActive: true,
        isGlobal: true
      },
      {
        name: 'Travel',
        description: 'A travel-themed template perfect for documenting journeys and adventures.',
        templateContent: '<div class="travel-template">Travel template content</div>',
        previewImagePath: '/images/diary/travel-preview.jpg',
        isActive: true,
        isGlobal: true
      },
    ];

    for (const skinData of skins) {
      // Check if skin already exists
      const existingSkin = await this.diarySkinRepository.findOne({
        where: { name: skinData.name },
      });

      if (existingSkin) {
        this.logger.log(`Diary skin '${skinData.name}' already exists. Skipping.`);
        continue;
      }

      // Create new skin
      const skin = this.diarySkinRepository.create(skinData);
      await this.diarySkinRepository.save(skin);

      this.logger.log(`Created diary skin: ${skin.name}`);
    }

    this.logger.log('Diary skins seeding completed.');
  }

  private async seedTutorApproval() {
    this.logger.log('Seeding tutor approval...');

    // Get tutor user ID from config or use default
    let tutorUserId = this.configService.get<string>('TUTOR_USER_ID') || 'tutor';
    // Trim the userId to remove any whitespace
    tutorUserId = trimUserId(tutorUserId);

    // Find the tutor user
    const tutorUser = await this.userRepository.findOne({
      where: { userId: tutorUserId, type: UserType.TUTOR }
    });

    if (!tutorUser) {
      this.logger.error(`Tutor user with userId ${tutorUserId} not found. Cannot create tutor approval.`);
      return;
    }

    this.logger.log(`Found tutor user: ${tutorUser.name} (${tutorUser.id})`);

    // Check if approval already exists
    const existingApproval = await this.tutorApprovalRepository.findOne({
      where: { userId: tutorUser.id }
    });

    if (existingApproval) {
      this.logger.log(`Tutor approval already exists with status: ${existingApproval.status}`);

      // Update the status to approved if it's not already
      if (existingApproval.status !== TutorApprovalStatus.APPROVED) {
        existingApproval.status = TutorApprovalStatus.APPROVED;
        existingApproval.adminNotes = 'Approved via seeding script';
        existingApproval.approvedAt = new Date();

        await this.tutorApprovalRepository.save(existingApproval);

        // Also update the user's active status
        tutorUser.isActive = true;
        await this.userRepository.save(tutorUser);

        this.logger.log(`Updated existing approval to APPROVED status`);
      }

      return;
    }

    // Create a new tutor approval
    const tutorApproval = new TutorApproval();
    tutorApproval.userId = tutorUser.id;
    tutorApproval.status = TutorApprovalStatus.APPROVED;
    tutorApproval.adminNotes = 'Approved via seeding script';
    tutorApproval.approvedAt = new Date();

    await this.tutorApprovalRepository.save(tutorApproval);

    // Update the user's active status
    tutorUser.isActive = true;
    await this.userRepository.save(tutorUser);

    this.logger.log(`Created and approved tutor approval for user: ${tutorUser.name}`);
    this.logger.log('Tutor approval seeding completed successfully');
  }

  private async seedEmoticonCategory() {
    this.logger.log('Seeding emoticon category...');

    // Check if emoticon category already exists (should exist after migration)
    const existingEmoticonCategory = await this.shopCategoryRepository.findOne({
      where: { name: SHOP_CONSTANTS.PROTECTED_CATEGORIES.EMOTICON }
    });

    if (existingEmoticonCategory) {
      this.logger.log(`Emoticon category already exists with ID: ${existingEmoticonCategory.id}`);

      // Verify and update the category to ensure it matches seeder specifications
      let needsUpdate = false;
      const expectedDescription = 'Special category for emoticons. This category is protected and cannot be modified or deleted.';

      if (existingEmoticonCategory.description !== expectedDescription) {
        existingEmoticonCategory.description = expectedDescription;
        needsUpdate = true;
      }

      if (!existingEmoticonCategory.isActive) {
        existingEmoticonCategory.isActive = true;
        needsUpdate = true;
      }

      if (existingEmoticonCategory.displayOrder !== 0) {
        existingEmoticonCategory.displayOrder = 0;
        needsUpdate = true;
      }

      if (needsUpdate) {
        await this.shopCategoryRepository.save(existingEmoticonCategory);
        this.logger.log(`Updated emoticon category ${existingEmoticonCategory.id} to match seeder specifications`);
      } else {
        this.logger.log(`Emoticon category ${existingEmoticonCategory.id} already matches seeder specifications`);
      }

      return;
    }

    // Create emoticon category (fallback for fresh installations)
    this.logger.log('Creating new emoticon category...');
    const emoticonCategory = this.shopCategoryRepository.create({
      name: SHOP_CONSTANTS.PROTECTED_CATEGORIES.EMOTICON,
      description: 'Special category for emoticons. This category is protected and cannot be modified or deleted.',
      isActive: true,
      displayOrder: 0
    });

    const savedCategory = await this.shopCategoryRepository.save(emoticonCategory);
    this.logger.log(`Created emoticon category with ID: ${savedCategory.id}`);
    this.logger.log('Emoticon category seeding completed successfully');
  }
}
