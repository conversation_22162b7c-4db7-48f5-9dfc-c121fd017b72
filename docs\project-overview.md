# HEC (Hello English Coachin) Platform

## Project Overview

The HEC (Hello English Coachin) platform is a comprehensive educational system designed to connect students with tutors for personalized learning experiences. The platform facilitates diary writing, essay submissions, tutor feedback, and social interactions between students.

## Key Features

- **Authentication System** - Secure login and registration for students, tutors, and administrators
- **Diary Module** - Students can create diary entries that tutors can review and provide feedback
- **Student Friendship** - Students can connect with each other and share diary entries
- **Automatic Tutor Assignment** - Intelligent system to automatically assign tutors to students based on subscription plans and workload balancing
- **Real-time Chat** - Instant messaging between students and tutors
- **Notification System** - Keep users informed about important events and updates
- **Subscription Plans** - Various subscription tiers with different feature sets
- **Essay Module** - Students can submit essays for tutor review and feedback

## Core Modules

### Authentication Module
Secure user authentication with JWT tokens, role-based access control, and account management. The system supports three user types: Ad<PERSON>, Tutor, and Student, each with specific permissions and access levels.

### Diary Module
Student diary entries with customizable skins, tutor feedback, and sharing capabilities. Students can create, edit, and submit diary entries for tutor review. Tutors can provide feedback and evaluations on entries.

### Student Friendship Module
Social connections between students with friend requests and diary sharing. Students can search for other students, send friend requests, and share diary entries with friends.

### Chat Module
Real-time messaging between students, tutors, and administrators. The system uses Socket.io for WebSocket communication, enabling instant messaging, typing indicators, and read receipts.

### Notification Module
Multi-channel notification system for in-app, email, and push notifications. The system supports various notification types and includes a retry mechanism for failed notifications.

### Subscription Module
Plan management with different tiers and feature sets. Students can purchase plans (Starter, Standard, Pro, Ultimate) with monthly/yearly options. Each plan provides access to different features.

### Tutor Assignment System
Automatic tutor assignment based on subscription plans and modules. When a student subscribes to a plan, the system automatically assigns appropriate tutors for each module included in the plan. The system uses workload balancing to ensure fair distribution of students among tutors and sends notifications to both students and tutors about new assignments.

### Essay Module
Students can submit essays for tutor review and feedback. Tutors can provide detailed feedback and evaluations on essays.

### File Management
Secure file upload and management system for profile pictures, diary images, and essay attachments.

## Technical Stack

The HEC backend is built with:

- **Framework**: NestJS, a progressive Node.js framework
- **Database**: PostgreSQL with TypeORM for ORM
- **Authentication**: JWT-based authentication
- **Real-time Communication**: Socket.io for WebSockets
- **File Storage**: Local file system with metadata in database
- **API Documentation**: Swagger/OpenAPI

## Architecture

The system follows a modular architecture with clear separation of concerns:

- **Controllers**: Handle HTTP requests and define API endpoints
- **Services**: Contain business logic and interact with repositories
- **Repositories**: Handle database operations
- **DTOs**: Define data transfer objects for API requests and responses
- **Entities**: Define database models
- **Guards**: Protect routes based on authentication and authorization
- **Interceptors**: Transform data before and after request handling
- **Filters**: Handle exceptions and errors

## Development Conventions

- **Naming**: camelCase for variables, PascalCase for classes, kebab-case for files
- **Database**: snake_case for database columns with custom TypeORM NamingStrategy
- **Audit Fields**: Automatic handling of createdAt/updatedAt, createdBy/updatedBy
- **Transactions**: Database transactions with rollback for operations involving multiple entities
- **Pagination**: All list responses use a pagedlist DTO with items, totalCount, totalItems, itemsPerPage, currentPage, and totalPages
- **Error Handling**: Consistent error responses with appropriate HTTP status codes
