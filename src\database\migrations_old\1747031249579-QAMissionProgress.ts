import { MigrationInterface, QueryRunner } from "typeorm";

export class QAMissionProgress1747031249579 implements MigrationInterface {
    name = 'QAMissionProgress1747031249579'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ADD "first_submitted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ADD "last_submitted_at" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ADD "first_revision_progress" double precision NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ADD "submission_mark_id" uuid`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ADD CONSTRAINT "UQ_f8c9a915a6f0809f96073936ea1" UNIQUE ("submission_mark_id")`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ADD CONSTRAINT "FK_f8c9a915a6f0809f96073936ea1" FOREIGN KEY ("submission_mark_id") REFERENCES "qa_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP CONSTRAINT "FK_f8c9a915a6f0809f96073936ea1"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP CONSTRAINT "UQ_f8c9a915a6f0809f96073936ea1"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP COLUMN "submission_mark_id"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP COLUMN "first_revision_progress"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP COLUMN "last_submitted_at"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP COLUMN "first_submitted_at"`);
    }

}
