import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete,
  Param, 
  Body, 
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
  HttpCode,
  BadRequestException
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiBody, ApiQuery, ApiParam } from '@nestjs/swagger';
import { QAService } from './qa.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType, ApiOkResponseWithArrayType } from '../../common/decorators/api-response.decorator';
import { 
  CreateQAQuestionDto,
  UpdateQAQuestionDto,
  QAQuestionResponseDto,
  CreateQAAssignmentDto,
  QAAssignmentResponseDto,
  QASubmissionResponseDto,
  QAQuestionPaginationDto,
  QAAssignmentPaginationDto
} from '../../database/models/qa.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { Roles } from 'src/common/decorators/roles.decorator';
import { StudentDropdownDto } from 'src/database/models/qa/student-dropdown.dto';
import { QAAssignmentStatus } from 'src/database/entities/qa-assignment.entity';

@Controller('admin/qa')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('Admin Q&A')
export class AdminQAController {
  constructor(private readonly qaService: QAService) {}

@Post('create')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiOperation({ summary: 'Create a new QA question' })
@ApiBody({
  type: CreateQAQuestionDto,
  description: 'QA question creation data',
  examples: {
    example1: {
      value: {
        question: 'What are the key principles of Object-Oriented Programming?',
        points: 10,
        minimumWords: 200,
        isActive: true
      }
    }
  }
})
@ApiOkResponseWithType(QAQuestionResponseDto, 'QA question created successfully')
@ApiErrorResponse(400, 'Invalid input data')
@ApiErrorResponse(401, 'Unauthorized - Authentication required')
@ApiErrorResponse(403, 'Forbidden - Admin access required')

  async create(
    @Body() createQuestionDto: CreateQAQuestionDto,
    @Request() req
  ): Promise<ApiResponse<QAQuestionResponseDto>> {
  console.log('Creating question with data:', createQuestionDto);
  const result = await this.qaService.create(createQuestionDto, req.user.id);
  return ApiResponse.success(
    result,
    'QA question created successfully',
    201
  );
}

// @Get('assignments')
//     @UseGuards(JwtAuthGuard, AdminGuard)
//     @ApiOperation({ summary: 'Get all QA assignments' })
//     @ApiQuery({
//       name: 'page',
//       required: false,
//       type: Number,
//       description: 'Page number',
//     })
//     @ApiQuery({
//       name: 'limit',
//       required: false,
//       type: Number,
//       description: 'Number of items per page',
//     })
//     @ApiQuery({
//       name: 'sortBy',
//       required: false,
//       type: String,
//       description: 'Field to sort by',
//     })
//     @ApiQuery({
//       name: 'sortDirection',
//       required: false,
//       type: String,
//       enum: ['ASC', 'DESC'],
//       description: 'Sort direction',
//     })
//     @ApiOkResponseWithPagedListType(QAAssignmentResponseDto, 'Assignments retrieved successfully')
//     @ApiErrorResponse(401, 'Unauthorized')
//     @ApiErrorResponse(403, 'Forbidden - Admin access required')
//     async getAllAssignments(
//       @Query() paginationDto: QAAssignmentPaginationDto
//     ): Promise<ApiResponse<PagedListDto<QAAssignmentResponseDto>>> {
//       return this.qaService.getAllAssignments(paginationDto);
//   }

  @Get('questions')
    @UseGuards(JwtAuthGuard, AdminGuard)
    @ApiOperation({ summary: 'Get all QA questions' })
    @ApiQuery({
      name: 'page',
      required: false,
      type: Number,
      description: 'Page number',
    })
    @ApiQuery({
      name: 'limit',
      required: false,
      type: Number,
      description: 'Number of items per page',
    })
    @ApiQuery({
      name: 'sortBy',
      required: false,
      type: String,
      description: 'Field to sort by',
    })
    @ApiQuery({
      name: 'sortDirection',
      required: false,
      type: String,
      enum: ['ASC', 'DESC'],
      description: 'Sort direction',
    })
    // @ApiQuery({
    //   name: 'isActive',
    //   required: false,
    //   type: Boolean,
    //   description: 'Filter by active status',
    // })
    @ApiOkResponseWithPagedListType(QAQuestionResponseDto, 'Questions retrieved successfully')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    async getAllQuestions(
      @Query() paginationDto: QAQuestionPaginationDto
    ): Promise<ApiResponse<PagedListDto<QAQuestionResponseDto>>> {
      return this.qaService.getAllQuestions(paginationDto);
  }

  @Put('questions/:id')
  @ApiOperation({
    summary: 'Update a QA question (Admin only)',
    description: 'Updates an existing question.'
  })
  @ApiOkResponseWithType(QAQuestionResponseDto, 'Question updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Question not found')
  async updateQuestion(
    @Param('id') id: string,
    @Body() updateQuestionDto: UpdateQAQuestionDto
  ): Promise<ApiResponse<QAQuestionResponseDto>> {
    const question = await this.qaService.updateQuestion(id, updateQuestionDto);
    return ApiResponse.success(question, 'Question updated successfully');
  }

  @Delete('questions/:id')
  @ApiOperation({
    summary: 'Delete a QA question (Admin only)',
    description: 'Deletes an existing question.'
  })
  @ApiOkResponseWithType(QAQuestionResponseDto, 'Question deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Question not found')
  async deleteQuestion(
    @Param('id') id: string
  ): Promise<ApiResponse<QAQuestionResponseDto>> {
    const question = await this.qaService.deleteQuestion(id);
    return ApiResponse.success(null, 'Question deleted successfully');
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: 'Get a specific QA question by ID (Admin only)' })
  @ApiParam({ name: 'id', description: 'ID of the QA question to retrieve', type: String })
  @ApiOkResponseWithType(QAQuestionResponseDto, 'QA question retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'QA question not found')
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<QAQuestionResponseDto>> {
    const result = await this.qaService.findById(id);
    return ApiResponse.success(result, 'QA question retrieved successfully');
  }

  // @Post('assignments')
  // @ApiOperation({
  //   summary: 'Create assignments for students (Admin only)',
  //   description: 'Creates new assignments for selected students.'
  // })
  // @ApiBody({
  //   type: CreateQAAssignmentDto,
  //   description: 'QA assignment creation data',
  //   examples: {
  //     example1: {
  //       value: {
  //         questionId: '123e4567-e89b-12d3-a456-************',
  //         studentId: '123e4567-e89b-12d3-a456-************',
  //         points: 10,
  //         deadline: '2023-12-31T23:59:59Z',
  //         //assignedDate: '2023-11-01T00:00:00Z',
  //         instructions: 'Please focus on SOLID principles in your answer'
  //       }
  //     }
  //   }
  // })
  
  // @ApiOkResponseWithType(QAAssignmentResponseDto, 'Assignments created successfully')
  // @ApiErrorResponse(400, 'Invalid input')
  // @ApiErrorResponse(401, 'Unauthorized')
  // @ApiErrorResponse(403, 'Forbidden - Admin access required')
  // async createAssignments(
  //   @Body() createAssignmentDto: CreateQAAssignmentDto
  // ): Promise<ApiResponse<QAAssignmentResponseDto[]>> {
  //   const assignments = await this.qaService.createAssignments(createAssignmentDto);
  //   return ApiResponse.success(assignments, 'Assignments created successfully', 201);
  // }

  @Get('student/dropdown')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get student list for dropdown',
    description: 'Returns a list of students with basic information for dropdown menus with search functionality'
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search query to filter students by name, userId, or email'
  })
  @ApiOkResponseWithArrayType(StudentDropdownDto, 'Student list retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  async getStudentDropdownList(
    @Query('search') search?: string
  ): Promise<ApiResponse<any[]>> {
    const students = await this.qaService.getStudentDropdownList(search);
    return ApiResponse.success(students, 'Student list retrieved successfully');
  }
}
