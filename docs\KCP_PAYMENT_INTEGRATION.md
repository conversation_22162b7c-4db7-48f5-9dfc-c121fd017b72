# KCP Payment Gateway Integration

This document provides a comprehensive guide for the KCP payment gateway integration implemented in the HEC backend system.

## Overview

The KCP payment gateway integration allows users to make payments for:
- Shop item purchases
- Plan subscriptions

The integration supports multiple payment methods:
- Credit/Debit Cards (`kcp_card`)
- Bank Transfer (`kcp_bank`)
- Mobile Payment (`kcp_mobile`)

## Architecture

### Core Components

1. **Payment Module** (`src/modules/payment/`)
   - `PaymentController` - Handles payment API endpoints
   - `PaymentService` - Core payment processing logic
   - `KcpService` - KCP-specific payment operations
   - `KcpConfigService` - KCP configuration management

2. **Database Entities**
   - `PaymentTransaction` - Stores payment transaction details
   - `PaymentWebhook` - Handles KCP webhook notifications
   - Enhanced `ShopItemPurchase` and `UserPlan` with payment references

3. **Enhanced Services**
   - `ShoppingCartService` - Extended with KCP payment support
   - `PlansService` - Extended with KCP payment support

## Database Schema

### New Tables

#### payment_transaction
```sql
CREATE TABLE payment_transaction (
  id UUID PRIMARY KEY,
  transaction_id VARCHAR(100) UNIQUE NOT NULL,
  kcp_transaction_id VARCHAR(100),
  order_id VARCHAR(100) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'KRW',
  payment_method VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'initiated',
  user_id UUID NOT NULL,
  purchase_type VARCHAR(20) NOT NULL,
  reference_id UUID NOT NULL,
  -- KCP specific fields
  kcp_trade_key VARCHAR(200),
  kcp_approval_key VARCHAR(200),
  kcp_approval_time TIMESTAMP,
  -- Metadata
  request_data JSONB,
  response_data JSONB,
  error_message TEXT,
  processed_at TIMESTAMP,
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### payment_webhook
```sql
CREATE TABLE payment_webhook (
  id UUID PRIMARY KEY,
  transaction_id VARCHAR(100) NOT NULL,
  webhook_type VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'received',
  payload JSONB NOT NULL,
  processed BOOLEAN DEFAULT FALSE,
  processed_at TIMESTAMP,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  next_retry_at TIMESTAMP,
  signature VARCHAR(255),
  source_ip VARCHAR(45),
  created_at TIMESTAMP DEFAULT NOW()
);
```

### Enhanced Tables

#### shop_item_purchase
- Added `payment_transaction_id` UUID column
- Extended `payment_method` enum with KCP methods
- Extended `status` enum with payment-specific statuses

#### user_plan
- Added `payment_transaction_id` UUID column

## API Endpoints

### Payment Endpoints

#### POST /payment/initiate
Initiate a payment process.

**Request Body:**
```json
{
  "orderId": "ORD-20240101-001",
  "amount": 29900,
  "currency": "KRW",
  "productName": "Premium Plan Subscription",
  "paymentMethod": "card",
  "buyerName": "John Doe",
  "buyerEmail": "<EMAIL>",
  "buyerPhone": "010-1234-5678",
  "purchaseType": "plan",
  "referenceId": "uuid",
  "returnUrl": "https://example.com/payment/success",
  "cancelUrl": "https://example.com/payment/cancel"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "success": true,
    "transactionId": "TXN-1234567890",
    "paymentUrl": "https://payment.gateway.url",
    "message": "Payment initiated successfully",
    "expiresAt": "2024-01-01T12:30:00Z"
  }
}
```

#### GET /payment/status/:transactionId
Get payment transaction status.

#### POST /payment/webhook/kcp
Handle KCP webhook notifications (internal endpoint).

### Enhanced Shop Endpoints

#### POST /shop/cart/checkout
Enhanced to support KCP payment methods.

**Request Body:**
```json
{
  "paymentMethod": "kcp_card",
  "useRewardPoints": false,
  "promoCode": "SUMMER20",
  "returnUrl": "https://example.com/payment/success",
  "cancelUrl": "https://example.com/payment/cancel"
}
```

**Response (for KCP payments):**
```json
{
  "success": true,
  "data": {
    "success": true,
    "orderId": "ORDER-123-1234567890",
    "totalAmount": 29900,
    "paymentMethod": "kcp_card",
    "paymentTransactionId": "TXN-1234567890",
    "paymentUrl": "https://payment.gateway.url",
    "items": [...],
    "rewardPointInfo": {...}
  }
}
```

### Enhanced Plans Endpoints

#### POST /plans/subscribe
Enhanced to support KCP payment methods.

**Request Body:**
```json
{
  "planId": "uuid",
  "paymentMethod": "kcp_card",
  "autoRenew": true,
  "returnUrl": "https://example.com/payment/success",
  "cancelUrl": "https://example.com/payment/cancel"
}
```

## Configuration

### Environment Variables

Copy `.env.kcp.example` to `.env` and configure:

```env
# KCP Configuration
KCP_SITE_CD=your_kcp_site_code
KCP_SITE_KEY=your_kcp_site_key
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_WEBHOOK_SECRET=your_webhook_secret

# Payment Configuration
PAYMENT_PROVIDER=kcp
PAYMENT_CURRENCY=KRW
PAYMENT_TIMEOUT=30000
PAYMENT_RETRY_ATTEMPTS=3
```

### KCP Environment Settings

**Development/Staging:**
```env
KCP_ENVIRONMENT=staging
KCP_TEST_MODE=true
KCP_API_URL=https://stg-spl.kcp.co.kr
```

**Production:**
```env
KCP_ENVIRONMENT=production
KCP_TEST_MODE=false
KCP_API_URL=https://spl.kcp.co.kr
```

## Payment Flow

### Shop Item Purchase Flow

1. User adds items to cart
2. User initiates checkout with KCP payment method
3. System creates temporary purchase record with `PAYMENT_PENDING` status
4. System initiates payment with KCP
5. User is redirected to KCP payment page
6. After payment completion, KCP sends webhook notification
7. System processes webhook and updates purchase status
8. User receives confirmation

### Plan Subscription Flow

1. User selects a plan
2. User chooses KCP payment method
3. System creates temporary user plan with `isActive: false`
4. System initiates payment with KCP
5. User completes payment on KCP
6. KCP sends webhook notification
7. System activates user plan and creates diary
8. User gains access to plan features

## Error Handling

### Payment Failures
- Failed payments are logged with detailed error messages
- Users receive appropriate error responses
- Temporary records are cleaned up for failed payments

### Webhook Processing
- Webhooks are processed asynchronously
- Failed webhook processing includes retry mechanism
- Maximum 3 retry attempts with exponential backoff

### Transaction Timeouts
- Payments expire after 30 minutes
- Expired payments are automatically cancelled
- Users can retry with new payment initiation

## Security

### Webhook Verification
- All webhooks are verified using HMAC signature
- Invalid signatures are rejected
- Source IP validation (optional)

### Data Protection
- Sensitive payment data is encrypted
- PCI compliance measures implemented
- Audit logging for all payment operations

## Testing

### Test Environment
Use KCP staging environment for testing:
```env
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_TEST_MODE=true
```

### Test Cards
KCP provides test card numbers for staging environment testing.

## Monitoring and Logging

### Payment Analytics
- Transaction success/failure rates
- Payment method usage statistics
- Revenue tracking

### Error Monitoring
- Failed payment notifications
- Webhook processing errors
- System performance metrics

### Audit Logging
- All payment operations are logged
- User actions are tracked
- Compliance reporting available

## Deployment

### Database Migration
Run the payment gateway migration:
```bash
npm run migration:run
```

### Environment Setup
1. Configure KCP credentials
2. Set up webhook endpoints
3. Configure SSL certificates
4. Test payment flows

### Production Checklist
- [ ] KCP production credentials configured
- [ ] Webhook endpoints accessible
- [ ] SSL certificates valid
- [ ] Payment flows tested
- [ ] Error monitoring configured
- [ ] Backup procedures in place

## Troubleshooting

### Common Issues

1. **Payment Initiation Fails**
   - Check KCP credentials
   - Verify API connectivity
   - Review request parameters

2. **Webhook Not Received**
   - Check webhook URL accessibility
   - Verify SSL certificate
   - Review firewall settings

3. **Payment Status Not Updated**
   - Check webhook processing logs
   - Verify signature validation
   - Review database connectivity

### Support Contacts
- KCP Technical Support: [KCP Support Portal]
- Internal Development Team: [Team Contact]

## Future Enhancements

### Planned Features
- Recurring payment support
- Partial refund functionality
- Multi-currency support
- Enhanced analytics dashboard

### Integration Roadmap
- Additional payment gateways
- Mobile SDK integration
- Real-time payment notifications
- Advanced fraud detection
