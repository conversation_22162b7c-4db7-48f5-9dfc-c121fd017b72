import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { ApiResponse, ErrorDetail } from '../dto/api-response.dto';
import { ValidationError } from 'class-validator';

/**
 * Global exception filter that catches all exceptions and formats them into a unified response structure
 */
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    // Generate a unique error reference ID for tracking
    const errorRefId = `ERR-${Date.now().toString(36)}-${Math.floor(Math.random() * 100000)
      .toString()
      .padStart(5, '0')}`.toUpperCase();

    let statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let errors: ErrorDetail[] = [];

    // Handle HttpExceptions (including NestJS built-in exceptions)
    if (exception instanceof HttpException) {
      statusCode = exception.getStatus();
      const exceptionResponse = exception.getResponse();

      // Extract message and errors from the exception response
      if (typeof exceptionResponse === 'object') {
        const exceptionObj = exceptionResponse as Record<string, any>;

        // Use the exception message if available
        message = exceptionObj.message || exception.message;

        // Handle validation errors from class-validator
        if (Array.isArray(exceptionObj.message) && statusCode === HttpStatus.BAD_REQUEST) {
          message = 'Validation failed';

          // Process validation errors
          errors = this.processValidationErrors(exceptionObj.message);
        }
        // Handle custom validation errors format
        else if (exceptionObj.errors || exceptionObj.validationErrors || (exceptionObj.error && exceptionObj.error.details)) {
          // First check for error.details structure (used in WaterfallAdminService)
          if (exceptionObj.error && exceptionObj.error.details) {
            const details = exceptionObj.error.details;
            if (typeof details === 'object' && !Array.isArray(details)) {
              // Handle object format: { field: ['error1', 'error2'] }
              errors = this.processObjectValidationErrors(details);
              // Log for debugging
              this.logger.debug(`Processing validation errors from error.details: ${JSON.stringify(details)}`);
            }
          }
          // Then check for other validation error formats
          else {
            const validationErrors = exceptionObj.errors || exceptionObj.validationErrors;

            if (typeof validationErrors === 'object' && !Array.isArray(validationErrors)) {
              // Handle object format: { field: ['error1', 'error2'] }
              errors = this.processObjectValidationErrors(validationErrors);
            } else if (Array.isArray(validationErrors)) {
              // Handle array format
              errors = validationErrors.map((err) => new ErrorDetail(err.error || err.message, err.field));
            }
          }
        }
      } else {
        // If exceptionResponse is a string, use it as the message
        message = exceptionResponse as string;
      }
    } else if (exception instanceof Error) {
      // Handle standard JavaScript errors
      message = exception.message;

      // Log the stack trace for debugging
      this.logger.error(`Unhandled exception: ${exception.message}`, exception.stack);
    }

    // Log the error with reference ID
    this.logger.error(`[${errorRefId}] ${request.method} ${request.url} - ${statusCode} - ${message}`, exception.stack);

    // Format validation errors as a Record<string, string[]> for consistent response format
    let validationErrors: Record<string, string[]> | null = null;

    if (errors.length > 0) {
      validationErrors = {};
      errors.forEach((error) => {
        const field = error.field || 'general';
        if (!validationErrors![field]) {
          validationErrors![field] = [];
        }
        validationErrors![field].push(error.error);
      });
    }

    // Check if the exception has validationErrors directly
    let directValidationErrors = null;
    if (exception instanceof HttpException) {
      const exceptionResponse = exception.getResponse();
      if (typeof exceptionResponse === 'object') {
        const exceptionObj = exceptionResponse as Record<string, any>;

        // Check for validationErrors property
        if (exceptionObj.validationErrors) {
          directValidationErrors = exceptionObj.validationErrors;
        }
        // Check for error.details structure (used in WaterfallAdminService)
        else if (exceptionObj.error && exceptionObj.error.details) {
          directValidationErrors = exceptionObj.error.details;
          this.logger.debug(`Found validation errors in error.details: ${JSON.stringify(directValidationErrors)}`);
        }
      }
    }

    // Create the response using our ApiResponse class
    const responseBody = {
      success: false,
      message: validationErrors || directValidationErrors ? 'Validation failed' : message,
      statusCode,
      data: null,
      error: {
        type: exception instanceof HttpException ? exception.name : 'Error',
        status: statusCode,
        refId: errorRefId
      },
      timestamp: new Date().toISOString(),
      path: request.url
    };

    // Add validation errors if present
    if (directValidationErrors) {
      responseBody['validationErrors'] = directValidationErrors;
      // Log the validation errors for debugging
      this.logger.debug(`Validation errors: ${JSON.stringify(directValidationErrors)}`);
    } else if (validationErrors) {
      responseBody['validationErrors'] = validationErrors;
    }

    // Send the response
    response.status(statusCode).json(responseBody);
  }

  /**
   * Process validation errors from class-validator
   * @param validationErrors Array of validation error messages
   * @returns Array of ErrorDetail objects
   */
  private processValidationErrors(validationErrors: any[]): ErrorDetail[] {
    const errors: ErrorDetail[] = [];

    validationErrors.forEach((error) => {
      if (typeof error === 'string') {
        // Handle simple string errors
        if (error.includes(':')) {
          // Format: "field: error message"
          const [field, errorMessage] = error.split(':', 2);
          errors.push(new ErrorDetail(errorMessage.trim(), field.trim()));
        }
        // Format: "property property should not exist"
        else if (error.includes('property') && error.includes('should not exist')) {
          // Extract the property name from the error message
          const match = error.match(/property\s+([^\s]+)\s+should/);
          if (match && match[1]) {
            errors.push(new ErrorDetail(error.trim(), match[1]));
          } else {
            // Fallback if we can't extract the field name
            errors.push(new ErrorDetail(error.trim()));
          }
        } else {
          // No field specified
          errors.push(new ErrorDetail(error));
        }
      } else if (typeof error === 'object' && error !== null) {
        // Handle object format from class-validator
        if (error.property && error.constraints) {
          // Extract all constraint messages
          Object.values(error.constraints).forEach((constraint) => {
            errors.push(new ErrorDetail(constraint as string, error.property));
          });
        }
        // Handle whitelist validation errors
        else if (error.children && error.children.length > 0) {
          // Process nested validation errors recursively
          this.processNestedValidationErrors(error, errors);
        }
      }
    });

    return errors;
  }

  /**
   * Process nested validation errors
   * @param error The error object with children
   * @param errors The errors array to populate
   * @param prefix Optional prefix for nested property names
   */
  private processNestedValidationErrors(error: any, errors: ErrorDetail[], prefix: string = ''): void {
    if (error.children && error.children.length > 0) {
      error.children.forEach((child: any) => {
        const nestedPrefix = prefix ? `${prefix}.${child.property}` : child.property;

        if (child.constraints) {
          Object.values(child.constraints).forEach((constraint: any) => {
            errors.push(new ErrorDetail(constraint as string, nestedPrefix));
          });
        }

        if (child.children && child.children.length > 0) {
          this.processNestedValidationErrors(child, errors, nestedPrefix);
        }
      });
    }
  }

  /**
   * Process object-based validation errors
   * @param validationErrors Object with field names as keys and error messages as values
   * @returns Array of ErrorDetail objects
   */
  private processObjectValidationErrors(validationErrors: Record<string, string[]>): ErrorDetail[] {
    const errors: ErrorDetail[] = [];

    Object.entries(validationErrors).forEach(([field, fieldErrors]) => {
      if (Array.isArray(fieldErrors)) {
        fieldErrors.forEach((error) => {
          errors.push(new ErrorDetail(error, field));
        });
      } else if (typeof fieldErrors === 'string') {
        errors.push(new ErrorDetail(fieldErrors, field));
      }
    });

    return errors;
  }
}
