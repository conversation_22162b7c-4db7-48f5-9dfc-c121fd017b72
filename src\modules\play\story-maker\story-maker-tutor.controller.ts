import { Controller, Post, Get, Body, Query, UseGuards, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiBody, ApiQuery, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { TutorGuard } from '../../../common/guards/tutor.guard';
import { StoryMakerTutorService } from './story-maker-tutor.service';
import { GetUser } from '../../../common/decorators/get-user.decorator';
import { User } from '../../../database/entities/user.entity';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../../common/decorators/api-response.decorator';

@ApiTags('Play-StoryMaker')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TutorGuard)
@Controller('play/story-maker/tutor')
export class StoryMakerTutorController {
  constructor(private readonly storyMakerTutorService: StoryMakerTutorService) {}

  @Get('submissions')
  @ApiOperation({
    summary: 'Get submissions for evaluation',
    description: 'Returns a list of story submissions assigned to the tutor for evaluation. Can filter by evaluation status.',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term for student name, email, or story title', example: 'john' })
  @ApiQuery({
    name: 'isEvaluated',
    required: false,
    type: Boolean,
    description: 'Filter by evaluation status (false to find submissions waiting for evaluation)',
    example: false,
  })
  @ApiOkResponseWithType(Object, 'Submissions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  async getSubmissions(@GetUser() user: User, @Query() query: { page?: number; limit?: number; search?: string; isEvaluated?: boolean }): Promise<ApiResponse<any>> {
    const result = await this.storyMakerTutorService.getSubmissionsForTutor(user.id, query);

    const message = result.submissions.length === 0 ? 'No submissions found' : 'Submissions retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Get('submissions/:id')
  @ApiOperation({
    summary: 'Get a specific submission by ID',
    description: 'Returns detailed information about a specific story submission, including student details, story details, and evaluation status.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the submission to retrieve',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiOkResponseWithType(Object, 'Submission retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or not assigned to this student')
  @ApiErrorResponse(404, 'Submission not found')
  async getSubmissionById(@GetUser() user: User, @Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<any>> {
    const result = await this.storyMakerTutorService.getSubmissionById(id, user.id);
    return ApiResponse.success(result, 'Submission retrieved successfully');
  }

  @Post('submissions/:id/evaluate')
  @ApiOperation({
    summary: 'Evaluate a submission',
    description: 'Evaluates a story submission with corrections, feedback, and score (score is required for first submissions only).',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the submission to evaluate',
    type: String,
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        corrections: {
          type: 'string',
          example: '<p>Once upon a time in a magical forest, there lived a wise old owl...</p>',
          description: 'Corrections to the submission in rich text format',
        },
        feedback: {
          type: 'string',
          example: 'Great use of descriptive language and creative storyline.',
          description: 'Optional feedback on the submission',
        },
        score: {
          type: 'number',
          example: 45,
          description: 'Score for the submission (required for first submissions only)',
        },
      },
      required: ['corrections'],
    },
  })
  @ApiOkResponseWithType(Object, 'Submission evaluated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  @ApiErrorResponse(404, 'Submission not found')
  async evaluateSubmission(
    @GetUser() user: User,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() evaluationDto: { corrections: string; feedback?: string; score?: number },
  ): Promise<ApiResponse<any>> {
    const result = await this.storyMakerTutorService.evaluateSubmission(id, user.id, evaluationDto.corrections, evaluationDto.feedback, evaluationDto.score);

    return ApiResponse.success(result, 'Submission evaluated successfully');
  }
}
