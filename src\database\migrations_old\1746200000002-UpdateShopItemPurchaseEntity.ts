import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateShopItemPurchaseEntity1746200000002 implements MigrationInterface {
    name = 'UpdateShopItemPurchaseEntity1746200000002'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD "reward_points_used" decimal(10,2) NOT NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD "quantity" integer NOT NULL DEFAULT 1`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP COLUMN "quantity"`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP COLUMN "reward_points_used"`);
    }
}
