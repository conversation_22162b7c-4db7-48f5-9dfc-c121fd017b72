# HEC Chat System - Frontend Integration Guide

The HEC Chat System provides real-time messaging capabilities between students, tutors, and administrators. This document outlines the integration points, API endpoints, and Socket.io events needed to implement the chat functionality in the frontend application.

## Table of Contents

1. [Server Information](#server-information)
2. [Overview](#overview)
3. [Authentication](#authentication)
4. [REST API Integration](#rest-api-integration)
5. [WebSocket Integration](#websocket-integration)
6. [Communication Flow Diagrams](#communication-flow-diagrams)
7. [File Upload](#file-upload)
8. [Common Use Cases](#common-use-cases)
9. [Error Handling](#error-handling)
10. [Data Models](#data-models)

## Server Information

- **Socket.io Server URL**: `http://**************:3010`
- **Socket.io Namespace**: `/chat`
- **REST API Base URL**: Same as your main application API
- **API Prefix**: `/api/chat`

## Overview

The HEC Chat System provides realtime messaging capabilities between students, tutors, and administrators. It consists of:

- REST API endpoints for managing conversations and messages
- WebSocket gateway for realtime communication
- File upload functionality for sharing images and documents

## Authentication

All chat API endpoints and WebSocket connections require authentication using JWT tokens.

### REST API Authentication

Include the JWT token in the `Authorization` header with the `Bearer` prefix:

```javascript
const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
};

fetch('/api/chat/conversations', { headers })
  .then(response => response.json())
  .then(data => console.log(data));
```

### WebSocket Authentication

Provide the JWT token in the `auth` object when establishing a WebSocket connection:

```javascript
import { io } from 'socket.io-client';

const socket = io('http://**************:3010/chat', {
  auth: { token: 'your-jwt-token' },
  transports: ['websocket', 'polling'],
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000
});

socket.on('connect', () => {
  console.log('Connected to chat server');
});

socket.on('connected', (data) => {
  console.log('Connection confirmed for user:', data.userId);
});

socket.on('connect_error', (error) => {
  console.error('Connection error:', error);
});
```

## REST API Integration

### Fetching Conversations

To get all conversations for the current user:

```javascript
// GET /chat/conversations
fetch('/api/chat/conversations', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    const conversations = data.data.items;
    // Process conversations
  }
});
```

You can also filter and sort conversations:

```javascript
// GET /chat/conversations?page=1&limit=10&sortBy=lastMessageAt&sortOrder=DESC
fetch('/api/chat/conversations?page=1&limit=10&sortBy=lastMessageAt&sortOrder=DESC', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    const conversations = data.data.items;
    const total = data.data.total;
    // Process conversations
  }
});
```

### Fetching Messages

To get messages for a specific conversation:

```javascript
// GET /chat/conversations/:id/messages
fetch(`/api/chat/conversations/${conversationId}/messages?page=1&limit=20`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    const messages = data.data.items;
    // Process messages
  }
});
```

### Sending Messages

To send a text message:

```javascript
// POST /chat/messages
fetch('/api/chat/messages', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    recipientId: 'recipient-user-id',
    content: 'Hello, world!',
    type: 'text'
  })
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    const message = data.data;
    // Message sent successfully
  }
});
```

To send a message with attachments:

```javascript
// POST /chat/messages
fetch('/api/chat/messages', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    recipientId: 'recipient-user-id',
    content: 'Check out this file',
    type: 'file',
    attachmentIds: ['attachment-id-1', 'attachment-id-2']
  })
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    const message = data.data;
    // Message with attachments sent successfully
  }
});
```

### Getting Chat Contacts

To get available chat contacts:

```javascript
// GET /chat/contacts
fetch('/api/chat/contacts', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    const contacts = data.data;
    // Process contacts
  }
});
```

## WebSocket Integration

### WebSocket Events Overview

The Socket.io implementation follows a request-response pattern for most operations. Here's how the communication typically flows:

1. **Frontend emits an event** to the server with relevant data
2. **Server processes the request** and performs necessary database operations
3. **Server emits response events** to the appropriate clients
4. **Frontend listens for these events** and updates the UI accordingly

### Connection Events

| Event Name | Direction | Description | Payload |
|------------|-----------|-------------|---------|
| `connect` | Client ← Server | Connection established | N/A |
| `connected` | Client ← Server | Connection confirmed with user details | `{ userId: string, name: string }` |
| `connect_error` | Client ← Server | Connection error | Error object |
| `user_status` | Client ← Server | User online/offline status update | `{ userId: string, isOnline: boolean, timestamp: Date }` |

### Establishing Connection

```javascript
import { io } from 'socket.io-client';

const socket = io('http://**************:3010/chat', {
  auth: { token: 'your-jwt-token' },
  transports: ['websocket', 'polling'],
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000
});

socket.on('connect', () => {
  console.log('Connected to chat server');
});

socket.on('connected', (data) => {
  console.log('Connection confirmed for user:', data.userId);
});

socket.on('disconnect', () => {
  console.log('Disconnected from chat server');
});

socket.on('error', (error) => {
  console.error('Socket error:', error);
});

socket.on('user_status', (data) => {
  console.log(`User ${data.userId} is ${data.isOnline ? 'online' : 'offline'}`);
});
```

### Subscribing to a Conversation

```javascript
// Subscribe to a conversation
socket.emit('subscribe_conversation', { conversationId: 'conversation-id' });

// Listen for subscription confirmation
socket.on('subscribed_conversation', (data) => {
  console.log(`Subscribed to conversation ${data.conversationId}`);
});
```

### Sending Messages via WebSocket

```javascript
// Send a text message
socket.emit('send_message', {
  recipientId: 'recipient-user-id',
  content: 'Hello via WebSocket!',
  type: 'text'
});

// Send a message with attachments
socket.emit('send_message', {
  recipientId: 'recipient-user-id',
  content: 'Check out this file',
  type: 'file',
  attachmentIds: ['attachment-id-1']
});
```

### Receiving Messages

```javascript
// Listen for new messages
socket.on('new_message', (message) => {
  console.log('New message received:', message);
  // Add message to UI
});
```

### Typing Indicators

```javascript
// Send typing indicator
function handleTyping(isTyping) {
  socket.emit('typing', {
    conversationId: 'conversation-id',
    isTyping: isTyping
  });
}

// Listen for typing indicators
socket.on('typing_indicator', (data) => {
  console.log(`User ${data.userId} is ${data.isTyping ? 'typing' : 'not typing'}`);
  // Update UI to show typing indicator
});
```

### Read Receipts

```javascript
// Mark messages as read
socket.emit('mark_read', { conversationId: 'conversation-id' });

// Listen for read receipts
socket.on('messages_read', (data) => {
  console.log(`Messages in conversation ${data.conversationId} were read by user ${data.userId}`);
  // Update message status in UI
});

// Listen for delivery receipts
socket.on('messages_delivered', (data) => {
  console.log(`Messages in conversation ${data.conversationId} were delivered to user ${data.userId}`);
  // Update message status in UI
});
```

### User Status

```javascript
// Listen for user status changes
socket.on('user_status', (data) => {
  console.log(`User ${data.userId} is ${data.isOnline ? 'online' : 'offline'}`);
  // Update user status in UI
});
```

### Unsubscribing from a Conversation

```javascript
// Unsubscribe from a conversation
socket.emit('unsubscribe_conversation', { conversationId: 'conversation-id' });

// Listen for unsubscription confirmation
socket.on('unsubscribed_conversation', (data) => {
  console.log(`Unsubscribed from conversation ${data.conversationId}`);
});
```

## Communication Flow Diagrams

### Connection Flow

```
Frontend                                  Backend
   |                                         |
   |------ connect (with JWT token) -------->|
   |                                         | [Verify token]
   |                                         | [Load user]
   |                                         | [Join user room]
   |<----------- connected event -----------|
   |                                         | [Broadcast user status]
   |<---------- user_status event ----------|
   |                                         |
```

### Conversation Subscription Flow

```
Frontend                                  Backend
   |                                         |
   |------ subscribe_conversation event ---->|
   |                                         | [Verify user is participant]
   |                                         | [Join conversation room]
   |                                         | [Mark messages as delivered]
   |<------ subscribed_conversation event ---|
   |<-------- messages_delivered event ------|
   |                                         |
```

### Message Sending Flow

```
Frontend                                  Backend
   |                                         |
   |---------- send_message event ---------->|
   |                                         | [Verify users can chat]
   |                                         | [Get/create conversation]
   |                                         | [Save message to database]
   |                                         | [Process attachments]
   |                                         | [Update conversation]
   |                                         | [Send notifications]
   |<----------- new_message event ----------|
   |                                         |
```

### Typing Indicator Flow

```
Frontend                                  Backend
   |                                         |
   |----------- typing event (true) -------->|
   |                                         | [Set typing timeout]
   |<--------- typing_indicator event -------|
   |                                         | [After 5s if no new event]
   |<--------- typing_indicator event -------|
   |            (isTyping: false)            |
   |                                         |
```

### Read Receipt Flow

```
Frontend                                  Backend
   |                                         |
   |----------- mark_read event ------------>|
   |                                         | [Update message status]
   |                                         | [Update unread counts]
   |<---------- messages_read event ---------|
   |                                         |
```

## File Upload

### Uploading Files

```javascript
// Create a FormData object
const formData = new FormData();
formData.append('file', fileObject);

// Upload the file
fetch('/api/chat/upload', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    const fileId = data.data.id;
    const fileUrl = data.data.fileUrl;

    // Now you can use the fileId in a message
    socket.emit('send_message', {
      recipientId: 'recipient-user-id',
      content: 'Check out this file',
      type: 'file',
      attachmentIds: [fileId]
    });
  }
});
```

### Uploading with Progress Tracking

```javascript
function uploadFileWithProgress(file, token, onProgress, onComplete, onError) {
  const xhr = new XMLHttpRequest();
  const formData = new FormData();

  formData.append('file', file);

  xhr.open('POST', '/api/chat/upload');
  xhr.setRequestHeader('Authorization', `Bearer ${token}`);

  xhr.upload.onprogress = (event) => {
    if (event.lengthComputable) {
      const percentComplete = Math.round((event.loaded / event.total) * 100);
      onProgress(percentComplete);
    }
  };

  xhr.onload = () => {
    if (xhr.status === 200) {
      const response = JSON.parse(xhr.responseText);
      if (response.success) {
        onComplete(response.data);
      } else {
        onError(response.message || 'Upload failed');
      }
    } else {
      onError(`Upload failed with status ${xhr.status}`);
    }
  };

  xhr.onerror = () => {
    onError('Upload failed');
  };

  xhr.send(formData);
}

// Usage
uploadFileWithProgress(
  fileObject,
  token,
  (progress) => {
    console.log(`Upload progress: ${progress}%`);
    // Update progress bar
  },
  (data) => {
    console.log('Upload complete:', data);
    // Use the file ID in a message
  },
  (error) => {
    console.error('Upload error:', error);
    // Show error message
  }
);
```

## Common Use Cases

### Building a Chat Interface

1. **Fetch and display conversations**:
   ```javascript
   fetch('/api/chat/conversations', {
     headers: { 'Authorization': `Bearer ${token}` }
   })
   .then(response => response.json())
   .then(data => {
     if (data.success) {
       renderConversationList(data.data.items);
     }
   });
   ```

2. **Select and load a conversation**:
   ```javascript
   function selectConversation(conversationId) {
     // Fetch messages
     fetch(`/api/chat/conversations/${conversationId}/messages`, {
       headers: { 'Authorization': `Bearer ${token}` }
     })
     .then(response => response.json())
     .then(data => {
       if (data.success) {
         renderMessages(data.data.items);

         // Subscribe to the conversation via WebSocket
         socket.emit('subscribe_conversation', { conversationId });

         // Mark messages as read
         socket.emit('mark_read', { conversationId });
       }
     });
   }
   ```

3. **Implement message pagination**:
   ```javascript
   let currentPage = 1;
   const messagesPerPage = 20;

   function loadMoreMessages(conversationId) {
     currentPage++;

     fetch(`/api/chat/conversations/${conversationId}/messages?page=${currentPage}&limit=${messagesPerPage}`, {
       headers: { 'Authorization': `Bearer ${token}` }
     })
     .then(response => response.json())
     .then(data => {
       if (data.success) {
         // Prepend older messages to the top of the message list
         prependMessages(data.data.items);

         // Check if there are more messages to load
         if (data.data.items.length < messagesPerPage) {
           hideLoadMoreButton();
         }
       }
     });
   }
   ```

4. **Send a message with attachment**:
   ```javascript
   // First upload the file
   const formData = new FormData();
   formData.append('file', fileObject);

   fetch('/api/chat/upload', {
     method: 'POST',
     headers: { 'Authorization': `Bearer ${token}` },
     body: formData
   })
   .then(response => response.json())
   .then(data => {
     if (data.success) {
       // Then send the message with the file attachment
       socket.emit('send_message', {
         recipientId: recipientId,
         content: messageText || 'Sent an attachment',
         type: fileObject.type.startsWith('image/') ? 'image' : 'file',
         attachmentIds: [data.data.id]
       });
     }
   });
   ```

### Handling Different Message Types

```javascript
function renderMessage(message) {
  let messageContent;

  switch (message.type) {
    case 'text':
      messageContent = `<div class="text-message">${escapeHtml(message.content)}</div>`;
      break;

    case 'image':
      if (message.attachments && message.attachments.length > 0) {
        const attachment = message.attachments[0];
        messageContent = `
          <div class="image-message">
            <img src="${attachment.fileUrl}" alt="${attachment.fileName}" />
            <div class="caption">${escapeHtml(message.content)}</div>
          </div>
        `;
      }
      break;

    case 'file':
      if (message.attachments && message.attachments.length > 0) {
        const attachment = message.attachments[0];
        messageContent = `
          <div class="file-message">
            <a href="${attachment.fileUrl}" target="_blank" class="file-link">
              <span class="file-icon">📄</span>
              <span class="file-name">${escapeHtml(attachment.fileName)}</span>
              <span class="file-size">${formatFileSize(attachment.fileSize)}</span>
            </a>
            <div class="caption">${escapeHtml(message.content)}</div>
          </div>
        `;
      }
      break;

    case 'quiz':
      if (message.metadata && message.metadata.quizId) {
        messageContent = `
          <div class="quiz-message">
            <a href="/quiz/${message.metadata.quizId}" class="quiz-link">
              <span class="quiz-icon">📝</span>
              <span class="quiz-title">${escapeHtml(message.content)}</span>
            </a>
          </div>
        `;
      }
      break;

    case 'system':
      messageContent = `<div class="system-message">${escapeHtml(message.content)}</div>`;
      break;

    default:
      messageContent = `<div class="unknown-message">${escapeHtml(message.content)}</div>`;
  }

  return `
    <div class="message ${message.senderId === currentUserId ? 'outgoing' : 'incoming'}">
      <div class="message-content">
        ${messageContent}
      </div>
      <div class="message-meta">
        <span class="message-time">${formatTime(message.createdAt)}</span>
        ${message.senderId === currentUserId ? getStatusIcon(message.status) : ''}
      </div>
    </div>
  `;
}

function getStatusIcon(status) {
  switch (status) {
    case 'sent':
      return '<span class="status-icon sent">✓</span>';
    case 'delivered':
      return '<span class="status-icon delivered">✓✓</span>';
    case 'read':
      return '<span class="status-icon read">✓✓</span>';
    default:
      return '';
  }
}

function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function formatFileSize(bytes) {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
  return (bytes / 1048576).toFixed(1) + ' MB';
}

function formatTime(dateString) {
  const date = new Date(dateString);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}
```

## Error Handling

### REST API Errors

```javascript
fetch('/api/chat/conversations', {
  headers: { 'Authorization': `Bearer ${token}` }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    // Process successful response
    const conversations = data.data.items;
    renderConversations(conversations);
  } else {
    // Handle API error
    console.error('API Error:', data.message);
    showErrorMessage(data.message || 'An error occurred');
  }
})
.catch(error => {
  // Handle network or parsing error
  console.error('Network Error:', error);
  showErrorMessage('Network error. Please check your connection.');
});
```

## Data Models

### ConversationDto

```typescript
{
  id: string;
  type: 'direct';
  participants: {
    id: string;
    userId: string;
    name: string;
    profilePicture?: string;
  }[];
  lastMessage?: {
    id: string;
    content: string;
    senderId: string;
    type: 'text' | 'file' | 'image';
    createdAt: string;
  };
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
  lastMessageAt: string;
  status: 'active' | 'archived';
}
```

### MessageDto

```typescript
{
  id: string;
  conversationId: string;
  senderId: string;
  recipientId: string;
  senderName: string;
  senderProfilePicture?: string;
  type: 'text' | 'file' | 'image';
  content: string;
  metadata?: {
    fileId?: string;
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
    url?: string;
    width?: number;
    height?: number;
  };
  status: 'sent' | 'delivered' | 'read';
  createdAt: string;
  updatedAt: string;
  attachments?: {
    id: string;
    fileName: string;
    fileSize: number;
    mimeType: string;
    url: string;
  }[];
}
```

### WebSocket Errors

```javascript
// Listen for connection errors
socket.on('connect_error', (error) => {
  console.error('Connection error:', error);
  showErrorMessage('Could not connect to chat server');
});

// Listen for general errors
socket.on('error', (error) => {
  console.error('Socket error:', error);
  showErrorMessage(error.message || 'An error occurred');
});

// Handle reconnection
socket.io.on('reconnect_attempt', (attempt) => {
  console.log(`Reconnection attempt ${attempt}`);
  showReconnectingMessage(`Reconnecting... (Attempt ${attempt})`);
});

socket.io.on('reconnect', () => {
  console.log('Reconnected to server');
  hideReconnectingMessage();
  showSuccessMessage('Reconnected to chat server');

  // Re-subscribe to active conversation
  if (activeConversationId) {
    socket.emit('subscribe_conversation', { conversationId: activeConversationId });
  }
});

socket.io.on('reconnect_failed', () => {
  console.error('Failed to reconnect');
  showErrorMessage('Failed to reconnect to chat server. Please refresh the page.');
});
```

### File Upload Errors

```javascript
function uploadFile(file) {
  const formData = new FormData();
  formData.append('file', file);

  // Show loading state
  showUploadingState();

  fetch('/api/chat/upload', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` },
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    // Hide loading state
    hideUploadingState();

    if (data.success) {
      // Handle successful upload
      handleSuccessfulUpload(data.data);
    } else {
      // Handle API error
      console.error('Upload Error:', data.message);
      showErrorMessage(data.message || 'Failed to upload file');
    }
  })
  .catch(error => {
    // Hide loading state
    hideUploadingState();

    // Handle network or parsing error
    console.error('Upload Network Error:', error);
    showErrorMessage('Network error. Please check your connection.');
  });
}
```
