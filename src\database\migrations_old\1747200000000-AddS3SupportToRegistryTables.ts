import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddS3SupportToRegistryTables1747200000000 implements MigrationInterface {
  name = 'AddS3SupportToRegistryTables1747200000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add S3 support columns to profile_picture_registry
    await queryRunner.query(`
      ALTER TABLE "profile_picture_registry" 
      ADD COLUMN "storage_provider" VARCHAR(10) NOT NULL DEFAULT 'local',
      ADD COLUMN "storage_key" VARCHAR(500),
      ADD COLUMN "s3_bucket" VARCHAR(100),
      ADD COLUMN "s3_region" VARCHAR(50),
      ADD COLUMN "s3_etag" VARCHAR(100),
      ADD COLUMN "s3_version_id" VARCHAR(100),
      ADD COLUMN "s3_storage_class" VARCHAR(50),
      ADD COLUMN "s3_server_side_encryption" VARCHAR(50),
      ADD COLUMN "cdn_url" VARCHAR(500),
      ADD COLUMN "presigned_url_expires_at" TIMESTAMP,
      ADD COLUMN "storage_metadata" JSONB,
      ADD COLUMN "is_migrated" BOOLEAN NOT NULL DEFAULT false,
      ADD COLUMN "migration_status" VARCHAR(20),
      ADD COLUMN "migration_error" TEXT,
      ADD COLUMN "migrated_at" TIMESTAMP
    `);

    // Add S3 support columns to shop_item_registry
    await queryRunner.query(`
      ALTER TABLE "shop_item_registry" 
      ADD COLUMN "storage_provider" VARCHAR(10) NOT NULL DEFAULT 'local',
      ADD COLUMN "storage_key" VARCHAR(500),
      ADD COLUMN "s3_bucket" VARCHAR(100),
      ADD COLUMN "s3_region" VARCHAR(50),
      ADD COLUMN "s3_etag" VARCHAR(100),
      ADD COLUMN "s3_version_id" VARCHAR(100),
      ADD COLUMN "s3_storage_class" VARCHAR(50),
      ADD COLUMN "s3_server_side_encryption" VARCHAR(50),
      ADD COLUMN "cdn_url" VARCHAR(500),
      ADD COLUMN "presigned_url_expires_at" TIMESTAMP,
      ADD COLUMN "storage_metadata" JSONB,
      ADD COLUMN "is_migrated" BOOLEAN NOT NULL DEFAULT false,
      ADD COLUMN "migration_status" VARCHAR(20),
      ADD COLUMN "migration_error" TEXT,
      ADD COLUMN "migrated_at" TIMESTAMP
    `);

    // Add S3 support columns to diary_skin_registry
    await queryRunner.query(`
      ALTER TABLE "diary_skin_registry" 
      ADD COLUMN "storage_provider" VARCHAR(10) NOT NULL DEFAULT 'local',
      ADD COLUMN "storage_key" VARCHAR(500),
      ADD COLUMN "s3_bucket" VARCHAR(100),
      ADD COLUMN "s3_region" VARCHAR(50),
      ADD COLUMN "s3_etag" VARCHAR(100),
      ADD COLUMN "s3_version_id" VARCHAR(100),
      ADD COLUMN "s3_storage_class" VARCHAR(50),
      ADD COLUMN "s3_server_side_encryption" VARCHAR(50),
      ADD COLUMN "cdn_url" VARCHAR(500),
      ADD COLUMN "presigned_url_expires_at" TIMESTAMP,
      ADD COLUMN "storage_metadata" JSONB,
      ADD COLUMN "is_migrated" BOOLEAN NOT NULL DEFAULT false,
      ADD COLUMN "migration_status" VARCHAR(20),
      ADD COLUMN "migration_error" TEXT,
      ADD COLUMN "migrated_at" TIMESTAMP
    `);

    // Add S3 support columns to diary_qr_registry
    await queryRunner.query(`
      ALTER TABLE "diary_qr_registry" 
      ADD COLUMN "storage_provider" VARCHAR(10) NOT NULL DEFAULT 'local',
      ADD COLUMN "storage_key" VARCHAR(500),
      ADD COLUMN "s3_bucket" VARCHAR(100),
      ADD COLUMN "s3_region" VARCHAR(50),
      ADD COLUMN "s3_etag" VARCHAR(100),
      ADD COLUMN "s3_version_id" VARCHAR(100),
      ADD COLUMN "s3_storage_class" VARCHAR(50),
      ADD COLUMN "s3_server_side_encryption" VARCHAR(50),
      ADD COLUMN "cdn_url" VARCHAR(500),
      ADD COLUMN "presigned_url_expires_at" TIMESTAMP,
      ADD COLUMN "storage_metadata" JSONB,
      ADD COLUMN "is_migrated" BOOLEAN NOT NULL DEFAULT false,
      ADD COLUMN "migration_status" VARCHAR(20),
      ADD COLUMN "migration_error" TEXT,
      ADD COLUMN "migrated_at" TIMESTAMP
    `);

    // Add S3 support columns to message_registry
    await queryRunner.query(`
      ALTER TABLE "message_registry" 
      ADD COLUMN "storage_provider" VARCHAR(10) NOT NULL DEFAULT 'local',
      ADD COLUMN "storage_key" VARCHAR(500),
      ADD COLUMN "s3_bucket" VARCHAR(100),
      ADD COLUMN "s3_region" VARCHAR(50),
      ADD COLUMN "s3_etag" VARCHAR(100),
      ADD COLUMN "s3_version_id" VARCHAR(100),
      ADD COLUMN "s3_storage_class" VARCHAR(50),
      ADD COLUMN "s3_server_side_encryption" VARCHAR(50),
      ADD COLUMN "cdn_url" VARCHAR(500),
      ADD COLUMN "presigned_url_expires_at" TIMESTAMP,
      ADD COLUMN "storage_metadata" JSONB,
      ADD COLUMN "is_migrated" BOOLEAN NOT NULL DEFAULT false,
      ADD COLUMN "migration_status" VARCHAR(20),
      ADD COLUMN "migration_error" TEXT,
      ADD COLUMN "migrated_at" TIMESTAMP
    `);

    // Add S3 support columns to story_maker_registry
    await queryRunner.query(`
      ALTER TABLE "story_maker_registry" 
      ADD COLUMN "storage_provider" VARCHAR(10) NOT NULL DEFAULT 'local',
      ADD COLUMN "storage_key" VARCHAR(500),
      ADD COLUMN "s3_bucket" VARCHAR(100),
      ADD COLUMN "s3_region" VARCHAR(50),
      ADD COLUMN "s3_etag" VARCHAR(100),
      ADD COLUMN "s3_version_id" VARCHAR(100),
      ADD COLUMN "s3_storage_class" VARCHAR(50),
      ADD COLUMN "s3_server_side_encryption" VARCHAR(50),
      ADD COLUMN "cdn_url" VARCHAR(500),
      ADD COLUMN "presigned_url_expires_at" TIMESTAMP,
      ADD COLUMN "storage_metadata" JSONB,
      ADD COLUMN "is_migrated" BOOLEAN NOT NULL DEFAULT false,
      ADD COLUMN "migration_status" VARCHAR(20),
      ADD COLUMN "migration_error" TEXT,
      ADD COLUMN "migrated_at" TIMESTAMP
    `);

    // Create indexes for better performance
    await queryRunner.query(`CREATE INDEX "IDX_profile_picture_registry_storage_provider" ON "profile_picture_registry" ("storage_provider")`);
    await queryRunner.query(`CREATE INDEX "IDX_profile_picture_registry_storage_key" ON "profile_picture_registry" ("storage_key")`);
    await queryRunner.query(`CREATE INDEX "IDX_profile_picture_registry_migration_status" ON "profile_picture_registry" ("migration_status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_shop_item_registry_storage_provider" ON "shop_item_registry" ("storage_provider")`);
    await queryRunner.query(`CREATE INDEX "IDX_shop_item_registry_storage_key" ON "shop_item_registry" ("storage_key")`);
    await queryRunner.query(`CREATE INDEX "IDX_shop_item_registry_migration_status" ON "shop_item_registry" ("migration_status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_diary_skin_registry_storage_provider" ON "diary_skin_registry" ("storage_provider")`);
    await queryRunner.query(`CREATE INDEX "IDX_diary_skin_registry_storage_key" ON "diary_skin_registry" ("storage_key")`);
    await queryRunner.query(`CREATE INDEX "IDX_diary_skin_registry_migration_status" ON "diary_skin_registry" ("migration_status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_diary_qr_registry_storage_provider" ON "diary_qr_registry" ("storage_provider")`);
    await queryRunner.query(`CREATE INDEX "IDX_diary_qr_registry_storage_key" ON "diary_qr_registry" ("storage_key")`);
    await queryRunner.query(`CREATE INDEX "IDX_diary_qr_registry_migration_status" ON "diary_qr_registry" ("migration_status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_message_registry_storage_provider" ON "message_registry" ("storage_provider")`);
    await queryRunner.query(`CREATE INDEX "IDX_message_registry_storage_key" ON "message_registry" ("storage_key")`);
    await queryRunner.query(`CREATE INDEX "IDX_message_registry_migration_status" ON "message_registry" ("migration_status")`);
    
    await queryRunner.query(`CREATE INDEX "IDX_story_maker_registry_storage_provider" ON "story_maker_registry" ("storage_provider")`);
    await queryRunner.query(`CREATE INDEX "IDX_story_maker_registry_storage_key" ON "story_maker_registry" ("storage_key")`);
    await queryRunner.query(`CREATE INDEX "IDX_story_maker_registry_migration_status" ON "story_maker_registry" ("migration_status")`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.query(`DROP INDEX "IDX_profile_picture_registry_storage_provider"`);
    await queryRunner.query(`DROP INDEX "IDX_profile_picture_registry_storage_key"`);
    await queryRunner.query(`DROP INDEX "IDX_profile_picture_registry_migration_status"`);
    
    await queryRunner.query(`DROP INDEX "IDX_shop_item_registry_storage_provider"`);
    await queryRunner.query(`DROP INDEX "IDX_shop_item_registry_storage_key"`);
    await queryRunner.query(`DROP INDEX "IDX_shop_item_registry_migration_status"`);
    
    await queryRunner.query(`DROP INDEX "IDX_diary_skin_registry_storage_provider"`);
    await queryRunner.query(`DROP INDEX "IDX_diary_skin_registry_storage_key"`);
    await queryRunner.query(`DROP INDEX "IDX_diary_skin_registry_migration_status"`);
    
    await queryRunner.query(`DROP INDEX "IDX_diary_qr_registry_storage_provider"`);
    await queryRunner.query(`DROP INDEX "IDX_diary_qr_registry_storage_key"`);
    await queryRunner.query(`DROP INDEX "IDX_diary_qr_registry_migration_status"`);
    
    await queryRunner.query(`DROP INDEX "IDX_message_registry_storage_provider"`);
    await queryRunner.query(`DROP INDEX "IDX_message_registry_storage_key"`);
    await queryRunner.query(`DROP INDEX "IDX_message_registry_migration_status"`);
    
    await queryRunner.query(`DROP INDEX "IDX_story_maker_registry_storage_provider"`);
    await queryRunner.query(`DROP INDEX "IDX_story_maker_registry_storage_key"`);
    await queryRunner.query(`DROP INDEX "IDX_story_maker_registry_migration_status"`);

    // Remove S3 columns from all registry tables
    const tables = [
      'profile_picture_registry',
      'shop_item_registry', 
      'diary_skin_registry',
      'diary_qr_registry',
      'message_registry',
      'story_maker_registry'
    ];

    for (const table of tables) {
      await queryRunner.query(`
        ALTER TABLE "${table}" 
        DROP COLUMN "storage_provider",
        DROP COLUMN "storage_key",
        DROP COLUMN "s3_bucket",
        DROP COLUMN "s3_region",
        DROP COLUMN "s3_etag",
        DROP COLUMN "s3_version_id",
        DROP COLUMN "s3_storage_class",
        DROP COLUMN "s3_server_side_encryption",
        DROP COLUMN "cdn_url",
        DROP COLUMN "presigned_url_expires_at",
        DROP COLUMN "storage_metadata",
        DROP COLUMN "is_migrated",
        DROP COLUMN "migration_status",
        DROP COLUMN "migration_error",
        DROP COLUMN "migrated_at"
      `);
    }
  }
}
