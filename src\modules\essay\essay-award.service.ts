import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EssayTaskSubmissions, SubmissionStatus } from '../../database/entities/essay-task-submissions.entity';
import { EssayTaskSubmissionMarking } from '../../database/entities/essay-task-submission-marking.entity';
import { EssayMissionTasks } from '../../database/entities/essay-mission-tasks.entity';
import { Award, AwardCriteria } from '../../database/entities/award.entity';
import { AwardsService } from '../awards/awards.service';

@Injectable()
export class EssayAwardService {
  private readonly logger = new Logger(EssayAwardService.name);

  constructor(
    @InjectRepository(EssayTaskSubmissions)
    private essayTaskSubmissionsRepository: Repository<EssayTaskSubmissions>,
    @InjectRepository(EssayTaskSubmissionMarking)
    private essayTaskSubmissionMarkingRepository: Repository<EssayTaskSubmissionMarking>,
    @InjectRepository(EssayMissionTasks)
    private essayMissionTasksRepository: Repository<EssayMissionTasks>,
    @Inject(forwardRef(() => AwardsService))
    private readonly awardsService: AwardsService,
  ) {}

  // Client-required award names for essay module
  private readonly CLIENT_AWARD_NAMES = [
    'Best Writer Award',
    'Best Perfect Award'
  ];

  /**
   * Generate essay awards for a specific date range
   * @param startDate Start date of the period
   * @param endDate End date of the period
   */
  async generateAwardsForRange(startDate: Date, endDate: Date): Promise<void> {
    this.logger.log(`Generating essay awards for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

    try {
      // Get all active essay awards that match client requirements
      const allAwards = await this.awardsService.getActiveAwardsByModule('essay');
      const awards = allAwards.filter(award => this.CLIENT_AWARD_NAMES.includes(award.name));

      if (awards.length === 0) {
        this.logger.log('No client-required essay awards found');
        return;
      }

      this.logger.log(`Found ${awards.length} client-required essay awards: ${awards.map(a => a.name).join(', ')}`);

      // Filter out any non-client awards
      const filteredCount = allAwards.length - awards.length;
      if (filteredCount > 0) {
        this.logger.log(`Filtered out ${filteredCount} non-client awards from calculation`);
      }

      // Get all students who submitted essays in the period
      const studentSubmissions = await this.getStudentSubmissionsInPeriod(startDate, endDate);

      if (studentSubmissions.length === 0) {
        this.logger.log('No essay submissions found in the period');
        return;
      }

      // Process each award
      for (const award of awards) {
        await this.processAward(award, studentSubmissions, startDate, endDate);
      }

      this.logger.log('Essay award generation completed successfully');
    } catch (error) {
      this.logger.error(`Error generating essay awards: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process a specific award
   */
  private async processAward(
    award: Award,
    studentSubmissions: any[],
    startDate: Date,
    endDate: Date
  ): Promise<void> {
    this.logger.log(`Processing award: ${award.name}`);

    const scorers = [];

    for (const studentData of studentSubmissions) {
      const userId = studentData.userId;
      let totalScore = 0;
      let meetsMinimumCriteria = true;

      // Calculate scores for each criterion
      for (const criterion of award.criteria) {
        switch (criterion) {
          case AwardCriteria.ESSAY_PERFORMANCE: {
            const essayScore = await this.calculateEssayPerformanceScore(
              userId,
              studentData,
              award.criteriaConfig
            );

            const minScore = award.criteriaConfig?.minScore || 0;
            if (essayScore < minScore) {
              meetsMinimumCriteria = false;
              break;
            }

            totalScore += essayScore;
            break;
          }
        }

        if (!meetsMinimumCriteria) break;
      }

      if (meetsMinimumCriteria && totalScore > 0) {
        scorers.push({
          userId,
          totalScore,
          metrics: {
            totalEssays: studentData.submissions.length,
            averageScore: studentData.averageScore,
            completionRate: studentData.completionRate,
            totalPoints: studentData.totalPoints
          }
        });
      }
    }

    // Sort by score and select winners
    scorers.sort((a, b) => b.totalScore - a.totalScore);
    const maxWinners = award.criteriaConfig?.maxWinners || 1;
    const winners = scorers.slice(0, maxWinners);

    // Create award records
    for (const winner of winners) {
      await this.awardsService.createAwardWinner({
        userId: winner.userId,
        awardId: award.id,
        awardDate: endDate.toISOString().split('T')[0], // Convert to YYYY-MM-DD format
        awardReason: `Achieved ${winner.totalScore.toFixed(1)} points in ${award.name}`,
        metadata: {
          ...winner.metrics,
          calculationPeriod: {
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
          },
          totalScore: winner.totalScore
        }
      });
    }

    this.logger.log(`Award ${award.name} processed: ${winners.length} winners selected from ${scorers.length} candidates`);
  }

  /**
   * Calculate essay performance score
   */
  private async calculateEssayPerformanceScore(
    userId: string,
    studentData: any,
    criteriaConfig: any
  ): Promise<number> {
    try {
      const submissions = studentData.submissions;
      const averageScore = studentData.averageScore;
      const completionRate = studentData.completionRate;

      // Quality Score (70% weight) - based on average essay points
      const qualityScore = Math.min(averageScore * 10, 100); // Convert to 0-100 scale

      // Quantity Score (30% weight) - based on number of completed essays
      const minEssays = criteriaConfig?.minEssays || 1;
      const quantityRatio = Math.min(submissions.length / minEssays, 2); // Cap at 200%
      const quantityScore = Math.min(quantityRatio * 50, 100);

      // Combined score with weights
      const combinedScore = (qualityScore * 0.7) + (quantityScore * 0.3);

      this.logger.log(`Essay performance score for user ${userId}: ${combinedScore.toFixed(1)}
        (Quality: ${qualityScore.toFixed(1)}, Quantity: ${quantityScore.toFixed(1)},
        Essays: ${submissions.length}, Avg Score: ${averageScore.toFixed(1)})`);

      return Math.round(combinedScore);
    } catch (error) {
      this.logger.error(`Error calculating essay performance score for user ${userId}: ${error.message}`);
      return 0;
    }
  }

  /**
   * Get student submissions in the specified period
   */
  private async getStudentSubmissionsInPeriod(startDate: Date, endDate: Date): Promise<any[]> {
    const submissions = await this.essayTaskSubmissionsRepository
      .createQueryBuilder('submission')
      .innerJoin('submission.submissionMark', 'marking')
      .innerJoin('submission.task', 'task')
      .where('submission.status = :status', { status: SubmissionStatus.REVIEWED })
      .andWhere('submission.lastSubmittedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .select([
        'submission.createdBy as userId',
        'COUNT(submission.id) as submissionCount',
        'AVG(marking.points) as averageScore',
        'SUM(marking.points) as totalPoints'
      ])
      .groupBy('submission.createdBy')
      .getRawMany();

    // Get detailed submission data for each student
    const studentData = [];
    for (const submission of submissions) {
      const userId = submission.userId;

      // Get all submissions for this student in the period
      const userSubmissions = await this.essayTaskSubmissionsRepository
        .createQueryBuilder('submission')
        .innerJoin('submission.submissionMark', 'marking')
        .where('submission.createdBy = :userId', { userId })
        .andWhere('submission.status = :status', { status: SubmissionStatus.REVIEWED })
        .andWhere('submission.lastSubmittedAt BETWEEN :startDate AND :endDate', { startDate, endDate })
        .select(['submission.id', 'marking.points', 'submission.lastSubmittedAt'])
        .getRawMany();

      // Get total available tasks in the period
      const availableTasks = await this.essayMissionTasksRepository
        .createQueryBuilder('task')
        .where('task.createdAt <= :endDate', { endDate })
        .getCount();

      const completionRate = availableTasks > 0 ? (userSubmissions.length / availableTasks) * 100 : 0;

      studentData.push({
        userId,
        submissions: userSubmissions,
        averageScore: parseFloat(submission.averageScore) || 0,
        totalPoints: parseFloat(submission.totalPoints) || 0,
        completionRate: Math.min(completionRate, 100)
      });
    }

    return studentData;
  }
}
