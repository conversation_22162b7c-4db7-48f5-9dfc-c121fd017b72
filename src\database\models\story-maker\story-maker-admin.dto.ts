import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUUID, IsIn, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { PaginationDto } from '../../../common/models/pagination.dto';

/**
 * DTO for querying story maker participants
 */
export class GetParticipantsQueryDto extends PaginationDto {
  @ApiProperty({
    description: 'Search term for student name or email',
    example: 'john',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Filter by evaluation status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isEvaluated?: boolean;

  @ApiProperty({
    description: 'Filter by story maker ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  storyMakerId?: string;

  @ApiProperty({
    description: 'Field to sort by',
    example: 'firstSubmittedAt',
    enum: ['firstSubmittedAt', 'score', 'studentName'],
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['firstSubmittedAt', 'score', 'studentName'])
  override sortBy?: string = 'firstSubmittedAt';
}

/**
 * DTO for participant list item (admin view)
 */
export class ParticipantListItemDto {
  @ApiProperty({
    description: 'The ID of the participation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-************',
  })
  student_id: string;

  @ApiProperty({
    description: 'The name of the student',
    example: 'Jane Smith',
  })
  student_name: string;

  @ApiProperty({
    description: 'The email of the student',
    example: '<EMAIL>',
  })
  student_email: string;

  @ApiPropertyOptional({
    description: 'The profile picture of the student',
    example: 'https://example.com/images/profile.jpg',
  })
  student_profile_picture?: string;

  @ApiProperty({
    description: 'The ID of the story maker',
    example: '123e4567-e89b-12d3-a456-************',
  })
  story_maker_id: string;

  @ApiProperty({
    description: 'The title of the story maker',
    example: 'Adventure in the Forest',
  })
  story_maker_title: string;

  @ApiProperty({
    description: 'Whether the participation has been evaluated',
    example: true,
  })
  is_evaluated: boolean;

  @ApiPropertyOptional({
    description: 'The score assigned to the participation (only available after evaluation)',
    example: 45,
  })
  score?: number;

  @ApiProperty({
    description: 'When the student first submitted',
    example: '2023-01-01T00:00:00.000Z',
  })
  first_submitted_at: Date;

  @ApiPropertyOptional({
    description: 'When the participation was evaluated (only available after evaluation)',
    example: '2023-01-02T00:00:00.000Z',
  })
  evaluated_at?: Date;

  @ApiPropertyOptional({
    description: 'The ID of the evaluator (only available after evaluation)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  evaluated_by?: string;

  @ApiPropertyOptional({
    description: 'The name of the evaluator (only available after evaluation)',
    example: 'John Doe',
  })
  evaluator_name?: string;

  @ApiPropertyOptional({
    description: 'The profile picture of the evaluator (only available after evaluation)',
    example: 'https://example.com/images/profile.jpg',
  })
  evaluator_profile_picture?: string;

  @ApiProperty({
    description: 'Total number of submissions',
    example: 3,
  })
  submission_count: number;

  @ApiProperty({
    description: 'When the latest submission was made',
    example: '2023-01-01T00:00:00.000Z',
  })
  latest_submitted_at: Date;
}

/**
 * DTO for grouped participant history item (admin view)
 */
export class GroupedParticipantItemDto {
  @ApiProperty({
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-************',
  })
  student_id: string;

  @ApiProperty({
    description: 'The name of the student',
    example: 'Jane Smith',
  })
  student_name: string;

  @ApiProperty({
    description: 'The email of the student',
    example: '<EMAIL>',
  })
  student_email: string;

  @ApiPropertyOptional({
    description: 'The profile picture of the student',
    example: 'https://example.com/images/profile.jpg',
  })
  student_profile_picture?: string;

  @ApiProperty({
    description: 'List of stories the student has participated in',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        story_maker_id: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
        story_maker_title: { type: 'string', example: 'Adventure in the Forest' },
        participation_id: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
        is_evaluated: { type: 'boolean', example: true },
        score: { type: 'number', example: 45 },
        first_submitted_at: { type: 'string', format: 'date-time', example: '2023-01-01T00:00:00.000Z' },
        latest_submitted_at: { type: 'string', format: 'date-time', example: '2023-01-01T00:00:00.000Z' },
        evaluated_at: { type: 'string', format: 'date-time', example: '2023-01-02T00:00:00.000Z' },
        attempt_count: { type: 'number', example: 3 },
        evaluator_name: { type: 'string', example: 'John Doe' },
      },
    },
  })
  stories: any[];
}

/**
 * DTO for participant list response (admin view)
 */
export class ParticipantListResponseDto {
  @ApiProperty({
    description: 'List of participants',
    type: [ParticipantListItemDto],
  })
  participants: ParticipantListItemDto[];

  @ApiProperty({
    description: 'Total number of participants',
    example: 10,
  })
  total_count: number;
}

/**
 * DTO for grouped participant list response (admin view)
 */
export class GroupedParticipantListResponseDto {
  @ApiProperty({
    description: 'List of grouped participants by student',
    type: [GroupedParticipantItemDto],
  })
  students: GroupedParticipantItemDto[];

  @ApiProperty({
    description: 'Total number of students',
    example: 5,
  })
  total_count: number;
}

/**
 * DTO for detailed participant view (admin view)
 */
export class ParticipantDetailResponseDto {
  @ApiProperty({
    description: 'The ID of the participation',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-************',
  })
  student_id: string;

  @ApiProperty({
    description: 'The name of the student',
    example: 'Jane Smith',
  })
  student_name: string;

  @ApiProperty({
    description: 'The email of the student',
    example: '<EMAIL>',
  })
  student_email: string;

  @ApiPropertyOptional({
    description: 'The profile picture of the student',
    example: 'https://example.com/images/profile.jpg',
  })
  student_profile_picture?: string;

  @ApiProperty({
    description: 'The ID of the story maker',
    example: '123e4567-e89b-12d3-a456-************',
  })
  story_maker_id: string;

  @ApiProperty({
    description: 'The title of the story maker',
    example: 'Adventure in the Forest',
  })
  story_maker_title: string;

  @ApiProperty({
    description: 'Whether the participation has been evaluated',
    example: true,
  })
  is_evaluated: boolean;

  @ApiPropertyOptional({
    description: 'The score assigned to the participation (only available after evaluation)',
    example: 45,
  })
  score?: number;

  @ApiProperty({
    description: 'When the student first submitted',
    example: '2023-01-01T00:00:00.000Z',
  })
  first_submitted_at: Date;

  @ApiPropertyOptional({
    description: 'When the participation was evaluated (only available after evaluation)',
    example: '2023-01-02T00:00:00.000Z',
  })
  evaluated_at?: Date;

  @ApiPropertyOptional({
    description: 'The ID of the evaluator (only available after evaluation)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  evaluated_by?: string;

  @ApiPropertyOptional({
    description: 'The name of the evaluator (only available after evaluation)',
    example: 'John Doe',
  })
  evaluator_name?: string;

  @ApiPropertyOptional({
    description: 'The profile picture of the evaluator (only available after evaluation)',
    example: 'https://example.com/images/profile.jpg',
  })
  evaluator_profile_picture?: string;

  @ApiProperty({
    description: 'List of submissions for this participation',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
        content: { type: 'string', example: '<p>Once upon a time in a magical forest...</p>' },
        submitted_at: { type: 'string', format: 'date-time', example: '2023-01-01T00:00:00.000Z' },
        is_evaluated: { type: 'boolean', example: true },
        evaluation: {
          type: 'object',
          properties: {
            id: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
            tutor_id: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
            tutor_name: { type: 'string', example: 'John Doe' },
            tutor_profile_picture: { type: 'string', example: 'https://example.com/images/profile.jpg' },
            corrections: { type: 'string', example: '<p>Once upon a time in a magical forest, there lived a wise old owl...</p>' },
            feedback: { type: 'string', example: 'Great use of descriptive language and creative storyline.' },
            evaluated_at: { type: 'string', format: 'date-time', example: '2023-01-02T00:00:00.000Z' },
          },
        },
      },
    },
  })
  submissions: any[]; // Using any[] for simplicity in the Swagger documentation

  @ApiProperty({
    description: 'When the participation was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'When the participation was last updated',
    example: '2023-01-02T00:00:00.000Z',
  })
  updated_at: Date;
}
