import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { PlanFeaturesService } from './plan-features.service';
import { CreatePlanFeatureDto, UpdatePlanFeatureDto, PlanFeatureResponseDto } from '../../database/models/plan-features.dto';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { PaginatedResponse } from '../../common/models/api-response.model';
import { ApiResponse } from 'src/common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithArrayType, ApiErrorResponse } from 'src/common/decorators/api-response.decorator';

@ApiTags('plan-features')
@Controller('plan-features')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
export class PlanFeaturesController {
    constructor(private readonly planFeaturesService: PlanFeaturesService) {}

    @Get()
    @ApiOperation({
        summary: 'Get all plan features',
        description: 'Retrieve a list of all available plan features.'
    })
    @ApiOkResponseWithArrayType(PlanFeatureResponseDto, 'List of plan features retrieved successfully')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    async findAll(): Promise<ApiResponse<PlanFeatureResponseDto[]>> {
        const result = await this.planFeaturesService.findAll();
        return ApiResponse.success(result, 'Plan features retrieved successfully');
    }

    @Get(':id')
    @ApiOperation({
        summary: 'Get a plan feature by ID',
        description: 'Retrieve a specific plan feature by its ID.'
    })
    @ApiParam({
        name: 'id',
        description: 'The ID of the plan feature to retrieve',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiOkResponseWithType(PlanFeatureResponseDto, 'Plan feature retrieved successfully')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'Plan feature not found')
    async findOne(@Param('id') id: string): Promise<ApiResponse<PlanFeatureResponseDto>> {
        const result = await this.planFeaturesService.findById(id);
        return ApiResponse.success(result, 'Plan feature retrieved successfully');
    }

    @Post()
    @ApiOperation({
        summary: 'Create a new plan feature',
        description: 'Create a new plan feature with the provided data.'
    })
    @ApiBody({
        type: CreatePlanFeatureDto,
        description: 'Plan feature creation data',
        examples: {
            feature: {
                summary: 'Create Plan Feature',
                description: 'Create a new plan feature',
                value: {
                    type: 'DIARY_SKIN',
                    name: 'Premium Diary Skin',
                    description: 'Access to premium diary skins',
                    isActive: true
                }
            }
        }
    })
    @ApiOkResponseWithType(PlanFeatureResponseDto, 'Plan feature created successfully')
    @ApiErrorResponse(400, 'Invalid input')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(409, 'Plan feature with the same type already exists')
    async create(@Body() createPlanFeatureDto: CreatePlanFeatureDto): Promise<ApiResponse<PlanFeatureResponseDto>> {
        const result = await this.planFeaturesService.create(createPlanFeatureDto);
        return ApiResponse.success(result, 'Plan feature created successfully', 201);
    }

    @Patch(':id')
    @ApiOperation({
        summary: 'Update a plan feature',
        description: 'Update an existing plan feature with the provided data.'
    })
    @ApiParam({
        name: 'id',
        description: 'The ID of the plan feature to update',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiBody({
        type: UpdatePlanFeatureDto,
        description: 'Plan feature update data',
        examples: {
            update: {
                summary: 'Update Plan Feature',
                description: 'Update an existing plan feature',
                value: {
                    name: 'Updated Feature Name',
                    description: 'Updated feature description',
                    isActive: true
                }
            }
        }
    })
    @ApiOkResponseWithType(PlanFeatureResponseDto, 'Plan feature updated successfully')
    @ApiErrorResponse(400, 'Invalid input')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'Plan feature not found')
    @ApiErrorResponse(409, 'Plan feature with the same type already exists')
    async update(@Param('id') id: string, @Body() updatePlanFeatureDto: UpdatePlanFeatureDto): Promise<ApiResponse<PlanFeatureResponseDto>> {
        const result = await this.planFeaturesService.update(id, updatePlanFeatureDto);
        return ApiResponse.success(result, 'Plan feature updated successfully');
    }

    @Delete(':id')
    @ApiOperation({
        summary: 'Delete a plan feature',
        description: 'Delete an existing plan feature by its ID.'
    })
    @ApiParam({
        name: 'id',
        description: 'The ID of the plan feature to delete',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiOkResponseWithType(Object, 'Plan feature deleted successfully')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'Plan feature not found')
    async remove(@Param('id') id: string): Promise<ApiResponse<void>> {
        await this.planFeaturesService.remove(id);
        return ApiResponse.success(null, 'Plan feature deleted successfully');
    }
}
