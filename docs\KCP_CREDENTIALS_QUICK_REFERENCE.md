# KCP Payment Gateway - Credentials Quick Reference

## 🔑 Essential Credentials Checklist

### ✅ **Must Have - KC<PERSON> Provided**
```env
KCP_SITE_CD=your_merchant_site_code        # From KCP merchant portal
KCP_SITE_KEY=your_secret_site_key          # From KCP merchant portal (KEEP SECRET!)
KCP_WEBHOOK_SECRET=your_webhook_secret     # You generate, configure in KCP portal
```

### ✅ **Must Have - Environment Specific**
```env
# Development/Staging
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_TEST_MODE=true

# Production
KCP_API_URL=https://spl.kcp.co.kr
KCP_TEST_MODE=false
```

### ⚙️ **Optional Configuration**
```env
KCP_TRADE_REG_URL=/std/tradeReg/register   # Default endpoint
KCP_PAYMENT_URL=/gw/enc/v1/payment         # Default endpoint
KCP_TIMEOUT=30000                          # 30 seconds
KCP_RETRY_ATTEMPTS=3                       # Retry failed requests
PAYMENT_PROVIDER=kcp                       # Payment provider identifier
PAYMENT_CURRENCY=KRW                       # Korean Won
```

## 📋 **Where to Get Each Credential**

| Credential | Source | How to Obtain |
|------------|--------|---------------|
| `KCP_SITE_CD` | KCP Portal | Merchant registration → API settings |
| `KCP_SITE_KEY` | KCP Portal | API settings → Download credentials |
| `KCP_WEBHOOK_SECRET` | You Generate | Create random string → Configure in KCP |
| `KCP_API_URL` | KCP Documentation | Use staging/production URLs |

## 🔒 **Security Requirements**

### **Never Commit to Git**
```bash
# Add to .gitignore
.env
.env.local
.env.production
.env.staging
*.key
```

### **Environment Variable Validation**
```typescript
// Validate on startup
const requiredVars = ['KCP_SITE_CD', 'KCP_SITE_KEY', 'KCP_WEBHOOK_SECRET'];
const missing = requiredVars.filter(v => !process.env[v]);
if (missing.length > 0) {
  throw new Error(`Missing: ${missing.join(', ')}`);
}
```

## 🧪 **Testing Your Credentials**

### **Quick Test Script**
```bash
#!/bin/bash
# Test KCP connectivity
curl -X POST "$KCP_API_URL/std/tradeReg/register" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "site_cd=$KCP_SITE_CD&kcp_cert_info=$KCP_SITE_CD:$KCP_SITE_KEY"
```

### **Backend Validation**
```typescript
// Test in your application
const isValid = await kcpService.validateCredentials();
console.log('KCP credentials valid:', isValid);
```

## 🌍 **Environment Setup Examples**

### **Development (.env.development)**
```env
# KCP Test Credentials
KCP_SITE_CD=T0000001
KCP_SITE_KEY=test_key_from_kcp_portal
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_WEBHOOK_SECRET=dev_webhook_secret_123
KCP_TEST_MODE=true

# App Configuration
NODE_ENV=development
API_URL=http://localhost:3012
FRONTEND_URL=http://localhost:3011
```

### **Production (.env.production)**
```env
# KCP Production Credentials
KCP_SITE_CD=A1234567
KCP_SITE_KEY=prod_key_from_kcp_portal
KCP_API_URL=https://spl.kcp.co.kr
KCP_WEBHOOK_SECRET=prod_webhook_secret_xyz
KCP_TEST_MODE=false

# App Configuration
NODE_ENV=production
API_URL=https://api.yourdomain.com
FRONTEND_URL=https://yourdomain.com
```

## 🚨 **Common Issues & Solutions**

### **"Authentication Failed"**
- ✅ Check `KCP_SITE_CD` and `KCP_SITE_KEY` are correct
- ✅ Verify credentials in KCP merchant portal
- ✅ Ensure using correct environment (test vs prod)

### **"Invalid Webhook Signature"**
- ✅ Check `KCP_WEBHOOK_SECRET` matches KCP portal configuration
- ✅ Verify webhook URL is accessible from internet
- ✅ Ensure HTTPS is properly configured

### **"Connection Timeout"**
- ✅ Check `KCP_API_URL` is correct for your environment
- ✅ Verify firewall allows outbound HTTPS traffic
- ✅ Test network connectivity to KCP servers

## 📞 **Getting Help**

### **KCP Support**
- **Portal**: KCP Merchant Portal
- **Documentation**: KCP API Documentation
- **Support**: KCP Technical Support Team

### **Credential Issues**
1. **Invalid credentials**: Contact KCP support
2. **Lost credentials**: Reset through KCP portal
3. **Webhook issues**: Check KCP portal webhook configuration
4. **API access**: Verify merchant account status

## 🔄 **Credential Rotation**

### **Regular Rotation (Recommended)**
- **Webhook Secret**: Every 90 days
- **Site Key**: When compromised or annually
- **Monitor**: Set up alerts for credential expiration

### **Emergency Rotation**
- **Immediate**: If credentials are compromised
- **Process**: Generate new → Update KCP portal → Deploy → Test

## ✅ **Pre-Deployment Checklist**

- [ ] All required environment variables set
- [ ] Credentials validated with test API call
- [ ] Webhook URL configured in KCP portal
- [ ] SSL certificate valid for webhook endpoint
- [ ] Firewall rules allow KCP traffic
- [ ] Monitoring and alerting configured
- [ ] Backup credentials stored securely
- [ ] Team trained on credential management

## 📱 **Mobile App Considerations**

### **Additional Configuration**
```env
# Mobile-specific settings
KCP_MOBILE_RETURN_URL=yourapp://payment/success
KCP_MOBILE_CANCEL_URL=yourapp://payment/cancel
KCP_MOBILE_APP_SCHEME=yourapp
```

### **Deep Link Setup**
- Configure app scheme in KCP portal
- Set up URL handling in mobile app
- Test payment flow from mobile browser

---

**⚠️ Security Reminder**: Never share credentials in chat, email, or commit to version control. Always use environment variables and secure credential management systems.
