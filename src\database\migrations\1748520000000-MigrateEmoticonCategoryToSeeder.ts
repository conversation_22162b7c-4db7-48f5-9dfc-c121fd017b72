import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateEmoticonCategoryToSeeder1748520000000 implements MigrationInterface {
    name = 'MigrateEmoticonCategoryToSeeder1748520000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        console.log('Starting emoticon category migration to seeder approach...');

        // Step 1: Find existing emoticon categories (case-insensitive)
        const existingCategories = await queryRunner.query(`
            SELECT id, name, description, is_active, display_order, created_at, updated_at
            FROM "shop_category" 
            WHERE LOWER(name) = 'emoticon'
            ORDER BY created_at ASC
        `);

        console.log(`Found ${existingCategories.length} existing emoticon categories`);

        let targetCategoryId: string | null = null;
        let wasCreated = false;

        if (existingCategories.length > 0) {
            // Step 2: Use the first (oldest) emoticon category as the target
            const primaryCategory = existingCategories[0];
            targetCategoryId = primaryCategory.id;

            console.log(`Using existing emoticon category with ID: ${targetCategoryId}`);

            // Step 3: Standardize the primary category to match seeder specifications
            await queryRunner.query(`
                UPDATE "shop_category" 
                SET 
                    name = 'Emoticon',
                    description = 'Special category for emoticons. This category is protected and cannot be modified or deleted.',
                    is_active = true,
                    display_order = 0,
                    updated_at = NOW()
                WHERE id = $1
            `, [targetCategoryId]);

            console.log(`Standardized emoticon category ${targetCategoryId} to match seeder specifications`);

            // Step 4: Handle duplicate emoticon categories if any exist
            if (existingCategories.length > 1) {
                console.log(`Found ${existingCategories.length - 1} duplicate emoticon categories, migrating their data...`);

                for (let i = 1; i < existingCategories.length; i++) {
                    const duplicateCategory = existingCategories[i];
                    
                    // Check if there are any shop items linked to this duplicate category
                    const linkedItems = await queryRunner.query(`
                        SELECT id, title FROM "shop_item" 
                        WHERE category_id = $1
                    `, [duplicateCategory.id]);

                    if (linkedItems.length > 0) {
                        console.log(`Migrating ${linkedItems.length} shop items from duplicate category ${duplicateCategory.id} to primary category ${targetCategoryId}`);
                        
                        // Move shop items to the primary category
                        await queryRunner.query(`
                            UPDATE "shop_item" 
                            SET category_id = $1, updated_at = NOW()
                            WHERE category_id = $2
                        `, [targetCategoryId, duplicateCategory.id]);

                        console.log(`Migrated shop items: ${linkedItems.map(item => item.title).join(', ')}`);
                    }

                    // Check for any other references to this duplicate category
                    // Add more checks here if there are other tables that reference shop_category

                    // Delete the duplicate category
                    await queryRunner.query(`
                        DELETE FROM "shop_category" WHERE id = $1
                    `, [duplicateCategory.id]);

                    console.log(`Deleted duplicate emoticon category ${duplicateCategory.id}`);
                }
            }
        } else {
            // Step 5: No existing emoticon category found, create one
            console.log('No existing emoticon category found, creating new one...');

            const result = await queryRunner.query(`
                INSERT INTO "shop_category" (name, description, is_active, display_order, created_at, updated_at)
                VALUES ('Emoticon', 'Special category for emoticons. This category is protected and cannot be modified or deleted.', true, 0, NOW(), NOW())
                RETURNING id
            `);

            targetCategoryId = result[0].id;
            wasCreated = true;

            console.log(`Created new emoticon category with ID: ${targetCategoryId}`);
        }

        // Step 6: Log the final result for reference
        console.log(`Migration completed. Emoticon category ID: ${targetCategoryId} (${wasCreated ? 'created' : 'migrated'})`);

        // Step 7: Verify the final state
        const finalCategory = await queryRunner.query(`
            SELECT id, name, description, is_active, display_order
            FROM "shop_category" 
            WHERE id = $1
        `, [targetCategoryId]);

        if (finalCategory.length > 0) {
            console.log('Final emoticon category state:', finalCategory[0]);
        }

        // Step 8: Count shop items in the emoticon category
        const itemCount = await queryRunner.query(`
            SELECT COUNT(*) as count FROM "shop_item" WHERE category_id = $1
        `, [targetCategoryId]);

        console.log(`Emoticon category now contains ${itemCount[0].count} shop items`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        console.log('Rolling back emoticon category migration...');

        // Note: This rollback is conservative and only removes categories that were created by this migration
        // It does not attempt to restore duplicate categories that were merged, as that would be complex and risky

        // Find the current emoticon category
        const emoticonCategory = await queryRunner.query(`
            SELECT id, created_at FROM "shop_category" 
            WHERE name = 'Emoticon' AND description = 'Special category for emoticons. This category is protected and cannot be modified or deleted.'
        `);

        if (emoticonCategory.length > 0) {
            const categoryId = emoticonCategory[0].id;
            
            // Check if there are any shop items linked to this category
            const linkedItems = await queryRunner.query(`
                SELECT COUNT(*) as count FROM "shop_item" WHERE category_id = $1
            `, [categoryId]);

            if (linkedItems[0].count > 0) {
                console.log(`Cannot rollback: ${linkedItems[0].count} shop items are linked to the emoticon category`);
                console.log('Manual intervention required to move items to another category before rollback');
                return;
            }

            // Only delete if it appears to be created by this migration (no linked items)
            await queryRunner.query(`DELETE FROM "shop_category" WHERE id = $1`, [categoryId]);
            console.log(`Removed emoticon category ${categoryId}`);
        } else {
            console.log('No emoticon category found to rollback');
        }
    }
}
