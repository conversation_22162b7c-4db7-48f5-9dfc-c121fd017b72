# Promotion Management - Testing Flow

This document outlines the testing flow for the Promotion Management API endpoints.

## Prerequisites

Before testing the Promotion Management API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (admin, student)
3. Set up your API testing tool (<PERSON><PERSON> recommended)

## Overview

The Promotion Management feature allows administrators to create and manage promotions that can be applied to shop items and plans. Students can apply promotion codes to get discounts on their purchases. This testing flow ensures all aspects of the feature work correctly.

## Admin Promotion Management Testing

### Test Case 1: Create Promotion

1. Authenticate with admin token
2. Send a POST request to `/promotions` with promotion details:
   - Name, description, promotion type, discount value
   - Applicable type (SHOP_ITEM/PLAN)
   - Category restrictions via applicableCategoryIds
   - Optional fields (promotion code, date range, usage limits)
3. Verify HTTP status code is 201 Created
4. Verify response contains complete promotion details
5. Verify promotion is stored in database with correct fields
6. Verify usageCount is initialized to 0

### Test Case 2: Get All Promotions

1. Authenticate with admin token
2. Send a GET request to `/promotions/admin`
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of promotions
5. Test filtering by status, type, and date range
6. Test sorting and pagination parameters

### Test Case 3: Get Promotion by ID

1. Authenticate with admin token
2. Send a GET request to `/promotions/{id}`
3. Verify HTTP status code is 200 OK
4. Verify response contains complete promotion details
5. Test with non-existent promotion ID and verify 404 Not Found response

### Test Case 4: Update Promotion

1. Authenticate with admin token
2. Send a PATCH request to `/promotions/{id}` with updated fields
3. Verify HTTP status code is 200 OK
4. Verify response contains updated promotion details
5. Verify only specified fields are updated
6. Verify database record is updated correctly

### Test Case 5: Delete Promotion

1. Authenticate with admin token
2. Send a DELETE request to `/promotions/{id}`
3. Verify HTTP status code is 200 OK
4. Verify promotion is removed from database
5. Verify shop items with this promotion have promotionId set to null

### Test Case 6: Generate Promotion Code

1. Authenticate with admin token
2. Send a POST request to `/promotions/generate-code` with prefix
3. Verify HTTP status code is 200 OK
4. Verify response contains a unique promotion code
5. Verify code starts with the specified prefix

## Promotion Application Testing

### Test Case 1: Apply Promotion to Shop Items

1. Authenticate with admin token
2. Send a POST request to `/shop/admin/apply-promotion` with:
   - promotionId
   - Array of shop item IDs
3. Verify HTTP status code is 200 OK
4. Verify response indicates successful application
5. Verify all shop items now have the specified promotionId
6. Verify the response includes the number of items updated

### Test Case 2: Apply Category-Restricted Promotion

1. Authenticate with admin token
2. Create a promotion with applicableCategoryIds set to specific categories
3. Send a POST request to `/shop/admin/apply-promotion` with:
   - promotionId (of category-restricted promotion)
   - Array of shop item IDs (some in applicable categories, some not)
4. Verify HTTP status code is 200 OK
5. Verify only items in applicable categories have the promotion applied
6. Verify response includes count of applied items and skipped items

## Student Promotion Usage Testing

### Test Case 1: Apply Promotion Code

1. Authenticate with student token
2. Send a POST request to `/promotions/apply-code` with:
   - promotionCode
   - originalPrice
   - itemType (SHOP_ITEM/PLAN)
   - category (for shop items)
3. Verify HTTP status code is 200 OK
4. Verify response contains discount information
5. Verify discount amount is calculated correctly
6. Verify final price is calculated correctly

### Test Case 2: Apply Category-Restricted Promotion Code

1. Authenticate with student token
2. Create a promotion with applicableCategoryIds set to specific categories
3. Send a POST request to `/promotions/apply-code` with:
   - promotionCode (of category-restricted promotion)
   - originalPrice
   - itemType (SHOP_ITEM)
   - category (not in applicable categories)
4. Verify HTTP status code is 200 OK
5. Verify isApplied field is false
6. Verify discount amount is 0
7. Verify message indicates category restriction

### Test Case 3: Get Applicable Promotions

1. Authenticate with student token
2. Send a GET request to `/promotions/applicable` with:
   - itemType (SHOP_ITEM/PLAN)
   - category (for shop items)
3. Verify HTTP status code is 200 OK
4. Verify response contains list of applicable promotions
5. Verify only active promotions are included
6. Verify only promotions applicable to specified type/category are included

## Shopping Cart Integration Testing

### Test Case 1: Add Item with Promotion to Cart

1. Authenticate with student token
2. Apply a promotion to a shop item (as admin)
3. Send a POST request to `/shop/cart/add` with:
   - shopItemId (of item with promotion)
   - quantity
4. Verify HTTP status code is 200 OK
5. Verify cart response includes item with:
   - Correct original price
   - Correct discounted price
   - Correct promotion ID
   - Correct category information

### Test Case 2: Checkout with Promotion

1. Authenticate with student token
2. Add items with promotions to cart
3. Send a POST request to `/shop/cart/checkout` with:
   - paymentMethod
   - paymentDetails
4. Verify HTTP status code is 200 OK
5. Verify checkout response includes purchase details
6. Verify purchase records in database include:
   - Correct promotion information
   - Correct category information
   - Correct original price, final price, and discount amount
7. Verify cart is cleared after checkout

## Edge Case Testing

### Test Case 1: Maximum Discount Amount

1. Create a promotion with maximumDiscountAmount set
2. Apply the promotion to an item with a high price
3. Verify discount is limited to the maximum amount
4. Verify final price is calculated correctly

### Test Case 2: Minimum Purchase Amount

1. Create a promotion with minimumPurchaseAmount set
2. Apply the promotion to an item with price below minimum
3. Verify isApplied field is false
4. Verify message indicates minimum purchase requirement

### Test Case 3: Maximum Purchase Amount

1. Create a promotion with maximumPurchaseAmount set
2. Apply the promotion to an item with price above maximum
3. Verify isApplied field is false
4. Verify message indicates maximum purchase requirement

### Test Case 4: Date Range Restrictions

1. Create a promotion with past endDate (expired)
2. Try to apply the expired promotion
3. Verify isApplied field is false
4. Verify message indicates promotion is expired
5. Create a promotion with future startDate (scheduled)
6. Try to apply the scheduled promotion
7. Verify isApplied field is false
8. Verify message indicates promotion is scheduled

### Test Case 5: Usage Limit

1. Create a promotion with usageLimit set
2. Apply the promotion until limit is reached
3. Try to apply the promotion again
4. Verify isApplied field is false
5. Verify message indicates usage limit reached

## Regression Testing

### Test Case 1: Verify Removal of discountCategory

1. Create a promotion with applicableCategoryIds
2. Verify promotion is created successfully
3. Try to create a promotion with discountCategory field
4. Verify discountCategory is ignored
5. Verify database schema no longer has discount_category column
6. Verify category-based filtering works with applicableCategoryIds

### Test Case 2: Verify Removal of discountAmount as Creation Field

1. Try to create a promotion with discountAmount field
2. Verify discountAmount is ignored in creation
3. Verify database schema no longer has discount_amount column
4. Apply the promotion and verify discount is calculated correctly
5. Verify discount calculation uses promotionType and discountValue

## Permission Testing

### Test Case 1: Admin-Only Endpoints

1. Authenticate with student token
2. Attempt to access admin promotion endpoints
3. Verify HTTP status code is 403 Forbidden
4. Repeat with tutor token
5. Verify HTTP status code is 403 Forbidden

### Test Case 2: Student-Only Endpoints

1. Authenticate with admin token
2. Attempt to access student promotion endpoints
3. Verify appropriate error responses
