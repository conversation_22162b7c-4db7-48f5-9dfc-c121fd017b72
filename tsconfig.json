{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "paths": {"src/*": ["src/*"]}, "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "esModuleInterop": true, "resolveJsonModule": true, "types": ["node", "jest"], "typeRoots": ["./node_modules/@types"]}, "include": ["src/**/*", "src/types/*.d.ts"], "exclude": ["node_modules", "dist"]}