import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, OneTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { DiarySkin } from './diary-skin.entity';

@Entity()
export class Novel extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @OneToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'default_skin_id' })
  defaultSkinId: string;

  @ManyToOne(() => DiarySkin)
  @JoinColumn({ name: 'default_skin_id' })
  defaultSkin: DiarySkin;

  @Column({ name: 'tutor_greeting', nullable: true, type: 'text' })
  tutorGreeting: string;
}
