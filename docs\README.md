# HEC Backend Documentation

Welcome to the HEC Backend Documentation. This documentation is organized into five main categories to help you find the information you need quickly and efficiently.

## Documentation Structure

### 1. System Documentation

Core information about the system architecture, database design, and key concepts.

- **Project Overview** - High-level description of the HEC platform
- **System Architecture** - Technical design and components
- **Database Schema** - Database tables, relationships, and constraints
- **Authentication Flow** - User authentication and authorization process
- **Automatic Tutor Assignment** - How tutors are assigned to students

### 2. API Reference

Comprehensive documentation of all API endpoints organized by module.

- **Authentication Module** - Login, registration, token management
- **Users Module** - User management and profiles
- **Plans Module** - Subscription plans and features
- **Diary Module** - Student diary entries and tutor feedback
- **Shop Module** - Shop items, cart, checkout, reward points, and diary skin integration
- **Tutor Module** - Tutor approval and assignment
- **Common Services** - Shared services and utilities

### 3. Integration Guides

Practical guides for frontend developers to integrate with the backend.

- **Authentication Integration** - How to implement login, registration, and token management
- **Diary Module Integration** - How to implement diary creation, submission, and review
- **Shop Integration** - How to implement shop browsing, cart, and checkout
- **Chat Integration** - How to implement real-time messaging
- **Notification Integration** - How to implement system notifications
- **Student Friendship Integration** - How to implement student connections

### 4. Development Resources

Resources for developers working on the backend codebase.

- **Development Conventions** - Coding standards and practices
- **Testing Conventions** - Unit, integration, and E2E testing
- **File Upload Conventions** - How file uploads are handled
- **API Versioning Conventions** - How API versioning is managed
- **Feature Flag Conventions** - How feature flags are implemented
- **Deployment Guide** - How to deploy the application

### 5. API Testing Flow

Comprehensive guides for testing API endpoints with examples and procedures.

- **API Testing Overview** - Introduction to API testing approach
- **Authentication API Testing** - Testing authentication endpoints
- **Users API Testing** - Testing user management endpoints
- **Plans API Testing** - Testing subscription plan endpoints
- **Diary API Testing** - Testing diary module endpoints
- **Shop API Testing** - Testing shop module endpoints

## Using This Documentation

To navigate this documentation:

1. Use the sidebar to browse through the different categories and documents
2. Use the search function to find specific topics
3. Click on document titles to view their contents

## Documentation Viewer

This documentation is best viewed using the included documentation viewer:

```
/docs/view-markdown.html
```

The documentation viewer provides a user-friendly interface with:

- Collapsible sidebar navigation
- Syntax highlighting for code blocks
- Search functionality
- Mobile-friendly responsive design
