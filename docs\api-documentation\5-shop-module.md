# Shop Module

The Shop Module handles shop item management, shopping cart functionality, checkout process, and reward points system. It supports different item types (free, in-app purchase) and categories (skin, emoticon).

## Epics

1. **Shop Category Management**
2. **Shop Item Management**
3. **Shopping Cart Management**
4. **Checkout Process**
5. **Reward Points System**
6. **Student Owned Items Management**
7. **Diary Skin to Shop Item Workflow**

## APIs

### Shop Category APIs

#### 1. Get All Categories

**Endpoint:** `GET /shop/categories`

**Description:** Retrieves a paginated list of all shop categories.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by (default: 'createdAt')
- `sortDirection`: Sort direction ('ASC' or 'DESC', default: 'DESC')

**Response:**
```json
{
  "success": true,
  "message": "Shop categories retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "name": "Skins",
        "description": "Diary skins and themes",
        "isActive": true,
        "parentId": null,
        "createdAt": "2023-06-15T10:30:00Z",
        "updatedAt": "2023-06-15T10:30:00Z"
      }
    ],
    "totalItems": 1,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract pagination parameters from query
2. Retrieve categories with pagination
3. Return paginated list of categories

#### 2. Get Category by ID

**Endpoint:** `GET /shop/categories/:id`

**Description:** Retrieves a specific shop category by ID.

**Path Parameters:**
- `id`: The ID of the category to retrieve

**Response:**
```json
{
  "success": true,
  "message": "Shop category retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Skins",
    "description": "Diary skins and themes",
    "isActive": true,
    "parentId": null,
    "createdAt": "2023-06-15T10:30:00Z",
    "updatedAt": "2023-06-15T10:30:00Z"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate category ID
2. Retrieve category by ID
3. Return category details

### Shop Item APIs

#### 1. Get All Shop Items

**Endpoint:** `GET /shop/items`

**Description:** Retrieves a paginated list of all shop items with optional filtering.

**Query Parameters:**
- `categoryId` (optional): Filter by category ID
- `type` (optional): Filter by item type (FREE, IN_APP_PURCHASE)
- `featuredOnly` (optional): Show only featured items (true/false)
- `itemNumber` (optional): Filter by item number
- `title` (optional): Filter by title (partial match)
- `promotionId` (optional): Filter by promotion ID
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by (default: 'createdAt')
- `sortDirection`: Sort direction ('ASC' or 'DESC', default: 'DESC')

**Response:**
```json
{
  "success": true,
  "message": "Shop items retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "itemNumber": "SK-001",
        "title": "Blue Ocean Theme",
        "description": "A calming blue ocean theme for your diary",
        "categoryId": "123e4567-e89b-12d3-a456-426614174000",
        "categoryName": "Skins",
        "type": "IN_APP_PURCHASE",
        "price": 9.99,
        "isPurchasableInRewardpoint": true,
        "shopItemCategory": "skin",
        "filePath": "https://example.com/media/shop-items/skin/blue-ocean.jpg",
        "isActive": true,
        "isFeatured": true,
        "finalPrice": 9.99,
        "isOnSale": false,
        "discountPercentage": 0,
        "isFreeOrPurchased": false
      }
    ],
    "totalItems": 1,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract pagination and filter parameters from query
2. Build query with filters
3. Retrieve items with pagination
4. For each item, calculate final price, discount percentage, and check if it's free or already purchased
5. Return paginated list of items

#### 2. Get Shop Item by ID

**Endpoint:** `GET /shop/items/:id`

**Description:** Retrieves a specific shop item by ID.

**Path Parameters:**
- `id`: The ID of the item to retrieve

**Response:**
```json
{
  "success": true,
  "message": "Shop item retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "itemNumber": "SK-001",
    "title": "Blue Ocean Theme",
    "description": "A calming blue ocean theme for your diary",
    "categoryId": "123e4567-e89b-12d3-a456-426614174000",
    "categoryName": "Skins",
    "type": "IN_APP_PURCHASE",
    "price": 9.99,
    "priceEquivalentToRewardPoint": 100,
    "shopItemCategory": "skin",
    "filePath": "https://example.com/media/shop-items/skin/blue-ocean.jpg",
    "isActive": true,
    "isFeatured": true,
    "finalPrice": 9.99,
    "isOnSale": false,
    "discountPercentage": 0,
    "isFreeOrPurchased": false
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate item ID
2. Retrieve item by ID with category information
3. Calculate final price, discount percentage, and check if it's free or already purchased
4. Return item details

#### 3. Get Available Shop Items

**Endpoint:** `GET /shop/items/available`

**Description:** Retrieves a paginated list of shop items that are available for purchase (not already owned by the student).

**Query Parameters:**
- `categoryId` (optional): Filter by category ID
- `type` (optional): Filter by item type (FREE, IN_APP_PURCHASE)
- `featuredOnly` (optional): Show only featured items (true/false)
- `itemNumber` (optional): Filter by item number
- `title` (optional): Filter by title (partial match)
- `promotionId` (optional): Filter by promotion ID
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by (default: 'createdAt')
- `sortDirection`: Sort direction ('ASC' or 'DESC', default: 'DESC')

**Response:**
```json
{
  "success": true,
  "message": "Available shop items retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "itemNumber": "SK-001",
        "title": "Blue Ocean Theme",
        "description": "A calming blue ocean theme for your diary",
        "categoryId": "123e4567-e89b-12d3-a456-426614174000",
        "categoryName": "Skins",
        "type": "IN_APP_PURCHASE",
        "price": 9.99,
        "isPurchasableInRewardpoint": true,
        "shopItemCategory": "skin",
        "filePath": "https://example.com/media/shop-items/skin/blue-ocean.jpg",
        "isActive": true,
        "isFeatured": true,
        "finalPrice": 9.99,
        "isOnSale": false,
        "discountPercentage": 0,
        "isFreeOrPurchased": false
      }
    ],
    "totalItems": 1,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Extract pagination and filter parameters from query
2. Get all active shop items matching filters
3. Filter out items that the student already owns (except free items)
4. For each item, calculate final price, discount percentage, and check if it's free
5. Return paginated list of available items

#### 4. Get Available Shop Items Grouped by Category

**Endpoint:** `GET /shop/items/available/grouped`

**Description:** Retrieves shop items that are available for purchase, grouped by category.

**Query Parameters:**
- `type` (optional): Filter by item type (FREE, IN_APP_PURCHASE)
- `featuredOnly` (optional): Show only featured items (true/false)

**Response:**
```json
{
  "success": true,
  "message": "Available shop items grouped by category retrieved successfully",
  "data": {
    "categories": [
      {
        "categoryId": "123e4567-e89b-12d3-a456-426614174000",
        "categoryName": "Skins",
        "items": [
          {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "itemNumber": "SK-001",
            "title": "Blue Ocean Theme",
            "description": "A calming blue ocean theme for your diary",
            "categoryId": "123e4567-e89b-12d3-a456-426614174000",
            "categoryName": "Skins",
            "type": "IN_APP_PURCHASE",
            "price": 9.99,
            "priceEquivalentToRewardPoint": 100,
            "shopItemCategory": "skin",
            "filePath": "https://example.com/media/shop-items/skin/blue-ocean.jpg",
            "isActive": true,
            "isFeatured": true,
            "finalPrice": 9.99,
            "isOnSale": false,
            "discountPercentage": 0,
            "isFreeOrPurchased": false
          }
        ]
      },
      {
        "categoryId": "223e4567-e89b-12d3-a456-426614174000",
        "categoryName": "Emoticons",
        "items": [
          {
            "id": "323e4567-e89b-12d3-a456-426614174000",
            "itemNumber": "EM-001",
            "title": "Happy Faces Pack",
            "description": "A collection of happy face emoticons",
            "categoryId": "223e4567-e89b-12d3-a456-426614174000",
            "categoryName": "Emoticons",
            "type": "IN_APP_PURCHASE",
            "price": 4.99,
            "isPurchasableInRewardpoint": true,
            "shopItemCategory": "emoticon",
            "filePath": "https://example.com/media/shop-items/emoticon/happy-faces.jpg",
            "isActive": true,
            "isFeatured": false,
            "finalPrice": 4.99,
            "isOnSale": false,
            "discountPercentage": 0,
            "isFreeOrPurchased": false
          }
        ]
      }
    ]
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Get all active categories
2. For each category, get available items for the student
3. Filter out categories with no available items
4. Return items grouped by category

#### 5. Get Available Shop Items by Category

**Endpoint:** `GET /shop/items/available/categories/:categoryId`

**Description:** Retrieves a paginated list of shop items that are available for purchase in a specific category.

**Path Parameters:**
- `categoryId`: Category ID

**Query Parameters:**
- `type` (optional): Filter by item type (FREE, IN_APP_PURCHASE)
- `featuredOnly` (optional): Show only featured items (true/false)
- `itemNumber` (optional): Filter by item number
- `title` (optional): Filter by title (partial match)
- `promotionId` (optional): Filter by promotion ID
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by (default: 'createdAt')
- `sortDirection`: Sort direction ('ASC' or 'DESC', default: 'DESC')

**Response:**
```json
{
  "success": true,
  "message": "Available shop items by category retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "itemNumber": "SK-001",
        "title": "Blue Ocean Theme",
        "description": "A calming blue ocean theme for your diary",
        "categoryId": "123e4567-e89b-12d3-a456-426614174000",
        "categoryName": "Skins",
        "type": "IN_APP_PURCHASE",
        "price": 9.99,
        "isPurchasableInRewardpoint": true,
        "shopItemCategory": "skin",
        "filePath": "https://example.com/media/shop-items/skin/blue-ocean.jpg",
        "isActive": true,
        "isFeatured": true,
        "finalPrice": 9.99,
        "isOnSale": false,
        "discountPercentage": 0,
        "isFreeOrPurchased": false
      }
    ],
    "totalItems": 1,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Verify the category exists
2. Extract pagination and filter parameters from query
3. Get available items for the student in the specified category
4. Return paginated list of available items

#### 6. Purchase Shop Item

**Endpoint:** `POST /shop/purchase`

**Description:** Purchases a shop item using reward points or other payment methods.

**Request Body:**
```json
{
  "shopItemId": "123e4567-e89b-12d3-a456-426614174000",
  "paymentMethod": "REWARD_POINTS",
  "paymentDetails": {},
  "notes": "Purchased using reward points"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Shop item purchased successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "userId": "123e4567-e89b-12d3-a456-426614174000",
    "shopItemId": "123e4567-e89b-12d3-a456-426614174000",
    "shopItemTitle": "Blue Ocean Theme",
    "originalPrice": 9.99,
    "finalPrice": 9.99,
    "paymentMethod": "REWARD_POINTS",
    "status": "COMPLETED",
    "createdAt": "2023-06-15T10:30:00Z"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate user and shop item
2. Check if item is already purchased
3. For free items, create a purchase record with zero price
4. For paid items, process payment based on payment method
5. For reward points, deduct points from user's balance
6. Create purchase record
7. Add item to user's owned items
8. Return purchase details

### Shopping Cart APIs

#### 1. Get Shopping Cart

**Endpoint:** `GET /shop/cart`

**Description:** Retrieves the current user's shopping cart, including promotion and discount information.

**Response:**
```json
{
  "success": true,
  "message": "Shopping cart retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "status": "active",
    "lastActivity": "2023-06-15T10:30:00Z",
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "shopItemId": "123e4567-e89b-12d3-a456-426614174000",
        "title": "Blue Ocean Theme",
        "originalPrice": 12.99,
        "price": 9.99,
        "discountPercentage": 23,
        "isOnSale": true,
        "promotionId": "123e4567-e89b-12d3-a456-426614174000",
        "rewardPoints": 100,
        "quantity": 1,
        "totalPrice": 9.99,
        "totalRewardPoints": 100,
        "imageUrl": "https://example.com/media/shop-items/skin/blue-ocean.jpg"
      }
    ],
    "totalPrice": 9.99,
    "totalRewardPoints": 100,
    "itemCount": 1
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Get user ID from request
2. Find or create active shopping cart for user
3. Retrieve cart items with shop item details
4. Calculate discount information (original price, final price, discount percentage)
5. Calculate totals (price, reward points, item count)
6. Return cart with items, discount information, and totals

#### 2. Add Item to Cart

**Endpoint:** `POST /shop/cart/add`

**Description:** Adds an item to the shopping cart, automatically applying any active promotions.

**Request Body:**
```json
{
  "shopItemId": "123e4567-e89b-12d3-a456-426614174000",
  "quantity": 1
}
```

**Response:**
```json
{
  "success": true,
  "message": "Item added to cart successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "status": "active",
    "lastActivity": "2023-06-15T10:30:00Z",
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "shopItemId": "123e4567-e89b-12d3-a456-426614174000",
        "title": "Blue Ocean Theme",
        "originalPrice": 12.99,
        "price": 9.99,
        "discountPercentage": 23,
        "isOnSale": true,
        "promotionId": "123e4567-e89b-12d3-a456-426614174000",
        "rewardPoints": 100,
        "quantity": 1,
        "totalPrice": 9.99,
        "totalRewardPoints": 100,
        "imageUrl": "https://example.com/media/shop-items/skin/blue-ocean.jpg"
      }
    ],
    "totalPrice": 9.99,
    "totalRewardPoints": 100,
    "itemCount": 1
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Validate shop item ID and quantity
2. Find or create active shopping cart for user
3. Check if item is already in cart
4. If already in cart, update quantity
5. If not in cart, add new item with the final price (including any promotions)
6. For free items, set reward points to 0
7. Update cart last activity timestamp
8. Return updated cart with items, discount information, and totals

#### 3. Checkout

**Endpoint:** `POST /shop/cart/checkout`

**Description:** Processes checkout for the shopping cart, preserving promotion information in purchase records.

**Request Body:**
```json
{
  "paymentMethod": "REWARD_POINTS",
  "useRewardPoints": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Checkout completed successfully",
  "data": {
    "orderId": "123e4567-e89b-12d3-a456-426614174000",
    "totalAmount": 9.99,
    "rewardPointsUsed": 100,
    "remainingRewardPoints": 900,
    "paymentMethod": "REWARD_POINTS",
    "purchaseDate": "2023-06-15T10:30:00Z",
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "shopItemId": "123e4567-e89b-12d3-a456-426614174000",
        "title": "Blue Ocean Theme",
        "originalPrice": 12.99,
        "price": 9.99,
        "discountPercentage": 23,
        "isOnSale": true,
        "promotionId": "123e4567-e89b-12d3-a456-426614174000",
        "rewardPoints": 100,
        "quantity": 1,
        "totalPrice": 9.99,
        "totalRewardPoints": 100,
        "imageUrl": "https://example.com/media/shop-items/skin/blue-ocean.jpg"
      }
    ]
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Get user ID from request
2. Find active shopping cart for user
3. Validate cart has items
4. Calculate total price and reward points
5. If using reward points, check user has sufficient balance
6. Process payment based on payment method
7. Create purchase records for each item, including:
   - Original price from the shop item
   - Final price from the cart item (already includes discounts)
   - Promotion ID from the shop item
   - Discount amount calculated from original and final prices
8. Add items to user's owned items
9. Mark the cart as checked out
10. Return order details with discount information

### Reward Points APIs

#### 1. Get Reward Points Balance

**Endpoint:** `GET /reward-points/balance`

**Description:** Retrieves the current user's reward points balance.

**Response:**
```json
{
  "success": true,
  "message": "Reward points balance retrieved successfully",
  "data": {
    "balance": 1000,
    "lastUpdated": "2023-06-15T10:30:00Z"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Get user ID from request
2. Calculate total earned points
3. Calculate total spent points
4. Calculate balance (earned - spent)
5. Return balance with last updated timestamp

### Student Owned Items APIs

#### 1. Get Owned Items

**Endpoint:** `GET /student/owned-items`

**Description:** Retrieves a paginated list of items owned by the current student.

**Query Parameters:**
- `category` (optional): Filter by item category (skin, emoticon)
- `status` (optional): Filter by item status (AVAILABLE, IN_USE, EXPIRED)
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by (default: 'acquiredDate')
- `sortDirection`: Sort direction ('ASC' or 'DESC', default: 'DESC')

**Response:**
```json
{
  "success": true,
  "message": "Student owned items retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "shopItemId": "123e4567-e89b-12d3-a456-426614174000",
        "title": "Blue Ocean Theme",
        "description": "A calming blue ocean theme for your diary",
        "categoryId": "123e4567-e89b-12d3-a456-426614174000",
        "categoryName": "Skin",
        "shopItemCategory": "skin",
        "status": "AVAILABLE",
        "acquiredDate": "2023-06-15T10:30:00Z",
        "expiryDate": null,
        "lastUsedDate": null,
        "isFavorite": false,
        "filePath": "https://example.com/media/shop-items/skin/blue-ocean.jpg",
        "notes": null
      }
    ],
    "totalItems": 1,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Get user ID from request
2. Extract pagination and filter parameters from query
3. Build query with filters
4. Retrieve owned items with pagination
5. Return paginated list of owned items

#### 2. Get Owned Items Grouped by Category

**Endpoint:** `GET /student/owned-items/grouped`

**Description:** Retrieves all items owned by the current student, grouped by category.

**Query Parameters:**
- `status` (optional): Filter by item status (AVAILABLE, IN_USE, EXPIRED)

**Response:**
```json
{
  "success": true,
  "message": "Student owned items grouped by category retrieved successfully",
  "data": {
    "categories": [
      {
        "categoryId": "123e4567-e89b-12d3-a456-426614174000",
        "categoryName": "Skin",
        "items": [
          {
            "id": "123e4567-e89b-12d3-a456-426614174000",
            "shopItemId": "123e4567-e89b-12d3-a456-426614174000",
            "title": "Blue Ocean Theme",
            "description": "A calming blue ocean theme for your diary",
            "categoryId": "123e4567-e89b-12d3-a456-426614174000",
            "categoryName": "Skin",
            "shopItemCategory": "skin",
            "status": "AVAILABLE",
            "acquiredDate": "2023-06-15T10:30:00Z",
            "expiryDate": null,
            "lastUsedDate": null,
            "isFavorite": false,
            "filePath": "https://example.com/media/shop-items/skin/blue-ocean.jpg",
            "notes": null
          }
        ]
      },
      {
        "categoryId": "223e4567-e89b-12d3-a456-426614174000",
        "categoryName": "Emoticon",
        "items": [
          {
            "id": "323e4567-e89b-12d3-a456-426614174000",
            "shopItemId": "423e4567-e89b-12d3-a456-426614174000",
            "title": "Happy Faces Pack",
            "description": "A collection of happy face emoticons",
            "categoryId": "223e4567-e89b-12d3-a456-426614174000",
            "categoryName": "Emoticon",
            "shopItemCategory": "emoticon",
            "status": "AVAILABLE",
            "acquiredDate": "2023-06-15T10:30:00Z",
            "expiryDate": null,
            "lastUsedDate": null,
            "isFavorite": false,
            "filePath": "https://example.com/media/shop-items/emoticon/happy-faces.jpg",
            "notes": null
          }
        ]
      }
    ]
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Get user ID from request
2. Query database for all owned items matching status filter
3. Group items by category
4. Return grouped items

#### 3. Get Owned Items by Category

**Endpoint:** `GET /student/owned-items/categories/:categoryId`

**Description:** Retrieves items owned by the current student in a specific category.

**Path Parameters:**
- `categoryId`: Category ID

**Query Parameters:**
- `status` (optional): Filter by item status (AVAILABLE, IN_USE, EXPIRED)
- `page` (optional): Page number for pagination
- `limit` (optional): Items per page for pagination
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Sort direction (ASC, DESC)

**Response:**
```json
{
  "success": true,
  "message": "Student owned items by category retrieved successfully",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-426614174000",
        "shopItemId": "123e4567-e89b-12d3-a456-426614174000",
        "title": "Blue Ocean Theme",
        "description": "A calming blue ocean theme for your diary",
        "categoryId": "123e4567-e89b-12d3-a456-426614174000",
        "categoryName": "Skin",
        "shopItemCategory": "skin",
        "status": "AVAILABLE",
        "acquiredDate": "2023-06-15T10:30:00Z",
        "expiryDate": null,
        "lastUsedDate": null,
        "isFavorite": false,
        "filePath": "https://example.com/media/shop-items/skin/blue-ocean.jpg",
        "notes": null
      }
    ],
    "totalItems": 1,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Get user ID from request
2. Verify category exists
3. Query database for owned items in the specified category
4. Apply pagination and sorting
5. Return items with pagination metadata

#### 4. Update Owned Item

**Endpoint:** `PATCH /student/owned-items/:id`

**Description:** Updates a specific owned item.

**Path Parameters:**
- `id`: Owned item ID

**Request Body:**
```json
{
  "status": "IN_USE",
  "isFavorite": true,
  "notes": "My favorite theme"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Owned item updated successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "shopItemId": "123e4567-e89b-12d3-a456-426614174000",
    "title": "Blue Ocean Theme",
    "description": "A calming blue ocean theme for your diary",
    "categoryId": "123e4567-e89b-12d3-a456-426614174000",
    "categoryName": "Skin",
    "shopItemCategory": "skin",
    "status": "IN_USE",
    "acquiredDate": "2023-06-15T10:30:00Z",
    "expiryDate": null,
    "lastUsedDate": "2023-06-16T10:30:00Z",
    "isFavorite": true,
    "filePath": "https://example.com/media/shop-items/skin/blue-ocean.jpg",
    "notes": "My favorite theme"
  },
  "errors": null,
  "statusCode": 200
}
```

**Algorithm:**
1. Get user ID from request
2. Verify owned item exists and belongs to the user
3. Update owned item with provided data
4. If status is changed to IN_USE, update lastUsedDate
5. Return updated owned item

## Data Models

### ShopItem

```typescript
enum ShopItemType {
  FREE = 'free',
  IN_APP_PURCHASE = 'in_app_purchase'
}

enum ShopItemCategory {
  SKIN = 'skin',
  EMOTICON = 'emoticon'
}

interface ShopItem {
  id: string;
  itemNumber: string;
  title: string;
  description: string;
  categoryId: string;
  category: ShopCategory;
  type: ShopItemType;
  price: number;
  priceEquivalentToRewardPoint: number;
  shopItemCategory: ShopItemCategory;
  filePath: string;
  isActive: boolean;
  isFeatured: boolean;
  promotionId: string;
  discountedPrice: number;
  metadata: any;
  purchaseCount: number;
  viewCount: number;
  createdAt: Date;
  updatedAt: Date;
}
```

### ShopItemPurchase

```typescript
enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  PAYPAL = 'paypal',
  REWARD_POINTS = 'reward_points',
  FREE = 'free'
}

enum PurchaseStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

interface ShopItemPurchase {
  id: string;
  userId: string;
  shopItemId: string;
  originalPrice: number;
  finalPrice: number;
  promotionId: string;
  discountAmount: number;
  paymentMethod: PaymentMethod;
  paymentDetails: any;
  status: PurchaseStatus;
  notes: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### RewardPoint

```typescript
enum RewardPointSource {
  DIARY_AWARD = 'diary_award',
  PLAY = 'play',
  QA = 'qa',
  NOVEL = 'novel',
  ESSAY = 'essay',
  SHOP_PURCHASE = 'shop_purchase',
  ADMIN_ADJUSTMENT = 'admin_adjustment'
}

enum RewardPointType {
  EARNED = 'earned',
  SPENT = 'spent'
}

interface RewardPoint {
  id: string;
  userId: string;
  source: RewardPointSource;
  type: RewardPointType;
  points: number;
  referenceId: string;
  description: string;
  expiryDate: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

## Features

1. **Shop Category Management**
   - Create, update, and delete shop categories
   - Organize shop items by category

2. **Shop Item Management**
   - Create, update, and delete shop items
   - Support for different item types (free, in-app purchase)
   - Support for different item categories (skin, emoticon)
   - File upload for item images
   - Automatic item number generation

3. **Shopping Cart Management**
   - Add items to cart with automatic promotion application
   - Update item quantities
   - Remove items from cart
   - Clear cart
   - Display original price, discounted price, and discount percentage
   - Track promotion information for each item

4. **Checkout Process**
   - Support for different payment methods
   - Reward points integration
   - Purchase history tracking with promotion details
   - Record original price, final price, and discount amount

5. **Reward Points System**
   - Earn points from various activities
   - Spend points on shop items
   - Track point balance and history

6. **Student Owned Items Management**
   - Track items owned by students
   - Group items by category
   - Filter items by category and status
   - Mark items as favorite
   - Apply skins to features
   - View owned items with pagination and sorting

7. **Diary Skin to Shop Item Workflow**
   - Draft shop items from diary skins
   - Publish diary skins to the shop
   - Automatic file path handling
   - Special handling for free items
   - Integration with the Diary Module
