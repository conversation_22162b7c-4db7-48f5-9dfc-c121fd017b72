import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { MissionDiaryEntry } from './mission-diary-entry.entity';

@Entity()
export class DiaryMission extends AuditableBaseEntity {
  @Column({ name: 'title' })
  title: string;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({ name: 'target_word_count', type: 'integer' })
  targetWordCount: number;

  @Column({ name: 'target_max_word_count', type: 'integer', nullable: true })
  targetMaxWordCount: number;

  @Column({ name: 'publish_date', type: 'timestamp' })
  publishDate: Date;

  @Column({ name: 'expiry_date', type: 'timestamp', nullable: true })
  expiryDate: Date;

  @Column({ name: 'admin_id', type: 'uuid' })
  adminId: string;

  @ManyToOne(() => User)
  @JoinC<PERSON>umn({ name: 'admin_id' })
  tutor: User;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'score', type: 'integer' })
  score: number;

  @OneToMany(() => MissionDiaryEntry, entry => entry.mission)
  entries: MissionDiaryEntry[];
}
