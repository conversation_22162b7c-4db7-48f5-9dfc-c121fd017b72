import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { JwtPayload } from './interfaces/jwt-payload.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    const secret = configService.get<string>('JWT_SECRET');
    if (!secret) {
      console.error('JWT_SECRET is not defined in environment variables');
      throw new Error('JWT_SECRET is not defined');
    }

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: secret,
    });
  }

  async validate(payload: JwtPayload) {
    return {
      id: payload.id || payload.sub, // Use id if available, otherwise use sub
      userId: payload.sub,
      username: payload.username, // This is now the userId from the token
      name: payload.name,
      type: payload.type,
      selectedRole: payload.selectedRole,
      roles: payload.roles,
      // Include default skin ID for students
      defaultSkinId: payload.defaultSkinId,
      // Include plan information for students
      activePlan: payload.activePlan,
      planType: payload.planType,
      planId: payload.planId,
      planExpiryDate: payload.planExpiryDate,
      planActive: payload.planActive
    };
  }
}
