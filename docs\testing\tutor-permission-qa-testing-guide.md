# Tutor Permission Management QA Testing Guide

This document provides a comprehensive testing guide for the Tutor Permission Management system, which allows admins to grant specific tutors permission to manage different features in the application.

## Test Environment Setup

1. **Database Setup**:
   - Ensure the database has the `tutor_permission` table
   - Ensure there are test users with the following roles:
     - Admin user
     - Multiple tutor users
     - Student user
   - Ensure there are test plan features in the database

2. **API Access**:
   - Obtain JWT tokens for admin, tutor, and student users
   - Set up API testing tools (Postman, Insomnia, etc.)

## Test Cases

### 1. Admin Permission Management

#### 1.1 Create Permission Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| CP-001 | Create permission with valid data | 1. Authenticate as admin<br>2. Send POST request to `/admin/permissions` with valid tutorId, planFeatureId, and notes | Permission created successfully with status 201<br>Response includes correct tutor and admin information |
| CP-002 | Create permission with invalid tutor ID | 1. Authenticate as admin<br>2. Send POST request with non-existent tutorId | 404 Not Found response |
| CP-003 | Create permission with non-tutor user | 1. Authenticate as admin<br>2. Send POST request with studentId as tutorId | 400 Bad Request response |
| CP-004 | Create permission with invalid plan feature ID | 1. Authenticate as admin<br>2. Send POST request with non-existent planFeatureId | 404 Not Found response |
| CP-005 | Create duplicate permission | 1. Authenticate as admin<br>2. Create permission<br>3. Create another permission with same tutorId and planFeatureId | 409 Conflict response |
| CP-006 | Create permission as non-admin | 1. Authenticate as tutor<br>2. Send POST request to create permission | 403 Forbidden response |

#### 1.2 Get Permissions Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| GP-001 | Get all permissions | 1. Authenticate as admin<br>2. Send GET request to `/admin/permissions` | List of all permissions returned |
| GP-002 | Get permissions with pagination | 1. Authenticate as admin<br>2. Send GET request with page=2&limit=5 | Second page of permissions with 5 items per page |
| GP-003 | Get permissions filtered by active status | 1. Authenticate as admin<br>2. Send GET request with isActive=true | Only active permissions returned |
| GP-004 | Get permissions filtered by plan feature | 1. Authenticate as admin<br>2. Send GET request with planFeatureId=xyz | Only permissions for specified plan feature returned |
| GP-005 | Get permissions with search term | 1. Authenticate as admin<br>2. Send GET request with searchTerm=john | Only permissions for tutors with "john" in name or email |
| GP-006 | Get permissions as non-admin | 1. Authenticate as tutor<br>2. Send GET request to get permissions | 403 Forbidden response |

#### 1.3 Update Permission Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| UP-001 | Update permission with valid data | 1. Authenticate as admin<br>2. Send PUT request to `/admin/permissions/:id` with updated notes | Permission updated successfully |
| UP-002 | Deactivate permission | 1. Authenticate as admin<br>2. Send PUT request with isActive=false | Permission deactivated successfully |
| UP-003 | Reactivate permission | 1. Authenticate as admin<br>2. Send PUT request with isActive=true | Permission reactivated successfully |
| UP-004 | Update non-existent permission | 1. Authenticate as admin<br>2. Send PUT request with invalid permission ID | 404 Not Found response |
| UP-005 | Update permission as non-admin | 1. Authenticate as tutor<br>2. Send PUT request to update permission | 403 Forbidden response |

#### 1.4 Delete Permission Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| DP-001 | Delete permission with valid ID | 1. Authenticate as admin<br>2. Send DELETE request to `/admin/permissions/:id` | Permission deleted successfully |
| DP-002 | Delete non-existent permission | 1. Authenticate as admin<br>2. Send DELETE request with invalid permission ID | 404 Not Found response |
| DP-003 | Delete permission as non-admin | 1. Authenticate as tutor<br>2. Send DELETE request to delete permission | 403 Forbidden response |

### 2. Feature Access Tests (Example: Q&A)

#### 2.1 Tutor with Permission Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| TP-001 | Create Q&A question with permission | 1. Grant Q&A permission to tutor<br>2. Authenticate as tutor<br>3. Send POST request to `/admin/qa/create` | Question created successfully |
| TP-002 | Get Q&A questions with permission | 1. Grant Q&A permission to tutor<br>2. Authenticate as tutor<br>3. Send GET request to `/admin/qa/questions` | List of questions returned |
| TP-003 | Update Q&A question with permission | 1. Grant Q&A permission to tutor<br>2. Authenticate as tutor<br>3. Send PUT request to `/admin/qa/question/:id` | Question updated successfully |
| TP-004 | Delete Q&A question with permission | 1. Grant Q&A permission to tutor<br>2. Authenticate as tutor<br>3. Send DELETE request to `/admin/qa/question/:id` | Question deleted successfully |
| TP-005 | Assign Q&A question with permission | 1. Grant Q&A permission to tutor<br>2. Authenticate as tutor<br>3. Send POST request to `/admin/qa/assign` | Question assigned successfully |
| TP-006 | Review Q&A submission with permission | 1. Grant Q&A permission to tutor<br>2. Authenticate as tutor<br>3. Send POST request to `/admin/qa/review/:id` | Submission reviewed successfully |

#### 2.2 Tutor without Permission Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| TN-001 | Create Q&A question without permission | 1. Authenticate as tutor without Q&A permission<br>2. Send POST request to `/admin/qa/create` | 403 Forbidden response |
| TN-002 | Get Q&A questions without permission | 1. Authenticate as tutor without Q&A permission<br>2. Send GET request to `/admin/qa/questions` | 403 Forbidden response |
| TN-003 | Update Q&A question without permission | 1. Authenticate as tutor without Q&A permission<br>2. Send PUT request to `/admin/qa/question/:id` | 403 Forbidden response |
| TN-004 | Delete Q&A question without permission | 1. Authenticate as tutor without Q&A permission<br>2. Send DELETE request to `/admin/qa/question/:id` | 403 Forbidden response |
| TN-005 | Assign Q&A question without permission | 1. Authenticate as tutor without Q&A permission<br>2. Send POST request to `/admin/qa/assign` | 403 Forbidden response |
| TN-006 | Review Q&A submission without permission | 1. Authenticate as tutor without Q&A permission<br>2. Send POST request to `/admin/qa/review/:id` | 403 Forbidden response |

#### 2.3 Admin Access Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| AA-001 | Admin access to Q&A features | 1. Authenticate as admin<br>2. Send requests to all Q&A endpoints | All requests successful |

#### 2.4 Student Access Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| SA-001 | Student access to admin Q&A features | 1. Authenticate as student<br>2. Send requests to admin Q&A endpoints | 403 Forbidden response for all requests |

### 3. Permission Revocation Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| PR-001 | Deactivate permission and test access | 1. Grant Q&A permission to tutor<br>2. Verify tutor can access Q&A features<br>3. Deactivate permission<br>4. Test tutor access to Q&A features | Tutor can no longer access Q&A features |
| PR-002 | Delete permission and test access | 1. Grant Q&A permission to tutor<br>2. Verify tutor can access Q&A features<br>3. Delete permission<br>4. Test tutor access to Q&A features | Tutor can no longer access Q&A features |

## Frontend Testing

### 1. Admin UI Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| AU-001 | Permission list display | 1. Login as admin<br>2. Navigate to permission management page | Permissions displayed correctly in table |
| AU-002 | Permission filtering | 1. Login as admin<br>2. Apply filters to permission list | Filtered results displayed correctly |
| AU-003 | Permission pagination | 1. Login as admin<br>2. Navigate through permission pages | Pagination works correctly |
| AU-004 | Create permission form | 1. Login as admin<br>2. Open create permission form<br>3. Fill in form and submit | Permission created and list updated |
| AU-005 | Edit permission form | 1. Login as admin<br>2. Click edit on a permission<br>3. Update form and submit | Permission updated and list updated |
| AU-006 | Delete permission | 1. Login as admin<br>2. Click delete on a permission<br>3. Confirm deletion | Permission deleted and list updated |

### 2. Tutor UI Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| TU-001 | Navigation menu with permission | 1. Grant Q&A permission to tutor<br>2. Login as tutor | Q&A management link appears in navigation |
| TU-002 | Navigation menu without permission | 1. Login as tutor without permissions | No admin feature links in navigation |
| TU-003 | Feature access with permission | 1. Grant Q&A permission to tutor<br>2. Login as tutor<br>3. Access Q&A management page | Page loads successfully |
| TU-004 | Feature access without permission | 1. Login as tutor without permissions<br>2. Try to access Q&A management page directly | Access denied with appropriate message |

## Error Handling Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| EH-001 | Invalid input error handling | 1. Submit form with invalid data | Appropriate error message displayed |
| EH-002 | Server error handling | 1. Trigger server error (e.g., database connection issue) | Appropriate error message displayed |
| EH-003 | Network error handling | 1. Disconnect from network<br>2. Submit form | Appropriate error message displayed |

## Performance Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| PT-001 | Permission check performance | 1. Create 100+ permissions<br>2. Measure time for permission checks | Permission checks complete in < 100ms |
| PT-002 | Permission list loading performance | 1. Create 1000+ permissions<br>2. Measure time to load permission list | List loads in < 2 seconds |

## Security Tests

| Test ID | Description | Steps | Expected Result |
|---------|-------------|-------|----------------|
| ST-001 | JWT token validation | 1. Use expired JWT token<br>2. Try to access protected endpoints | 401 Unauthorized response |
| ST-002 | CSRF protection | 1. Attempt CSRF attack on permission endpoints | Attack fails due to CSRF protection |
| ST-003 | SQL injection protection | 1. Attempt SQL injection in searchTerm parameter | No SQL injection vulnerability |

## Test Reporting

For each test case, record the following information:

1. Test ID
2. Test description
3. Test steps
4. Expected result
5. Actual result
6. Pass/Fail status
7. Comments/Issues
8. Test date and tester

## Regression Testing

After any changes to the permission management system, run the following regression tests:

1. Create, read, update, and delete permissions
2. Feature access with and without permissions
3. Permission revocation tests
4. Error handling tests

## Conclusion

This QA testing guide provides a comprehensive set of test cases for the Tutor Permission Management system. By following these test cases, you can ensure that the system works correctly and securely, allowing admins to grant and revoke permissions for tutors to manage specific features in the application.
