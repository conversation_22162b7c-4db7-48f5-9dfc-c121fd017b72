# Notification Service Integration Guide

This guide explains how to integrate the HEC Notification Service into your modules to send notifications to users through various channels.

## Table of Contents

1. [Overview](#overview)
2. [Setup](#setup)
3. [Basic Usage](#basic-usage)
4. [Notification Types](#notification-types)
5. [Extending Notification Types](#extending-notification-types)
6. [Notification Channels](#notification-channels)
7. [Advanced Usage](#advanced-usage)
8. [Best Practices](#best-practices)
9. [Examples](#examples)

## Overview

The HEC Notification Service provides a unified way to send notifications to users through multiple channels:

- In-app notifications
- Email notifications
- Push notifications
- Mobile notifications
- SMS notifications
- Real-time messaging

The service handles delivery tracking, retries for failed notifications, and user notification preferences.

## Setup

### 1. Import the NotificationModule

In your module file, import the `NotificationModule`:

```typescript
import { Module } from '@nestjs/common';
import { NotificationModule } from '../notification/notification.module';

@Module({
  imports: [
    NotificationModule,
    // other imports
  ],
  // ...
})
export class YourModule {}
```

### 2. Inject the NotificationHelperService

In your service file, inject the `NotificationHelperService`:

```typescript
import { Injectable } from '@nestjs/common';
import { NotificationHelperService } from '../notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';

@Injectable()
export class YourService {
  constructor(
    private readonly notificationHelper: NotificationHelperService,
    // other dependencies
  ) {}

  // Your service methods
}
```

## Basic Usage

The `NotificationHelperService` provides a simple interface to send notifications:

```typescript
async someMethod(userId: string) {
  await this.notificationHelper.notify(
    userId,
    NotificationType.SYSTEM,
    'Notification Title',
    'This is the notification message.'
  );
}
```

This will send a notification to the user through all enabled channels based on their preferences.

## Notification Types

The system supports the following notification types:

```typescript
export enum NotificationType {
  DIARY_SUBMISSION = 'diary_submission',  // Notification for diary entry submission
  DIARY_REVIEW = 'diary_review',          // Notification for diary entry review
  DIARY_FEEDBACK = 'diary_feedback',      // Notification for diary feedback
  TUTOR_GREETING = 'tutor_greeting',      // Notification for student greeting to tutor
  TUTOR_ASSIGNMENT = 'tutor_assignment',  // Notification for tutor assignment
  TUTOR_VERIFICATION = 'tutor_verification', // Notification for tutor verification
  CHAT_MESSAGE = 'chat_message',          // Notification for chat message
  SYSTEM = 'system'                       // System notification
}
```

Choose the appropriate type for your notification to ensure proper categorization and user preferences are respected.

## Extending Notification Types

As your application grows, you may need to add new notification types. Here's how to extend the notification system with custom types:

### 1. Update the NotificationType Enum

First, you need to add your new notification type to the `NotificationType` enum in `src/database/entities/notification.entity.ts`:

```typescript
export enum NotificationType {
  // Existing types
  DIARY_SUBMISSION = 'diary_submission',
  DIARY_REVIEW = 'diary_review',
  DIARY_FEEDBACK = 'diary_feedback',
  TUTOR_ASSIGNMENT = 'tutor_assignment',
  TUTOR_VERIFICATION = 'tutor_verification',
  CHAT_MESSAGE = 'chat_message',
  SYSTEM = 'system',

  // Add your new notification types here
  ESSAY_SUBMISSION = 'essay_submission',
  ESSAY_REVIEW = 'essay_review',
  PAYMENT_CONFIRMATION = 'payment_confirmation',
  SUBSCRIPTION_EXPIRY = 'subscription_expiry'
}
```

### 2. Create a Database Migration

After updating the enum, you need to create a database migration to update the PostgreSQL enum type:

```bash
npm run migration:generate -- src/database/migrations/AddNewNotificationTypes
```

Then edit the generated migration file:

```typescript
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNewNotificationTypes1234567890123 implements MigrationInterface {
  name = 'AddNewNotificationTypes1234567890123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add new values to the existing enum
    await queryRunner.query(`
      ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'essay_submission';
      ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'essay_review';
      ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'payment_confirmation';
      ALTER TYPE "public"."notification_type_enum" ADD VALUE IF NOT EXISTS 'subscription_expiry';
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // PostgreSQL doesn't support removing values from enums
    // You would need to create a new enum type and migrate data if you want to remove values
    // For simplicity, we'll leave this empty
  }
}
```

### 3. Run the Migration

Apply the migration to update the database:

```bash
npm run migration:run
```

### 4. Add Default Preferences (Optional)

If you want to set default preferences for your new notification types, you can add them to the seed data:

```typescript
// In your seed service or a dedicated migration
async function seedDefaultNotificationPreferences() {
  // For each user
  const users = await userRepository.find();

  for (const user of users) {
    // For each new notification type
    const newPreferences = [
      {
        userId: user.id,
        notificationType: NotificationType.ESSAY_SUBMISSION,
        channel: NotificationChannel.IN_APP,
        isEnabled: true
      },
      {
        userId: user.id,
        notificationType: NotificationType.ESSAY_SUBMISSION,
        channel: NotificationChannel.EMAIL,
        isEnabled: true
      },
      // Add more default preferences as needed
    ];

    await userNotificationPreferenceRepository.save(newPreferences);
  }
}
```

### 5. Update Frontend (If Applicable)

If you have a frontend that displays notification preferences, make sure to update it to include the new notification types.

### 6. Use Your New Notification Types

Now you can use your new notification types in your code:

```typescript
await this.notificationHelper.notify(
  userId,
  NotificationType.ESSAY_SUBMISSION,
  'Essay Submitted',
  'Your essay has been submitted successfully.',
  {
    relatedEntityId: essayId,
    relatedEntityType: 'essay',
    sendEmail: true,
    sendInApp: true
  }
);
```

### Best Practices for Extending Notification Types

1. **Use descriptive names**: Choose clear, descriptive names for your notification types.
2. **Document your types**: Add comments to the enum to explain what each type is used for.
3. **Group related types**: Keep related notification types together in the enum.
4. **Consider user preferences**: Think about which channels make sense for each notification type.
5. **Test thoroughly**: After adding new types, test that notifications are sent correctly.

## Notification Channels

The system supports the following notification channels:

```typescript
export enum NotificationChannel {
  IN_APP = 'in_app',                    // In-app notification
  EMAIL = 'email',                      // Email notification
  PUSH = 'push',                        // Push notification
  MOBILE = 'mobile',                    // Mobile notification
  SMS = 'sms',                          // SMS notification
  REALTIME_MESSAGE = 'realtime_message' // Real-time messaging
}
```

By default, the system will determine which channels to use based on user preferences. You can also specify which channels to use for a specific notification.

## Advanced Usage

### Specifying Channels

You can specify which channels to use for a notification:

```typescript
await this.notificationHelper.notify(
  userId,
  NotificationType.SYSTEM,
  'Notification Title',
  'This is the notification message.',
  {
    // Use channel control parameters
    sendEmail: true,
    sendPush: true,
    sendInApp: true,
    sendMobile: false,
    sendSms: false,
    sendRealtime: false
  }
);
```

### Adding Related Entity Information

You can associate a notification with a specific entity:

```typescript
await this.notificationHelper.notify(
  userId,
  NotificationType.DIARY_REVIEW,
  'Diary Entry Reviewed',
  'Your diary entry has been reviewed by your tutor.',
  {
    relatedEntityId: diaryEntryId,
    relatedEntityType: 'diary_entry'
  }
);
```

### HTML Content for Email Notifications

For email notifications, you can provide HTML content:

```typescript
await this.notificationHelper.notify(
  userId,
  NotificationType.DIARY_FEEDBACK,
  'New Feedback Received',
  'You have received feedback on your diary entry.',
  {
    htmlContent: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="color: #333;">New Feedback Received</h2>
        </div>
        <div style="margin-bottom: 20px;">
          <p>Hello,</p>
          <p>You have received feedback on your diary entry: <strong>"${diaryEntryTitle}"</strong></p>
          <p>Please log in to the system to view the feedback.</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
          <p>This is an automated message from the HEC system.</p>
          <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
        </div>
      </div>
    `,
    sendEmail: true
  }
);
```

### Sending to Multiple Users

You can send the same notification to multiple users:

```typescript
await this.notificationHelper.notifyMany(
  [userId1, userId2, userId3],
  NotificationType.SYSTEM,
  'System Maintenance',
  'The system will be down for maintenance on Saturday from 2-4 AM.'
);
```

### Channel-Specific Methods

For convenience, there are methods to send notifications through specific channels:

```typescript
// Send only via email
await this.notificationHelper.notifyEmail(
  userId,
  NotificationType.SYSTEM,
  'Email Only Notification',
  'This notification is sent only via email.',
  {
    htmlContent: '<p>HTML content for the email</p>'
  }
);

// Send only via push notification
await this.notificationHelper.notifyPush(
  userId,
  NotificationType.SYSTEM,
  'Push Only Notification',
  'This notification is sent only as a push notification.'
);

// Send only as in-app notification
await this.notificationHelper.notifyInApp(
  userId,
  NotificationType.SYSTEM,
  'In-App Only Notification',
  'This notification is only visible in the app.'
);
```

## Best Practices

1. **Choose the right notification type**: Use the appropriate notification type to ensure proper categorization and respect user preferences.

2. **Keep messages concise**: Notification messages should be clear and concise, especially for push notifications.

3. **Provide rich content for emails**: Use the `htmlContent` option to provide rich HTML content for email notifications.

4. **Handle errors gracefully**: The notification service handles errors internally and won't throw exceptions, but it's good practice to log any issues.

5. **Respect user preferences**: The system respects user notification preferences by default. Only override channels when necessary.

6. **Use related entity information**: Always include `relatedEntityId` and `relatedEntityType` when the notification is about a specific entity to enable deep linking.

## Examples

### Diary Submission Notification

```typescript
async notifyTutorAboutDiarySubmission(studentId: string, tutorId: string, diaryEntryId: string, diaryEntryTitle: string): Promise<void> {
  // Get student name
  const student = await this.userRepository.findOne({ where: { id: studentId } });

  // Create HTML content for email notification
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h2 style="color: #333;">New Diary Submission</h2>
      </div>
      <div style="margin-bottom: 20px;">
        <p>Hello,</p>
        <p>${student.name} has submitted a diary entry for review: <strong>"${diaryEntryTitle}"</strong></p>
        <p>Please log in to the system to review this submission.</p>
      </div>
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
        <p>This is an automated message from the HEC system.</p>
        <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
      </div>
    </div>
  `;

  // Send notification to tutor with all channels
  await this.notificationHelper.notify(
    tutorId,
    NotificationType.DIARY_SUBMISSION,
    'New Diary Submission',
    `${student.name} has submitted a diary entry for review: "${diaryEntryTitle}"`,
    {
      relatedEntityId: diaryEntryId,
      relatedEntityType: 'diary_entry',
      htmlContent: htmlContent,
      // Use channel control parameters
      sendEmail: true,
      sendPush: true,
      sendInApp: true,
      sendMobile: true,
      sendSms: false,
      sendRealtime: false
    }
  );
}
```

### System Maintenance Notification

```typescript
async notifyAllUsersAboutMaintenance(maintenanceDate: Date): Promise<void> {
  // Get all active user IDs
  const users = await this.userRepository.find({
    where: { isActive: true },
    select: ['id']
  });
  const userIds = users.map(user => user.id);

  // Format the date
  const formattedDate = maintenanceDate.toLocaleString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  // Create HTML content for email
  const htmlContent = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <div style="text-align: center; margin-bottom: 20px;">
        <h2 style="color: #333;">System Maintenance Notice</h2>
      </div>
      <div style="margin-bottom: 20px;">
        <p>Dear User,</p>
        <p>We will be performing scheduled maintenance on our systems on <strong>${formattedDate}</strong>.</p>
        <p>During this time, the HEC platform will be unavailable. We apologize for any inconvenience this may cause.</p>
        <p>Thank you for your understanding.</p>
      </div>
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
        <p>This is an automated message from the HEC system.</p>
        <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
      </div>
    </div>
  `;

  // Send notification to all users
  await this.notificationHelper.notifyMany(
    userIds,
    NotificationType.SYSTEM,
    'Scheduled Maintenance',
    `The system will be unavailable for maintenance on ${formattedDate}.`,
    {
      htmlContent: htmlContent,
      sendEmail: true,
      sendInApp: true,
      sendPush: true,
      sendMobile: false,
      sendSms: false,
      sendRealtime: false
    }
  );
}
```

### Tutor Greeting Notification

```typescript
async notifyTutorAboutGreeting(studentId: string, tutorId: string, greeting: string): Promise<void> {
  // Get student information
  const student = await this.userRepository.findOne({ where: { id: studentId } });

  // Generate a deep link to the student's profile
  const profileLink = this.deeplinkService.getWebLink(DeeplinkType.PROFILE, {
    userId: studentId,
    userType: 'student'
  });

  // Create HTML content for email notification
  const htmlContent = `
  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
    <div style="text-align: center; margin-bottom: 20px;">
      <h2 style="color: #333;">New Student Greeting</h2>
    </div>
    <div style="margin-bottom: 20px;">
      <p>Hello,</p>
      <p>${student.name} has set a greeting message for you:</p>
      <div style="background-color: #f9f9f9; padding: 15px; border-left: 4px solid #4CAF50; margin: 15px 0;">
        <p style="font-style: italic;">"${greeting}"</p>
      </div>
      <p>You can view their profile and start interacting with them.</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${profileLink}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Student Profile</a>
      </div>
    </div>
    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
      <p>This is an automated message from the HEC system.</p>
      <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
    </div>
  </div>
  `;

  // Send notification to the tutor
  await this.notificationHelper.notify(
    tutorId,
    NotificationType.TUTOR_GREETING,
    'New Student Greeting',
    `${student.name} has set a greeting message for you.`,
    {
      relatedEntityId: student.id,
      relatedEntityType: 'user',
      htmlContent: htmlContent,
      // Use all notification channels
      sendEmail: true,
      sendPush: true,
      sendInApp: true,
      sendMobile: true,
      sendSms: false,
      sendRealtime: false
    }
  );
}
```

### Chat Message Notification

```typescript
async notifyChatMessage(senderId: string, recipientId: string, conversationId: string, message: string): Promise<void> {
  // Get sender name
  const sender = await this.userRepository.findOne({ where: { id: senderId } });

  // Create notification title and message
  const title = `New message from ${sender.name}`;
  const notificationMessage = message.length > 50
    ? `${message.substring(0, 50)}...`
    : message;

  // Send notification
  await this.notificationHelper.notify(
    recipientId,
    NotificationType.CHAT_MESSAGE,
    title,
    notificationMessage,
    {
      relatedEntityId: conversationId,
      relatedEntityType: 'conversation',
      // For chat messages, we typically want push and in-app, but not email
      sendPush: true,
      sendInApp: true,
      sendEmail: false,
      sendMobile: true,
      sendSms: false,
      sendRealtime: true
    }
  );
}
```
