import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseFileRegistry } from './base-file-registry.entity';
import { DiaryEntry } from './diary-entry.entity';

@Entity()
export class DiaryQrRegistry extends BaseFileRegistry {
  @Column({ name: 'diary_entry_id' })
  diaryEntryId: string;

  @ManyToOne(() => DiaryEntry, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'diary_entry_id' })
  diaryEntry: DiaryEntry;

  @Column({ name: 'share_url' })
  shareUrl: string;

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      ...this.toSimpleObject(),
      diaryEntryId: this.diaryEntryId,
      shareUrl: this.shareUrl,
      diaryEntry: this.diaryEntry ? {
        id: this.diaryEntry.id,
        title: this.diaryEntry.title,
        content: this.diaryEntry.content,
        status: this.diaryEntry.status
      } : null
    };
  }
}
