# Diary Mission Management - Integration Flow

This document outlines the integration flow for the Diary Mission Management feature.

## Overview

The Diary Mission Management feature allows tutors to create writing missions with word count targets for students. Students can select missions, write entries to meet the targets, and tutors can provide feedback, corrections, and scores.

## API Endpoints

### Mission Management (Tutor Side)

#### Create a Mission
- **Endpoint**: `POST /diary/tutor/missions`
- **Auth**: JWT (Tutor only)
- **Request Body**:
  ```json
  {
    "title": "Weekly Writing Challenge",
    "description": "Write about your favorite book and why you enjoyed it.",
    "targetWordCount": 200,
    "targetMaxWordCount": 300, // Optional
    "publishDate": "2023-08-15T00:00:00Z",
    "expiryDate": "2023-08-30T23:59:59Z", // Optional
    "score": 100
  }
  ```
- **Response**: The created mission object

#### Update a Mission
- **Endpoint**: `PUT /diary/tutor/missions/:id`
- **Auth**: JWT (Tutor only)
- **Request Body**: Same as create, all fields optional
- **Response**: The updated mission object

#### Get a Specific Mission
- **Endpoint**: `GET /diary/tutor/missions/:id`
- **Auth**: JWT (Tutor only)
- **Response**: The mission object

#### Get Tutor's Missions
- **Endpoint**: `GET /diary/tutor/missions`
- **Auth**: JWT (Tutor only)
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10)
  - `isActive`: Filter by active status (boolean)
  - `publishDateFrom`: Filter by publish date (YYYY-MM-DD)
  - `publishDateTo`: Filter by publish date (YYYY-MM-DD)
- **Response**: Paged list of missions

#### Delete a Mission
- **Endpoint**: `DELETE /diary/tutor/missions/:id`
- **Auth**: JWT (Tutor only)
- **Response**: Success message

### Mission Management (Student Side)

#### Get Today's Mission
- **Endpoint**: `GET /diary/missions/today`
- **Auth**: JWT (Student only)
- **Response**: The featured mission for today

#### Get Available Missions
- **Endpoint**: `GET /diary/missions`
- **Auth**: JWT (Student only)
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10)
  - `publishDateFrom`: Filter by publish date (YYYY-MM-DD)
  - `publishDateTo`: Filter by publish date (YYYY-MM-DD)
  - `createdBy`: Filter by tutor ID
- **Response**: Paged list of available missions

#### Get a Specific Mission
- **Endpoint**: `GET /diary/missions/:id`
- **Auth**: JWT (Student only)
- **Response**: The mission object

### Mission Diary Entries (Student Side)

#### Create a Mission Entry
- **Endpoint**: `POST /diary/missions/entries`
- **Auth**: JWT (Student only)
- **Request Body**:
  ```json
  {
    "missionId": "123e4567-e89b-12d3-a456-426614174000",
    "content": "My favorite book is..."
  }
  ```
- **Response**: The created entry object

#### Update a Mission Entry
- **Endpoint**: `PUT /diary/missions/entries/:id`
- **Auth**: JWT (Student only)
- **Request Body**:
  ```json
  {
    "content": "Updated content about my favorite book..."
  }
  ```
- **Response**: The updated entry object

#### Submit a Mission Entry
- **Endpoint**: `POST /diary/missions/entries/:id/submit`
- **Auth**: JWT (Student only)
- **Request Body**: Empty object `{}`
- **Response**: The submitted entry object

#### Get Student's Mission Entries
- **Endpoint**: `GET /diary/missions/entries`
- **Auth**: JWT (Student only)
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10)
  - `missionId`: Filter by mission ID
  - `status`: Filter by status (NEW, SUBMITTED, REVIEWED, CONFIRMED)
  - `createdAtFrom`: Filter by creation date (YYYY-MM-DD)
  - `createdAtTo`: Filter by creation date (YYYY-MM-DD)
- **Response**: Paged list of entries

#### Get a Specific Mission Entry
- **Endpoint**: `GET /diary/missions/entries/:id`
- **Auth**: JWT (Student only)
- **Response**: The entry object with mission, feedback, and correction details

### Mission Diary Entries (Tutor Side)

#### Get Tutor's Mission Entries
- **Endpoint**: `GET /diary/tutor/missions/entries`
- **Auth**: JWT (Tutor only)
- **Query Parameters**:
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10)
  - `missionId`: Filter by mission ID
  - `studentId`: Filter by student ID
  - `status`: Filter by status (NEW, SUBMITTED, REVIEWED, CONFIRMED)
  - `createdAtFrom`: Filter by creation date (YYYY-MM-DD)
  - `createdAtTo`: Filter by creation date (YYYY-MM-DD)
- **Response**: Paged list of entries

#### Get a Specific Mission Entry
- **Endpoint**: `GET /diary/tutor/missions/entries/:id`
- **Auth**: JWT (Tutor only)
- **Response**: The entry object with mission, feedback, and correction details

#### Add Feedback to a Mission Entry
- **Endpoint**: `POST /diary/tutor/missions/entries/:id/feedback`
- **Auth**: JWT (Tutor only)
- **Request Body**:
  ```json
  {
    "feedback": "Great job on your analysis of the book!",
    "rating": 4 // Optional, 1-5
  }
  ```
- **Response**: The created feedback object

#### Add Correction with Score to a Mission Entry
- **Endpoint**: `POST /diary/tutor/missions/entries/:id/correction`
- **Auth**: JWT (Tutor only)
- **Description**: Provide both correction text and score for a mission diary entry in a single operation. Both fields are required.
- **Request Body**:
  ```json
  {
    "correction": "Here are some grammar corrections...",
    "score": 85
  }
  ```
- **Response**: The updated entry object with correction and score
- **Status**: Entry status will be set to `CONFIRMED` after successful operation

#### Deprecated Endpoints (Backward Compatibility)

##### Add Correction Only (DEPRECATED)
- **Endpoint**: `POST /diary/tutor/missions/entries/:id/correction-only`
- **Auth**: JWT (Tutor only)
- **Status**: DEPRECATED - Use the combined endpoint instead
- **Request Body**:
  ```json
  {
    "correction": "Here are some grammar corrections..."
  }
  ```
- **Response**: The updated entry object with correction

##### Assign Score Only (DEPRECATED)
- **Endpoint**: `POST /diary/tutor/missions/entries/:id/score`
- **Auth**: JWT (Tutor only)
- **Status**: DEPRECATED - Use the combined endpoint instead
- **Request Body**:
  ```json
  {
    "score": 85
  }
  ```
- **Response**: The updated entry object with score

## Integration Flow

### Tutor Flow

1. Create a mission with title, description, target word count, and publish date
2. View list of missions with filtering options
3. Update or delete missions as needed
4. Review submitted mission entries
5. Add feedback to entries
6. Add correction to entries (one-time only)
7. Assign score to entries

### Student Flow

1. View today's featured mission
2. Browse available missions
3. Create a mission entry
4. Update entry content until it meets the target word count
5. Submit entry for review
6. Receive feedback, correction, and score from tutor

## Notifications

The backend sends notifications for:
1. Student submits a mission entry (to tutor)
2. Tutor provides feedback (to student)
3. Tutor provides correction (to student)
4. Tutor assigns score (to student)

Note: No notification is sent when a tutor creates a mission.

## Deeplinks

The backend generates deeplinks for:
1. Missions: `diary/missions/:id`
2. Mission entries: `diary/mission-entries/:id`

Note: The deeplink paths remain the same despite the API endpoint changes.

## Error Handling

Common error scenarios:
- Mission not available (future publish date, expired, inactive)
- Cannot submit entry (insufficient word count, expired mission)
- Permission errors (403) when accessing resources from other users

## Conclusion

This document outlines the integration flow for the Diary Mission Management feature, including API endpoints, data models, and notification handling.
