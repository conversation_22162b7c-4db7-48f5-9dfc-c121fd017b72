# Feature Flag Convention

This document outlines the feature flag strategy for the HEC Backend project to enable controlled rollout of new features and safe experimentation.

## Table of Contents

1. [Feature Flag Philosophy](#feature-flag-philosophy)
2. [Types of Feature Flags](#types-of-feature-flags)
3. [Implementation](#implementation)
4. [Naming Conventions](#naming-conventions)
5. [Flag Lifecycle](#flag-lifecycle)
6. [Configuration Management](#configuration-management)
7. [Testing with Feature Flags](#testing-with-feature-flags)
8. [Documentation](#documentation)

## Feature Flag Philosophy

Feature flags (also known as feature toggles) allow us to:

1. **Deploy Without Releasing**: Separate deployment from feature release
2. **Controlled Rollout**: Gradually release features to subsets of users
3. **A/B Testing**: Compare different implementations
4. **Kill Switch**: Quickly disable problematic features
5. **Environment-Specific Features**: Enable features only in specific environments

## Types of Feature Flags

We use the following types of feature flags:

1. **Release Flags**: Control the visibility of features (temporary)
2. **Experiment Flags**: Support A/B testing (temporary)
3. **Ops Flags**: Control operational aspects like performance features (long-lived)
4. **Permission Flags**: Control access based on user roles or plans (long-lived)

## Implementation

### Feature Flag Service

```typescript
// feature-flag.service.ts
@Injectable()
export class FeatureFlagService {
  constructor(
    @InjectRepository(FeatureFlag)
    private readonly featureFlagRepository: Repository<FeatureFlag>,
    private readonly configService: ConfigService,
    private readonly cacheManager: Cache,
  ) {}

  /**
   * Check if a feature is enabled
   * @param flagName The name of the feature flag
   * @param context Additional context (user, environment, etc.)
   * @returns True if the feature is enabled, false otherwise
   */
  async isEnabled(flagName: string, context?: any): Promise<boolean> {
    // Check cache first
    const cacheKey = `feature_flag:${flagName}:${JSON.stringify(context || {})}`;
    const cachedValue = await this.cacheManager.get(cacheKey);
    if (cachedValue !== undefined) {
      return cachedValue as boolean;
    }

    // Check environment variables for override
    const envOverride = this.configService.get<string>(`FEATURE_FLAG_${flagName.toUpperCase()}`);
    if (envOverride !== undefined) {
      const isEnabled = envOverride === 'true';
      await this.cacheManager.set(cacheKey, isEnabled, { ttl: 60 }); // Cache for 1 minute
      return isEnabled;
    }

    // Check database
    const flag = await this.featureFlagRepository.findOne({ where: { name: flagName } });
    if (!flag) {
      await this.cacheManager.set(cacheKey, false, { ttl: 60 });
      return false;
    }

    // Check if flag is enabled globally
    if (!flag.isEnabled) {
      await this.cacheManager.set(cacheKey, false, { ttl: 60 });
      return false;
    }

    // Check user-specific rules
    if (context?.userId && flag.userIds) {
      const userIds = JSON.parse(flag.userIds);
      if (userIds.includes(context.userId)) {
        await this.cacheManager.set(cacheKey, true, { ttl: 60 });
        return true;
      }
    }

    // Check role-specific rules
    if (context?.roles && flag.roles) {
      const roles = JSON.parse(flag.roles);
      const userRoles = Array.isArray(context.roles) ? context.roles : [context.roles];
      if (userRoles.some(role => roles.includes(role))) {
        await this.cacheManager.set(cacheKey, true, { ttl: 60 });
        return true;
      }
    }

    // Check percentage rollout
    if (flag.percentage > 0 && context?.userId) {
      const hash = createHash('md5').update(context.userId + flagName).digest('hex');
      const hashValue = parseInt(hash.substring(0, 8), 16);
      const percentage = (hashValue % 100) + 1;
      const isEnabled = percentage <= flag.percentage;
      await this.cacheManager.set(cacheKey, isEnabled, { ttl: 60 });
      return isEnabled;
    }

    // Default to global setting
    await this.cacheManager.set(cacheKey, flag.isEnabled, { ttl: 60 });
    return flag.isEnabled;
  }

  /**
   * Get all feature flags
   * @returns List of all feature flags
   */
  async getAllFlags(): Promise<FeatureFlag[]> {
    return this.featureFlagRepository.find();
  }

  /**
   * Update a feature flag
   * @param flagName The name of the feature flag
   * @param updates The updates to apply
   * @returns The updated feature flag
   */
  async updateFlag(flagName: string, updates: Partial<FeatureFlag>): Promise<FeatureFlag> {
    const flag = await this.featureFlagRepository.findOne({ where: { name: flagName } });
    if (!flag) {
      throw new NotFoundException(`Feature flag ${flagName} not found`);
    }

    Object.assign(flag, updates);
    await this.featureFlagRepository.save(flag);

    // Invalidate cache
    await this.cacheManager.del(`feature_flag:${flagName}:*`);

    return flag;
  }
}
```

### Feature Flag Entity

```typescript
// feature-flag.entity.ts
@Entity('feature_flags')
export class FeatureFlag {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  name: string;

  @Column({ default: false })
  isEnabled: boolean;

  @Column({ nullable: true })
  description: string;

  @Column({ type: 'enum', enum: FeatureFlagType, default: FeatureFlagType.RELEASE })
  type: FeatureFlagType;

  @Column({ nullable: true })
  userIds: string; // JSON string of user IDs

  @Column({ nullable: true })
  roles: string; // JSON string of roles

  @Column({ default: 0 })
  percentage: number; // Percentage of users who get the feature (0-100)

  @Column({ nullable: true })
  expiresAt: Date; // When the flag should be automatically removed

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

export enum FeatureFlagType {
  RELEASE = 'release',
  EXPERIMENT = 'experiment',
  OPS = 'ops',
  PERMISSION = 'permission',
}
```

### Usage in Controllers and Services

```typescript
// users.controller.ts
@Controller('api/v1/users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly featureFlagService: FeatureFlagService,
  ) {}

  @Get()
  async findAll(@Request() req): Promise<ApiResponse<UserResponseDto[]>> {
    const users = await this.usersService.findAll();
    
    // Check if enhanced user data feature is enabled
    const enhancedDataEnabled = await this.featureFlagService.isEnabled(
      'enhanced_user_data',
      { userId: req.user?.id, roles: req.user?.roles }
    );
    
    if (enhancedDataEnabled) {
      // Return enhanced user data
      const enhancedUsers = await this.usersService.enhanceUsers(users);
      return ApiResponse.success(enhancedUsers, 'Users retrieved successfully');
    }
    
    // Return standard user data
    return ApiResponse.success(users, 'Users retrieved successfully');
  }
}
```

## Naming Conventions

Feature flag names should be:

1. **Descriptive**: Clearly indicate what the flag controls
2. **Consistent**: Follow a consistent naming pattern
3. **Lowercase**: Use lowercase with underscores
4. **Prefixed**: Use prefixes to categorize flags

Examples:
- `release_new_dashboard`
- `exp_alternative_login_flow`
- `ops_enhanced_caching`
- `perm_advanced_analytics`

## Flag Lifecycle

Feature flags, especially release flags, should have a defined lifecycle:

1. **Creation**: Flag is created and disabled by default
2. **Testing**: Flag is enabled in development/testing environments
3. **Gradual Rollout**: Flag is enabled for a percentage of users or specific user groups
4. **Full Rollout**: Flag is enabled for all users
5. **Cleanup**: Flag is removed once the feature is stable

### Cleanup Process

1. Identify flags that have been fully enabled for more than 30 days
2. Create a cleanup plan for each flag
3. Remove flag checks from the codebase
4. Remove the flag from the database

## Configuration Management

### Environment-Specific Configuration

Use environment variables to override feature flags in different environments:

```
# .env.development
FEATURE_FLAG_NEW_DASHBOARD=true
FEATURE_FLAG_EXPERIMENTAL_SEARCH=true

# .env.production
FEATURE_FLAG_NEW_DASHBOARD=false
FEATURE_FLAG_EXPERIMENTAL_SEARCH=false
```

### Admin Interface

Create an admin interface to manage feature flags:

```typescript
// feature-flags.controller.ts
@Controller('api/v1/admin/feature-flags')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
export class FeatureFlagsController {
  constructor(private readonly featureFlagService: FeatureFlagService) {}

  @Get()
  async getAllFlags(): Promise<ApiResponse<FeatureFlag[]>> {
    const flags = await this.featureFlagService.getAllFlags();
    return ApiResponse.success(flags, 'Feature flags retrieved successfully');
  }

  @Patch(':name')
  async updateFlag(
    @Param('name') name: string,
    @Body() updates: UpdateFeatureFlagDto,
  ): Promise<ApiResponse<FeatureFlag>> {
    const flag = await this.featureFlagService.updateFlag(name, updates);
    return ApiResponse.success(flag, 'Feature flag updated successfully');
  }
}
```

## Testing with Feature Flags

### Unit Testing

```typescript
// users.controller.spec.ts
describe('UsersController', () => {
  let controller: UsersController;
  let usersService: UsersService;
  let featureFlagService: FeatureFlagService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: {
            findAll: jest.fn(),
            enhanceUsers: jest.fn(),
          },
        },
        {
          provide: FeatureFlagService,
          useValue: {
            isEnabled: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get<UsersService>(UsersService);
    featureFlagService = module.get<FeatureFlagService>(FeatureFlagService);
  });

  describe('findAll', () => {
    it('should return enhanced user data when feature flag is enabled', async () => {
      // Arrange
      const users = [{ id: '1', name: 'John' }];
      const enhancedUsers = [{ id: '1', name: 'John', extraData: 'value' }];
      const req = { user: { id: '1', roles: ['admin'] } };
      
      usersService.findAll.mockResolvedValue(users);
      usersService.enhanceUsers.mockResolvedValue(enhancedUsers);
      featureFlagService.isEnabled.mockResolvedValue(true);
      
      // Act
      const result = await controller.findAll(req);
      
      // Assert
      expect(featureFlagService.isEnabled).toHaveBeenCalledWith(
        'enhanced_user_data',
        { userId: '1', roles: ['admin'] }
      );
      expect(usersService.enhanceUsers).toHaveBeenCalledWith(users);
      expect(result.data).toEqual(enhancedUsers);
    });

    it('should return standard user data when feature flag is disabled', async () => {
      // Arrange
      const users = [{ id: '1', name: 'John' }];
      const req = { user: { id: '1', roles: ['admin'] } };
      
      usersService.findAll.mockResolvedValue(users);
      featureFlagService.isEnabled.mockResolvedValue(false);
      
      // Act
      const result = await controller.findAll(req);
      
      // Assert
      expect(featureFlagService.isEnabled).toHaveBeenCalledWith(
        'enhanced_user_data',
        { userId: '1', roles: ['admin'] }
      );
      expect(usersService.enhanceUsers).not.toHaveBeenCalled();
      expect(result.data).toEqual(users);
    });
  });
});
```

### Integration Testing

```typescript
// users.integration.spec.ts
describe('Users Integration', () => {
  let app: INestApplication;
  let featureFlagRepository: Repository<FeatureFlag>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    featureFlagRepository = moduleFixture.get(getRepositoryToken(FeatureFlag));
  });

  afterAll(async () => {
    await app.close();
  });

  describe('GET /api/v1/users', () => {
    it('should return enhanced user data when feature flag is enabled', async () => {
      // Arrange
      await featureFlagRepository.save({
        name: 'enhanced_user_data',
        isEnabled: true,
        description: 'Enable enhanced user data',
        type: FeatureFlagType.RELEASE,
      });

      const token = await getAuthToken(app, 'admin', 'Admin@123');

      // Act
      const response = await request(app.getHttpServer())
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data[0]).toHaveProperty('extraData');
    });

    it('should return standard user data when feature flag is disabled', async () => {
      // Arrange
      await featureFlagRepository.update(
        { name: 'enhanced_user_data' },
        { isEnabled: false }
      );

      const token = await getAuthToken(app, 'admin', 'Admin@123');

      // Act
      const response = await request(app.getHttpServer())
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      // Assert
      expect(response.body.success).toBe(true);
      expect(response.body.data[0]).not.toHaveProperty('extraData');
    });
  });
});
```

## Documentation

### Feature Flag Inventory

Maintain a document listing all feature flags:

```markdown
# Feature Flag Inventory

| Flag Name | Type | Description | Default | Expiry Date | Owner |
|-----------|------|-------------|---------|-------------|-------|
| enhanced_user_data | RELEASE | Enables enhanced user data in API responses | false | 2023-12-31 | @johndoe |
| experimental_search | EXPERIMENT | A/B test for new search algorithm | false | 2023-10-15 | @janedoe |
| advanced_analytics | PERMISSION | Access to advanced analytics features | false | N/A | @johndoe |
```

### API Documentation

Document feature flag effects in API documentation:

```typescript
@Get()
@ApiOperation({
  summary: 'Get all users',
  description: 'Retrieves all users. When the "enhanced_user_data" feature flag is enabled, additional user data is included in the response.',
})
@ApiOkResponseWithArrayType(UserResponseDto, 'Users retrieved successfully')
async findAll(@Request() req): Promise<ApiResponse<UserResponseDto[]>> {
  // Implementation
}
```

---

This feature flag convention provides a framework for safely introducing new features while maintaining API stability. By following these guidelines, the team can experiment with new functionality without disrupting existing clients.
