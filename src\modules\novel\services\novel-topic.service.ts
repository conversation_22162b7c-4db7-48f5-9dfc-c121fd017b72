import { Injectable, NotFoundException, ConflictException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NovelTopic } from '../../../database/entities/novel-topic.entity';
import {
  CreateNovelTopicDto,
  UpdateNovelTopicDto,
  NovelTopicResponseDto,
  NovelCategoryResponseDto
} from '../../../database/models/novel.dto';
import { NOVEL_TOPIC_CATEGORIES, NOVEL_TOPIC_CATEGORY_DESCRIPTIONS } from '../constants/novel.constants';

@Injectable()
export class NovelTopicService {
  private readonly logger = new Logger(NovelTopicService.name);

  constructor(
    @InjectRepository(NovelTopic)
    private readonly novelTopicRepository: Repository<NovelTopic>
  ) {}

  async createTopic(createTopicDto: CreateNovelTopicDto): Promise<NovelTopicResponseDto> {
    try {
      // Check if sequence title already exists
      const existingTopic = await this.novelTopicRepository.findOne({
        where: { sequenceTitle: createTopicDto.sequenceTitle }
      });

      if (existingTopic) {
        throw new ConflictException(`Topic with sequence title "${createTopicDto.sequenceTitle}" already exists`);
      }

      const topic = this.novelTopicRepository.create({
        ...createTopicDto,
        category: createTopicDto.category as any
      });
      const savedTopic = await this.novelTopicRepository.save(topic);

      this.logger.log(`Created novel topic: ${savedTopic.sequenceTitle}`);
      return this.mapTopicToResponseDto(savedTopic);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Error creating novel topic: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getAllTopics(): Promise<NovelTopicResponseDto[]> {
    const topics = await this.novelTopicRepository.find({
      order: { createdAt: 'DESC' }
    });

    return topics.map(topic => this.mapTopicToResponseDto(topic));
  }

  async getActiveTopicsByCategory(category?: string): Promise<NovelTopicResponseDto[]> {
    const whereCondition: any = { isActive: true };

    if (category) {
      whereCondition.category = category;
    }

    const topics = await this.novelTopicRepository.find({
      where: whereCondition,
      order: { createdAt: 'DESC' }
    });

    return topics.map(topic => this.mapTopicToResponseDto(topic));
  }

  async getTopicById(id: string): Promise<NovelTopicResponseDto> {
    const topic = await this.novelTopicRepository.findOne({
      where: { id }
    });

    if (!topic) {
      throw new NotFoundException(`Novel topic with ID ${id} not found`);
    }

    return this.mapTopicToResponseDto(topic);
  }

  async updateTopic(id: string, updateTopicDto: UpdateNovelTopicDto): Promise<NovelTopicResponseDto> {
    const topic = await this.novelTopicRepository.findOne({
      where: { id }
    });

    if (!topic) {
      throw new NotFoundException(`Novel topic with ID ${id} not found`);
    }

    // Check if sequence title already exists (if being updated)
    if (updateTopicDto.sequenceTitle && updateTopicDto.sequenceTitle !== topic.sequenceTitle) {
      const existingTopic = await this.novelTopicRepository.findOne({
        where: { sequenceTitle: updateTopicDto.sequenceTitle }
      });

      if (existingTopic) {
        throw new ConflictException(`Topic with sequence title "${updateTopicDto.sequenceTitle}" already exists`);
      }
    }

    Object.assign(topic, updateTopicDto);
    const updatedTopic = await this.novelTopicRepository.save(topic);

    this.logger.log(`Updated novel topic: ${updatedTopic.sequenceTitle}`);
    return this.mapTopicToResponseDto(updatedTopic);
  }

  async deleteTopic(id: string): Promise<void> {
    const topic = await this.novelTopicRepository.findOne({
      where: { id },
      relations: ['entries']
    });

    if (!topic) {
      throw new NotFoundException(`Novel topic with ID ${id} not found`);
    }

    // Check if topic has entries
    if (topic.entries && topic.entries.length > 0) {
      throw new ConflictException('Cannot delete topic that has entries');
    }

    await this.novelTopicRepository.remove(topic);
    this.logger.log(`Deleted novel topic: ${topic.sequenceTitle}`);
  }

  async getCategories(): Promise<NovelCategoryResponseDto[]> {
    return Object.values(NOVEL_TOPIC_CATEGORIES).map(category => ({
      value: category,
      description: NOVEL_TOPIC_CATEGORY_DESCRIPTIONS[category]
    }));
  }

  private mapTopicToResponseDto(topic: NovelTopic): NovelTopicResponseDto {
    return {
      id: topic.id,
      title: topic.title,
      sequenceTitle: topic.sequenceTitle,
      category: topic.category,
      instruction: topic.instruction,
      isActive: topic.isActive,
      createdAt: topic.createdAt,
      updatedAt: topic.updatedAt
    };
  }
}
