# Automatic Tutor Assignment System - Integration Flow

This document provides a clear integration flow for frontend developers to implement the Automatic Tutor Assignment features with minimal verbal communication needed. It focuses exclusively on API endpoints and data flows without prescribing specific frontend implementation approaches.

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Integration Flow Diagrams](#integration-flow-diagrams)
   - [Plan Subscription Flow](#plan-subscription-flow)
   - [Tutor Assignment Flow](#tutor-assignment-flow)
   - [Tutor-Student Interaction Flow](#tutor-student-interaction-flow)
4. [API Endpoints](#api-endpoints)
   - [Student Tutor Assignments](#student-tutor-assignments)
   - [Tutor Student Lists](#tutor-student-lists)
   - [Assignment Management](#assignment-management)
5. [Notification Integration](#notification-integration)
6. [Chat Integration](#chat-integration)
7. [Error Handling](#error-handling)

## Overview

The Automatic Tutor Assignment system allows:
- Automatic assignment of tutors to students based on subscription plans
- Workload balancing among tutors
- Module-specific tutor assignments
- Student-tutor communication
- Assignment management by administrators

## Authentication

All API requests require authentication using a JWT token. Include the token in the Authorization header:

```
Authorization: Bearer <token>
```

The token contains user information including the user's ID and role, which is used to enforce access control.

## Integration Flow Diagrams

### Plan Subscription Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│             │      │             │      │             │      │             │
│  Subscribe  │      │  Process    │      │  Assign     │      │  Send       │
│  to Plan    │─────▶│  Payment    │─────▶│  Tutors     │─────▶│  Notifications│
│             │      │             │      │             │      │             │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

1. Student subscribes to a plan
2. System processes payment
3. System automatically assigns tutors for each module in the plan
4. Notifications are sent to both student and assigned tutors

### Tutor Assignment Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│             │      │             │      │             │      │             │
│  Check      │      │  Select     │      │  Create     │      │  Update     │
│  Modules    │─────▶│  Tutors     │─────▶│  Mappings   │─────▶│  Last Activity│
│             │      │             │      │             │      │             │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

1. System identifies modules in student's plan
2. System selects appropriate tutors based on workload
3. System creates student-tutor mappings
4. Last activity dates are updated when students interact with tutors

### Tutor-Student Interaction Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│             │      │             │      │             │      │             │
│  View       │      │  Start      │      │  Exchange   │      │  Submit     │
│  Assignments│─────▶│  Chat       │─────▶│  Messages   │─────▶│  Work       │
│             │      │             │      │             │      │             │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

1. Student/Tutor views their assignments
2. Student/Tutor initiates chat
3. Student/Tutor exchanges messages
4. Student submits work (diary entries, essays) for tutor review

## API Endpoints

### Student Tutor Assignments

#### Get Student's Tutors

```
GET /api/tutor-matching/student/tutors
```

Query Parameters:
- `planFeatureId` (optional): Filter by module
- `status` (optional): Filter by status (active, inactive)

Response:
```json
{
  "success": true,
  "message": "Tutors retrieved successfully",
  "data": {
    "items": [
      {
        "id": "tutor-id",
        "name": "Tutor Name",
        "email": "<EMAIL>",
        "profilePicture": "https://example.com/profile.jpg",
        "bio": "Tutor bio...",
        "planFeatureId": "module-id",
        "moduleName": "Diary Module",
        "status": "active",
        "assignedDate": "2023-01-01T00:00:00Z"
      }
    ],
    "totalItems": 1,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  },
  "errors": null,
  "statusCode": 200
}
```

### Tutor Student Lists

#### Get Tutor's Students

```
GET /api/tutor-matching/tutor/students
```

Query Parameters:
- `planFeatureId` (optional): Filter by module
- `status` (optional): Filter by status (active, inactive)
- `page` (optional): Page number
- `limit` (optional): Items per page
- `sortBy` (optional): Field to sort by
- `sortDirection` (optional): Sort direction (ASC, DESC)

Response:
```json
{
  "success": true,
  "message": "Students retrieved successfully",
  "data": {
    "items": [
      {
        "id": "student-id",
        "name": "Student Name",
        "email": "<EMAIL>",
        "profilePicture": "https://example.com/profile.jpg",
        "planFeatureId": "module-id",
        "moduleName": "Diary Module",
        "status": "active",
        "assignedDate": "2023-01-01T00:00:00Z",
        "lastActivityDate": "2023-01-15T00:00:00Z"
      }
    ],
    "totalItems": 1,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 1
  },
  "errors": null,
  "statusCode": 200
}
```

### Assignment Management

#### Admin: Assign Tutor

```
POST /api/admin/tutor-matching/assign
```

Request Body:
```json
{
  "studentId": "student-id",
  "tutorId": "tutor-id",
  "planFeatureId": "module-id",
  "notes": "Manual assignment by admin"
}
```

Response:
```json
{
  "success": true,
  "message": "Tutor assigned successfully",
  "data": {
    "id": "assignment-id",
    "studentId": "student-id",
    "studentName": "Student Name",
    "tutorId": "tutor-id",
    "tutorName": "Tutor Name",
    "planFeatureId": "module-id",
    "moduleName": "Diary Module",
    "status": "active",
    "assignedDate": "2023-01-01T00:00:00Z",
    "lastActivityDate": null,
    "notes": "Manual assignment by admin",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  },
  "errors": null,
  "statusCode": 200
}
```

#### Admin: Update Assignment

```
PUT /api/admin/tutor-matching/assignments/:id
```

Request Body:
```json
{
  "tutorId": "new-tutor-id",
  "status": "active",
  "notes": "Reassigned to new tutor"
}
```

Response:
```json
{
  "success": true,
  "message": "Assignment updated successfully",
  "data": {
    "id": "assignment-id",
    "studentId": "student-id",
    "studentName": "Student Name",
    "tutorId": "new-tutor-id",
    "tutorName": "New Tutor Name",
    "planFeatureId": "module-id",
    "moduleName": "Diary Module",
    "status": "active",
    "assignedDate": "2023-01-01T00:00:00Z",
    "lastActivityDate": null,
    "notes": "Reassigned to new tutor",
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-02T00:00:00Z"
  },
  "errors": null,
  "statusCode": 200
}
```

## Notification Integration

The tutor assignment system integrates with the notification system to inform users about assignments:

### Student Notifications

When a student is assigned a tutor, they receive notifications through multiple channels:

1. **In-App Notification**:
```json
{
  "type": "TUTOR_ASSIGNMENT",
  "title": "New Tutor for Diary Module",
  "message": "You have been assigned John Doe as your tutor for Diary Module.",
  "relatedEntityId": "assignment-id",
  "relatedEntityType": "student_tutor_mapping",
  "deepLink": "hec://tutor-matching/mapping/assignment-id"
}
```

2. **Push Notification**:
```json
{
  "title": "New Tutor Assignments",
  "body": "You have been assigned 1 tutor for your modules.",
  "data": {
    "notificationId": "notification-id",
    "type": "TUTOR_ASSIGNMENT",
    "relatedEntityId": "assignment-id",
    "relatedEntityType": "student_tutor_mapping",
    "deepLink": "hec://tutor-matching/mapping/assignment-id"
  }
}
```

### Tutor Notifications

When a tutor is assigned a student, they receive notifications through multiple channels:

1. **In-App Notification**:
```json
{
  "type": "TUTOR_ASSIGNMENT",
  "title": "New Student Assignment",
  "message": "You have been assigned as a tutor for Jane Doe in Diary Module.",
  "relatedEntityId": "assignment-id",
  "relatedEntityType": "student_tutor_mapping",
  "deepLink": "hec://tutor-matching/mapping/assignment-id"
}
```

2. **Email Notification**:
```html
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
  <div style="text-align: center; margin-bottom: 20px;">
    <h2 style="color: #333;">New Student Assignment</h2>
  </div>
  <div style="margin-bottom: 20px;">
    <p>Hello John Doe,</p>
    <p>You have been assigned as a tutor for Jane Doe in Diary Module.</p>
    <p>Please review your new student's profile and be prepared to provide guidance and feedback.</p>
    <div style="text-align: center; margin: 30px 0;">
      <a href="https://example.com/tutor-matching/mapping/assignment-id" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">View Assignment</a>
    </div>
  </div>
  <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
    <p>This is an automated message from the HEC system.</p>
    <p>&copy; 2023 HEC. All rights reserved.</p>
  </div>
</div>
```

## Chat Integration

The tutor assignment system integrates with the chat system to enable communication between students and tutors:

1. When a tutor is assigned to a student, they are automatically added to each other's chat contacts
2. Either the student or tutor can initiate a conversation
3. The chat system uses the existing WebSocket connection for real-time messaging

### Get Chat Contacts

```
GET /api/chat/contacts
```

Response:
```json
{
  "success": true,
  "message": "Contacts retrieved successfully",
  "data": [
    {
      "id": "user-id",
      "name": "User Name",
      "email": "<EMAIL>",
      "type": "student|tutor|admin",
      "profilePicture": "https://example.com/profile.jpg"
    }
  ],
  "errors": null,
  "statusCode": 200
}
```

### Get or Create Conversation

```
GET /api/chat/conversations/with/:userId
```

Response:
```json
{
  "success": true,
  "message": "Conversation retrieved successfully",
  "data": {
    "id": "conversation-id",
    "participants": [
      {
        "id": "user-id-1",
        "name": "User Name 1",
        "email": "<EMAIL>",
        "type": "student|tutor|admin",
        "profilePicture": "https://example.com/profile1.jpg"
      },
      {
        "id": "user-id-2",
        "name": "User Name 2",
        "email": "<EMAIL>",
        "type": "student|tutor|admin",
        "profilePicture": "https://example.com/profile2.jpg"
      }
    ],
    "lastMessage": {
      "id": "message-id",
      "senderId": "user-id-1",
      "content": "Hello!",
      "type": "text",
      "status": "sent|delivered|read",
      "createdAt": "2023-01-01T00:00:00Z"
    },
    "unreadCount": 0,
    "createdAt": "2023-01-01T00:00:00Z",
    "updatedAt": "2023-01-01T00:00:00Z"
  },
  "errors": null,
  "statusCode": 200
}
```



## Error Handling

### Common Error Scenarios

1. **Tutor Already Assigned**
   - Error Code: 409
   - Message: "Student already has an active tutor for this module"
   - Response includes details about the existing assignment

2. **No Tutors Available**
   - Error Code: 400
   - Message: "No tutors available for assignment"
   - Response includes reason for unavailability

3. **Invalid Module**
   - Error Code: 404
   - Message: "Module feature with ID {id} not found"
   - Response includes details about valid modules

4. **Assignment Not Found**
   - Error Code: 404
   - Message: "Assignment with ID {id} not found"
   - Response includes error details

### API Error Responses

All API endpoints return standardized error responses:

```json
{
  "success": false,
  "message": "Error message",
  "data": null,
  "errors": [
    {
      "field": "moduleId",
      "message": "Module not found"
    }
  ],
  "statusCode": 400
}
```
