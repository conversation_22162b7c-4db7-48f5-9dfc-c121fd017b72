import { MigrationInterface, QueryRunner } from "typeorm";

export class DiaryAttendance1747052756053 implements MigrationInterface {
    name = 'DiaryAttendance1747052756053'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tutor_education" DROP CONSTRAINT "FK_tutor_education_tutor"`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_attendance_status_enum" AS ENUM('present', 'absent')`);
        await queryRunner.query(`CREATE TABLE "diary_entry_attendance" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "diary_entry_id" uuid NOT NULL, "student_id" uuid NOT NULL, "entry_date" date NOT NULL, "status" "public"."diary_entry_attendance_status_enum" NOT NULL, "word_count" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_869bd8123a9db9ded610171cc65" UNIQUE ("diary_entry_id"), CONSTRAINT "PK_cbdcdff13f3054421bca81f2cad" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_ddc7d254be54de06f5ad66ca4e" ON "diary_entry_attendance" ("student_id", "entry_date") `);
        await queryRunner.query(`ALTER TABLE "tutor_education" ALTER COLUMN "updated_at" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "tutor_education" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "tutor_education" ADD "created_by" character varying(36)`);
        await queryRunner.query(`ALTER TABLE "tutor_education" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "tutor_education" ADD "updated_by" character varying(36)`);
        await queryRunner.query(`ALTER TABLE "tutor_education" ADD CONSTRAINT "FK_111e99a139edbc15490fec3032d" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry_attendance" ADD CONSTRAINT "FK_869bd8123a9db9ded610171cc65" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry_attendance" ADD CONSTRAINT "FK_07a5b32e05c38a848b51f314b31" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "diary_entry_attendance" DROP CONSTRAINT "FK_07a5b32e05c38a848b51f314b31"`);
        await queryRunner.query(`ALTER TABLE "diary_entry_attendance" DROP CONSTRAINT "FK_869bd8123a9db9ded610171cc65"`);
        await queryRunner.query(`ALTER TABLE "tutor_education" DROP CONSTRAINT "FK_111e99a139edbc15490fec3032d"`);
        await queryRunner.query(`ALTER TABLE "tutor_education" DROP COLUMN "updated_by"`);
        await queryRunner.query(`ALTER TABLE "tutor_education" ADD "updated_by" uuid`);
        await queryRunner.query(`ALTER TABLE "tutor_education" DROP COLUMN "created_by"`);
        await queryRunner.query(`ALTER TABLE "tutor_education" ADD "created_by" uuid`);
        await queryRunner.query(`ALTER TABLE "tutor_education" ALTER COLUMN "updated_at" SET NOT NULL`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ddc7d254be54de06f5ad66ca4e"`);
        await queryRunner.query(`DROP TABLE "diary_entry_attendance"`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_attendance_status_enum"`);
        await queryRunner.query(`ALTER TABLE "tutor_education" ADD CONSTRAINT "FK_tutor_education_tutor" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
