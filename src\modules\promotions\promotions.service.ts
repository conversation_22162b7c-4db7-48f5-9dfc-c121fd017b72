import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, LessThanOrEqual, MoreThanOrEqual, Not, IsNull, Like } from 'typeorm';
import { Promotion, DiscountType, PromotionType, PromotionApplicableType, PromotionStatus } from '../../database/entities/promotion.entity';
import {
  CreatePromotionDto,
  UpdatePromotionDto,
  PromotionResponseDto,
  ApplyPromotionCodeDto,
  PromotionApplicationResponseDto,
  GetApplicablePromotionsDto
} from '../../database/models/promotion.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

@Injectable()
export class PromotionsService {
  private readonly logger = new Logger(PromotionsService.name);

  constructor(
    @InjectRepository(Promotion)
    private readonly promotionRepository: Repository<Promotion>,
  ) {}

  /**
   * Create a new promotion
   * @param createPromotionDto Promotion creation data
   * @returns Created promotion
   */
  async createPromotion(createPromotionDto: CreatePromotionDto): Promise<PromotionResponseDto> {
    try {
      // Check if promotion with same code already exists (if code is provided)
      if (createPromotionDto.promotionCode) {
        const existingPromotion = await this.promotionRepository.findOne({
          where: { promotionCode: createPromotionDto.promotionCode }
        });

        if (existingPromotion) {
          throw new ConflictException(`Promotion with code '${createPromotionDto.promotionCode}' already exists`);
        }
      }

      // Validate dates if both are provided
      let startDate = null;
      let endDate = null;

      if (createPromotionDto.startDate) {
        startDate = new Date(createPromotionDto.startDate);
      }

      if (createPromotionDto.endDate) {
        endDate = new Date(createPromotionDto.endDate);
      }

      if (startDate && endDate && endDate <= startDate) {
        throw new BadRequestException('End date must be after start date');
      }

      // Create new promotion
      const promotion = this.promotionRepository.create({
        ...createPromotionDto,
        startDate: startDate,
        endDate: endDate,
        usageCount: 0,
      });

      // Save promotion
      const savedPromotion = await this.promotionRepository.save(promotion);

      return this.mapPromotionToDto(savedPromotion);
    } catch (error) {
      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error creating promotion: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to create promotion');
    }
  }

  /**
   * Get all promotions
   * @param status Optional status filter
   * @param applicableType Optional applicable type filter
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of promotions
   */
  async getAllPromotions(
    status?: PromotionStatus,
    applicableType?: PromotionApplicableType,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<PromotionResponseDto>> {
    try {
      const now = new Date();
      const whereConditions: any = {};

      // Filter by status if provided
      if (status) {
        switch (status) {
          case PromotionStatus.ACTIVE:
            whereConditions.isActive = true;
            // For active promotions, either startDate is null or it's less than or equal to now
            whereConditions.startDate = [IsNull(), LessThanOrEqual(now)];
            // And either endDate is null or it's greater than or equal to now
            whereConditions.endDate = [IsNull(), MoreThanOrEqual(now)];
            break;
          case PromotionStatus.INACTIVE:
            whereConditions.isActive = false;
            break;
          case PromotionStatus.EXPIRED:
            whereConditions.isActive = true;
            whereConditions.endDate = LessThanOrEqual(now);
            break;
          case PromotionStatus.SCHEDULED:
            whereConditions.isActive = true;
            whereConditions.startDate = MoreThanOrEqual(now);
            break;
        }
      }

      // Filter by applicable type if provided
      if (applicableType) {
        whereConditions.applicableType = applicableType;
      }

      // Get total count for pagination
      const totalCount = await this.promotionRepository.count({
        where: whereConditions
      });

      // Apply pagination if provided
      let options: any = {
        where: whereConditions,
        order: { createdAt: 'DESC' }
      };

      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        options.skip = skip;
        options.take = limit;

        if (sortBy && sortDirection) {
          options.order = { [sortBy]: sortDirection };
        }
      }

      const promotions = await this.promotionRepository.find(options);

      return new PagedListDto(
        promotions.map(promotion => this.mapPromotionToDto(promotion)),
        totalCount
      );
    } catch (error) {
      this.logger.error(`Error getting promotions: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get promotions');
    }
  }

  /**
   * Get promotion by ID
   * @param id Promotion ID
   * @returns Promotion details
   */
  async getPromotionById(id: string): Promise<PromotionResponseDto> {
    try {
      const promotion = await this.promotionRepository.findOne({
        where: { id: id }
      });

      if (!promotion) {
        throw new NotFoundException(`Promotion with ID ${id} not found`);
      }

      return this.mapPromotionToDto(promotion);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting promotion: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get promotion');
    }
  }

  /**
   * Update a promotion
   * @param id Promotion ID
   * @param updatePromotionDto Promotion update data
   * @returns Updated promotion
   */
  async updatePromotion(id: string, updatePromotionDto: UpdatePromotionDto): Promise<PromotionResponseDto> {
    this.logger.log(`Updating promotion with ID ${id}`);

    // Check if promotion exists
    const promotion = await this.promotionRepository.findOne({
      where: { id: id }
    });

    if (!promotion) {
      this.logger.error(`Promotion with ID ${id} not found`);
      throw new NotFoundException(`Promotion with ID ${id} not found`);
    }

    // Check if promotion code is being changed and if new code already exists
    if (updatePromotionDto.promotionCode && updatePromotionDto.promotionCode !== promotion.promotionCode) {
      const existingPromotion = await this.promotionRepository.findOne({
        where: {
          promotionCode: updatePromotionDto.promotionCode,
          id: Not(id)
        }
      });

      if (existingPromotion) {
        this.logger.error(`Promotion with code '${updatePromotionDto.promotionCode}' already exists`);
        throw new ConflictException(`Promotion with code '${updatePromotionDto.promotionCode}' already exists`);
      }
    }

    // Validate dates if both are provided
    if (updatePromotionDto.startDate && updatePromotionDto.endDate) {
      const startDate = new Date(updatePromotionDto.startDate);
      const endDate = new Date(updatePromotionDto.endDate);

      if (endDate <= startDate) {
        this.logger.error('End date must be after start date');
        throw new BadRequestException('End date must be after start date');
      }
    } else if (updatePromotionDto.startDate) {
      // If only start date is provided, check against existing end date
      const startDate = new Date(updatePromotionDto.startDate);
      if (startDate >= promotion.endDate) {
        this.logger.error('Start date must be before end date');
        throw new BadRequestException('Start date must be before end date');
      }
    } else if (updatePromotionDto.endDate) {
      // If only end date is provided, check against existing start date
      const endDate = new Date(updatePromotionDto.endDate);
      if (endDate <= promotion.startDate) {
        this.logger.error('End date must be after start date');
        throw new BadRequestException('End date must be after start date');
      }
    }

    try {
      // Handle arrays properly
      const updatedPromotion = {
        ...promotion,
        ...updatePromotionDto,
        startDate: updatePromotionDto.startDate ? new Date(updatePromotionDto.startDate) : promotion.startDate,
        endDate: updatePromotionDto.endDate ? new Date(updatePromotionDto.endDate) : promotion.endDate,
      };

      // Validate dates if both are provided
      if (updatedPromotion.startDate && updatedPromotion.endDate &&
          updatedPromotion.endDate <= updatedPromotion.startDate) {
        throw new BadRequestException('End date must be after start date');
      }

      // Ensure arrays are properly handled
      if (updatePromotionDto.hasOwnProperty('applicableCategoryIds')) {
        if (updatePromotionDto.applicableCategoryIds === null ||
            (Array.isArray(updatePromotionDto.applicableCategoryIds) &&
             updatePromotionDto.applicableCategoryIds.length === 0)) {
          updatedPromotion.applicableCategoryIds = null;
        } else {
          updatedPromotion.applicableCategoryIds = updatePromotionDto.applicableCategoryIds;
        }
      }

      if (updatePromotionDto.hasOwnProperty('applicablePlanIds')) {
        if (updatePromotionDto.applicablePlanIds === null ||
            (Array.isArray(updatePromotionDto.applicablePlanIds) &&
             updatePromotionDto.applicablePlanIds.length === 0)) {
          updatedPromotion.applicablePlanIds = null;
        } else {
          updatedPromotion.applicablePlanIds = updatePromotionDto.applicablePlanIds;
        }
      }

      // Log the updated promotion for debugging
      this.logger.log(`Updated promotion data: ${JSON.stringify(updatedPromotion)}`);

      // Save updated promotion
      const savedPromotion = await this.promotionRepository.save(updatedPromotion);
      this.logger.log(`Promotion with ID ${id} updated successfully`);

      return this.mapPromotionToDto(savedPromotion);
    } catch (error) {
      this.logger.error(`Error updating promotion: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to update promotion');
    }
  }

  /**
   * Update promotion activation status
   * @param id Promotion ID
   * @param isActive Whether the promotion should be active
   * @returns Updated promotion
   */
  async updatePromotionActivation(id: string, isActive: boolean): Promise<PromotionResponseDto> {
    try {
      // Check if promotion exists
      const promotion = await this.promotionRepository.findOne({
        where: { id: id }
      });

      if (!promotion) {
        this.logger.error(`Promotion with ID ${id} not found`);
        throw new NotFoundException(`Promotion with ID ${id} not found`);
      }

      // Update activation status
      promotion.isActive = isActive;

      // Save updated promotion
      const savedPromotion = await this.promotionRepository.save(promotion);
      this.logger.log(`Promotion ${id} ${isActive ? 'activated' : 'deactivated'} successfully`);

      return this.mapPromotionToDto(savedPromotion);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error updating promotion activation status: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to update promotion activation status');
    }
  }

  /**
   * Delete a promotion
   * @param id Promotion ID
   * @returns Success message
   */
  async deletePromotion(id: string): Promise<{ success: boolean; message: string }> {
    try {
      // Check if promotion exists
      const promotion = await this.promotionRepository.findOne({
        where: { id: id }
      });

      if (!promotion) {
        throw new NotFoundException(`Promotion with ID ${id} not found`);
      }

      // Delete promotion
      await this.promotionRepository.remove(promotion);
      return { success: true, message: 'Promotion deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error deleting promotion: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to delete promotion');
    }
  }

  /**
   * Apply a promotion code
   * @param applyPromotionCodeDto Promotion code application data
   * @returns Promotion application result
   */
  async applyPromotionCode(applyPromotionCodeDto: ApplyPromotionCodeDto): Promise<PromotionApplicationResponseDto> {
    try {
      const { promotionCode, originalPrice, itemType, category, planType } = applyPromotionCodeDto;

      // Find promotion by code
      const promotion = await this.promotionRepository.findOne({
        where: { promotionCode: promotionCode }
      });

      if (!promotion) {
        return {
          promotionId: null,
          promotionName: null,
          originalPrice,
          discountAmount: 0,
          finalPrice: originalPrice,
          discountType: null,
          discountValue: 0,
          isApplied: false,
          message: 'Promotion code not found'
        };
      }

      // Check if promotion is valid
      const now = new Date();
      const isValid = promotion.isActive &&
                     now >= promotion.startDate &&
                     now <= promotion.endDate &&
                     (!promotion.usageLimit || promotion.usageCount < promotion.usageLimit);

      if (!isValid) {
        // Calculate status
        let status: PromotionStatus;
        if (!promotion.isActive) {
          status = PromotionStatus.INACTIVE;
        } else if (promotion.startDate && now < promotion.startDate) {
          status = PromotionStatus.SCHEDULED;
        } else if (promotion.endDate && now > promotion.endDate) {
          status = PromotionStatus.EXPIRED;
        } else if (promotion.usageLimit && promotion.usageCount >= promotion.usageLimit) {
          status = PromotionStatus.EXPIRED;
        } else {
          status = PromotionStatus.ACTIVE;
        }

        return {
          promotionId: promotion.id,
          promotionName: promotion.name,
          originalPrice,
          discountAmount: 0,
          finalPrice: originalPrice,
          discountType: promotion.discountType,
          discountValue: promotion.discountValue,
          isApplied: false,
          message: `Promotion is ${status}`
        };
      }

      // Check if promotion is applicable to the item type
      if (promotion.applicableType !== PromotionApplicableType.ALL && promotion.applicableType !== itemType) {
        return {
          promotionId: promotion.id,
          promotionName: promotion.name,
          originalPrice,
          discountAmount: 0,
          finalPrice: originalPrice,
          discountType: promotion.discountType,
          discountValue: promotion.discountValue,
          isApplied: false,
          message: `Promotion is not applicable to ${itemType}`
        };
      }

      // Check if promotion is applicable to the category (for shop items)
      if (itemType === PromotionApplicableType.SHOP_ITEM &&
          promotion.applicableCategoryIds &&
          promotion.applicableCategoryIds.length > 0 &&
          category &&
          !promotion.applicableCategoryIds.includes(category)) {
        return {
          promotionId: promotion.id,
          promotionName: promotion.name,
          originalPrice,
          discountAmount: 0,
          finalPrice: originalPrice,
          discountType: promotion.discountType,
          discountValue: promotion.discountValue,
          isApplied: false,
          message: `Promotion is not applicable to category '${category}'`
        };
      }

      // Check if promotion is applicable to the plan type (for plans)
      if (itemType === PromotionApplicableType.PLAN &&
          promotion.applicablePlanIds &&
          promotion.applicablePlanIds.length > 0 &&
          planType &&
          !promotion.applicablePlanIds.includes(planType)) {
        return {
          promotionId: promotion.id,
          promotionName: promotion.name,
          originalPrice,
          discountAmount: 0,
          finalPrice: originalPrice,
          discountType: promotion.discountType,
          discountValue: promotion.discountValue,
          isApplied: false,
          message: `Promotion is not applicable to plan '${planType}'`
        };
      }

      // Check minimum purchase amount
      if (promotion.minimumPurchaseAmount && originalPrice < promotion.minimumPurchaseAmount) {
        return {
          promotionId: promotion.id,
          promotionName: promotion.name,
          originalPrice,
          discountAmount: 0,
          finalPrice: originalPrice,
          discountType: promotion.discountType,
          discountValue: promotion.discountValue,
          isApplied: false,
          message: `Minimum purchase amount of ${promotion.minimumPurchaseAmount} not met`
        };
      }

      // Check maximum purchase amount
      if (promotion.maximumPurchaseAmount && originalPrice > promotion.maximumPurchaseAmount) {
        return {
          promotionId: promotion.id,
          promotionName: promotion.name,
          originalPrice,
          discountAmount: 0,
          finalPrice: originalPrice,
          discountType: promotion.discountType,
          discountValue: promotion.discountValue,
          isApplied: false,
          message: `Maximum purchase amount of ${promotion.maximumPurchaseAmount} exceeded`
        };
      }

      // Calculate discount
      let discountAmount = 0;

      // Calculate based on promotionType and discountValue
      if (promotion.promotionType === PromotionType.PERCENTAGE) {
        discountAmount = (originalPrice * promotion.discountValue) / 100;
      } else {
        discountAmount = Number(promotion.discountValue);
      }

      // Apply maximum discount amount if set
      if (promotion.maximumDiscountAmount && discountAmount > promotion.maximumDiscountAmount) {
        discountAmount = promotion.maximumDiscountAmount;
      }

      const finalPrice = originalPrice - discountAmount;

      // Increment usage count
      promotion.usageCount += 1;
      await this.promotionRepository.save(promotion);

      return {
        promotionId: promotion.id,
        promotionName: promotion.name,
        originalPrice,
        discountAmount,
        finalPrice,
        discountType: promotion.discountType,
        discountValue: promotion.discountValue,
        isApplied: true,
        message: 'Promotion applied successfully'
      };
    } catch (error) {
      this.logger.error(`Error applying promotion code: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to apply promotion code');
    }
  }

  /**
   * Get applicable promotions for an item
   * @param getApplicablePromotionsDto Item data to get promotions for
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of applicable promotions
   */
  async getApplicablePromotions(
    getApplicablePromotionsDto: GetApplicablePromotionsDto,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<PromotionResponseDto>> {
    try {
      const { itemType, category, planType } = getApplicablePromotionsDto;
      const now = new Date();

      // Get active promotions
      const promotions = await this.promotionRepository.find({
        where: {
          isActive: true,
          startDate: LessThanOrEqual(now),
          endDate: MoreThanOrEqual(now),
        }
      });

      // Filter promotions by applicable type and category/plan
      const applicablePromotions = promotions.filter(promotion => {
        // Check if promotion is valid
        const now = new Date();
        const isValid = promotion.isActive &&
                       (!promotion.startDate || now >= promotion.startDate) &&
                       (!promotion.endDate || now <= promotion.endDate) &&
                       (!promotion.usageLimit || promotion.usageCount < promotion.usageLimit);

        if (!isValid) {
          return false;
        }

        // Check if promotion is applicable to the item type
        if (promotion.applicableType !== PromotionApplicableType.ALL && promotion.applicableType !== itemType) {
          return false;
        }

        // Check if promotion is applicable to the category (for shop items)
        if (itemType === PromotionApplicableType.SHOP_ITEM &&
            promotion.applicableCategoryIds &&
            promotion.applicableCategoryIds.length > 0 &&
            category &&
            !promotion.applicableCategoryIds.includes(category)) {
          return false;
        }

        // Check if promotion is applicable to the plan type (for plans)
        if (itemType === PromotionApplicableType.PLAN &&
            promotion.applicablePlanIds &&
            promotion.applicablePlanIds.length > 0 &&
            planType &&
            !promotion.applicablePlanIds.includes(planType)) {
          return false;
        }

        return true;
      });

      // Apply pagination if provided
      let paginatedPromotions = applicablePromotions;
      let totalCount = applicablePromotions.length;

      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
        const skip = (page - 1) * limit;

        // Apply sorting if provided
        if (sortBy && sortDirection) {
          paginatedPromotions.sort((a, b) => {
            const aValue = a[sortBy];
            const bValue = b[sortBy];

            if (sortDirection === 'ASC') {
              return aValue > bValue ? 1 : -1;
            } else {
              return aValue < bValue ? 1 : -1;
            }
          });
        }

        // Apply pagination
        paginatedPromotions = paginatedPromotions.slice(skip, skip + limit);
      }

      return new PagedListDto(
        paginatedPromotions.map(promotion => this.mapPromotionToDto(promotion)),
        totalCount
      );
    } catch (error) {
      this.logger.error(`Error getting applicable promotions: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to get applicable promotions');
    }
  }

  /**
   * Generate a unique promotion code
   * @param prefix Prefix for the promotion code
   * @returns Generated promotion code
   */
  async generatePromotionCode(prefix: string = 'PROMO'): Promise<string> {
    try {
      // Generate a random 6-character alphanumeric code
      const randomCode = Math.random().toString(36).substring(2, 8).toUpperCase();
      const promotionCode = `${prefix}${randomCode}`;

      // Check if code already exists
      const existingPromotion = await this.promotionRepository.findOne({
        where: { promotionCode: promotionCode }
      });

      // If code already exists, generate a new one recursively
      if (existingPromotion) {
        return this.generatePromotionCode(prefix);
      }

      return promotionCode;
    } catch (error) {
      this.logger.error(`Error generating promotion code: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to generate promotion code');
    }
  }

  /**
   * Map promotion entity to DTO
   * @param promotion Promotion entity
   * @returns Promotion DTO
   */
  private mapPromotionToDto(promotion: Promotion): PromotionResponseDto {
    // Calculate status directly
    const now = new Date();
    let status: PromotionStatus;

    if (!promotion.isActive) {
      status = PromotionStatus.INACTIVE;
    } else if (promotion.startDate && now < promotion.startDate) {
      status = PromotionStatus.SCHEDULED;
    } else if (promotion.endDate && now > promotion.endDate) {
      status = PromotionStatus.EXPIRED;
    } else if (promotion.usageLimit && promotion.usageCount >= promotion.usageLimit) {
      status = PromotionStatus.EXPIRED;
    } else {
      status = PromotionStatus.ACTIVE;
    }

    return {
      id: promotion.id,
      name: promotion.name,
      description: promotion.description,
      promotionType: promotion.promotionType,
      discountType: promotion.discountType,
      discountValue: Number(promotion.discountValue),
      applicableType: promotion.applicableType,
      applicableCategoryIds: promotion.applicableCategoryIds || [],
      applicablePlanIds: promotion.applicablePlanIds || [],
      appliedToPlan: promotion.appliedToPlan || false,
      promotionCode: promotion.promotionCode,
      startDate: promotion.startDate,
      endDate: promotion.endDate,
      isActive: promotion.isActive,
      usageLimit: promotion.usageLimit,
      usageCount: promotion.usageCount,
      minimumPurchaseAmount: promotion.minimumPurchaseAmount ? Number(promotion.minimumPurchaseAmount) : null,
      maximumPurchaseAmount: promotion.maximumPurchaseAmount ? Number(promotion.maximumPurchaseAmount) : null,
      maximumDiscountAmount: promotion.maximumDiscountAmount ? Number(promotion.maximumDiscountAmount) : null,
      status: status,
      createdAt: promotion.createdAt,
      updatedAt: promotion.updatedAt
    };
  }
}
