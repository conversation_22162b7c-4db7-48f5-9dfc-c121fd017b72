import { Module } from '@nestjs/common';
import { WaterfallModule } from './waterfall/waterfall.module';
import { StoryMakerModule } from './story-maker/story-maker.module';
import { BlockGameModule } from './block-game/block-game.module';
import { CommonModule } from '../../common/common.module';

@Module({
  imports: [CommonModule, WaterfallModule, StoryMakerModule, BlockGameModule],
  exports: [WaterfallModule, StoryMakerModule, BlockGameModule],
})
export class PlayModule {}
