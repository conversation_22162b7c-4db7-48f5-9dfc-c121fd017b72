# API Versioning Convention

This document outlines the API versioning strategy for the HEC Backend project to ensure backward compatibility while allowing for evolution.

## Table of Contents

1. [Versioning Philosophy](#versioning-philosophy)
2. [Versioning Strategy](#versioning-strategy)
3. [URL-Based Versioning](#url-based-versioning)
4. [Implementation](#implementation)
5. [Version Lifecycle](#version-lifecycle)
6. [Breaking vs. Non-Breaking Changes](#breaking-vs-non-breaking-changes)
7. [Documentation](#documentation)
8. [Client Migration](#client-migration)

## Versioning Philosophy

Our API versioning philosophy is based on the following principles:

1. **Stability**: Existing clients should continue to work without modification
2. **Evolution**: The API should be able to evolve to meet new requirements
3. **Clarity**: Version differences should be clearly documented
4. **Simplicity**: The versioning scheme should be easy to understand and implement

## Versioning Strategy

We use explicit versioning with the following guidelines:

1. **Major Versions**: Increment for breaking changes (e.g., v1 to v2)
2. **Minor Versions**: Increment for backward-compatible additions
3. **Patch Versions**: Increment for backward-compatible bug fixes

Only major versions are exposed in the API URL. Minor and patch versions are tracked internally.

## URL-Based Versioning

We use URL-based versioning with the following format:

```
/api/v{major-version}/{resource}
```

Examples:
- `/api/v1/users`
- `/api/v2/users`

Benefits of URL-based versioning:
- Clear and explicit
- Easy to understand
- Works with all HTTP methods
- Supports caching
- Compatible with most clients

## Implementation

### Controller Structure

```typescript
// v1 controller
@Controller('api/v1/users')
export class UsersControllerV1 {
  // Controller implementation
}

// v2 controller
@Controller('api/v2/users')
export class UsersControllerV2 {
  // Controller implementation
}
```

### Module Structure

```typescript
// app.module.ts
@Module({
  imports: [
    UsersModuleV1,
    UsersModuleV2,
    // Other modules
  ],
})
export class AppModule {}

// users-v1.module.ts
@Module({
  controllers: [UsersControllerV1],
  providers: [UsersServiceV1],
})
export class UsersModuleV1 {}

// users-v2.module.ts
@Module({
  controllers: [UsersControllerV2],
  providers: [UsersServiceV2],
})
export class UsersModuleV2 {}
```

### Service Reuse

When possible, reuse services between versions to minimize code duplication:

```typescript
// users-v2.module.ts
@Module({
  controllers: [UsersControllerV2],
  providers: [
    {
      provide: UsersServiceV2,
      useFactory: (usersServiceV1: UsersServiceV1) => {
        return new UsersServiceV2(usersServiceV1);
      },
      inject: [UsersServiceV1],
    },
    UsersServiceV1,
  ],
})
export class UsersModuleV2 {}

// users-service-v2.ts
@Injectable()
export class UsersServiceV2 {
  constructor(private readonly usersServiceV1: UsersServiceV1) {}

  // Reuse v1 methods or extend them as needed
  async findAll() {
    const users = await this.usersServiceV1.findAll();
    // Add v2-specific transformations
    return users;
  }

  // Add v2-specific methods
  async findByEmail(email: string) {
    // Implementation
  }
}
```

## Version Lifecycle

Each API version goes through the following lifecycle:

1. **Development**: Version is under development and not yet released
2. **Active**: Version is released and fully supported
3. **Deprecated**: Version is still available but will be removed in the future
4. **Retired**: Version is no longer available

### Deprecation Process

1. Announce deprecation with timeline (at least 6 months notice)
2. Add deprecation warnings in API responses
3. Provide migration guides to newer versions
4. Monitor usage of deprecated endpoints
5. Retire the version according to the announced timeline

## Breaking vs. Non-Breaking Changes

### Breaking Changes (Require New Major Version)

- Removing or renaming fields in response
- Changing field types in response
- Removing endpoints
- Adding required request parameters
- Changing authentication or authorization requirements
- Changing error response formats

### Non-Breaking Changes (No New Major Version)

- Adding new endpoints
- Adding optional request parameters
- Adding new fields to response
- Changing implementation details without affecting interface
- Bug fixes that maintain the same behavior
- Performance improvements

## Documentation

### Swagger Configuration

Configure Swagger to document each API version separately:

```typescript
// main.ts
async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Configure Swagger for v1
  const v1Options = new DocumentBuilder()
    .setTitle('HEC API')
    .setDescription('HEC API Documentation')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Enter JWT token with Bearer prefix',
        in: 'header',
      },
      'JWT-auth',
    )
    .build();
  const v1Document = SwaggerModule.createDocument(app, v1Options, {
    include: [UsersModuleV1, AuthModuleV1, /* other v1 modules */],
  });
  SwaggerModule.setup('api-docs/v1', app, v1Document);

  // Configure Swagger for v2
  const v2Options = new DocumentBuilder()
    .setTitle('HEC API')
    .setDescription('HEC API Documentation')
    .setVersion('2.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Enter JWT token with Bearer prefix',
        in: 'header',
      },
      'JWT-auth',
    )
    .build();
  const v2Document = SwaggerModule.createDocument(app, v2Options, {
    include: [UsersModuleV2, AuthModuleV2, /* other v2 modules */],
  });
  SwaggerModule.setup('api-docs/v2', app, v2Document);

  await app.listen(3000);
}
```

### Version Differences

Document the differences between versions:

```markdown
# API Version Differences

## v1 to v2 Changes

### Users API

#### GET /api/v{version}/users

**Response Changes:**
- Added `lastLoginDate` field to user object
- Changed `roles` from string array to object array with `id` and `name` fields

#### POST /api/v{version}/users

**Request Changes:**
- Added optional `preferences` object
- Removed support for `username` field (use `userId` instead)
```

## Client Migration

Provide guidelines for clients to migrate between versions:

```markdown
# Migration Guide: v1 to v2

## Step 1: Update API Base URL

Change your API base URL from `/api/v1` to `/api/v2`.

## Step 2: Update User Object Handling

The user object structure has changed:

```javascript
// v1
const roles = user.roles; // ['admin', 'tutor']

// v2
const roles = user.roles.map(role => role.name); // Convert from [{id: '1', name: 'admin'}, ...] to ['admin', ...]
```

## Step 3: Update User Creation

When creating users, replace `username` with `userId`:

```javascript
// v1
const newUser = {
  username: 'john_doe',
  // other fields
};

// v2
const newUser = {
  userId: 'john_doe',
  // other fields
};
```
```

---

This API versioning convention provides a framework for maintaining API stability while allowing for evolution. By following these guidelines, the team can confidently make changes to the API without breaking existing clients.
