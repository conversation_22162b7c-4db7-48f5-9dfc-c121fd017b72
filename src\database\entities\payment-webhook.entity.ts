import { <PERSON>ti<PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { PaymentTransaction } from './payment-transaction.entity';

/**
 * Webhook status
 * @enum {string}
 */
export enum WebhookStatus {
  /** Webhook received */
  RECEIVED = 'received',
  /** Webhook processing */
  PROCESSING = 'processing',
  /** Webhook processed successfully */
  PROCESSED = 'processed',
  /** Webhook processing failed */
  FAILED = 'failed',
  /** Webhook ignored (duplicate or invalid) */
  IGNORED = 'ignored'
}

/**
 * Webhook type
 * @enum {string}
 */
export enum WebhookType {
  /** Payment completion notification */
  PAYMENT_COMPLETE = 'payment_complete',
  /** Payment failure notification */
  PAYMENT_FAILED = 'payment_failed',
  /** Payment cancellation notification */
  PAYMENT_CANCELLED = 'payment_cancelled',
  /** Refund notification */
  REFUND = 'refund',
  /** Status update notification */
  STATUS_UPDATE = 'status_update'
}

@Entity()
export class PaymentWebhook extends AuditableBaseEntity {
  @Column({ name: 'transaction_id' })
  transactionId: string;

  @ManyToOne(() => PaymentTransaction)
  @JoinColumn({ name: 'transaction_id', referencedColumnName: 'transactionId' })
  paymentTransaction: PaymentTransaction;

  @Column({
    name: 'webhook_type',
    type: 'enum',
    enum: WebhookType
  })
  webhookType: WebhookType;

  @Column({
    name: 'status',
    type: 'enum',
    enum: WebhookStatus,
    default: WebhookStatus.RECEIVED
  })
  status: WebhookStatus;

  @Column({ name: 'payload', type: 'jsonb' })
  payload: any;

  @Column({ name: 'processed', default: false })
  processed: boolean;

  @Column({ name: 'processed_at', nullable: true })
  processedAt: Date;

  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;

  @Column({ name: 'retry_count', default: 0 })
  retryCount: number;

  @Column({ name: 'max_retries', default: 3 })
  maxRetries: number;

  @Column({ name: 'next_retry_at', nullable: true })
  nextRetryAt: Date;

  @Column({ name: 'signature', nullable: true })
  signature: string;

  @Column({ name: 'source_ip', nullable: true })
  sourceIp: string;

  @Column({ name: 'user_agent', nullable: true })
  userAgent: string;

  /**
   * Check if webhook can be retried
   */
  canRetry(): boolean {
    return this.retryCount < this.maxRetries && 
           this.status === WebhookStatus.FAILED;
  }

  /**
   * Mark webhook as processed
   */
  markAsProcessed(): void {
    this.processed = true;
    this.processedAt = new Date();
    this.status = WebhookStatus.PROCESSED;
  }

  /**
   * Mark webhook as failed
   */
  markAsFailed(errorMessage: string): void {
    this.status = WebhookStatus.FAILED;
    this.errorMessage = errorMessage;
    this.retryCount += 1;
    
    if (this.canRetry()) {
      // Calculate next retry time with exponential backoff
      const backoffMinutes = Math.pow(2, this.retryCount) * 5; // 5, 10, 20 minutes
      this.nextRetryAt = new Date(Date.now() + backoffMinutes * 60 * 1000);
    }
  }

  /**
   * Check if webhook is ready for retry
   */
  isReadyForRetry(): boolean {
    return this.canRetry() && 
           this.nextRetryAt && 
           new Date() >= this.nextRetryAt;
  }
}
