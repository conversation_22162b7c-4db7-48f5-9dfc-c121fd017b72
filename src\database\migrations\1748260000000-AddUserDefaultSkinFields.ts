import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserDefaultSkinFields1748260000000 implements MigrationInterface {
  name = 'AddUserDefaultSkinFields1748260000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if columns already exist to make migration idempotent
    const hasDefaultDiarySkinId = await queryRunner.hasColumn('user', 'default_diary_skin_id');
    const hasDefaultNovelSkinId = await queryRunner.hasColumn('user', 'default_novel_skin_id');

    if (!hasDefaultDiarySkinId && !hasDefaultNovelSkinId) {
      // Add default skin columns to user table
      await queryRunner.query(`
        ALTER TABLE "user"
        ADD COLUMN "default_diary_skin_id" uuid,
        ADD COLUMN "default_novel_skin_id" uuid
      `);
    } else if (!hasDefaultDiarySkinId) {
      await queryRunner.query(`
        ALTER TABLE "user"
        ADD COLUMN "default_diary_skin_id" uuid
      `);
    } else if (!hasDefaultNovelSkinId) {
      await queryRunner.query(`
        ALTER TABLE "user"
        ADD COLUMN "default_novel_skin_id" uuid
      `);
    } else {
      console.log('Default skin columns already exist, skipping column creation...');
    }

    console.log('Columns added successfully. Data migration will be handled separately.');

    // Add foreign key constraints if they don't exist
    const hasDiaryConstraint = await queryRunner.query(`
      SELECT 1 FROM information_schema.table_constraints
      WHERE constraint_name = 'FK_user_default_diary_skin'
      AND table_name = 'user'
    `);

    const hasNovelConstraint = await queryRunner.query(`
      SELECT 1 FROM information_schema.table_constraints
      WHERE constraint_name = 'FK_user_default_novel_skin'
      AND table_name = 'user'
    `);

    if (!hasDiaryConstraint || hasDiaryConstraint.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "user"
        ADD CONSTRAINT "FK_user_default_diary_skin"
        FOREIGN KEY ("default_diary_skin_id") REFERENCES "diary_skin"("id") ON DELETE SET NULL
      `);
    }

    if (!hasNovelConstraint || hasNovelConstraint.length === 0) {
      await queryRunner.query(`
        ALTER TABLE "user"
        ADD CONSTRAINT "FK_user_default_novel_skin"
        FOREIGN KEY ("default_novel_skin_id") REFERENCES "diary_skin"("id") ON DELETE SET NULL
      `);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('WARNING: Rolling back default skin migration. Data will be lost!');

    // Remove foreign key constraints if they exist
    try {
      await queryRunner.query(`
        ALTER TABLE "user" DROP CONSTRAINT IF EXISTS "FK_user_default_novel_skin"
      `);
    } catch (error) {
      console.log('FK_user_default_novel_skin constraint does not exist, skipping...');
    }

    try {
      await queryRunner.query(`
        ALTER TABLE "user" DROP CONSTRAINT IF EXISTS "FK_user_default_diary_skin"
      `);
    } catch (error) {
      console.log('FK_user_default_diary_skin constraint does not exist, skipping...');
    }

    // Remove columns if they exist
    const hasDefaultDiarySkinId = await queryRunner.hasColumn('user', 'default_diary_skin_id');
    const hasDefaultNovelSkinId = await queryRunner.hasColumn('user', 'default_novel_skin_id');

    if (hasDefaultDiarySkinId || hasDefaultNovelSkinId) {
      const columnsToRemove = [];
      if (hasDefaultNovelSkinId) columnsToRemove.push('default_novel_skin_id');
      if (hasDefaultDiarySkinId) columnsToRemove.push('default_diary_skin_id');

      await queryRunner.query(`
        ALTER TABLE "user"
        DROP COLUMN ${columnsToRemove.map(col => `"${col}"`).join(', ')}
      `);
    }

    console.log('Default skin migration rollback completed.');
  }
}
