# Shop Management System - Frontend Integration Flow

This document provides a comprehensive integration flow for frontend developers to implement the Shop Management System, with minimal verbal communication needed. It focuses exclusively on API endpoints and data flows without prescribing specific frontend implementation approaches.

## Table of Contents

1. [Shop Item Management](#shop-item-management)
   - [Category Management](#category-management)
   - [Shop Item CRUD](#shop-item-crud)
   - [Skin to Shop Item Conversion](#skin-to-shop-item-conversion)
2. [Promotion Management](#promotion-management)
   - [Promotion CRUD](#promotion-crud)
   - [Promotion Code Generation](#promotion-code-generation)
3. [Shop Item Promotion Management](#shop-item-promotion-management)
   - [Apply Promotions to Items](#apply-promotions-to-items)
   - [Apply Promotions to Categories](#apply-promotions-to-categories)
4. [Plan Promotion Management](#plan-promotion-management)
   - [Apply Promotions to Plans](#apply-promotions-to-plans)
   - [Remove Promotions from Plans](#remove-promotions-from-plans)
5. [Shopping](#shopping)
   - [Cart Management](#cart-management)
   - [Apply Promotion Code](#apply-promotion-code)
   - [Checkout Process](#checkout-process)
   - [Reward Point Purchase](#reward-point-purchase)

## Shop Item Management

### Category Management

First, create and manage shop categories to organize shop items.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Get All    │      │  Create     │      │  Update     │      │  Delete     │
│  Categories │ ──▶  │  Category   │ ──▶  │  Category   │ ──▶  │  Category   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /shop/  │      │ POST /admin/│      │ PATCH /admin│      │ DELETE      │
│ categories  │      │ shop/       │      │ /shop/      │      │ /admin/shop/│
│             │      │ categories  │      │ categories/ │      │ categories/ │
│             │      │             │      │ :id         │      │ :id         │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /shop/categories` - Get all categories
- `GET /shop/categories/:id` - Get category by ID
- `POST /admin/shop/categories` - Create category
- `PATCH /admin/shop/categories/:id` - Update category
- `DELETE /admin/shop/categories/:id` - Delete category

### Shop Item CRUD

After setting up categories, create and manage shop items.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Generate   │      │  Create     │      │  Update     │      │  Delete     │
│  Item Number│ ──▶  │  Shop Item  │ ──▶  │  Shop Item  │ ──▶  │  Shop Item  │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /admin/│      │ POST /admin/│      │ PATCH /admin│      │ DELETE      │
│ shop/items/ │      │ shop/items  │      │ /shop/items/│      │ /admin/shop/│
│ generate-   │      │ (with file) │      │ :id         │      │ items/:id   │
│ number      │      │             │      │ (with file) │      │             │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /shop/items` - Get all items (with filtering)
- `GET /shop/items/:id` - Get item by ID
- `POST /admin/shop/items/generate-number` - Generate unique item number
- `POST /admin/shop/items` - Create item (with file upload)
- `PATCH /admin/shop/items/:id` - Update item (with file upload)
- `DELETE /admin/shop/items/:id` - Delete item

**Notes:**
- File uploads are handled directly during item creation or update, not as a separate step
- When an item is marked as `FREE`, both price and isPurchasableInRewardpoint are automatically set to 0/false

### Skin to Shop Item Conversion

Convert diary skins to shop items for sale.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Create     │      │  Get Draft  │      │  Publish    │
│  Diary Skin │ ──▶  │  Shop Item  │ ──▶  │  to Shop    │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /admin/│      │ GET /admin/ │      │ POST /admin/│
│ diary/skins │      │ shop/diary- │      │ shop/diary- │
│             │      │ skins/:id/  │      │ skins/:id/  │
│             │      │ draft       │      │ publish     │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /admin/shop/diary-skins/:id/draft` - Get draft shop item from diary skin
- `POST /admin/shop/diary-skins/:id/publish` - Publish diary skin to shop

## Promotion Management

### Promotion CRUD

First, create and manage promotions before applying them to items.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Get All    │      │  Create     │      │  Update     │      │  Delete     │
│  Promotions │ ──▶  │  Promotion  │ ──▶  │  Promotion  │ ──▶  │  Promotion  │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /admin/ │      │ POST /admin/│      │ PATCH /admin│      │ DELETE      │
│ promotions  │      │ promotions  │      │ /promotions/│      │ /admin/     │
│             │      │             │      │ :id         │      │ promotions/ │
│             │      │             │      │             │      │ :id         │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /admin/promotions` - Get all promotions
- `POST /admin/promotions` - Create promotion
- `PATCH /admin/promotions/:id` - Update promotion
- `DELETE /admin/promotions/:id` - Delete promotion

### Promotion Code Generation

Generate unique promotion codes for use in promotions.

```
┌─────────────┐      ┌─────────────┐
│  Generate   │      │  Create     │
│  Promo Code │ ──▶  │  Promotion  │
└─────────────┘      └─────────────┘
       │                    │
       ▼                    ▼
┌─────────────┐      ┌─────────────┐
│ POST /admin/│      │ POST /admin/│
│ promotions/ │      │ promotions  │
│ generate-   │      │             │
│ code        │      │             │
└─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /admin/promotions/generate-code` - Generate promotion code

## Shop Item Promotion Management

### Apply Promotions to Items

After creating promotions, apply them to specific shop items or remove them.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Get        │      │  Select     │      │  Apply      │      │  Remove     │
│  Promotions │ ──▶  │  Items      │ ──▶  │  Promotion  │ ──▶  │  Promotion  │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /admin/ │      │ GET /admin/ │      │ POST /admin/│      │ DELETE      │
│ promotions  │      │ shop/items  │      │ shop/items/ │      │ /admin/shop/│
│             │      │             │      │ apply-      │      │ items/:id/  │
│             │      │             │      │ promotion   │      │ promotion   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
                                                                      │
                                                                      ▼
                                                              ┌─────────────┐
                                                              │  View Items │
                                                              │  with Promos│
                                                              └─────────────┘
                                                                      │
                                                                      ▼
                                                              ┌─────────────┐
                                                              │ GET /shop/  │
                                                              │ items/with- │
                                                              │ promotions  │
                                                              └─────────────┘
```

**API Endpoints:**
- `POST /admin/shop/items/apply-promotion` - Apply promotion to items
- `DELETE /admin/shop/items/:id/promotion` - Remove promotion from a specific item
- `GET /shop/items/with-promotions?searchTerm=example` - Get shop items with promotions (public)
- `GET /admin/shop/items/with-promotions?searchTerm=example` - Get shop items with promotions (admin)

### Apply Promotions to Categories

Apply promotions to entire categories of items.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Get        │      │  Select     │      │  Apply to   │
│  Promotions │ ──▶  │  Categories │ ──▶  │  Category   │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /admin/ │      │ GET /shop/  │      │ POST /admin/│
│ promotions  │      │ categories  │      │ promotions/ │
│             │      │             │      │ apply-to-   │
│             │      │             │      │ category    │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /admin/promotions/apply-to-category` - Apply promotion to category

## Plan Promotion Management

For detailed information about plan promotion integration, please refer to the [Plan Promotion Integration](plan-promotion-integration.md) document.

### Apply Promotions to Plans

Apply promotions to specific plans to offer discounts.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  View       │      │  Select     │      │  Choose     │      │  Apply      │
│  Plans      │ ──▶  │  Plans      │ ──▶  │  Promotion  │ ──▶  │  Promotion  │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /plans  │      │ Select Plans│      │ GET /promo- │      │ POST /plans/│
│             │      │ in UI       │      │ tions/admin │      │ admin/apply-│
│             │      │             │      │             │      │ promotion   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /plans` - Get all plans
- `GET /promotions/admin` - Get all promotions (admin)
- `POST /plans/admin/apply-promotion` - Apply promotion to plans

### Remove Promotions from Plans

Remove promotions from specific plans.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  View Plan  │      │  Select     │      │  Remove     │
│  Details    │ ──▶  │  Plan       │ ──▶  │  Promotion  │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /plans/ │      │ Select Plan │      │ DELETE      │
│ :id         │      │ in UI       │      │ /plans/admin│
│             │      │             │      │ /:id/       │
│             │      │             │      │ promotion   │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /plans/:id` - Get plan details
- `DELETE /plans/admin/:id/promotion` - Remove promotion from plan

## Shopping

### Cart Management

First, implement cart functionality to allow users to add and manage items.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Get Cart   │      │  Add Item   │      │  Update     │      │  Remove     │
│             │ ──▶  │  to Cart    │ ──▶  │  Quantity   │ ──▶  │  Item       │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /shop/  │      │ POST /shop/ │      │ PATCH /shop/│      │ DELETE      │
│ cart        │      │ cart/add    │      │ cart/items/ │      │ /shop/cart/ │
│             │      │             │      │ :id         │      │ items/:id   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /shop/cart` - Get shopping cart
- `POST /shop/cart/add` - Add item to cart
- `PATCH /shop/cart/items/:id` - Update cart item
- `DELETE /shop/cart/items/:id` - Remove item from cart
- `DELETE /shop/cart/clear` - Clear cart



### Checkout Process

Finally, users can complete their purchase through checkout.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Review     │      │  Apply      │      │  Select     │      │  Complete   │
│  Cart       │ ──▶  │  Promo Code │ ──▶  │  Payment    │ ──▶  │  Checkout   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /shop/  │      │ POST        │      │ GET /reward-│      │ POST /shop/ │
│ cart        │      │ /promotions/│      │ points/     │      │ cart/       │
│             │      │ apply       │      │ balance     │      │ checkout    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /promotions/apply` - Apply promotion code
- `POST /shop/cart/checkout` - Process checkout
- `GET /shop/purchases` - Get purchase history

### Reward Point Purchase

Users can purchase items using reward points.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Check      │      │  Select     │      │  Complete   │
│  Balance    │ ──▶  │  Reward Pts │ ──▶  │  Checkout   │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /reward-│      │ Set useReward│      │ POST /shop/ │
│ points/     │      │ Points=true  │      │ cart/       │
│ balance     │      │ (Frontend)   │      │ checkout    │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /reward-points/balance` - Get reward points balance
- `GET /reward-points/history` - Get reward points history

## Implementation Notes

- **Shop Item Management**:
  - File uploads are handled directly during item creation or update, not as a separate step
  - When an item is marked as `FREE`, both price and isPurchasableInRewardpoint are automatically set to 0/false
  - Each item has a unique `itemNumber` that can be generated using the API

- **Promotion Management**:
  - Promotions can be percentage-based or fixed amount
  - Promotions can be applied to specific categories, individual items, or plans
  - Promotions can be removed from individual items using the `DELETE /admin/shop/items/:id/promotion` endpoint
  - Promotions can be removed from plans using the `DELETE /plans/admin/:id/promotion` endpoint
  - Shop items with promotions can be viewed using the `GET /shop/items/with-promotions` endpoint
  - All plan-related APIs now include promotion information (`isApplicableForPromotion` and `promotionId` fields)
  - The search functionality supports a single `searchTerm` parameter that filters across item number, title, category name, and promotion name (minimum 3 characters for partial matching, except for item numbers with hyphens)
  - The response includes human-readable discount displays (e.g., "20%" or "$40.00")
  - Promotion codes can be generated with optional prefixes
  - The system will validate promotion eligibility (dates, usage limits, etc.)

- **Shopping Cart**:
  - Cart items will include original price, discounted price, and discount percentage
  - Promotions will be applied automatically when items are added to the cart
  - Cart state will be maintained with a last activity timestamp

- **Checkout**:
  - Checkout will support multiple payment methods (CREDIT_CARD, REWARD_POINTS, BANK_TRANSFER, FREE)
  - Purchase records will be created for each item, preserving promotion information
  - Items will be added to the student's owned items collection after purchase
