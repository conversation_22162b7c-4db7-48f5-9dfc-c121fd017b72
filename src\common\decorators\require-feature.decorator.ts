import { SetMetadata } from '@nestjs/common';
import { FeatureType } from '../../database/entities/plan-feature.entity';

export const FEATURE_ACCESS_KEY = 'feature_access';

/**
 * Decorator to specify which feature is required to access an endpoint
 * @param featureType The feature type required for access
 */
export const RequireFeature = (featureType: FeatureType) => 
  SetMetadata(FEATURE_ACCESS_KEY, featureType);

/**
 * Decorator to specify multiple features where any one is sufficient for access
 * @param featureTypes Array of feature types where any one grants access
 */
export const RequireAnyFeature = (featureTypes: FeatureType[]) => 
  SetMetadata(FEATURE_ACCESS_KEY, featureTypes);

/**
 * Decorator to specify multiple features where all are required for access
 * @param featureTypes Array of feature types where all are required
 */
export const RequireAllFeatures = (featureTypes: FeatureType[]) => 
  SetMetadata(`${FEATURE_ACCESS_KEY}_all`, featureTypes);
