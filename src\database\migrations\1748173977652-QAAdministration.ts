import { MigrationInterface, QueryRunner } from "typeorm";

export class QAAdministration1748173977652 implements MigrationInterface {
    name = 'QAAdministration1748173977652'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "qa-assignment-sets" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "set_sequence" integer NOT NULL, "instructions" text, CONSTRAINT "PK_6d8688e47cf94587040f2a1bc37" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "qa-assignment-sets" ADD CONSTRAINT "UQ_qa_assignment_sets_set_sequence" UNIQUE ("set_sequence")`);
        await queryRunner.query(`CREATE TYPE "public"."qa-assignment-items_status_enum" AS ENUM('assigned', 'draft', 'in_progress', 'completed', 'expired')`);
        await queryRunner.query(`CREATE TABLE "qa-assignment-items" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "question_id" uuid NOT NULL, "student_id" uuid NOT NULL, "score" integer NOT NULL, "deadline" TIMESTAMP NOT NULL, "set_sequence" integer NOT NULL, "status" "public"."qa-assignment-items_status_enum" NOT NULL DEFAULT 'assigned', "assigned_date" TIMESTAMP NOT NULL, "setSequence" integer, CONSTRAINT "PK_d0c2b3e5c9f38e6cca4404e5fb8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "qa-assignment-items" ADD CONSTRAINT "FK_65a318308818041d17df9521a6a" FOREIGN KEY ("question_id") REFERENCES "qa_question"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa-assignment-items" ADD CONSTRAINT "FK_ba32ef93a613c46c2abaea50473" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa-assignment-items" ADD CONSTRAINT "FK_97f76bc072ee1cf97e241ea44fc" FOREIGN KEY ("setSequence") REFERENCES "qa-assignment-sets"("set_sequence") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa-assignment-items" DROP CONSTRAINT "FK_97f76bc072ee1cf97e241ea44fc"`);
        await queryRunner.query(`ALTER TABLE "qa-assignment-items" DROP CONSTRAINT "FK_ba32ef93a613c46c2abaea50473"`);
        await queryRunner.query(`ALTER TABLE "qa-assignment-items" DROP CONSTRAINT "FK_65a318308818041d17df9521a6a"`);
        await queryRunner.query(`DROP TABLE "qa-assignment-items"`);
        await queryRunner.query(`DROP TYPE "public"."qa-assignment-items_status_enum"`);
        await queryRunner.query(`DROP TABLE "qa-assignment-sets"`);
    }
}
