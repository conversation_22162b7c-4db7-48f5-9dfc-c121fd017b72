import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Award, AwardModule, AwardCriteria, AwardFrequency } from '../../database/entities/award.entity';

@Injectable()
export class AwardSeed {
  private readonly logger = new Logger(AwardSeed.name);

  constructor(
    @InjectRepository(Award)
    private readonly awardRepository: Repository<Award>,
  ) {}

  async seed(): Promise<void> {
    this.logger.log('Seeding awards...');

    // Define initial awards
    const initialAwards = [
      // Diary module awards
      {
        name: 'Gold Star Diarist',
        description: 'Awarded to the student with the highest diary scores for the month',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 100,
        isActive: true,
        criteriaConfig: {
          minScore: 70,
          entriesRequired: 3,
        },
      },
      {
        name: 'Silver Star Diarist',
        description: 'Awarded to the student with the second highest diary scores for the month',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 75,
        isActive: true,
        criteriaConfig: {
          minScore: 70,
          entriesRequired: 3,
        },
      },
      {
        name: 'Bronze Star Diarist',
        description: 'Awarded to the student with the third highest diary scores for the month',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 50,
        isActive: true,
        criteriaConfig: {
          minScore: 70,
          entriesRequired: 3,
        },
      },
      {
        name: 'Weekly Diary Champion',
        description: 'Awarded to the student with the highest diary scores for the week',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE],
        frequency: AwardFrequency.WEEKLY,
        rewardPoints: 50,
        isActive: true,
        criteriaConfig: {
          minScore: 70,
          entriesRequired: 2,
        },
      },
      {
        name: 'Perfect Attendance',
        description: 'Awarded to students who submit diary entries every day for a month',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.ATTENDANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 80,
        isActive: true,
        criteriaConfig: {
          daysRequired: 20,
        },
      },
      {
        name: 'Creative Decorator',
        description: 'Awarded to students who create the most beautifully decorated diaries',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_DECORATION],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 60,
        isActive: true,
        criteriaConfig: {
          minSkins: 2,
        },
      },
      {
        name: 'All-Around Diary Excellence',
        description: 'Awarded to students who excel in all aspects of diary keeping',
        module: AwardModule.DIARY,
        criteria: [AwardCriteria.DIARY_SCORE, AwardCriteria.ATTENDANCE, AwardCriteria.DIARY_DECORATION],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 150,
        isActive: true,
        criteriaConfig: {
          minScore: 85,
          daysRequired: 15,
          minSkins: 2,
        },
      },

      // Placeholder awards for other modules

      {
        name: 'Novel Reader',
        description: 'Awarded to students with exceptional performance in the Novel module',
        module: AwardModule.NOVEL,
        criteria: [AwardCriteria.NOVEL_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 100,
        isActive: true,
        criteriaConfig: {
          minScore: 80,
        },
      },
      {
        name: 'Essay Master',
        description: 'Awarded to students with exceptional performance in the Essay module',
        module: AwardModule.ESSAY,
        criteria: [AwardCriteria.ESSAY_PERFORMANCE],
        frequency: AwardFrequency.MONTHLY,
        rewardPoints: 100,
        isActive: true,
        criteriaConfig: {
          minScore: 80,
        },
      },
    ];

    // Seed each award
    for (const awardData of initialAwards) {
      // Check if award already exists
      const existingAward = await this.awardRepository.findOne({
        where: { name: awardData.name }
      });

      if (!existingAward) {
        // Create new award
        const award = this.awardRepository.create({
          ...awardData,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        await this.awardRepository.save(award);
        this.logger.log(`Created award: ${award.name}`);
      } else {
        this.logger.log(`Award already exists: ${existingAward.name}`);
      }
    }

    this.logger.log('Award seeding completed');
  }
}
