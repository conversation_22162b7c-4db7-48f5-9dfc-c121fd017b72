import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponse } from '../dto/api-response.dto';

/**
 * Interceptor to transform all successful responses into the unified ApiResponse format
 */
@Injectable()
export class ResponseTransformerInterceptor<T> implements NestInterceptor<T, ApiResponse<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<ApiResponse<T>> {
    const statusCode = context.switchToHttp().getResponse().statusCode || 200;
    
    return next.handle().pipe(
      map(data => {
        // If the response is already in the ApiResponse format, return it as is
        if (data instanceof ApiResponse) {
          return data;
        }
        
        // Otherwise, wrap the response in the ApiResponse format
        return ApiResponse.success(data, 'Operation completed successfully', statusCode);
      }),
    );
  }
}
