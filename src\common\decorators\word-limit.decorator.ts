import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

/**
 * Custom validator to check if text content does not exceed the specified word limit
 */
export function WordLimit(maxWords: number, validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'wordLimit',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [maxWords],
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (!value || typeof value !== 'string') {
            return true; // Let other validators handle type checking
          }

          const [maxWordCount] = args.constraints;
          
          // Normalize content: trim and replace multiple spaces with single space
          const normalizedContent = value.trim().replace(/\s+/g, ' ');
          
          // Handle case where content is just whitespace
          if (normalizedContent === '') {
            return true;
          }
          
          // Split by spaces and count words
          const wordCount = normalizedContent.split(' ').length;
          
          return wordCount <= maxWordCount;
        },
        defaultMessage(args: ValidationArguments) {
          const [maxWordCount] = args.constraints;
          const value = args.value as string;
          
          if (!value || typeof value !== 'string') {
            return `${args.property} must be a string`;
          }
          
          const normalizedContent = value.trim().replace(/\s+/g, ' ');
          const wordCount = normalizedContent === '' ? 0 : normalizedContent.split(' ').length;
          
          return `${args.property} cannot exceed ${maxWordCount} words (currently ${wordCount} words)`;
        },
      },
    });
  };
}
