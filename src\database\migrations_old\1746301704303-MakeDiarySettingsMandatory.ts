import { MigrationInterface, QueryRunner } from "typeorm";

export class MakeDiarySettingsMandatory1746301704303 implements MigrationInterface {
    name = 'MakeDiarySettingsMandatory1746301704303'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // First, check if there are any diary entries without settings
        const entriesWithoutSettings = await queryRunner.query(`
            SELECT de.id 
            FROM diary_entry de
            LEFT JOIN diary_entry_settings des ON de.id = des.diary_entry_id
            WHERE des.id IS NULL
        `);

        if (entriesWithoutSettings.length > 0) {
            // Get the default settings template (first active one)
            const defaultTemplate = await queryRunner.query(`
                SELECT id, title, level, word_limit 
                FROM diary_settings_template 
                WHERE is_active = true 
                ORDER BY level ASC 
                LIMIT 1
            `);

            if (defaultTemplate.length === 0) {
                // Create a default template if none exists
                await queryRunner.query(`
                    INSERT INTO diary_settings_template (id, title, level, word_limit, is_active, description, created_at, updated_at)
                    VALUES (uuid_generate_v4(), 'Default Level', 1, 100, true, 'Default template created by migration', NOW(), NOW())
                    RETURNING id, title, level, word_limit
                `);

                // Get the newly created template
                const newTemplate = await queryRunner.query(`
                    SELECT id, title, level, word_limit 
                    FROM diary_settings_template 
                    ORDER BY created_at DESC 
                    LIMIT 1
                `);

                // Use the new template
                const templateId = newTemplate[0].id;
                const templateTitle = newTemplate[0].title;
                const templateLevel = newTemplate[0].level;
                const templateWordLimit = newTemplate[0].word_limit;

                // Create settings for each entry without settings
                for (const entry of entriesWithoutSettings) {
                    await queryRunner.query(`
                        INSERT INTO diary_entry_settings (id, diary_entry_id, settings_template_id, title, level, word_limit, created_at, updated_at)
                        VALUES (uuid_generate_v4(), $1, $2, $3, $4, $5, NOW(), NOW())
                    `, [entry.id, templateId, templateTitle, templateLevel, templateWordLimit]);
                }
            } else {
                // Use the existing template
                const templateId = defaultTemplate[0].id;
                const templateTitle = defaultTemplate[0].title;
                const templateLevel = defaultTemplate[0].level;
                const templateWordLimit = defaultTemplate[0].word_limit;

                // Create settings for each entry without settings
                for (const entry of entriesWithoutSettings) {
                    await queryRunner.query(`
                        INSERT INTO diary_entry_settings (id, diary_entry_id, settings_template_id, title, level, word_limit, created_at, updated_at)
                        VALUES (uuid_generate_v4(), $1, $2, $3, $4, $5, NOW(), NOW())
                    `, [entry.id, templateId, templateTitle, templateLevel, templateWordLimit]);
                }
            }
        }

        // Now update the relationship to be non-nullable
        await queryRunner.query(`
            ALTER TABLE diary_entry_settings
            ALTER COLUMN diary_entry_id SET NOT NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert the non-nullable constraint
        await queryRunner.query(`
            ALTER TABLE diary_entry_settings
            ALTER COLUMN diary_entry_id DROP NOT NULL
        `);
    }
}
