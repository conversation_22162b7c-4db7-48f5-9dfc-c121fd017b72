# Member Management System - Frontend Integration Flow

This document provides a comprehensive integration flow for frontend developers to implement the Member Management System, with minimal verbal communication needed. It focuses exclusively on API endpoints and data flows without prescribing specific frontend implementation approaches.

## Table of Contents

1. [User Registration](#user-registration)
   - [Student Registration](#student-registration)
   - [Tutor Registration](#tutor-registration)
   - [Email Verification](#email-verification)
2. [Authentication](#authentication)
   - [User Login](#user-login)
   - [Token Management](#token-management)
   - [Password Recovery](#password-recovery)
3. [Profile Management](#profile-management)
   - [View Profile](#view-profile)
   - [Update Profile](#update-profile)
   - [Profile Picture Management](#profile-picture-management)
4. [Tutor Approval](#tutor-approval)
   - [Approval Process](#approval-process)
   - [Approval Management](#approval-management)
5. [Student-Tutor Matching](#student-tutor-matching)
   - [Manual Assignment](#manual-assignment)
   - [Auto Assignment](#auto-assignment)
   - [View Assignments](#view-assignments)

## User Registration

### Student Registration

New students can register through the public registration endpoint.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Fill       │      │  Submit     │      │  Verify     │
│  Form       │ ──▶  │  Registration │ ──▶  │  Email      │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Collect     │      │ POST /auth/ │      │ GET /auth/  │
│ User Data   │      │ register    │      │ verify-email│
│             │      │             │      │ ?token=     │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /auth/register` - Register a new student

**Request Body:**
```json
{
  "userId": "student123",
  "email": "<EMAIL>",
  "phoneNumber": "+1234567890",
  "gender": "male",
  "password": "StrongP@ss123",
  "confirmPassword": "StrongP@ss123",
  "agreedToTerms": true,
  "type": "student"
}
```

**Notes:**
- `userId` is a unique identifier that cannot be changed later
- `email` must be unique and will require verification
- `password` and `confirmPassword` must match
- `agreedToTerms` must be true
- `type` should be "student" for student registration

### Tutor Registration

Tutors register similarly to students but require approval before they can access the system.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Fill       │      │  Submit     │      │  Verify     │      │  Await      │
│  Form       │ ──▶  │  Registration │ ──▶  │  Email      │ ──▶  │  Approval   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Collect     │      │ POST /auth/ │      │ GET /auth/  │      │ System      │
│ User Data   │      │ register    │      │ verify-email│      │ creates     │
│ with Bio    │      │             │      │ ?token=     │      │ approval req│
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /auth/register` - Register a new tutor

**Request Body:**
```json
{
  "userId": "tutor123",
  "email": "<EMAIL>",
  "phoneNumber": "+1234567890",
  "gender": "male",
  "password": "StrongP@ss123",
  "confirmPassword": "StrongP@ss123",
  "agreedToTerms": true,
  "type": "tutor",
  "bio": "Experienced English teacher with 5 years of teaching experience."
}
```

**Notes:**
- `bio` is required for tutor registration
- After registration, a tutor approval request is automatically created
- Tutors cannot log in until their approval request is approved by an admin

### Email Verification

After registration, users must verify their email address.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Receive    │      │  Click      │      │  Account    │
│  Email      │ ──▶  │  Link       │ ──▶  │  Verified   │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ System      │      │ GET /auth/  │      │ Redirect to │
│ sends email │      │ verify-email│      │ login page  │
│ with token  │      │ ?token=     │      │             │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /auth/verify-email` - Verify email with token

**Query Parameters:**
- `token` - Email verification token

## Authentication

### User Login

Users can log in with their userId and password.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Enter      │      │  Submit     │      │  Redirect   │
│  Credentials│ ──▶  │  Login      │ ──▶  │  to App     │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Collect     │      │ POST /auth/ │      │ Store tokens│
│ userId and  │      │ login       │      │ and user    │
│ password    │      │             │      │ data        │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /auth/login` - User login

**Request Body:**
```json
{
  "userId": "student123",
  "password": "StrongP@ss123",
  "rememberMe": true,
  "selectedRole": "student",
  "returnUrl": "/home"
}
```

**Notes:**
- `userId` is the unique identifier provided during registration (not display name)
- `rememberMe` extends token validity to 30 days (default is 1 day)
- `selectedRole` is optional and defaults to the user's primary role
- `returnUrl` is optional and defaults to "/home"

### Token Management

The system uses JWT tokens for authentication.

**Response from login:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token_expires": "2023-06-01T00:00:00.000Z",
    "user": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "John Doe",
      "userId": "student123",
      "email": "<EMAIL>",
      "type": "student",
      "isActive": true,
      "isConfirmed": true,
      "roles": ["student"],
      "selectedRole": "student",
      "profilePictureUrl": "https://example.com/uploads/profile-pictures/123e4567-e89b-12d3-a456-426614174000.jpg"
    },
    "returnUrl": "/home"
  }
}
```

**Notes:**
- Store `access_token` in memory for API requests
- Include token in Authorization header: `Authorization: Bearer {access_token}`
- Store `refresh_token` securely if `rememberMe` is true

### Password Recovery

Users can recover their password if forgotten.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Request    │      │  Receive    │      │  Enter New  │      │  Password   │
│  Reset      │ ──▶  │  Email      │ ──▶  │  Password   │ ──▶  │  Updated    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /auth/ │      │ System      │      │ POST /auth/ │      │ Redirect to │
│ forgot-     │      │ sends email │      │ reset-      │      │ login page  │
│ password    │      │ with token  │      │ password    │      │             │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password with token

## Profile Management

### View Profile

Users can view their own profile or other users' profiles.

```
┌─────────────┐      ┌─────────────┐
│  Request    │      │  Display    │
│  Profile    │ ──▶  │  Profile    │
└─────────────┘      └─────────────┘
       │                    │
       ▼                    ▼
┌─────────────┐      ┌─────────────┐
│ GET /users/ │      │ Render      │
│ profile     │      │ profile     │
│             │      │ data        │
└─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /users/profile` - Get current user's profile
- `GET /profile/admin/:id` - View admin profile (requires authentication)
- `GET /profile/tutor/:id` - View tutor profile (requires authentication)
- `GET /profile/student/:id` - View student profile (requires authentication)

### Update Profile

Users can update their profile information.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Edit       │      │  Submit     │      │  Profile    │
│  Profile    │ ──▶  │  Changes    │ ──▶  │  Updated    │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Collect     │      │ PATCH /users│      │ Display     │
│ updated     │      │ /profile    │      │ success     │
│ profile data│      │             │      │ message     │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `PATCH /users/profile` - Update user profile

**Request Body:**
```json
{
  "name": "John Doe",
  "phoneNumber": "+1234567890",
  "address": "123 Main St",
  "city": "New York",
  "state": "NY",
  "country": "USA",
  "postalCode": "10001",
  "bio": "Student at XYZ University",
  "dateOfBirth": "1990-01-01",
  "gender": "male",
  "socialLinks": {
    "linkedin": "https://linkedin.com/in/johndoe",
    "twitter": "https://twitter.com/johndoe"
  }
}
```

**Notes:**
- `userId` and `email` cannot be changed and should not be included
- All fields are optional - only include fields you want to update

### Profile Picture Management

Users can upload and update their profile picture.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Select     │      │  Upload     │      │  Picture    │
│  Image      │ ──▶  │  Image      │ ──▶  │  Updated    │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Choose file │      │ POST /users/│      │ Display new │
│ from device │      │ profile-    │      │ profile     │
│             │      │ picture     │      │ picture     │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /users/profile-picture` - Upload profile picture

**Request:**
- Use `multipart/form-data` with a file field named `file`
- Maximum file size: 5MB
- Supported formats: JPEG, PNG, GIF

## Tutor Approval

### Approval Process

After registration, tutors must be approved by an admin before they can access the system.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Tutor      │      │  Admin      │      │  Admin      │      │  Tutor      │
│  Registers  │ ──▶  │  Reviews    │ ──▶  │  Approves   │ ──▶  │  Notified   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /auth/ │      │ GET /tutor- │      │ POST /tutor-│      │ System sends│
│ register    │      │ approval/   │      │ approval/   │      │ email to    │
│ (type=tutor)│      │ requests    │      │ approve     │      │ tutor       │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

### Approval Management

Admins can view, approve, or reject tutor approval requests.

**API Endpoints:**
- `GET /tutor-approval/requests` - Get all tutor approval requests (admin only)
- `POST /tutor-approval/approve` - Approve a tutor (admin only)
- `POST /tutor-approval/reject` - Reject a tutor (admin only)

**Approve Request Body:**
```json
{
  "approvalId": "123e4567-e89b-12d3-a456-426614174000",
  "adminNotes": "Credentials verified and approved"
}
```

**Reject Request Body:**
```json
{
  "approvalId": "123e4567-e89b-12d3-a456-426614174000",
  "rejectionReason": "Missing required qualifications",
  "adminNotes": "Applicant does not meet our requirements"
}
```

## Student-Tutor Matching

### Manual Assignment

Admins can manually assign tutors to students for specific modules.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Select     │      │  Assign     │      │  Assignment │
│  Student    │ ──▶  │  Tutor      │ ──▶  │  Created    │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /admin/ │      │ POST /tutor-│      │ System      │
│ users?type= │      │ matching/   │      │ notifies    │
│ student     │      │ assign      │      │ both parties│
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /tutor-matching/assign` - Assign a tutor to a student (admin only)

**Request Body:**
```json
{
  "studentId": "123e4567-e89b-12d3-a456-426614174000",
  "tutorId": "123e4567-e89b-12d3-a456-426614174001",
  "planFeatureId": "123e4567-e89b-12d3-a456-426614174002",
  "notes": "Assigned based on student's request"
}
```

### Auto Assignment

Admins can automatically assign tutors to students based on workload balancing.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Select     │      │  Auto       │      │  Assignments│
│  Module     │ ──▶  │  Assign     │ ──▶  │  Created    │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /admin/ │      │ POST /tutor-│      │ System      │
│ plan-       │      │ matching/   │      │ notifies    │
│ features    │      │ auto-assign │      │ all parties │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /tutor-matching/auto-assign` - Auto-assign tutors to students (admin only)

**Request Body:**
```json
{
  "planFeatureId": "123e4567-e89b-12d3-a456-426614174000",
  "maxStudentsPerTutor": 10
}
```

### View Assignments

Students, tutors, and admins can view their assignments.

**API Endpoints:**
- `GET /student/tutors` - Get tutors assigned to the current student (student only)
- `GET /tutor/students` - Get students assigned to the current tutor (tutor only)
- `GET /admin/tutor-matching/assignments` - Get all assignments (admin only)

**Query Parameters:**
- `status` - Filter by status (active, inactive)
- `planFeatureId` - Filter by module
- `page` - Page number for pagination
- `limit` - Items per page
