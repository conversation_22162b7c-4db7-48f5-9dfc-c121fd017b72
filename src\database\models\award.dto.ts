import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEnum, IsInt, IsOptional, IsBoolean, IsDateString, IsObject, Min, IsUUID, IsArray } from 'class-validator';
import { AwardModule, AwardCriteria, AwardFrequency } from '../entities/award.entity';
import { RewardPointSource, RewardPointType } from '../entities/reward-point.entity';
import { AwardCriteriaConfig } from '../../constants/award-criteria.constant';
import { IsValidCriteriaForModule } from '../decorators/is-valid-criteria-for-module.decorator';

/**
 * DTO for an award criteria schema field
 */
export class AwardCriteriaSchemaFieldDto {
  @ApiProperty({
    example: 'number',
    description: 'The type of the field',
    enum: ['number', 'boolean', 'string']
  })
  type: 'number' | 'boolean' | 'string';

  @ApiProperty({
    example: 'Minimum score required',
    description: 'Description of what this field represents'
  })
  description: string;

  @ApiProperty({
    example: true,
    description: 'Whether this field is required'
  })
  required: boolean;

  @ApiProperty({
    example: 0,
    description: 'Minimum value for number fields',
    required: false
  })
  minimum?: number;

  @ApiProperty({
    example: 100,
    description: 'Maximum value for number fields',
    required: false
  })
  maximum?: number;
}

/**
 * DTO for creating a new award
 */
export class CreateAwardDto {
  @ApiProperty({
    example: 'Gold Star Diarist',
    description: 'Name of the award'
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: 'Awarded to the student with the highest diary scores for the month',
    description: 'Description of the award'
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    example: AwardModule.DIARY,
    description: 'Module that the award belongs to',
    enum: AwardModule
  })
  @IsNotEmpty()
  @IsEnum(AwardModule)
  module: AwardModule;

  @ApiProperty({
    example: [AwardCriteria.DIARY_SCORE, AwardCriteria.ATTENDANCE],
    description: 'Criteria for the award (can have multiple). Must be valid for the specified module.',
    enum: AwardCriteria,
    isArray: true
  })
  @IsNotEmpty()
  @IsArray()
  @IsValidCriteriaForModule()
  criteria: AwardCriteria[];

  @ApiProperty({
    example: AwardFrequency.MONTHLY,
    description: 'Frequency of the award',
    enum: AwardFrequency
  })
  @IsNotEmpty()
  @IsEnum(AwardFrequency)
  frequency: AwardFrequency;

  @ApiProperty({
    example: 100,
    description: 'Number of reward points for this award'
  })
  @IsNotEmpty()
  @IsInt()
  @Min(0)
  rewardPoints: number;

  @ApiProperty({
    example: true,
    description: 'Whether the award is active',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    example: { minScore: 90, entriesRequired: 5 },
    description: 'Configuration for the award criteria',
    required: false
  })
  @IsOptional()
  @IsObject()
  criteriaConfig?: any;

  @ApiProperty({
    example: '2023-01-01',
    description: 'Start date for the award',
    required: false
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    example: '2023-12-31',
    description: 'End date for the award',
    required: false
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    example: 'https://example.com/award-image.png',
    description: 'URL for the award image',
    required: false
  })
  @IsOptional()
  @IsString()
  imageUrl?: string;
}

/**
 * DTO for updating an award
 */
export class UpdateAwardDto {
  @ApiProperty({
    example: 'Gold Star Diarist',
    description: 'Name of the award',
    required: false
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    example: 'Awarded to the student with the highest diary scores for the month',
    description: 'Description of the award',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: AwardModule.DIARY,
    description: 'Module that the award belongs to',
    enum: AwardModule,
    required: false
  })
  @IsOptional()
  @IsEnum(AwardModule)
  module?: AwardModule;

  @ApiProperty({
    example: [AwardCriteria.DIARY_SCORE, AwardCriteria.ATTENDANCE],
    description: 'Criteria for the award (can have multiple). Must be valid for the specified module.',
    enum: AwardCriteria,
    isArray: true,
    required: false
  })
  @IsOptional()
  @IsArray()
  @IsValidCriteriaForModule()
  criteria?: AwardCriteria[];

  @ApiProperty({
    example: AwardFrequency.MONTHLY,
    description: 'Frequency of the award',
    enum: AwardFrequency,
    required: false
  })
  @IsOptional()
  @IsEnum(AwardFrequency)
  frequency?: AwardFrequency;

  @ApiProperty({
    example: 100,
    description: 'Number of reward points for this award',
    required: false
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  rewardPoints?: number;

  @ApiProperty({
    example: true,
    description: 'Whether the award is active',
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    example: { minScore: 90, entriesRequired: 5 },
    description: 'Configuration for the award criteria',
    required: false
  })
  @IsOptional()
  @IsObject()
  criteriaConfig?: any;

  @ApiProperty({
    example: '2023-01-01',
    description: 'Start date for the award',
    required: false
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({
    example: '2023-12-31',
    description: 'End date for the award',
    required: false
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({
    example: 'https://example.com/award-image.png',
    description: 'URL for the award image',
    required: false
  })
  @IsOptional()
  @IsString()
  imageUrl?: string;
}

/**
 * DTO for award response
 */
export class AwardResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty({ enum: AwardModule })
  module: AwardModule;

  @ApiProperty({ enum: AwardCriteria, isArray: true })
  criteria: AwardCriteria[];

  @ApiProperty({ enum: AwardFrequency })
  frequency: AwardFrequency;

  @ApiProperty()
  rewardPoints: number;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty({ required: false })
  criteriaConfig?: any;

  @ApiProperty({ required: false })
  startDate?: Date;

  @ApiProperty({ required: false })
  endDate?: Date;

  @ApiProperty({ required: false })
  imageUrl?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

/**
 * DTO for creating a reward point transaction
 */
export class CreateRewardPointDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'User ID to assign points to'
  })
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiProperty({
    example: RewardPointSource.ADMIN_ADJUSTMENT,
    description: 'Source of the reward points',
    enum: RewardPointSource
  })
  @IsNotEmpty()
  @IsEnum(RewardPointSource)
  source: RewardPointSource;

  @ApiProperty({
    example: RewardPointType.EARNED,
    description: 'Type of reward point transaction',
    enum: RewardPointType
  })
  @IsNotEmpty()
  @IsEnum(RewardPointType)
  type: RewardPointType;

  @ApiProperty({
    example: 100,
    description: 'Number of points to add/subtract'
  })
  @IsNotEmpty()
  @IsInt()
  points: number;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Reference ID (e.g., award ID, diary entry ID)',
    required: false
  })
  @IsOptional()
  @IsString()
  referenceId?: string;

  @ApiProperty({
    example: 'Admin adjustment for excellent performance',
    description: 'Description of the reward point transaction',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: '2023-12-31',
    description: 'Expiry date for the points',
    required: false
  })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;
}

/**
 * DTO for reward point response
 */
export class RewardPointResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty({ enum: RewardPointSource })
  source: RewardPointSource;

  @ApiProperty({ enum: RewardPointType })
  type: RewardPointType;

  @ApiProperty()
  points: number;

  @ApiProperty({ required: false })
  referenceId?: string;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty({ required: false })
  expiryDate?: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

/**
 * DTO for user reward point balance
 */
export class UserRewardPointBalanceDto {
  @ApiProperty()
  userId: string;

  @ApiProperty()
  userName: string;

  @ApiProperty()
  totalPoints: number;

  @ApiProperty()
  availablePoints: number;

  @ApiProperty()
  spentPoints: number;

  @ApiProperty()
  expiredPoints: number;

  @ApiProperty({ type: [RewardPointResponseDto] })
  recentTransactions: RewardPointResponseDto[];
}

/**
 * DTO for creating an award winner
 */
export class CreateAwardWinnerDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'User ID of the winner'
  })
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Award ID'
  })
  @IsNotEmpty()
  @IsUUID()
  awardId: string;

  @ApiProperty({
    example: '2023-07-31',
    description: 'Date the award was given'
  })
  @IsNotEmpty()
  @IsDateString()
  awardDate: string;

  @ApiProperty({
    example: 'Highest score in July 2023',
    description: 'Reason for the award',
    required: false
  })
  @IsOptional()
  @IsString()
  awardReason?: string;

  @ApiProperty({
    example: { score: 95, entries: 20 },
    description: 'Additional metadata for the award',
    required: false
  })
  @IsOptional()
  @IsObject()
  metadata?: any;
}

/**
 * DTO for award winner response
 */
export class AwardWinnerResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  userName: string;

  @ApiProperty()
  awardId: string;

  @ApiProperty()
  awardName: string;

  @ApiProperty()
  awardDate: Date;

  @ApiProperty({ required: false })
  awardReason?: string;

  @ApiProperty({ required: false })
  metadata?: any;

  @ApiProperty()
  rewardPoints: number;

  @ApiProperty()
  createdAt: Date;
}

/**
 * DTO for user awards response
 */
export class UserAwardsResponseDto {
  @ApiProperty()
  userId: string;

  @ApiProperty()
  userName: string;

  @ApiProperty({ type: [AwardWinnerResponseDto] })
  awards: AwardWinnerResponseDto[];

  @ApiProperty()
  totalAwards: number;

  @ApiProperty()
  totalRewardPoints: number;
}
