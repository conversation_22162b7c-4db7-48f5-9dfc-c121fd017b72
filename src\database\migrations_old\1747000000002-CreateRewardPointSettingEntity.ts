import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateRewardPointSettingEntity1747000000002 implements MigrationInterface {
    name = 'CreateRewardPointSettingEntity1747000000002'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the reward_point_setting table
        await queryRunner.query(`
            CREATE TABLE "reward_point_setting" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL,
                "updated_at" TIMESTAMP NOT NULL,
                "name" character varying(100) NOT NULL,
                "description" text,
                "conversion_rate" decimal(10,2) NOT NULL,
                "is_active" boolean NOT NULL DEFAULT false,
                CONSTRAINT "PK_reward_point_setting" PRIMARY KEY ("id")
            )
        `);

        // Insert a default setting
        const now = new Date().toISOString();
        await queryRunner.query(`
            INSERT INTO "reward_point_setting" 
            ("name", "description", "conversion_rate", "is_active", "created_at", "updated_at")
            VALUES 
            ('Default Conversion Rate', 'Default conversion rate for reward points (100 points = $1)', 100, true, '${now}', '${now}')
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the reward_point_setting table
        await queryRunner.query(`DROP TABLE "reward_point_setting"`);
    }
}
