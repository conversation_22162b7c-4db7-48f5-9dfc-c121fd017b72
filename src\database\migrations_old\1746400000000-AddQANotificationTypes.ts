import { MigrationInterface, QueryRunner } from "typeorm";

export class AddQANotificationTypes1746400000000 implements MigrationInterface {
    name = 'AddQANotificationTypes1746400000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Rename the existing enum type to a temporary name
        await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" RENAME TO "notification_type_enum_old"`);
        
        // Create a new enum type with the additional values
        await queryRunner.query(`CREATE TYPE "public"."notification_type_enum" AS ENUM('diary_submission', 'diary_update', 'diary_review', 'diary_feedback', 'mission_created', 'mission_submission', 'mission_feedback', 'mission_correction', 'mission_review_complete', 'tutor_greeting', 'tutor_assignment', 'tutor_verification', 'chat_message', 'system', 'qa_submission', 'qa_review', 'qa_feedback')`);
        
        // Update the column to use the new enum type
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum" USING "type"::"text"::"public"."notification_type_enum"`);
        
        // Drop the old enum type
        await queryRunner.query(`DROP TYPE "public"."notification_type_enum_old"`);
        
        // Update user_notification_preference table if it exists
        try {
            // Check if the table exists
            const tableExists = await queryRunner.hasTable('user_notification_preference');
            if (tableExists) {
                // Rename the existing enum type for the preference table
                await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" RENAME TO "user_notification_preference_notification_type_enum_old"`);
                
                // Create a new enum type with the additional values
                await queryRunner.query(`CREATE TYPE "public"."user_notification_preference_notification_type_enum" AS ENUM('diary_submission', 'diary_update', 'diary_review', 'diary_feedback', 'mission_created', 'mission_submission', 'mission_feedback', 'mission_correction', 'mission_review_complete', 'tutor_greeting', 'tutor_assignment', 'tutor_verification', 'chat_message', 'system', 'qa_submission', 'qa_review', 'qa_feedback')`);
                
                // Update the column to use the new enum type
                await queryRunner.query(`ALTER TABLE "user_notification_preference" ALTER COLUMN "notification_type" TYPE "public"."user_notification_preference_notification_type_enum" USING "notification_type"::"text"::"public"."user_notification_preference_notification_type_enum"`);
                
                // Drop the old enum type
                await queryRunner.query(`DROP TYPE "public"."user_notification_preference_notification_type_enum_old"`);
            }
        } catch (error) {
            console.log('Error updating user_notification_preference table:', error);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Rename the existing enum type to a temporary name
        await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" RENAME TO "notification_type_enum_old"`);
        
        // Create a new enum type without the new values
        await queryRunner.query(`CREATE TYPE "public"."notification_type_enum" AS ENUM('diary_submission', 'diary_update', 'diary_review', 'diary_feedback', 'mission_created', 'mission_submission', 'mission_feedback', 'mission_correction', 'mission_review_complete', 'tutor_greeting', 'tutor_assignment', 'tutor_verification', 'chat_message', 'system')`);
        
        // Update the column to use the new enum type
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum" USING "type"::"text"::"public"."notification_type_enum"`);
        
        // Drop the old enum type
        await queryRunner.query(`DROP TYPE "public"."notification_type_enum_old"`);
        
        // Update user_notification_preference table if it exists
        try {
            // Check if the table exists
            const tableExists = await queryRunner.hasTable('user_notification_preference');
            if (tableExists) {
                // Rename the existing enum type for the preference table
                await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" RENAME TO "user_notification_preference_notification_type_enum_old"`);
                
                // Create a new enum type without the new values
                await queryRunner.query(`CREATE TYPE "public"."user_notification_preference_notification_type_enum" AS ENUM('diary_submission', 'diary_update', 'diary_review', 'diary_feedback', 'mission_created', 'mission_submission', 'mission_feedback', 'mission_correction', 'mission_review_complete', 'tutor_greeting', 'tutor_assignment', 'tutor_verification', 'chat_message', 'system')`);
                
                // Update the column to use the new enum type
                await queryRunner.query(`ALTER TABLE "user_notification_preference" ALTER COLUMN "notification_type" TYPE "public"."user_notification_preference_notification_type_enum" USING "notification_type"::"text"::"public"."user_notification_preference_notification_type_enum"`);
                
                // Drop the old enum type
                await queryRunner.query(`DROP TYPE "public"."user_notification_preference_notification_type_enum_old"`);
            }
        } catch (error) {
            console.log('Error updating user_notification_preference table:', error);
        }
    }
}
