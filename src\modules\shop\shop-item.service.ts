import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Not, Like, DataSource, FindOptionsWhere, Between, MoreThan, LessThan } from 'typeorm';
import { ShopItem, ShopItemType } from '../../database/entities/shop-item.entity';
import { ShopSkinMapping } from '../../database/entities/shop-skin-mapping.entity';
import { PromotionsService } from '../promotions/promotions.service';
import { PromotionResponseDto } from '../../database/models/promotion.dto';
import { DiscountType } from '../../database/entities/promotion.entity';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { ShopCategoryService } from './shop-category.service';
import { ShopFileService } from './shop-file.service';
import {
  CreateShopItemDto,
  UpdateShopItemDto,
  ShopItemResponseDto,
  ShopItemWithPromotionDto,
  BulkPriceUpdateDto,
  BulkDiscountUpdateDto,
  ApplyPromotionToItemsDto
} from '../../database/models/shop.dto';

// Define the search params interface
export interface ShopItemSearchParams {
  categoryId?: string;
  type?: ShopItemType;
  isActive?: boolean;
  isFeatured?: boolean;
  minPrice?: number;
  maxPrice?: number;
  search?: string;
  itemNumber?: string;
  promotionId?: string;
}
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

@Injectable()
export class ShopItemService {
  private readonly logger = new Logger(ShopItemService.name);

  constructor(
    @InjectRepository(ShopItem)
    private readonly shopItemRepository: Repository<ShopItem>,
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => PromotionsService))
    private readonly promotionsService: PromotionsService,
    private readonly fileRegistryService: FileRegistryService,
    private readonly shopCategoryService: ShopCategoryService,
    private readonly shopFileService: ShopFileService
  ) {}

  /**
   * Create a new shop item
   * @param createShopItemDto Shop item creation data
   * @returns Created shop item
   */
  async createShopItem(createShopItemDto: CreateShopItemDto): Promise<ShopItemResponseDto> {
    try {
      // Check if item number already exists
      if (createShopItemDto.itemNumber) {
        const existingItem = await this.shopItemRepository.findOne({
          where: { itemNumber: createShopItemDto.itemNumber }
        });

        if (existingItem) {
          throw new ConflictException(`Shop item with item number ${createShopItemDto.itemNumber} already exists`);
        }
      } else {
        // Generate item number if not provided
        createShopItemDto.itemNumber = await this.generateItemNumber({
          categoryId: createShopItemDto.categoryId
        });
      }

      // Check if the category is a skin category
      const isSkinCategory = await this.isSkinCategory(createShopItemDto.categoryId);
      if (isSkinCategory) {
        throw new BadRequestException('Skin items cannot be created directly. They must be created in the diary skin system first.');
      }

      // Create shop item
      const shopItem = this.shopItemRepository.create(createShopItemDto);
      const savedShopItem = await this.shopItemRepository.save(shopItem);

      // Get category name for response
      const categoryInfo = await this.shopCategoryService.getShopCategoryById(savedShopItem.categoryId);

      return this.mapShopItemToDto(savedShopItem, categoryInfo.name);
    } catch (error) {
      if (error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error creating shop item: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create shop item: ${error.message}`);
    }
  }

  /**
   * Get a shop item by ID
   * @param id Shop item ID
   * @returns Shop item details
   */
  async getShopItemById(id: string): Promise<ShopItemResponseDto> {
    try {
      const shopItem = await this.shopItemRepository.findOne({
        where: { id }
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${id} not found`);
      }

      // Increment view count
      shopItem.viewCount += 1;
      await this.shopItemRepository.save(shopItem);

      // Get category name
      const categoryInfo = await this.shopCategoryService.getShopCategoryById(shopItem.categoryId);

      // Get file URL if available
      let dto = this.mapShopItemToDto(shopItem, categoryInfo.name);
      if (shopItem.filePath) {
        try {
          const fileUrl = await this.shopFileService.getShopItemFileUrl(shopItem.id);
          if (fileUrl) {
            dto.filePath = fileUrl;
          }
        } catch (error) {
          this.logger.error(`Error generating file URL: ${error.message}`, error.stack);
        }
      }

      return dto;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting shop item: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get shop item: ${error.message}`);
    }
  }

  /**
   * Get a shop item by item number
   * @param itemNumber Shop item number
   * @returns Shop item details
   */
  async getShopItemByItemNumber(itemNumber: string): Promise<ShopItemResponseDto> {
    try {
      const shopItem = await this.shopItemRepository.findOne({
        where: { itemNumber }
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with item number ${itemNumber} not found`);
      }

      // Increment view count
      shopItem.viewCount += 1;
      await this.shopItemRepository.save(shopItem);

      // Get category name
      const categoryInfo = await this.shopCategoryService.getShopCategoryById(shopItem.categoryId);

      // Get file URL if available
      let dto = this.mapShopItemToDto(shopItem, categoryInfo.name);
      if (shopItem.filePath) {
        try {
          const fileUrl = await this.shopFileService.getShopItemFileUrl(shopItem.id);
          if (fileUrl) {
            dto.filePath = fileUrl;
          }
        } catch (error) {
          this.logger.error(`Error generating file URL: ${error.message}`, error.stack);
        }
      }

      return dto;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting shop item: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get shop item: ${error.message}`);
    }
  }

  /**
   * Get all shop items with optional filtering and pagination
   * @param searchParams Search parameters
   * @param paginationDto Pagination parameters
   * @returns Paged list of shop items
   */
  async getAllShopItems(searchParams?: ShopItemSearchParams, paginationDto?: PaginationDto): Promise<PagedListDto<ShopItemResponseDto>> {
    try {
      this.logger.log(`getAllShopItems called with searchParams: ${JSON.stringify(searchParams)}, paginationDto: ${JSON.stringify(paginationDto)}`);

      const { categoryId, type, isActive, isFeatured, minPrice, maxPrice, search, itemNumber, promotionId } = searchParams || {};

      // Build where conditions
      const whereConditions: FindOptionsWhere<ShopItem> = {};

      if (categoryId) {
        whereConditions.categoryId = categoryId;
      }

      if (type) {
        whereConditions.type = type;
      }

      if (isActive !== undefined) {
        whereConditions.isActive = isActive;
      }

      if (isFeatured !== undefined) {
        whereConditions.isFeatured = isFeatured;
      }

      // Handle price range
      if (minPrice !== undefined && maxPrice !== undefined) {
        whereConditions.price = Between(minPrice, maxPrice);
      } else if (minPrice !== undefined) {
        whereConditions.price = MoreThan(minPrice);
      } else if (maxPrice !== undefined) {
        whereConditions.price = LessThan(maxPrice);
      }

      // Handle search
      if (search) {
        whereConditions.title = Like(`%${search}%`);
      }

      // Handle item number filter
      if (itemNumber) {
        if (itemNumber.length >= 3) {
          whereConditions.itemNumber = Like(`%${itemNumber}%`);
        } else {
          whereConditions.itemNumber = itemNumber;
        }
      }

      // Handle promotion ID filter
      if (promotionId) {
        whereConditions.promotionId = promotionId;
      }

      // Apply pagination
      const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC' } = paginationDto || {};
      const skip = (page - 1) * limit;

      // Log the where conditions
      this.logger.log(`Where conditions: ${JSON.stringify(whereConditions)}`);

      // Get total count
      const totalCount = await this.shopItemRepository.count({
        where: whereConditions
      });

      this.logger.log(`Total count: ${totalCount}`);

      // Get items
      const shopItems = await this.shopItemRepository.find({
        where: whereConditions,
        order: { [sortBy]: sortDirection },
        skip,
        take: limit
      });

      this.logger.log(`Found ${shopItems.length} shop items`);

      // If no items found, log a more detailed query to help debug
      if (shopItems.length === 0) {
        this.logger.warn('No shop items found. Checking if any items exist in the database...');
        const allItemsCount = await this.shopItemRepository.count();
        this.logger.log(`Total items in database: ${allItemsCount}`);

        if (allItemsCount > 0) {
          // There are items, but our query didn't find any
          this.logger.warn('Items exist in the database but none matched the query criteria');

          // Get a sample of items to see what's in the database
          const sampleItems = await this.shopItemRepository.find({ take: 5 });
          this.logger.log(
            `Sample items: ${JSON.stringify(
              sampleItems.map((item) => ({
                id: item.id,
                title: item.title,
                isActive: item.isActive,
                type: item.type
              }))
            )}`
          );
        }
      }

      // Map to DTOs
      const dtos = shopItems.map((item) => this.mapShopItemToDto(item));

      // Generate file URLs for all items with file paths
      await Promise.all(
        dtos.map(async (dto, index) => {
          const item = shopItems[index];
          if (item && item.filePath) {
            try {
              const fileUrl = await this.shopFileService.getShopItemFileUrl(item.id);

              if (fileUrl) {
                dto.filePath = fileUrl;
              }
            } catch (error) {
              this.logger.error(`Error generating file URL for shop item ${item.id}: ${error.message}`, error.stack);
            }
          }
        })
      );

      // Map category names to DTOs
      await Promise.all(
        dtos.map(async (dto, index) => {
          const categoryId = shopItems[index].categoryId;
          try {
            const category = await this.shopCategoryService.getShopCategoryById(categoryId);
            dto.categoryName = category.name;
          } catch (error) {
            this.logger.error(`Error getting category name for ID ${categoryId}: ${error.message}`);
          }
        })
      );

      return new PagedListDto(dtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting shop items: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get shop items: ${error.message}`);
    }
  }

  /**
   * Get all shop items grouped by category
   * @param isActive Optional filter for active items only
   * @param title Optional title to filter items by (partial match)
   * @returns Shop items grouped by category
   */
  async getAllShopItemsGroupedByCategory(isActive?: boolean, title?: string): Promise<any> {
    try {
      const categories = await this.shopCategoryService.getAllShopCategories();

      const whereConditions: FindOptionsWhere<ShopItem> = {};
      if (isActive !== undefined) {
        whereConditions.isActive = isActive;
      }

      let allItems: ShopItem[];
      if (title && title.length >= 3) {
        const queryBuilder = this.shopItemRepository.createQueryBuilder('shopItem');

        // Apply active filter if provided
        if (isActive !== undefined) {
          queryBuilder.where('shopItem.isActive = :isActive', { isActive });
        }

        // Apply title filter (case-insensitive partial match)
        queryBuilder.andWhere('LOWER(shopItem.title) LIKE LOWER(:title)', { title: `%${title}%` });

        // Set order
        queryBuilder.orderBy('shopItem.categoryId', 'ASC').addOrderBy('shopItem.createdAt', 'DESC');

        allItems = await queryBuilder.getMany();
      } else {
        // No title filter or title too short, use regular find
        allItems = await this.shopItemRepository.find({
          where: whereConditions,
          order: { categoryId: 'ASC', createdAt: 'DESC' }
        });
      }

      // Group items by category
      const result = [];
      for (const category of categories.items) {
        const categoryItems = allItems.filter((item) => item.categoryId === category.id);

        if (categoryItems.length === 0) {
          continue; // Skip empty categories
        }

        const itemDtos = categoryItems.map((item) => this.mapShopItemToDto(item));

        // Check if this is the skin category
        const isSkinCategory = await this.isSkinCategory(category.id);
        this.logger.log(`Category ${category.name} (${category.id}) is ${isSkinCategory ? 'a skin category' : 'not a skin category'}`);

        // Generate file URLs for all items with file paths
        await Promise.all(
          itemDtos.map(async (dto, index) => {
            const item = categoryItems[index];
            if (item && item.filePath) {
              try {
                if (isSkinCategory) {
                  // For skin category items, get the diary skin ID and use it to generate the URL
                  this.logger.log(`Getting diary skin ID for shop item ${item.id} in skin category`);
                  try {
                    // Use a direct database query to get the diary skin ID
                    const skinMapping = await this.dataSource.getRepository(ShopSkinMapping).findOne({
                      where: { shopItemId: item.id }
                    });

                    // Get the diary skin ID from the mapping
                    const diarySkinId = skinMapping?.diarySkinId;

                    if (diarySkinId) {
                      // Generate URL using the diary skin ID
                      this.logger.log(`Found diary skin ID ${diarySkinId} for shop item ${item.id}`);
                      const skinFileUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.DIARY_SKIN, diarySkinId);

                      if (skinFileUrl) {
                        this.logger.log(`Generated skin file URL: ${skinFileUrl}`);
                        dto.filePath = skinFileUrl;
                      } else {
                        // Fallback to the original file path
                        this.logger.warn(`Could not generate skin file URL, falling back to original file path`);
                        const fileUrl = await this.shopFileService.getShopItemFileUrl(item.id);
                        if (fileUrl) {
                          dto.filePath = fileUrl;
                        }
                      }
                    } else {
                      // Fallback to the original file path
                      this.logger.warn(`No diary skin ID found for shop item ${item.id}, falling back to original file path`);
                      const fileUrl = await this.shopFileService.getShopItemFileUrl(item.id);
                      if (fileUrl) {
                        dto.filePath = fileUrl;
                      }
                    }
                  } catch (skinError) {
                    this.logger.error(`Error getting diary skin ID: ${skinError.message}`, skinError.stack);
                    // Fallback to the original file path
                    const fileUrl = await this.shopFileService.getShopItemFileUrl(item.id);
                    if (fileUrl) {
                      dto.filePath = fileUrl;
                    }
                  }
                } else {
                  // For regular items, use the shop file service
                  const fileUrl = await this.shopFileService.getShopItemFileUrl(item.id);
                  if (fileUrl) {
                    dto.filePath = fileUrl;
                  }
                }
              } catch (error) {
                this.logger.error(`Error generating file URL for shop item ${item.id}: ${error.message}`, error.stack);
              }
            }
          })
        );

        // Add category with items to result
        result.push({
          categoryId: category.id,
          categoryName: category.name,
          description: category.description,
          imageUrl: category.imageUrl,
          items: itemDtos
        });
      }

      return {
        categories: result,
        totalCount: allItems.length
      };
    } catch (error) {
      this.logger.error(`Error getting shop items grouped by category: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get shop items grouped by category: ${error.message}`);
    }
  }

  /**
   * Update a shop item
   * @param id Shop item ID
   * @param updateShopItemDto Shop item update data
   * @returns Updated shop item
   */
  async updateShopItem(id: string, updateShopItemDto: UpdateShopItemDto): Promise<ShopItemResponseDto> {
    try {
      const shopItem = await this.shopItemRepository.findOne({
        where: { id }
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${id} not found`);
      }

      // Check if item number is being changed and if it already exists
      if (updateShopItemDto.itemNumber && updateShopItemDto.itemNumber !== shopItem.itemNumber) {
        const existingItem = await this.shopItemRepository.findOne({
          where: {
            itemNumber: updateShopItemDto.itemNumber,
            id: Not(id) // Exclude current item
          }
        });

        if (existingItem) {
          throw new ConflictException(`Shop item with item number ${updateShopItemDto.itemNumber} already exists`);
        }
      }

      // If the item is being changed to free, ensure price is 0 and isPurchasableInRewardpoint is false
      if (updateShopItemDto.type === ShopItemType.FREE) {
        updateShopItemDto.price = 0;
        updateShopItemDto.isPurchasableInRewardpoint = false;
        updateShopItemDto.discountedPrice = null;
      }

      // Update shop item
      const updatedShopItem = { ...shopItem, ...updateShopItemDto };
      const savedShopItem = await this.shopItemRepository.save(updatedShopItem);

      // Get category name for response
      const categoryInfo = await this.shopCategoryService.getShopCategoryById(savedShopItem.categoryId);

      // Generate a proper file URL for the response if the item has a file path
      let dto = this.mapShopItemToDto(savedShopItem, categoryInfo.name);
      if (savedShopItem.filePath) {
        try {
          const fileUrl = await this.shopFileService.getShopItemFileUrl(savedShopItem.id);
          if (fileUrl) {
            this.logger.log(`Generated file URL for shop item: ${fileUrl}`);
            dto.filePath = fileUrl;
          }
        } catch (error) {
          this.logger.error(`Error generating file URL: ${error.message}`, error.stack);
        }
      }

      return dto;
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Error updating shop item: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to update shop item: ${error.message}`);
    }
  }

  /**
   * Delete a shop item
   * @param id Shop item ID
   * @returns Success message
   */
  async deleteShopItem(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const shopItem = await this.shopItemRepository.findOne({
        where: { id }
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${id} not found`);
      }

      // Delete the shop item
      await this.shopItemRepository.remove(shopItem);

      // Delete the file if it exists
      if (shopItem.filePath) {
        try {
          await this.shopFileService.deleteShopItemFile(shopItem.filePath);
        } catch (error) {
          this.logger.error(`Error deleting file: ${error.message}`, error.stack);
        }
      }

      return { success: true, message: `Shop item with ID ${id} deleted successfully` };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error deleting shop item: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to delete shop item: ${error.message}`);
    }
  }

  /**
   * Create a new shop item with file upload in one step
   * @param file File to upload
   * @param createShopItemDto Item creation data
   * @returns Created item
   */
  async createShopItemWithFile(file: any, createShopItemDto: CreateShopItemDto): Promise<ShopItemResponseDto> {
    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log('Starting createShopItemWithFile transaction');

      // If no item number is provided, generate one
      if (!createShopItemDto.itemNumber) {
        createShopItemDto.itemNumber = await this.generateItemNumber({
          categoryId: createShopItemDto.categoryId
        });
        this.logger.log(`Generated item number: ${createShopItemDto.itemNumber}`);
      }

      // Check if item with same item number already exists
      const existingItem = await this.shopItemRepository.findOne({
        where: { itemNumber: createShopItemDto.itemNumber }
      });

      if (existingItem) {
        throw new ConflictException(`Shop item with item number '${createShopItemDto.itemNumber}' already exists`);
      }

      // Check if category exists
      try {
        await this.shopCategoryService.getShopCategoryById(createShopItemDto.categoryId);
      } catch (error) {
        throw new NotFoundException(`Shop category with ID ${createShopItemDto.categoryId} not found`);
      }

      // If promotion ID is provided and not null, check if promotion exists
      if (createShopItemDto.promotionId !== null && createShopItemDto.promotionId !== undefined && createShopItemDto.promotionId !== '') {
        try {
          await this.promotionsService.getPromotionById(createShopItemDto.promotionId);
        } catch (error) {
          throw new NotFoundException(`Promotion with ID ${createShopItemDto.promotionId} not found`);
        }
      } else {
        // Ensure promotionId is explicitly set to null
        createShopItemDto.promotionId = null;
      }

      // If the item is free, ensure price is 0 and isPurchasableInRewardpoint is false
      if (createShopItemDto.type === ShopItemType.FREE) {
        createShopItemDto.price = 0;
        createShopItemDto.isPurchasableInRewardpoint = false;
        createShopItemDto.discountedPrice = null;
      }

      // Store original file path if any
      const initialFilePath = createShopItemDto.filePath;

      // For S3 storage, we need to create the shop item first to get the UUID
      // For local storage, we can upload the file first
      let uploadedFilePath = null;
      let shouldUploadAfterCreation = false;

      // Check if we're using S3 storage and have a file to upload
      if (file && this.fileRegistryService.isS3Provider()) {
        this.logger.log('S3 storage detected - will upload file after creating shop item');
        shouldUploadAfterCreation = true;
      } else if (file) {
        try {
          this.logger.log('Local storage - uploading file before saving shop item');
          // Upload the file first, before creating the shop item (local storage)
          const result = await this.shopFileService.uploadShopItemFile(file, createShopItemDto.itemNumber, {
            categoryId: createShopItemDto.categoryId,
            userId: null // No user ID for admin uploads
          });

          uploadedFilePath = result.filePath;
          this.logger.log(`File uploaded successfully: ${uploadedFilePath}`);
        } catch (fileError) {
          this.logger.error(`Error uploading file: ${fileError.message}`, fileError.stack);
          throw new BadRequestException(`Failed to upload file: ${fileError.message}`);
        }
      }

      // Set the file path for shop item creation
      createShopItemDto.filePath = uploadedFilePath || initialFilePath;

      // Check if the category is a skin category
      const isSkinCategory = await this.isSkinCategory(createShopItemDto.categoryId);
      if (isSkinCategory) {
        throw new BadRequestException('Skin items cannot be created directly. They must be created in the diary skin system first.');
      }

      // Create and save the shop item
      const shopItem = this.shopItemRepository.create({
        title: createShopItemDto.title,
        description: createShopItemDto.description,
        categoryId: createShopItemDto.categoryId,
        type: createShopItemDto.type,
        price: createShopItemDto.price,
        isPurchasableInRewardpoint: createShopItemDto.isPurchasableInRewardpoint !== undefined ? createShopItemDto.isPurchasableInRewardpoint : false,
        filePath: createShopItemDto.filePath,
        isActive: createShopItemDto.isActive !== undefined ? createShopItemDto.isActive : true,
        isFeatured: createShopItemDto.isFeatured !== undefined ? createShopItemDto.isFeatured : false,
        promotionId: createShopItemDto.promotionId,
        discountedPrice: createShopItemDto.discountedPrice,
        metadata: createShopItemDto.metadata,
        itemNumber: createShopItemDto.itemNumber
      });
      await queryRunner.manager.save(shopItem);

      this.logger.log(`Shop item saved with ID: ${shopItem.id}`);

      // Commit the transaction
      await queryRunner.commitTransaction();
      this.logger.log('Transaction committed successfully');

      // Fetch the complete shop item with all methods after transaction is committed
      const savedShopItem = await this.shopItemRepository.findOne({
        where: { id: shopItem.id }
      });

      // Handle S3 file upload after shop item creation
      if (shouldUploadAfterCreation && file && savedShopItem) {
        try {
          this.logger.log('Uploading file to S3 after shop item creation');
          // Upload the file with the shop item UUID for S3
          const result = await this.shopFileService.uploadShopItemFile(file, createShopItemDto.itemNumber, {
            categoryId: createShopItemDto.categoryId,
            userId: null, // No user ID for admin uploads
            entityId: savedShopItem.id // Pass the shop item UUID for S3
          });

          uploadedFilePath = result.filePath;
          this.logger.log(`S3 file uploaded successfully: ${uploadedFilePath}`);

          // Update the shop item with the file path
          savedShopItem.filePath = uploadedFilePath;
          await this.shopItemRepository.save(savedShopItem);
          this.logger.log(`Shop item updated with S3 file path: ${uploadedFilePath}`);
        } catch (fileError) {
          this.logger.error(`Error uploading file to S3: ${fileError.message}`, fileError.stack);
          throw new BadRequestException(`Failed to upload file to S3: ${fileError.message}`);
        }
      }

      // Register the file in the registry table if a file was uploaded
      if (uploadedFilePath && savedShopItem) {
        try {
          // Register the file in the registry
          await this.fileRegistryService.registerFile(
            FileEntityType.SHOP_ITEM,
            savedShopItem.id,
            uploadedFilePath,
            file.originalname,
            file.mimetype,
            file.size,
            null // No user ID for admin uploads
          );
          this.logger.log(`File registered in registry for shop item ${savedShopItem.id}`);
        } catch (registryError) {
          this.logger.error(`Error registering file in registry: ${registryError.message}`, registryError.stack);
          // Continue even if registry fails, as the file is already uploaded and the shop item is created
        }
      }

      // Get category name
      const categoryInfo = await this.shopCategoryService.getShopCategoryById(savedShopItem.categoryId);

      // Map to DTO first
      const dto = this.mapShopItemToDto(savedShopItem, categoryInfo.name);

      // Generate a proper file URL for the response if the item has a file path
      if (savedShopItem && savedShopItem.filePath) {
        try {
          // Use the ShopFileService to get the file URL
          const fileUrl = await this.shopFileService.getShopItemFileUrl(savedShopItem.id);

          if (fileUrl) {
            this.logger.log(`Generated file URL for shop item: ${fileUrl}`);
            dto.filePath = fileUrl;
          }
        } catch (error) {
          this.logger.error(`Error generating file URL: ${error.message}`, error.stack);
        }
      }

      return dto;
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();
      this.logger.error(`Transaction rolled back due to error: ${error.message}`);

      if (error instanceof ConflictException || error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error creating shop item with file: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to create shop item with file');
    } finally {
      // Release the query runner
      await queryRunner.release();
      this.logger.log('Query runner released');
    }
  }

  /**
   * Update a shop item with file upload in one step
   * @param id Shop item ID
   * @param file File to upload
   * @param updateShopItemDto Shop item update data
   * @returns Updated shop item
   */
  async updateShopItemWithFile(id: string, file: any, updateShopItemDto: UpdateShopItemDto): Promise<ShopItemResponseDto> {
    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log('Starting updateShopItemWithFile transaction');

      // Check if shop item exists
      const shopItem = await this.shopItemRepository.findOne({
        where: { id }
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${id} not found`);
      }

      // Check if item number is being changed and if new item number already exists
      if (updateShopItemDto.itemNumber && updateShopItemDto.itemNumber !== shopItem.itemNumber) {
        const existingItem = await this.shopItemRepository.findOne({
          where: {
            itemNumber: updateShopItemDto.itemNumber,
            id: Not(id)
          }
        });

        if (existingItem) {
          throw new ConflictException(`Shop item with item number '${updateShopItemDto.itemNumber}' already exists`);
        }
      }

      // If category ID is being changed, check if new category exists
      let categoryName = null;
      if (updateShopItemDto.categoryId && updateShopItemDto.categoryId !== shopItem.categoryId) {
        try {
          const category = await this.shopCategoryService.getShopCategoryById(updateShopItemDto.categoryId);
          categoryName = category.name;
        } catch (error) {
          throw new NotFoundException(`Shop category with ID ${updateShopItemDto.categoryId} not found`);
        }
      } else {
        // Get the current category name
        try {
          const category = await this.shopCategoryService.getShopCategoryById(shopItem.categoryId);
          categoryName = category.name;
        } catch (error) {
          this.logger.error(`Error getting category name: ${error.message}`, error.stack);
        }
      }

      // If promotion ID is being changed and not null, check if new promotion exists
      if (updateShopItemDto.hasOwnProperty('promotionId')) {
        if (updateShopItemDto.promotionId !== null && updateShopItemDto.promotionId !== undefined && updateShopItemDto.promotionId !== '' && updateShopItemDto.promotionId !== shopItem.promotionId) {
          try {
            await this.promotionsService.getPromotionById(updateShopItemDto.promotionId);
          } catch (error) {
            throw new NotFoundException(`Promotion with ID ${updateShopItemDto.promotionId} not found`);
          }
        } else if (updateShopItemDto.promotionId === '' || updateShopItemDto.promotionId === undefined) {
          // Ensure promotionId is explicitly set to null
          updateShopItemDto.promotionId = null;
        }
      }

      // Variable to store the path of the uploaded file
      let uploadedFilePath = null;

      // Process file upload if provided
      if (file) {
        try {
          this.logger.log('Uploading new file for shop item update');
          // Upload the file
          const result = await this.shopFileService.uploadShopItemFile(file, updateShopItemDto.itemNumber || shopItem.itemNumber, {
            categoryId: updateShopItemDto.categoryId || shopItem.categoryId,
            userId: null // No user ID for admin uploads
          });

          uploadedFilePath = result.filePath;
          this.logger.log(`New file uploaded successfully: ${uploadedFilePath}`);

          // Update the file path in the DTO
          updateShopItemDto.filePath = uploadedFilePath;
        } catch (fileError) {
          this.logger.error(`Error uploading file: ${fileError.message}`, fileError.stack);
          throw new BadRequestException(`Failed to upload file: ${fileError.message}`);
        }
      }

      // Create a copy of the update DTO
      const updateData = { ...updateShopItemDto };

      // If changing to free type, ensure price is 0 and isPurchasableInRewardpoint is false
      if (updateData.type === ShopItemType.FREE) {
        updateData.price = 0;
        updateData.isPurchasableInRewardpoint = false;
        updateData.discountedPrice = null;
      } else if (updateData.type === ShopItemType.IN_APP_PURCHASE || !updateData.type) {
        // If type is IN_APP_PURCHASE or not changing type, but price is not provided, keep the original price
        if (updateData.price === undefined || updateData.price === null) {
          updateData.price = shopItem.price;
        }
      }

      // Update shop item
      const updatedShopItem = this.shopItemRepository.create({
        ...shopItem,
        ...updateData
      });

      // Double-check: Ensure free items always have price 0 and isPurchasableInRewardpoint false
      if (updatedShopItem.type === ShopItemType.FREE) {
        updatedShopItem.price = 0;
        updatedShopItem.isPurchasableInRewardpoint = false;
        updatedShopItem.discountedPrice = null;
      }

      // Save updated shop item
      await queryRunner.manager.save(updatedShopItem);

      // Commit the transaction
      await queryRunner.commitTransaction();
      this.logger.log('Transaction committed successfully');

      // Fetch the complete shop item with all methods after transaction is committed
      const savedShopItem = await this.shopItemRepository.findOne({
        where: { id }
      });

      // Register the file in the registry table if a file was uploaded
      if (uploadedFilePath && savedShopItem) {
        try {
          // Register the file in the registry
          await this.fileRegistryService.registerFile(
            FileEntityType.SHOP_ITEM,
            savedShopItem.id,
            uploadedFilePath,
            file.originalname,
            file.mimetype,
            file.size,
            null // No user ID for admin uploads
          );
          this.logger.log(`File registered in registry for shop item ${savedShopItem.id}`);
        } catch (registryError) {
          this.logger.error(`Error registering file in registry: ${registryError.message}`, registryError.stack);
          // Continue even if registry fails, as the file is already uploaded and the shop item is updated
        }
      }

      // Map to DTO first
      const dto = this.mapShopItemToDto(savedShopItem, categoryName);

      // Generate a proper file URL for the response if the item has a file path
      if (savedShopItem && savedShopItem.filePath) {
        try {
          // Use the ShopFileService to get the file URL
          const fileUrl = await this.shopFileService.getShopItemFileUrl(savedShopItem.id);

          if (fileUrl) {
            this.logger.log(`Generated file URL for shop item: ${fileUrl}`);
            dto.filePath = fileUrl;
          }
        } catch (error) {
          this.logger.error(`Error generating file URL: ${error.message}`, error.stack);
        }
      }

      return dto;
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();
      this.logger.error(`Transaction rolled back due to error: ${error.message}`);

      if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error updating shop item with file: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to update shop item');
    } finally {
      // Release the query runner
      await queryRunner.release();
      this.logger.log('Query runner released');
    }
  }

  /**
   * Apply a promotion to multiple shop items
   * @param applyPromotionToItemsDto Promotion application data
   * @returns Success message
   */
  async applyPromotionToItems(applyPromotionToItemsDto: ApplyPromotionToItemsDto): Promise<{ success: boolean; message: string }> {
    try {
      const { promotionId, itemIds } = applyPromotionToItemsDto;

      // Check if promotion exists and get its details
      let promotion: PromotionResponseDto;
      try {
        promotion = await this.promotionsService.getPromotionById(promotionId);
      } catch (error) {
        throw new NotFoundException(`Promotion with ID ${promotionId} not found`);
      }

      // Get all shop items to update
      const shopItems = await this.shopItemRepository.find({
        where: { id: In(itemIds) }
      });

      if (shopItems.length === 0) {
        throw new NotFoundException('No shop items found with the provided IDs');
      }

      // Check if promotion has applicable category restrictions
      const hasApplicableCategoryRestrictions = promotion.applicableCategoryIds && promotion.applicableCategoryIds.length > 0;

      // Filter items that are in applicable categories
      const applicableItems = hasApplicableCategoryRestrictions ? shopItems.filter((item) => promotion.applicableCategoryIds.includes(item.categoryId)) : shopItems;

      // Check if any items are not in applicable categories
      const nonApplicableItems = hasApplicableCategoryRestrictions ? shopItems.filter((item) => !promotion.applicableCategoryIds.includes(item.categoryId)) : [];

      if (hasApplicableCategoryRestrictions && applicableItems.length === 0) {
        throw new BadRequestException('None of the selected items are in categories applicable to this promotion');
      }

      // Apply promotion to applicable shop items
      for (const shopItem of applicableItems) {
        shopItem.promotionId = promotionId;
      }

      // Save updated shop items
      await this.shopItemRepository.save(applicableItems);

      // Prepare response message
      let message = `Promotion applied to ${applicableItems.length} shop items`;
      if (nonApplicableItems.length > 0) {
        message += `. ${nonApplicableItems.length} items were skipped because they are not in applicable categories.`;
      }

      return { success: true, message };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error applying promotion to shop items: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to apply promotion to shop items');
    }
  }

  /**
   * Upload a file for a shop item
   * @param file File to upload
   * @param itemNumber Item number for reference
   * @param categoryId Optional category ID for organizing files
   * @param userId Optional user ID who uploaded the file
   * @returns Path to the uploaded file and registry entry
   */
  async uploadShopItemFile(file: any, itemNumber: string, categoryId?: string, userId?: string): Promise<{ filePath: string; registry: any }> {
    try {
      // Use the FileRegistryService to upload the file
      const result = await this.fileRegistryService.uploadFile(FileEntityType.SHOP_ITEM, file, itemNumber, { categoryId, userId });

      this.logger.log(`Uploaded file for shop item ${itemNumber}: ${result.filePath}`);
      return result;
    } catch (error) {
      this.logger.error(`Error uploading file for shop item ${itemNumber}: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Bulk update prices for shop items
   * @param bulkPriceUpdateDto Bulk price update data
   * @returns Success message
   */
  async bulkUpdatePrices(bulkPriceUpdateDto: BulkPriceUpdateDto): Promise<{ success: boolean; message: string }> {
    try {
      const { items } = bulkPriceUpdateDto;

      // Get all shop items to update
      const itemIds = items.map((item) => item.id);
      const shopItems = await this.shopItemRepository.find({
        where: { id: In(itemIds) }
      });

      if (shopItems.length === 0) {
        throw new NotFoundException('No shop items found with the provided IDs');
      }

      // Update prices
      for (const shopItem of shopItems) {
        const updateData = items.find((item) => item.id === shopItem.id);
        if (updateData) {
          shopItem.price = updateData.price;

          // If discounted price is higher than new price, reset it
          if (shopItem.discountedPrice !== null && shopItem.discountedPrice > shopItem.price) {
            shopItem.discountedPrice = null;
          }
        }
      }

      // Save updated shop items
      await this.shopItemRepository.save(shopItems);

      return { success: true, message: `Prices updated for ${shopItems.length} shop items` };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error updating shop item prices: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to update shop item prices');
    }
  }

  /**
   * Bulk update discounts for shop items
   * @param bulkDiscountUpdateDto Bulk update data
   * @returns Success message
   */
  async bulkUpdateDiscounts(bulkDiscountUpdateDto: BulkDiscountUpdateDto): Promise<{ success: boolean; message: string }> {
    try {
      const { items } = bulkDiscountUpdateDto;

      // Get all shop items to update
      const itemIds = items.map((item) => item.id);
      const shopItems = await this.shopItemRepository.find({
        where: { id: In(itemIds) }
      });

      if (shopItems.length === 0) {
        throw new NotFoundException('No shop items found with the provided IDs');
      }

      // Update discounted prices
      for (const shopItem of shopItems) {
        const updateData = items.find((item) => item.id === shopItem.id);
        if (updateData) {
          // Ensure discounted price is not higher than regular price
          if (updateData.discountedPrice > shopItem.price) {
            throw new BadRequestException(`Discounted price for item ${shopItem.itemNumber} cannot be higher than regular price`);
          }

          shopItem.discountedPrice = updateData.discountedPrice;
        }
      }

      // Save updated shop items
      await this.shopItemRepository.save(shopItems);

      return { success: true, message: `Discounted prices updated for ${shopItems.length} shop items` };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error updating shop item discounts: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to update shop item discounts');
    }
  }

  /**
   * Remove promotion from a shop item
   * @param itemId Shop item ID
   * @returns Success message
   */
  async removePromotionFromItem(itemId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Find the shop item
      const shopItem = await this.shopItemRepository.findOne({
        where: { id: itemId }
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${itemId} not found`);
      }

      // Check if the item has a promotion
      if (!shopItem.promotionId) {
        return { success: false, message: `Shop item ${shopItem.itemNumber} does not have a promotion applied` };
      }

      // Store the promotion ID for the message
      const promotionId = shopItem.promotionId;

      // Remove the promotion
      shopItem.promotionId = null;

      // Save the updated shop item
      await this.shopItemRepository.save(shopItem);

      return {
        success: true,
        message: `Promotion (ID: ${promotionId}) removed from shop item ${shopItem.itemNumber}`
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error removing promotion from shop item: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to remove promotion from shop item: ${error.message}`);
    }
  }

  /**
   * Generate an item number for a shop item
   * @param options Options for generating the item number
   * @returns Generated item number
   */
  async generateItemNumber(options: { categoryId: string }): Promise<string> {
    try {
      const { categoryId } = options;

      // Get the category to determine the prefix
      const category = await this.shopCategoryService.getShopCategoryById(categoryId);

      // Use the first two letters of the category name as the prefix
      let prefix = category.name.substring(0, 2).toUpperCase();

      // Find the highest item number with this prefix
      const items = await this.shopItemRepository.find({
        where: { itemNumber: Like(`${prefix}-%`) },
        order: { itemNumber: 'DESC' }
      });

      let nextNumber = 1;

      if (items.length > 0) {
        // Extract the number part from the highest item number
        const highestItemNumber = items[0].itemNumber;
        const match = highestItemNumber.match(/-(\d+)$/);

        if (match && match[1]) {
          nextNumber = parseInt(match[1], 10) + 1;
        }
      }

      // Format the number with leading zeros (e.g., 001, 010, 100)
      const formattedNumber = nextNumber.toString().padStart(3, '0');
      const itemNumber = `${prefix}-${formattedNumber}`;

      return itemNumber;
    } catch (error) {
      this.logger.error(`Error generating item number: ${error.message}`, error.stack);
      // Return a fallback item number
      const timestamp = Date.now().toString().slice(-6);
      return `ITEM-${timestamp}`;
    }
  }

  /**
   * Map shop item entity to DTO
   * @param shopItem Shop item entity
   * @param categoryName Optional category name
   * @returns Shop item DTO
   */
  private mapShopItemToDto(shopItem: ShopItem, categoryName?: string): ShopItemResponseDto {
    return {
      id: shopItem.id,
      itemNumber: shopItem.itemNumber,
      title: shopItem.title,
      description: shopItem.description,
      categoryId: shopItem.categoryId,
      categoryName: categoryName,
      type: shopItem.type,
      price: Number(shopItem.price),
      isPurchasableInRewardpoint: shopItem.isPurchasableInRewardpoint,
      filePath: shopItem.filePath,
      isActive: shopItem.isActive,
      isFeatured: shopItem.isFeatured,
      shopItemCategory: categoryName?.toLowerCase() || null,
      promotionId: shopItem.promotionId,
      isPromotionActive: shopItem.isPromotionActive,
      discountedPrice: shopItem.discountedPrice ? Number(shopItem.discountedPrice) : null,
      finalPrice: shopItem.getFinalPrice(),
      isOnSale: shopItem.isOnSale(),
      discountPercentage: shopItem.getDiscountPercentage(),
      metadata: shopItem.metadata,
      purchaseCount: shopItem.purchaseCount,
      viewCount: shopItem.viewCount,
      createdAt: shopItem.createdAt,
      updatedAt: shopItem.updatedAt
    };
  }

  /**
   * Check if a category is a skin category
   * @param categoryId Category ID to check
   * @returns True if the category is a skin category, false otherwise
   */
  private async isSkinCategory(categoryId: string): Promise<boolean> {
    try {
      const category = await this.shopCategoryService.getShopCategoryById(categoryId);

      // Check if the category name contains 'skin' (case-insensitive)
      return category.name.toLowerCase().includes('skin');
    } catch (error) {
      this.logger.error(`Error checking if category is a skin category: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Get shop items with promotion details
   * @param searchParams Search parameters for filtering items
   * @param paginationDto Pagination parameters
   * @returns Paged list of shop items with promotion details
   */
  async getShopItemsWithPromotions(searchParams?: ShopItemSearchParams, paginationDto?: PaginationDto): Promise<PagedListDto<ShopItemWithPromotionDto>> {
    try {
      this.logger.log(`getShopItemsWithPromotions called with searchParams: ${JSON.stringify(searchParams)}`);

      // Get shop items using the existing method
      const shopItemsResult = await this.getAllShopItems(searchParams, paginationDto);

      // Filter items to only include those with promotions
      const itemsWithPromotionIds = shopItemsResult.items.filter((item) => item.promotionId);
      this.logger.log(`Found ${itemsWithPromotionIds.length} items with promotions out of ${shopItemsResult.items.length} total items`);

      // Convert to ShopItemWithPromotionDto
      const itemsWithPromotions: ShopItemWithPromotionDto[] = await Promise.all(
        itemsWithPromotionIds.map(async (item) => {
          // Create a new DTO with only the fields we need
          const itemWithPromotion: ShopItemWithPromotionDto = {
            id: item.id,
            itemNumber: item.itemNumber,
            title: item.title,
            categoryId: item.categoryId,
            categoryName: item.categoryName,
            isPromotionActive: item.isPromotionActive,
            price: Number(item.price)
          };

          // Add promotion details
          try {
            const promotion = await this.promotionsService.getPromotionById(item.promotionId);

            // Add promotion name
            itemWithPromotion.promotionName = promotion.name;

            // Create human-readable discount display
            if (promotion.discountType === DiscountType.PERCENTAGE) {
              itemWithPromotion.discountDisplay = `${promotion.discountValue}%`;
            } else if (promotion.discountType === DiscountType.FIXED_AMOUNT) {
              itemWithPromotion.discountDisplay = `$${promotion.discountValue.toFixed(2)}`;
            }
          } catch (error) {
            this.logger.error(`Error getting promotion details for item ${item.id}: ${error.message}`, error.stack);
          }

          return itemWithPromotion;
        })
      );

      return new PagedListDto(itemsWithPromotions, itemsWithPromotions.length);
    } catch (error) {
      this.logger.error(`Error getting shop items with promotions: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get shop items with promotions: ${error.message}`);
    }
  }

  /**
   * Get shop items by category
   * @param categoryId Category ID to filter by
   * @param includeInactive Whether to include inactive items
   * @param itemNumber Optional item number to filter by
   * @param title Optional title to filter by
   * @param promotionId Optional promotion ID to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of shop items
   */
  async getShopItemsByCategory(
    categoryId: string,
    includeInactive: boolean = false,
    itemNumber?: string,
    title?: string,
    promotionId?: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<ShopItemResponseDto>> {
    try {
      // Get shop items from repository with filtering
      const queryBuilder = this.shopItemRepository.createQueryBuilder('shopItem')
        .leftJoinAndSelect('shopItem.category', 'category')
        .where('shopItem.categoryId = :categoryId', { categoryId });

      // Add optional filters
      if (!includeInactive) {
        queryBuilder.andWhere('shopItem.isActive = :isActive', { isActive: true });
      }

      if (itemNumber) {
        queryBuilder.andWhere('shopItem.itemNumber = :itemNumber', { itemNumber });
      }

      if (title) {
        queryBuilder.andWhere('shopItem.title LIKE :title', { title: `%${title}%` });
      }

      if (promotionId) {
        queryBuilder.andWhere('shopItem.promotionId = :promotionId', { promotionId });
      }

      const totalCount = await queryBuilder.getCount();

      // Apply pagination if provided
      if (paginationDto) {
        const { page = 1, limit = 10, sortBy, sortDirection = 'DESC' } = paginationDto;
        const skip = (page - 1) * limit;

        queryBuilder.skip(skip).take(limit);

        if (sortBy) {
          if (sortBy === 'price') {
            queryBuilder.orderBy('COALESCE(shopItem.discountedPrice, shopItem.price)', sortDirection);
          } else {
            queryBuilder.orderBy(`shopItem.${sortBy}`, sortDirection);
          }
        } else {
          queryBuilder.orderBy('shopItem.createdAt', 'DESC');
        }
      } else {
        queryBuilder.orderBy('shopItem.createdAt', 'DESC');
      }

      // Execute the query
      const shopItems = await queryBuilder.getMany();

      // Map items to DTOs
      const dtos = shopItems.map((item) => this.mapShopItemToDto(item, item.category?.name));

      // Generate file URLs for all items with file paths
      await Promise.all(
        dtos.map(async (dto, index) => {
          const item = shopItems[index];
          if (item && item.filePath) {
            try {
              const fileUrl = await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.SHOP_ITEM, item.id);

              if (fileUrl) {
                dto.filePath = fileUrl;
              }
            } catch (error) {
              this.logger.error(`Error generating file URL for shop item ${item.id}: ${error.message}`, error.stack);
            }
          }
        })
      );

      return new PagedListDto(dtos, totalCount);

    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting shop items by category: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get shop items by category: ${error.message}`);
    }
  }
}
