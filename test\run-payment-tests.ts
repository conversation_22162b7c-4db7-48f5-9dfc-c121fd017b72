#!/usr/bin/env ts-node

/**
 * Payment Integration Test Runner
 *
 * This script runs comprehensive tests for the KCP payment gateway integration
 * including unit tests, integration tests, and end-to-end tests.
 */

import { execSync } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';

interface TestSuite {
  name: string;
  description: string;
  command: string;
  files: string[];
}

const testSuites: TestSuite[] = [
  {
    name: 'Unit Tests',
    description: 'Tests individual components in isolation',
    command: 'npm run test',
    files: [
      'src/modules/payment/services/kcp.service.spec.ts',
      'src/modules/payment/services/payment.service.spec.ts',
      'src/modules/payment/services/kcp-config.service.spec.ts',
      'src/modules/payment/payment.controller.spec.ts',
    ],
  },
  {
    name: 'Integration Tests',
    description: 'Tests component interactions and database operations',
    command: 'npm run test:e2e',
    files: [
      'test/payment-integration.e2e-spec.ts',
    ],
  },
];

async function runTestSuite(suite: TestSuite): Promise<boolean> {
  console.log(`\n🧪 Running ${suite.name}`);
  console.log(`📝 ${suite.description}`);
  console.log('─'.repeat(60));

  // Check if test files exist
  const missingFiles = suite.files.filter(file => !fs.existsSync(file));
  if (missingFiles.length > 0) {
    console.log(`❌ Missing test files:`);
    missingFiles.forEach(file => console.log(`   - ${file}`));
    return false;
  }

  try {
    // Run tests for specific files
    const testPattern = suite.files.join('|');
    const command = `${suite.command} --testPathPattern="${testPattern}"`;

    console.log(`🚀 Executing: ${command}`);
    execSync(command, {
      stdio: 'inherit',
      cwd: process.cwd(),
      env: { ...process.env, NODE_ENV: 'test' }
    });

    console.log(`✅ ${suite.name} completed successfully`);
    return true;
  } catch (error) {
    console.log(`❌ ${suite.name} failed`);
    console.error(error.message);
    return false;
  }
}

async function checkPrerequisites(): Promise<boolean> {
  console.log('🔍 Checking prerequisites...');

  // Check if .env.test exists
  if (!fs.existsSync('.env.test')) {
    console.log('❌ .env.test file not found');
    console.log('   Please create .env.test with test configuration');
    return false;
  }

  // Check if test utilities exist
  const requiredFiles = [
    'test/utils/test-database.config.ts',
    'test/utils/test-helpers.ts',
  ];

  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  if (missingFiles.length > 0) {
    console.log('❌ Missing required test utility files:');
    missingFiles.forEach(file => console.log(`   - ${file}`));
    return false;
  }

  console.log('✅ All prerequisites met');
  return true;
}

async function generateTestReport(results: { suite: TestSuite; success: boolean }[]): Promise<void> {
  console.log('\n📊 Test Report');
  console.log('═'.repeat(60));

  const totalSuites = results.length;
  const passedSuites = results.filter(r => r.success).length;
  const failedSuites = totalSuites - passedSuites;

  results.forEach(({ suite, success }) => {
    const status = success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${suite.name}`);
  });

  console.log('─'.repeat(60));
  console.log(`Total: ${totalSuites} | Passed: ${passedSuites} | Failed: ${failedSuites}`);

  if (failedSuites > 0) {
    console.log('\n⚠️  Some tests failed. Please check the output above for details.');
    process.exit(1);
  } else {
    console.log('\n🎉 All payment integration tests passed!');
    console.log('\n📋 Test Coverage Summary:');
    console.log('   ✅ KCP Service - Payment initiation, trade registration, webhook validation');
    console.log('   ✅ Payment Service - Transaction management, database operations');
    console.log('   ✅ KCP Config Service - Configuration management, security');
    console.log('   ✅ Payment Controller - API endpoints, authentication, validation');
    console.log('   ✅ Integration Tests - End-to-end payment flows, error handling');
  }
}

async function main(): Promise<void> {
  console.log('🚀 KCP Payment Gateway Integration Test Suite');
  console.log('═'.repeat(60));

  // Check prerequisites
  const prerequisitesMet = await checkPrerequisites();
  if (!prerequisitesMet) {
    process.exit(1);
  }

  // Run test suites
  const results: { suite: TestSuite; success: boolean }[] = [];

  for (const suite of testSuites) {
    const success = await runTestSuite(suite);
    results.push({ suite, success });
  }

  // Generate report
  await generateTestReport(results);
}

// Run the test suite
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

export { runTestSuite, checkPrerequisites, generateTestReport };
