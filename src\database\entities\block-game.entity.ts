import { <PERSON><PERSON><PERSON>, Column, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { BlockGameSentence } from './block-game-sentence.entity';
import { BlockGameAttempt } from './block-game-attempt.entity';

@Entity()
export class BlockGame extends AuditableBaseEntity {
  @Column()
  title: string;

  @Column()
  score: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  // Relationships
  @OneToMany(() => BlockGameSentence, (sentence) => sentence.blockGame, { cascade: true })
  sentences: BlockGameSentence[];

  @OneToMany(() => BlockGameAttempt, (attempt) => attempt.blockGame)
  attempts: BlockGameAttempt[];
}
