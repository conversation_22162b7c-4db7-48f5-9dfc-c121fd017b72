import { UserType } from '../../../database/entities/user.entity';

export interface JwtPayload {
  id: string;       // User's database ID (for backward compatibility)
  username: string;  // User's userId
  sub: string;       // User's database ID
  name: string;      // User's name
  type: UserType;    // User's type (admin, tutor, student)
  selectedRole?: UserType; // Role selected during login
  roles: string[];   // Array of role names
  defaultSkinId?: string; //  Default skin ID for the user
  activePlan?: string; // Active subscription plan name
  planType?: string; // Type of the active plan
  planId?: string;   // ID of the active plan
  planExpiryDate?: string; // Expiry date of the active plan
  planActive?: boolean; // Whether the plan is active (not expired)
  iat?: number;      // Issued at timestamp (added by JWT)
  exp?: number;      // Expiration timestamp (added by JWT)
}
