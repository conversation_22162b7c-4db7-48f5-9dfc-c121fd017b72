import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveShopItemCategoryAndUpdateMetadata1746400000000 implements MigrationInterface {
    name = 'RemoveShopItemCategoryAndUpdateMetadata1746400000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // First, add a temporary column to store the converted metadata
        await queryRunner.query(`ALTER TABLE "shop_item" ADD COLUMN "metadata_text" text`);

        // Get all items with metadata
        const items = await queryRunner.query(`SELECT id, metadata FROM "shop_item" WHERE metadata IS NOT NULL`);

        // Convert JSON metadata to string tags for all existing items
        for (const item of items) {
            try {
                if (item.metadata) {
                    let tags = '';

                    // Parse the metadata if it's a string, otherwise use it directly
                    let metadata: any;
                    try {
                        metadata = typeof item.metadata === 'string'
                            ? JSON.parse(item.metadata)
                            : item.metadata;
                    } catch (parseError) {
                        console.error(`Error parsing metadata for item ${item.id}:`, parseError);
                        continue;
                    }

                    // Extract tags from metadata if they exist
                    if (metadata && metadata.tags && Array.isArray(metadata.tags)) {
                        tags = metadata.tags.join(',');
                    }

                    // Update the temporary column with the comma-separated tags
                    await queryRunner.query(
                        `UPDATE "shop_item" SET "metadata_text" = $1 WHERE "id" = $2`,
                        [tags, item.id]
                    );
                }
            } catch (error) {
                console.error(`Error converting metadata for item ${item.id}:`, error);
            }
        }

        // Drop the original metadata column
        await queryRunner.query(`ALTER TABLE "shop_item" DROP COLUMN "metadata"`);

        // Rename the temporary column to metadata
        await queryRunner.query(`ALTER TABLE "shop_item" RENAME COLUMN "metadata_text" TO "metadata"`);

        // Drop the shop_item_category column
        // await queryRunner.query(`ALTER TABLE "shop_item" DROP COLUMN "shop_item_category"`);

        // // Drop the enum type
        // await queryRunner.query(`DROP TYPE "public"."shop_item_category_enum"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Recreate the enum type
        await queryRunner.query(`CREATE TYPE "public"."shop_item_category_enum" AS ENUM('skin', 'emoticon')`);

        // Add the column back with default value 'skin'
        await queryRunner.query(`ALTER TABLE "shop_item" ADD "shop_item_category" "public"."shop_item_category_enum" NOT NULL DEFAULT 'skin'`);

        // Add a temporary JSON column
        await queryRunner.query(`ALTER TABLE "shop_item" ADD COLUMN "metadata_json" jsonb`);

        // Get all items with metadata
        const items = await queryRunner.query(`SELECT id, metadata FROM "shop_item" WHERE metadata IS NOT NULL`);

        // Convert metadata text back to JSON
        for (const item of items) {
            try {
                if (item.metadata) {
                    const tags = item.metadata.split(',').map((tag: string) => tag.trim());
                    const jsonMetadata = JSON.stringify({ tags });

                    // Update the temporary column with the JSON object
                    await queryRunner.query(
                        `UPDATE "shop_item" SET "metadata_json" = $1::jsonb WHERE "id" = $2`,
                        [jsonMetadata, item.id]
                    );
                }
            } catch (error) {
                console.error(`Error converting metadata back to JSON for item ${item.id}:`, error);
            }
        }

        // Drop the original metadata column
        await queryRunner.query(`ALTER TABLE "shop_item" DROP COLUMN "metadata"`);

        // Rename the temporary column to metadata
        await queryRunner.query(`ALTER TABLE "shop_item" RENAME COLUMN "metadata_json" TO "metadata"`);
    }
}
