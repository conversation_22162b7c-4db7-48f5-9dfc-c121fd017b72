import { MigrationInterface, QueryRunner } from "typeorm";

export class QAAssignmentEnum1746681237043 implements MigrationInterface {
    name = 'QAAssignmentEnum1746681237043'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."qa_assignment_status_enum" RENAME TO "qa_assignment_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."qa_assignment_status_enum" AS ENUM('assigned', 'draft', 'in_progress', 'completed', 'expired')`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" ALTER COLUMN "status" TYPE "public"."qa_assignment_status_enum" USING "status"::"text"::"public"."qa_assignment_status_enum"`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" ALTER COLUMN "status" SET DEFAULT 'assigned'`);
        await queryRunner.query(`DROP TYPE "public"."qa_assignment_status_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."qa_assignment_status_enum_old" AS ENUM('pending', 'in_progress', 'completed', 'expired')`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" ALTER COLUMN "status" TYPE "public"."qa_assignment_status_enum_old" USING "status"::"text"::"public"."qa_assignment_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" ALTER COLUMN "status" SET DEFAULT 'pending'`);
        await queryRunner.query(`DROP TYPE "public"."qa_assignment_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."qa_assignment_status_enum_old" RENAME TO "qa_assignment_status_enum"`);
    }
}
