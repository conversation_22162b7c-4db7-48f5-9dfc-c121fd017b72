import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsOptional, IsUUID, IsEnum, IsDate, Min, IsBoolean, IsObject, IsArray } from 'class-validator';
import { QASubmissionStatus } from '../entities/qa-submission.entity';
import { QAAssignmentStatus } from '../entities/qa-assignment.entity';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { QAMissionFrequency } from '../entities/qa-mission-goal.entity';
import { UserType } from '../entities/user.entity';

export class CreateQAQuestionDto {
  @ApiProperty({
    example: 'What are the key principles of Object-Oriented Programming?',
    description: 'The question text'
  })
  @IsNotEmpty()
  @IsString()
  question: string;

  @ApiProperty({
    example: 10,
    description: 'Points allocated for this question'
  })
  @IsNumber()
  @Min(1)
  points: number;

  @ApiProperty({
    example: 100,
    description: 'Minimum words required for the answer'
  })
  @IsNumber()
  @Min(1)
  minimumWords: number;

  @ApiProperty({
    example: true,
    description: 'Whether the question is active'
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;
}

//tutor mission list
export class SubmissionHistoryDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  content: string;

  @ApiProperty()
  wordCount: number;

  @ApiProperty()
  submissionDate: Date;
}

export class TaskInfoDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  description: string;
}

export class QASubmissionWithDetailsDto extends PaginationDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  status: string;

  @ApiProperty()
  createdBy: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty({ type: () => TaskInfoDto })
  task: TaskInfoDto;

  @ApiProperty({ type: () => [SubmissionHistoryDto] })
  submissionHistory: SubmissionHistoryDto[];
}

//tutor mission list

export class CreateQAAssignmentDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'UUID of the question'
  })
  @IsNotEmpty()
  @IsUUID()
  questionId: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'UUID of the student'
  })
  @IsNotEmpty()
  @IsUUID()
  studentId: string;

  @ApiProperty({
    example: 10,
    description: 'Points allocated for this assignment'
  })
  @IsNumber()
  @Min(1)
  points: number;

  @ApiProperty({
    example: '2023-12-31T23:59:59Z',
    description: 'Deadline for the assignment'
  })
  @IsDate()
  deadline: Date;

  @ApiProperty({
    example: 'Please focus on SOLID principles in your answer',
    description: 'Additional instructions for the assignment'
  })
  @IsOptional()
  @IsString()
  instructions?: string;
}

export class CreateQAAssignmentItemsDto {
  @ApiProperty({
    type: [String], 
    example: '123e4567-e89b-12d3-a456-************',
    description: 'UUID of the question'
  })
  @IsNotEmpty()
  //@IsUUID()
  @IsArray()
  @IsUUID('4', { each: true })
  questionIds: string[];

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'UUID of the student'
  })
  @IsNotEmpty()
  @IsUUID()
  studentId: string;

  @ApiProperty({
    example: 'Please focus on SOLID principles in your answer',
    description: 'Additional instructions for the assignment'
  })
  @IsOptional()
  @IsString()
  instructions?: string;
}

//QAAssignmentResponseDto
export class QAAssignmentResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  questionId: string;

  @ApiProperty()
  studentId: string;

  @ApiProperty()
  points: number;

  @ApiProperty()
  deadline: Date;

  @ApiProperty()
  instructions: string;

  @ApiProperty({ enum: QAAssignmentStatus })
  status: QAAssignmentStatus;

  @ApiProperty()
  assignedDate: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class QAAssignmentItemDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  questionId: string;

  @ApiProperty()
  studentId: string;

  @ApiProperty()
  points: number;

  @ApiProperty()
  deadline: Date;

  @ApiProperty()
  instructions: string;

  @ApiProperty({ enum: QAAssignmentStatus })
  status: QAAssignmentStatus;

  @ApiProperty()
  assignedDate: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ type: () => QuestionDto }) // if needed
  question?: any;

  @ApiProperty({ type: () => QASubmissionDto, required: false })
  submission?: any;

  // @ApiProperty({ type: QuestionDto })
  // question?: QuestionDto;

  // @ApiProperty({ type: SubmissionDto, required: false })
  // submission?: SubmissionDto;
}

//QAAssignmentSetResponseDto
export class QuestionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  content: string;
}

export class QASubmissionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  answer: string;

  @ApiProperty({ enum: QASubmissionStatus })
  status: QASubmissionStatus;

  @ApiProperty({ required: false })
  submissionDate: Date;

  @ApiProperty({ required: false })
  feedback: string;

  @ApiProperty({ required: false })
  corrections: any;

  @ApiProperty({ required: false })
  reviewedAt: Date;

  @ApiProperty({ required: false })
  reviewedBy: string;
}

export class QAAssignSubmissionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  answer: string;

  @ApiProperty({ enum: QASubmissionStatus })
  status: QASubmissionStatus;

  @ApiProperty({ required: false })
  submissionDate: Date;

  @ApiProperty()
  score: number;

  @ApiProperty({ required: false })
  feedback: string;

  @ApiProperty({ required: false })
  corrections: any;

  @ApiProperty()
  setSequence: number;

  @ApiProperty({ required: false })
  reviewedAt: Date;

  @ApiProperty({ required: false })
  reviewedBy: string;
}

export class QAAssignmentSetResponseDto {
  @ApiProperty()
  setSequence: number;

  @ApiProperty()
  instructions: string;

  @ApiProperty()
  studentId: string;

  @ApiProperty({ type: [QAAssignmentItemDto] })
  assignments: QAAssignmentItemDto[];

  @ApiProperty({ type: [QAAssignSubmissionDto] })
  submissions: QAAssignSubmissionDto[];
}

export class QAAssignmentItemsResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  questionIds: string[];

  @ApiProperty()
  studentId: string;

  @ApiProperty()
  points: number;

  @ApiProperty()
  deadline: Date;

  @ApiProperty({ enum: QAAssignmentStatus })
  status: QAAssignmentStatus;

  @ApiProperty()
  assignedDate: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class CreateQASubmissionDto {
  @ApiProperty({
    example: '1',
    description: 'Set number of the assignment'
  })
  @IsNotEmpty()
  setSequence: number;

  @ApiProperty({
    example: 'SOLID principles include...',
    description: 'The answer text'
  })
  @IsNotEmpty()
  @IsString()
  answer: string;
}

export class QAQuestionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  question: string;

  @ApiProperty()
  points: number;

  @ApiProperty()
  minimumWords: number;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  createdBy: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class QAUserAssignmentResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  questionId: string;

  @ApiProperty()
  studentId: string;

  @ApiProperty()
  points: number;

  @ApiProperty()
  deadline: Date;

  @ApiProperty()
  instructions: string;

  @ApiProperty({ enum: QAAssignmentStatus })
  status: QAAssignmentStatus;

  @ApiProperty()
  assignedDate: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ type: () => QAQuestionResponseDto }) 
  question: QAQuestionResponseDto;

  @ApiProperty({ type: QASubmissionDto, required: false })
  submission?: QASubmissionDto;
}

export class QASubmissionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  answer: string;

  @ApiProperty()
  score: number;

  @ApiProperty({ enum: QASubmissionStatus })
  status: QASubmissionStatus;

  @ApiProperty()
  submissionDate: Date;

  @ApiProperty({ required: false })
  feedback?: string;

  @ApiProperty({ required: false })
  corrections?: any;

  @ApiProperty({ required: false })
  reviewedAt?: Date;

  @ApiProperty({ required: false })
  reviewedBy?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class QAQuestionPaginationDto extends PaginationDto {
  @ApiProperty({
    description: 'Mission frequency',
    enum: QAMissionFrequency,
    required: false
  })
  @IsEnum(QAMissionFrequency)
  @IsOptional()
  timeFrequency?: QAMissionFrequency;
}

export class QAAssignmentPaginationDto extends PaginationDto {
  @ApiProperty({
    description: 'Assignment Status',
    enum: QAAssignmentStatus,
    required: false
  })
  @IsEnum(QAAssignmentStatus)
  @IsOptional()
  status?: QAAssignmentStatus;
}

export class UpdateQAQuestionDto {
  @ApiProperty({
    example: 'What are the key principles of Object-Oriented Programming?',
    description: 'The question text',
    required: false
  })
  @IsOptional()
  @IsString()
  question?: string;

  @ApiProperty({
    example: 10,
    description: 'Points allocated for this question',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  points?: number;

  @ApiProperty({
    example: 100,
    description: 'Minimum words required for the answer',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minimumWords?: number;

  @ApiProperty({
    example: true,
    description: 'Whether the question is active',
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class ReviewSubmissionDto {
  @ApiProperty({
    example: 'Good work, but needs improvement in...',
    description: 'Feedback for the submission'
  })
  @IsNotEmpty()
  @IsString()
  feedback: string;

  @ApiProperty()
  score: number;

  @ApiProperty({
    example: { grammar: ['Error 1', 'Error 2'] },
    description: 'Detailed corrections for the submission'
  })
  @IsOptional()
  @IsObject()
  corrections?: any;
}

/**
 * DTO for creating a new QA permission
 * This allows admins to grant Q&A management permissions to tutors
 */
export class CreateQAPermissionDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'UUID of the tutor to grant permissions to'
  })
  @IsNotEmpty()
  @IsUUID()
  tutorId: string;

  @ApiProperty({
    example: 'Assigned to handle English grammar Q&A',
    description: 'Notes about the permission assignment',
    required: false
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

/**
 * DTO for updating a QA permission
 */
export class UpdateQAPermissionDto {
  @ApiProperty({
    example: true,
    description: 'Whether the permission is active'
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    example: 'Updated to handle all Q&A categories',
    description: 'Notes about the permission assignment',
    required: false
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

/**
 * DTO for QA permission response
 */
export class QAPermissionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  tutorId: string;

  @ApiProperty()
  tutorName: string;

  @ApiProperty()
  tutorEmail: string;

  @ApiProperty()
  grantedBy: string;

  @ApiProperty()
  grantedByName: string;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty({ required: false })
  notes?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

/**
 * DTO for QA permission pagination
 */
export class QAPermissionPaginationDto extends PaginationDto {
  @ApiProperty({
    description: 'Filter by active status',
    required: false
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({
    description: 'Search term for tutor name or email',
    required: false
  })
  @IsOptional()
  @IsString()
  searchTerm?: string;
}
