# Next.js Chat System Implementation Guide

This guide provides a comprehensive implementation of the HEC Backend chat system events in a Next.js project using Socket.io client.

## Table of Contents

1. [Overview](#overview)
2. [Installation & Setup](#installation--setup)
3. [Socket Connection Management](#socket-connection-management)
4. [Event Handlers](#event-handlers)
5. [React Hooks](#react-hooks)
6. [Components](#components)
7. [TypeScript Interfaces](#typescript-interfaces)
8. [Error Handling](#error-handling)
9. [Best Practices](#best-practices)

## Overview

The HEC Backend chat system uses Socket.io with the following key features:
- JWT-based authentication
- Real-time messaging
- Typing indicators
- Message status tracking (sent, delivered, read)
- User presence (online/offline)
- File attachments
- Conversation management

### WebSocket Endpoints
- **Chat Namespace**: `/chat`
- **Server URL**: `http://**************:3010`
- **Authentication**: JWT token via `auth.token` or query parameter

## Installation & Setup

```bash
npm install socket.io-client
npm install @types/socket.io-client # For TypeScript
```

## Socket Connection Management

### 1. Socket Service (`lib/socket.ts`)

```typescript
import { io, Socket } from 'socket.io-client';

class SocketService {
  private socket: Socket | null = null;
  private token: string | null = null;

  connect(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.token = token;

      this.socket = io('http://**************:3010/chat', {
        auth: { token },
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        timeout: 20000,
      });

      this.socket.on('connect', () => {
        console.log('Connected to chat server');
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        console.error('Connection error:', error);
        reject(error);
      });

      this.socket.on('connected', (data) => {
        console.log('Chat connection confirmed:', data);
      });

      // Handle authentication errors
      this.socket.on('auth_error', (error) => {
        console.error('Authentication error:', error);
        this.disconnect();
        reject(new Error(error.message));
      });
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Authenticate if connection failed initially
  authenticate(token: string): void {
    if (this.socket) {
      this.socket.emit('authenticate', { token });
    }
  }
}

export const socketService = new SocketService();
```

### 2. Socket Context (`contexts/SocketContext.tsx`)

```typescript
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { socketService } from '@/lib/socket';
import { Socket } from 'socket.io-client';

interface SocketContextType {
  socket: Socket | null;
  isConnected: boolean;
  connect: (token: string) => Promise<void>;
  disconnect: () => void;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

interface SocketProviderProps {
  children: ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  const connect = async (token: string) => {
    try {
      await socketService.connect(token);
      const socketInstance = socketService.getSocket();
      setSocket(socketInstance);
      setIsConnected(true);
    } catch (error) {
      console.error('Failed to connect to chat:', error);
      throw error;
    }
  };

  const disconnect = () => {
    socketService.disconnect();
    setSocket(null);
    setIsConnected(false);
  };

  useEffect(() => {
    const socketInstance = socketService.getSocket();
    if (socketInstance) {
      const handleConnect = () => setIsConnected(true);
      const handleDisconnect = () => setIsConnected(false);

      socketInstance.on('connect', handleConnect);
      socketInstance.on('disconnect', handleDisconnect);

      return () => {
        socketInstance.off('connect', handleConnect);
        socketInstance.off('disconnect', handleDisconnect);
      };
    }
  }, [socket]);

  return (
    <SocketContext.Provider value={{ socket, isConnected, connect, disconnect }}>
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = (): SocketContextType => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
```

## Event Handlers

### 3. Chat Hook (`hooks/useChat.ts`)

```typescript
import { useEffect, useState, useCallback } from 'react';
import { useSocket } from '@/contexts/SocketContext';
import { MessageDto, CreateMessageDto, ConversationDto } from '@/types/chat';

export const useChat = () => {
  const { socket, isConnected } = useSocket();
  const [messages, setMessages] = useState<MessageDto[]>([]);
  const [conversations, setConversations] = useState<ConversationDto[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [typingUsers, setTypingUsers] = useState<Record<string, string>>({});
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());

  // Subscribe to conversation
  const subscribeToConversation = useCallback((conversationId: string) => {
    if (socket && isConnected) {
      socket.emit('subscribe_conversation', { conversationId });
      setCurrentConversationId(conversationId);
    }
  }, [socket, isConnected]);

  // Unsubscribe from conversation
  const unsubscribeFromConversation = useCallback((conversationId: string) => {
    if (socket && isConnected) {
      socket.emit('unsubscribe_conversation', { conversationId });
      if (currentConversationId === conversationId) {
        setCurrentConversationId(null);
      }
    }
  }, [socket, isConnected, currentConversationId]);

  // Send message
  const sendMessage = useCallback((messageData: CreateMessageDto) => {
    if (socket && isConnected) {
      socket.emit('send_message', messageData);
    }
  }, [socket, isConnected]);

  // Mark messages as read
  const markAsRead = useCallback((conversationId: string) => {
    if (socket && isConnected) {
      socket.emit('mark_read', { conversationId });
    }
  }, [socket, isConnected]);

  // Send typing indicator
  const sendTyping = useCallback((conversationId: string, isTyping: boolean) => {
    if (socket && isConnected) {
      socket.emit('typing', { conversationId, isTyping });
    }
  }, [socket, isConnected]);

  useEffect(() => {
    if (!socket) return;

    // Message events
    const handleNewMessage = (message: MessageDto) => {
      setMessages(prev => [...prev, message]);
    };

    const handleMessagesDelivered = (data: { conversationId: string; userId: string }) => {
      setMessages(prev =>
        prev.map(msg =>
          msg.conversationId === data.conversationId && msg.status === 'sent'
            ? { ...msg, status: 'delivered' as const }
            : msg
        )
      );
    };

    const handleMessagesRead = (data: { conversationId: string; userId: string }) => {
      setMessages(prev =>
        prev.map(msg =>
          msg.conversationId === data.conversationId &&
          (msg.status === 'sent' || msg.status === 'delivered')
            ? { ...msg, status: 'read' as const }
            : msg
        )
      );
    };

    // Typing events
    const handleTyping = (data: { conversationId: string; userId: string; userName: string; isTyping: boolean }) => {
      setTypingUsers(prev => {
        const key = `${data.conversationId}:${data.userId}`;
        if (data.isTyping) {
          return { ...prev, [key]: data.userName };
        } else {
          const { [key]: _, ...rest } = prev;
          return rest;
        }
      });
    };

    // User status events
    const handleUserStatus = (data: { userId: string; isOnline: boolean }) => {
      setOnlineUsers(prev => {
        const newSet = new Set(prev);
        if (data.isOnline) {
          newSet.add(data.userId);
        } else {
          newSet.delete(data.userId);
        }
        return newSet;
      });
    };

    // Subscription events
    const handleSubscribedConversation = (data: { conversationId: string }) => {
      console.log('Subscribed to conversation:', data.conversationId);
    };

    const handleUnsubscribedConversation = (data: { conversationId: string }) => {
      console.log('Unsubscribed from conversation:', data.conversationId);
    };

    // Error handling
    const handleError = (error: { message: string }) => {
      console.error('Chat error:', error.message);
    };

    // Register event listeners
    socket.on('new_message', handleNewMessage);
    socket.on('messages_delivered', handleMessagesDelivered);
    socket.on('messages_read', handleMessagesRead);
    socket.on('typing', handleTyping);
    socket.on('user_status', handleUserStatus);
    socket.on('subscribed_conversation', handleSubscribedConversation);
    socket.on('unsubscribed_conversation', handleUnsubscribedConversation);
    socket.on('error', handleError);

    // Cleanup
    return () => {
      socket.off('new_message', handleNewMessage);
      socket.off('messages_delivered', handleMessagesDelivered);
      socket.off('messages_read', handleMessagesRead);
      socket.off('typing', handleTyping);
      socket.off('user_status', handleUserStatus);
      socket.off('subscribed_conversation', handleSubscribedConversation);
      socket.off('unsubscribed_conversation', handleUnsubscribedConversation);
      socket.off('error', handleError);
    };
  }, [socket]);

  return {
    messages,
    conversations,
    currentConversationId,
    typingUsers,
    onlineUsers,
    subscribeToConversation,
    unsubscribeFromConversation,
    sendMessage,
    markAsRead,
    sendTyping,
    isConnected
  };
};
```

## TypeScript Interfaces

### 4. Chat Types (`types/chat.ts`)

```typescript
export interface MessageDto {
  id: string;
  conversationId: string;
  senderId: string;
  recipientId: string;
  senderName: string;
  senderProfilePicture?: string;
  type: 'text' | 'file' | 'image';
  content: string;
  metadata?: {
    fileId?: string;
    fileName?: string;
    fileSize?: number;
    mimeType?: string;
    url?: string;
    width?: number;
    height?: number;
  };
  status: 'sent' | 'delivered' | 'read';
  createdAt: string;
  updatedAt: string;
  attachments?: {
    id: string;
    fileName: string;
    fileSize: number;
    mimeType: string;
    url: string;
  }[];
}

export interface CreateMessageDto {
  recipientId: string;
  type?: 'text' | 'file' | 'image';
  content: string;
  metadata?: any;
  attachmentIds?: string[];
}

export interface ConversationDto {
  id: string;
  type: 'direct' | 'group';
  status: 'active' | 'archived' | 'deleted';
  participants: ConversationParticipantDto[];
  lastMessage?: MessageDto;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface ConversationParticipantDto {
  id: string;
  name: string;
  email: string;
  profilePicture?: string;
  type: 'admin' | 'tutor' | 'student';
  isOnline?: boolean;
  lastSeen?: string;
}

export interface TypingIndicator {
  conversationId: string;
  userId: string;
  userName: string;
  isTyping: boolean;
}

export interface UserStatus {
  userId: string;
  isOnline: boolean;
  timestamp: Date;
}
```

## Components

### 5. Chat Container (`components/Chat/ChatContainer.tsx`)

```typescript
import React, { useEffect, useState } from 'react';
import { useChat } from '@/hooks/useChat';
import { useSocket } from '@/contexts/SocketContext';
import { ConversationList } from './ConversationList';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { TypingIndicator } from './TypingIndicator';

interface ChatContainerProps {
  token: string;
}

export const ChatContainer: React.FC<ChatContainerProps> = ({ token }) => {
  const { connect, isConnected } = useSocket();
  const {
    messages,
    conversations,
    currentConversationId,
    typingUsers,
    onlineUsers,
    subscribeToConversation,
    sendMessage,
    markAsRead,
    sendTyping
  } = useChat();

  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [isTyping, setIsTyping] = useState(false);

  useEffect(() => {
    if (token && !isConnected) {
      connect(token).catch(console.error);
    }
  }, [token, isConnected, connect]);

  const handleConversationSelect = (conversationId: string) => {
    if (selectedConversation) {
      // Unsubscribe from previous conversation
      // Note: Backend handles this automatically when subscribing to new conversation
    }

    setSelectedConversation(conversationId);
    subscribeToConversation(conversationId);
    markAsRead(conversationId);
  };

  const handleSendMessage = (content: string, attachmentIds?: string[]) => {
    if (!selectedConversation) return;

    const conversation = conversations.find(c => c.id === selectedConversation);
    if (!conversation) return;

    const recipientId = conversation.participants.find(p => p.id !== 'current-user-id')?.id;
    if (!recipientId) return;

    sendMessage({
      recipientId,
      content,
      type: 'text',
      attachmentIds
    });
  };

  const handleTyping = (typing: boolean) => {
    if (selectedConversation && typing !== isTyping) {
      setIsTyping(typing);
      sendTyping(selectedConversation, typing);
    }
  };

  const currentConversationMessages = messages.filter(
    m => m.conversationId === selectedConversation
  );

  const currentTypingUsers = Object.entries(typingUsers)
    .filter(([key]) => key.startsWith(`${selectedConversation}:`))
    .map(([, userName]) => userName);

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Conversation List */}
      <div className="w-1/3 bg-white border-r">
        <ConversationList
          conversations={conversations}
          selectedConversation={selectedConversation}
          onlineUsers={onlineUsers}
          onConversationSelect={handleConversationSelect}
        />
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedConversation ? (
          <>
            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4">
              <MessageList messages={currentConversationMessages} />
              <TypingIndicator users={currentTypingUsers} />
            </div>

            {/* Message Input */}
            <div className="border-t bg-white p-4">
              <MessageInput
                onSendMessage={handleSendMessage}
                onTyping={handleTyping}
                disabled={!isConnected}
              />
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            Select a conversation to start chatting
          </div>
        )}
      </div>
    </div>
  );
};
```

### 6. Message Input Component (`components/Chat/MessageInput.tsx`)

```typescript
import React, { useState, useRef, useEffect } from 'react';

interface MessageInputProps {
  onSendMessage: (content: string, attachmentIds?: string[]) => void;
  onTyping: (isTyping: boolean) => void;
  disabled?: boolean;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  onTyping,
  disabled = false
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);

    // Handle typing indicator
    if (value.trim() && !isTyping) {
      setIsTyping(true);
      onTyping(true);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        onTyping(false);
      }
    }, 1000);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (message.trim() && !disabled) {
      onSendMessage(message.trim());
      setMessage('');

      // Stop typing indicator
      if (isTyping) {
        setIsTyping(false);
        onTyping(false);
      }

      // Clear timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  return (
    <form onSubmit={handleSubmit} className="flex items-end space-x-2">
      <div className="flex-1">
        <textarea
          value={message}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder="Type a message..."
          disabled={disabled}
          className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={1}
          style={{ minHeight: '44px', maxHeight: '120px' }}
        />
      </div>
      <button
        type="submit"
        disabled={!message.trim() || disabled}
        className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Send
      </button>
    </form>
  );
};
```

### 7. Additional Components

#### Message List Component (`components/Chat/MessageList.tsx`)

```typescript
import React from 'react';
import { MessageDto } from '@/types/chat';

interface MessageListProps {
  messages: MessageDto[];
}

export const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  return (
    <div className="space-y-4">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`flex ${message.senderId === 'current-user-id' ? 'justify-end' : 'justify-start'}`}
        >
          <div
            className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
              message.senderId === 'current-user-id'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-800'
            }`}
          >
            <p className="text-sm">{message.content}</p>
            <div className="flex items-center justify-between mt-1">
              <span className="text-xs opacity-75">
                {new Date(message.createdAt).toLocaleTimeString()}
              </span>
              {message.senderId === 'current-user-id' && (
                <span className="text-xs opacity-75">
                  {message.status === 'sent' && '✓'}
                  {message.status === 'delivered' && '✓✓'}
                  {message.status === 'read' && '✓✓'}
                </span>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
```

#### Typing Indicator Component (`components/Chat/TypingIndicator.tsx`)

```typescript
import React from 'react';

interface TypingIndicatorProps {
  users: string[];
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({ users }) => {
  if (users.length === 0) return null;

  const displayText = users.length === 1
    ? `${users[0]} is typing...`
    : `${users.slice(0, -1).join(', ')} and ${users[users.length - 1]} are typing...`;

  return (
    <div className="flex items-center space-x-2 text-gray-500 text-sm mt-2">
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
      </div>
      <span>{displayText}</span>
    </div>
  );
};
```

## Error Handling

### 8. Error Handling Hook (`hooks/useErrorHandler.ts`)

```typescript
import { useCallback } from 'react';

export const useErrorHandler = () => {
  const handleSocketError = useCallback((error: any) => {
    console.error('Socket error:', error);

    // Handle different types of errors
    if (error.message?.includes('authentication')) {
      // Handle authentication errors
      // Redirect to login or refresh token
      window.location.href = '/login';
    } else if (error.message?.includes('connection')) {
      // Handle connection errors
      // Show connection status indicator
    } else {
      // Handle general errors
      // Show error toast/notification
    }
  }, []);

  const handleChatError = useCallback((error: { message: string }) => {
    console.error('Chat error:', error.message);

    // Show user-friendly error message
    // You can integrate with your notification system here
    alert(`Chat error: ${error.message}`);
  }, []);

  return {
    handleSocketError,
    handleChatError
  };
};
```

## Best Practices

### 9. Connection Management

```typescript
// utils/connectionManager.ts
export class ConnectionManager {
  private static instance: ConnectionManager;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  static getInstance(): ConnectionManager {
    if (!ConnectionManager.instance) {
      ConnectionManager.instance = new ConnectionManager();
    }
    return ConnectionManager.instance;
  }

  async reconnect(token: string): Promise<boolean> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return false;
    }

    this.reconnectAttempts++;

    try {
      await new Promise(resolve => setTimeout(resolve, this.reconnectDelay));
      await socketService.connect(token);
      this.reconnectAttempts = 0; // Reset on successful connection
      return true;
    } catch (error) {
      console.error(`Reconnection attempt ${this.reconnectAttempts} failed:`, error);
      this.reconnectDelay *= 2; // Exponential backoff
      return false;
    }
  }

  resetReconnectAttempts(): void {
    this.reconnectAttempts = 0;
    this.reconnectDelay = 1000;
  }
}
```

### 10. Usage in App Component

```typescript
// app/chat/page.tsx
'use client';

import React from 'react';
import { SocketProvider } from '@/contexts/SocketContext';
import { ChatContainer } from '@/components/Chat/ChatContainer';

export default function ChatPage() {
  // Get token from your auth system
  const token = 'your-jwt-token';

  return (
    <SocketProvider>
      <div className="h-screen">
        <ChatContainer token={token} />
      </div>
    </SocketProvider>
  );
}
```

## Event Reference

### Client to Server Events

| Event | Payload | Description |
|-------|---------|-------------|
| `subscribe_conversation` | `{ conversationId: string }` | Subscribe to conversation updates |
| `unsubscribe_conversation` | `{ conversationId: string }` | Unsubscribe from conversation |
| `send_message` | `CreateMessageDto` | Send a new message |
| `mark_read` | `{ conversationId: string }` | Mark messages as read |
| `typing` | `{ conversationId: string, isTyping: boolean }` | Send typing indicator |
| `authenticate` | `{ token: string }` | Authenticate with JWT token |

### Server to Client Events

| Event | Payload | Description |
|-------|---------|-------------|
| `connected` | `{ userId: string, name: string }` | Connection confirmed |
| `new_message` | `MessageDto` | New message received |
| `messages_delivered` | `{ conversationId: string, userId: string }` | Messages delivered |
| `messages_read` | `{ conversationId: string, userId: string }` | Messages read |
| `typing` | `{ conversationId: string, userId: string, userName: string, isTyping: boolean }` | Typing indicator |
| `user_status` | `{ userId: string, isOnline: boolean, timestamp: Date }` | User online status |
| `subscribed_conversation` | `{ conversationId: string }` | Conversation subscription confirmed |
| `unsubscribed_conversation` | `{ conversationId: string }` | Conversation unsubscription confirmed |
| `error` | `{ message: string }` | Error occurred |
| `auth_error` | `{ message: string }` | Authentication error |
| `authenticated` | `{ userId: string, name: string, success: boolean }` | Authentication successful |

## File Upload Integration

For file uploads, use the REST API endpoint and then include the file ID in the message:

```typescript
const uploadFile = async (file: File, token: string): Promise<string> => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch('http://**************:3010/api/chat/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  const result = await response.json();
  return result.data.id; // File ID to include in message
};
```

This guide provides a complete implementation of the HEC Backend chat system in Next.js with TypeScript, including all the necessary components, hooks, and event handling patterns.
