import { Injectable, OnModuleInit, Logger } from '@nestjs/common';
import { DiarySettingsService } from './diary-settings.service';

@Injectable()
export class DiarySettingsInitializer implements OnModuleInit {
  private readonly logger = new Logger(DiarySettingsInitializer.name);

  constructor(private readonly diarySettingsService: DiarySettingsService) {}

  /**
   * Initialize diary settings when the module is initialized
   */
  async onModuleInit() {
    this.logger.log('Initializing diary settings module...');
    
    try {
      // Seed default diary settings templates
      await this.diarySettingsService.seedDefaultTemplates();
      
      this.logger.log('Diary settings module initialized successfully');
    } catch (error) {
      this.logger.error(`Error initializing diary settings module: ${error.message}`, error.stack);
    }
  }
}
