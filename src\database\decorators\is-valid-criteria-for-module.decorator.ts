import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { getCriteriaForModule } from '../../constants/award-criteria.constant';
import { AwardModule } from '../entities/award.entity';

/**
 * Custom validator to check if criteria are valid for the specified module
 */
export function IsValidCriteriaForModule(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidCriteriaForModule',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(criteria: string[], args: ValidationArguments) {
          // Get the module from the same object
          const module = (args.object as any).module as AwardModule;
          
          if (!module) {
            // If module is not set, we can't validate criteria yet
            // Let the module validation handle this case
            return true;
          }

          if (!criteria || !Array.isArray(criteria) || criteria.length === 0) {
            return false;
          }

          // Get valid criteria for the module
          const validCriteriaForModule = getCriteriaForModule(module);
          const validCriteriaIds = validCriteriaForModule.map(c => c.id);

          // Check if all criteria are valid for this module
          const invalidCriteria = criteria.filter(criterion => !validCriteriaIds.includes(criterion));
          
          return invalidCriteria.length === 0;
        },
        defaultMessage(args: ValidationArguments) {
          const criteria = args.value as string[];
          const module = (args.object as any).module as AwardModule;
          
          if (!module) {
            return 'Module must be specified before validating criteria';
          }

          if (!criteria || !Array.isArray(criteria) || criteria.length === 0) {
            return 'At least one criterion must be specified';
          }

          // Get valid criteria for the module
          const validCriteriaForModule = getCriteriaForModule(module);
          const validCriteriaIds = validCriteriaForModule.map(c => c.id);

          // Find invalid criteria
          const invalidCriteria = criteria.filter(criterion => !validCriteriaIds.includes(criterion));
          
          if (invalidCriteria.length > 0) {
            return `Invalid criteria for '${module}' module: ${invalidCriteria.join(', ')}. Valid criteria for '${module}' module are: ${validCriteriaIds.join(', ')}`;
          }

          return 'Invalid criteria';
        },
      },
    });
  };
}
