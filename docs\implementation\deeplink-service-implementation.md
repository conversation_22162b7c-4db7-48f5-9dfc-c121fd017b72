# DeeplinkService Implementation Guide

This document provides guidance on using the `DeeplinkService` for generating all types of links in the application.

## Overview

The `DeeplinkService` is a unified service for generating all types of links in the application, including:

- Profile links
- Diary entry links
- Notification links
- Verification links
- Password reset links
- QR codes for sharing
- And more

## Implementation

### Module Import

Import the `DeeplinkModule` in your feature module:

```typescript
import { DeeplinkModule } from '../../common/utils/deeplink.module';

@Module({
  imports: [
    // ...
    DeeplinkModule,
    // ...
  ],
})
export class YourFeatureModule {}
```

### Service Injection

Inject the `DeeplinkService` in your service:

```typescript
import { DeeplinkService } from '../../common/utils/deeplink.service';

@Injectable()
export class YourService {
  constructor(
    private readonly deeplinkService: DeeplinkService,
    // ...
  ) {}
  
  // Your methods here
}
```

### Using the DeeplinkService

The `DeeplinkService` provides several methods for generating different types of links:

#### Link Types

```typescript
import { DeeplinkType } from '../../common/utils/deeplink.service';

// Available link types
DeeplinkType.PROFILE        // User profile links
DeeplinkType.DIARY_ENTRY    // Diary entry links
DeeplinkType.DIARY_SHARE    // Diary sharing links
DeeplinkType.NOTIFICATION   // Notification links
DeeplinkType.VERIFICATION   // Email verification links
DeeplinkType.PASSWORD_RESET // Password reset links
```

#### Generating Web Links

Web links are URLs that can be opened in a browser:

```typescript
// Generate a profile web link
const profileLink = this.deeplinkService.getWebLink(DeeplinkType.PROFILE, { 
  userId: '123', 
  userType: 'student' 
});
// Result: https://example.com/student-profile/123

// Generate a diary entry web link
const diaryLink = this.deeplinkService.getWebLink(DeeplinkType.DIARY_ENTRY, { 
  entryId: '456' 
});
// Result: https://example.com/diary/entry/456

// Generate a verification link with a token
const verificationLink = this.deeplinkService.getWebLink(DeeplinkType.VERIFICATION, {
  additionalParams: { token: 'abc123' }
});
// Result: https://example.com/verify-email?token=abc123
```

#### Generating Deep Links

Deep links can be opened in the mobile app:

```typescript
// Generate a profile deep link
const profileDeepLink = this.deeplinkService.getDeepLink(DeeplinkType.PROFILE, { 
  userId: '123', 
  userType: 'student' 
});
// Result: hec://profile/student/123

// Generate a notification deep link
const notificationDeepLink = this.deeplinkService.getDeepLink(DeeplinkType.NOTIFICATION, {
  id: '789',
  notificationType: 'friend_request',
  relatedEntityType: 'student'
});
// Result: hec://notification/friend_request/student/789
```

#### Generating HTML Links

HTML links can be included in emails or notifications:

```typescript
// Generate a simple HTML link
const profileLinkHtml = this.deeplinkService.getLinkHtml(DeeplinkType.PROFILE, { 
  userId: '123', 
  userType: 'student', 
  linkText: 'View Profile' 
});
// Result: <a href="https://example.com/student-profile/123">View Profile</a>

// Generate a button-style HTML link
const profileButtonHtml = this.deeplinkService.getLinkHtml(DeeplinkType.PROFILE, { 
  userId: '123', 
  userType: 'student', 
  linkText: 'View Profile', 
  buttonStyle: true 
});
// Result: <a href="https://example.com/student-profile/123" style="...">View Profile</a>
```

#### Generating QR Codes

QR codes can be generated for any link type:

```typescript
// Generate a QR code for a diary share link
const qrCodeBuffer = await this.deeplinkService.generateQRCode(DeeplinkType.DIARY_SHARE, {
  token: 'share123'
});

// Save the QR code to a file or serve it to the client
```

## Benefits of the DeeplinkService

1. **Centralized Link Management**: All links are generated from a single service
2. **Consistent URL Structure**: Links follow a consistent pattern
3. **Type Safety**: Link types are defined as enums
4. **Easier Maintenance**: Changes to URL structure only need to be made in one place
5. **Enhanced Functionality**: Support for query parameters, HTML styling, and QR code generation

## Real-World Examples

### Profile Links in Notifications

```typescript
@Injectable()
export class NotificationService {
  constructor(
    private readonly deeplinkService: DeeplinkService,
    // ...
  ) {}

  async sendFriendRequestNotification(senderId: string, receiverId: string): Promise<void> {
    // Generate profile link for the sender
    const profileLink = this.deeplinkService.getWebLink(DeeplinkType.PROFILE, {
      userId: senderId,
      userType: 'student'
    });

    // Generate deep link for mobile app
    const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.PROFILE, {
      userId: senderId,
      userType: 'student'
    });

    // Create notification with links
    await this.notificationRepository.create({
      userId: receiverId,
      type: 'friend_request',
      message: 'You have a new friend request',
      relatedEntityId: senderId,
      relatedEntityType: 'student',
      webLink: profileLink,
      deepLink: deepLink
    });
  }
}
```

### Diary Sharing with QR Codes

```typescript
@Injectable()
export class DiaryShareService {
  constructor(
    private readonly deeplinkService: DeeplinkService,
    private readonly fileService: FileService,
    // ...
  ) {}

  async generateShareLink(diaryEntryId: string): Promise<{ shareUrl: string; qrCodeUrl: string }> {
    // Generate a unique share token
    const shareToken = this.generateUniqueToken();
    
    // Save the share token in the database
    await this.diaryShareRepository.create({
      diaryEntryId,
      shareToken,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
    });
    
    // Generate the share URL
    const shareUrl = this.deeplinkService.getWebLink(DeeplinkType.DIARY_SHARE, {
      token: shareToken
    });
    
    // Generate QR code for the share URL
    const qrCodeBuffer = await this.deeplinkService.generateQRCode(DeeplinkType.DIARY_SHARE, {
      token: shareToken
    });
    
    // Save the QR code to a file
    const qrCodeUrl = await this.fileService.saveQRCode(qrCodeBuffer, `diary-share-${shareToken}.png`);
    
    return { shareUrl, qrCodeUrl };
  }
}
```
