# Diary Module - Frontend Integration Flow

This document provides a comprehensive integration flow for frontend developers to implement the Diary Module, with minimal verbal communication needed. It focuses exclusively on API endpoints and data flows without prescribing specific frontend implementation approaches.

## Table of Contents

1. [Diary Management](#diary-management)
   - [Student Diary](#student-diary)
   - [Default Skin Management](#default-skin-management)
   - [<PERSON><PERSON>](#tutor-greeting)
2. [Diary Entry Management](#diary-entry-management)
   - [Create and Update Entries](#create-and-update-entries)
   - [Entry Submission Workflow](#entry-submission-workflow)
   - [Entry Sharing](#entry-sharing)
3. [Diary Skin Management](#diary-skin-management)
   - [Global Skins](#global-skins)
   - [Student Skins](#student-skins)
   - [Apply Skins](#apply-skins)
4. [Tutor Feedback and Evaluation](#tutor-feedback-and-evaluation)
   - [Review Workflow](#review-workflow)
   - [Feedback and Scoring](#feedback-and-scoring)
   - [Corrections](#corrections)
5. [Diary Missions](#diary-missions)
   - [Mission Management](#mission-management)
   - [Mission Entries](#mission-entries)
   - [Mission Feedback](#mission-feedback)

## Diary Management

### Student Diary

Each student has a single diary that contains all their entries. The diary is automatically created when first accessed.

```
┌─────────────┐      ┌─────────────┐
│  Access     │      │  Diary      │
│  Diary      │ ──▶  │  Retrieved  │
└─────────────┘      └─────────────┘
       │                    │
       ▼                    ▼
┌─────────────┐      ┌─────────────┐
│ GET /diary  │      │ System      │
│             │      │ creates if  │
│             │      │ not exists  │
└─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /diary` - Get or create student diary

**Response:**
```json
{
  "success": true,
  "message": "Diary retrieved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "userId": "123e4567-e89b-12d3-a456-426614174001",
    "defaultSkinId": "123e4567-e89b-12d3-a456-426614174002",
    "tutorGreeting": "Welcome to your diary! I'm excited to read your entries.",
    "totalEntries": 15,
    "reviewedEntries": 10,
    "averageScore": 85
  }
}
```

### Default Skin Management

Students can set a default skin for their diary that will be applied to new entries.

```
┌─────────────┐      ┌─────────────┐
│  Select     │      │  Update     │
│  Skin       │ ──▶  │  Default    │
└─────────────┘      └─────────────┘
       │                    │
       ▼                    ▼
┌─────────────┐      ┌─────────────┐
│ GET /diary/ │      │ PATCH /diary│
│ skins       │      │ /default-   │
│             │      │ skin        │
└─────────────┘      └─────────────┘
```

**API Endpoints:**
- `PATCH /diary/default-skin` - Update default skin

**Request Body:**
```json
{
  "skinId": "123e4567-e89b-12d3-a456-426614174000"
}
```

### Tutor Greeting

Tutors can set a personalized greeting for their students' diaries.

**API Endpoints:**
- `PATCH /tutor/diary/:studentId/greeting` - Update tutor greeting (tutor only)

**Request Body:**
```json
{
  "greeting": "Welcome to your diary! I'm excited to read your entries."
}
```

## Diary Entry Management

### Create and Update Entries

Students can create and update diary entries with various attributes.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Create     │      │  Edit       │      │  Submit     │
│  Entry      │ ──▶  │  Entry      │ ──▶  │  Entry      │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /diary/│      │ PATCH /diary│      │ POST /diary/│
│ entries     │      │ /entries/:id│      │ entries/:id/│
│             │      │             │      │ submit      │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /diary/entries` - Create diary entry
- `PATCH /diary/entries/:id` - Update diary entry
- `GET /diary/entries/:id` - Get diary entry by ID
- `GET /diary/entries` - Get all diary entries (with filtering)

**Create Entry Request Body:**
```json
{
  "entryDate": "2023-07-25",
  "title": "My First Diary Entry",
  "content": "Today I learned about...",
  "skinId": "123e4567-e89b-12d3-a456-426614174000",
  "backgroundColor": "#f5f5f5",
  "isPrivate": false,
  "settingsTemplateId": "123e4567-e89b-12d3-a456-426614174000"
}
```

**Update Entry Request Body:**
```json
{
  "title": "Updated Title",
  "content": "Updated content...",
  "skinId": "123e4567-e89b-12d3-a456-426614174000",
  "backgroundColor": "#ffffff",
  "isPrivate": true
}
```

### Entry Submission Workflow

Diary entries follow a specific workflow from draft to reviewed.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Draft      │      │  Submitted  │      │  Under      │      │  Reviewed   │
│  Entry      │ ──▶  │  Entry      │ ──▶  │  Review     │ ──▶  │  Entry      │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ Student     │      │ Student     │      │ Tutor       │      │ Tutor       │
│ creates/    │      │ submits     │      │ picks for   │      │ provides    │
│ edits       │      │ for review  │      │ review      │      │ feedback    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /diary/entries/:id/submit` - Submit entry for review
- `GET /tutor/diary/pending-reviews` - Get entries pending review (tutor only)
- `POST /tutor/diary/entries/:id/pick-for-review` - Pick entry for review (tutor only)

### Entry Sharing

Students can share diary entries with others via unique links and QR codes.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Select     │      │  Generate   │      │  Share      │
│  Entry      │ ──▶  │  Share Link │ ──▶  │  Entry      │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /diary/ │      │ POST /diary/│      │ Share URL   │
│ entries/:id │      │ entries/:id/│      │ and QR code │
│             │      │ share       │      │             │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /diary/entries/:id/share` - Share diary entry
- `GET /diary/shared/:token` - View shared diary entry (public)

**Share Request Body:**
```json
{
  "expiryDate": "2023-08-25T00:00:00Z"
}
```

## Diary Skin Management

### Global Skins

Administrators can create global skins available to all students.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Create     │      │  Update     │      │  Delete     │
│  Global Skin│ ──▶  │  Global Skin│ ──▶  │  Global Skin│
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /admin/│      │ PATCH /admin│      │ DELETE      │
│ diary/skins │      │ /diary/skins│      │ /admin/diary│
│             │      │ /:id        │      │ /skins/:id  │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /admin/diary/skins` - Create global skin (admin only)
- `PATCH /admin/diary/skins/:id` - Update global skin (admin only)
- `DELETE /admin/diary/skins/:id` - Delete global skin (admin only)

**Note:** Global skin creation requires a multipart/form-data request with a file upload for the preview image.

### Student Skins

Students can create their own custom skins.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Create     │      │  Update     │      │  Delete     │
│ Student Skin│ ──▶  │ Student Skin│ ──▶  │ Student Skin│
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST /diary/│      │ PATCH /diary│      │ DELETE      │
│ student-    │      │ /student-   │      │ /diary/     │
│ skins       │      │ skins/:id   │      │ student-    │
│             │      │             │      │ skins/:id   │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /diary/student-skins` - Create student skin
- `PATCH /diary/student-skins/:id` - Update student skin
- `DELETE /diary/student-skins/:id` - Delete student skin

**Note:** Student skin creation requires a multipart/form-data request with a file upload for the preview image.

### Apply Skins

Students can apply skins to their diary entries.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Browse     │      │  Select     │      │  Apply      │
│  Skins      │ ──▶  │  Skin       │ ──▶  │  Skin       │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /diary/ │      │ GET /diary/ │      │ PATCH /diary│
│ skins       │      │ skins/:id   │      │ /entries/:id│
│             │      │             │      │ (with skinId)│
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /diary/skins` - Get all available skins
- `GET /diary/skins/:id` - Get skin by ID
- `PATCH /diary/today/skin` - Update today's diary entry skin

## Tutor Feedback and Evaluation

### Review Workflow

Tutors follow a specific workflow to review student diary entries.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  View       │      │  Pick       │      │  Add        │      │  Complete   │
│  Pending    │ ──▶  │  Entry      │ ──▶  │  Feedback   │ ──▶  │  Review     │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /tutor/ │      │ POST /tutor/│      │ POST /tutor/│      │ Entry status│
│ diary/      │      │ diary/      │      │ diary/      │      │ changes to  │
│ pending-    │      │ entries/:id/│      │ entries/:id/│      │ REVIEWED    │
│ reviews     │      │ pick-for-   │      │ feedback    │      │             │
│             │      │ review      │      │             │      │             │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /tutor/diary/pending-reviews` - Get entries pending review (tutor only)
- `POST /tutor/diary/entries/:id/pick-for-review` - Pick entry for review (tutor only)
- `GET /tutor/diary/entries/:id` - Get entry details (tutor only)

### Feedback and Scoring

Tutors provide feedback and scores for student diary entries.

```
┌─────────────┐      ┌─────────────┐
│  Add        │      │  Add        │
│  Feedback   │ ──▶  │  Score      │
└─────────────┘      └─────────────┘
       │                    │
       ▼                    ▼
┌─────────────┐      ┌─────────────┐
│ POST /tutor/│      │ Feedback    │
│ diary/      │      │ includes    │
│ entries/:id/│      │ rating and  │
│ feedback    │      │ score       │
└─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /tutor/diary/entries/:id/feedback` - Add feedback (tutor only)

**Feedback Request Body:**
```json
{
  "feedback": "Great work on your diary entry! I particularly liked your analysis of the topic.",
  "rating": 5,
  "award": "Gold Star",
  "score": 95
}
```

### Corrections

Tutors can provide grammar and content corrections for student diary entries.

**API Endpoints:**
- `POST /tutor/diary/entries/:id/correction` - Add correction (tutor only)

**Correction Request Body:**
```json
{
  "correction": "Here are some grammar corrections: 1. 'their' should be 'there', 2. 'affect' should be 'effect'"
}
```

## Diary Missions

### Mission Management

Admin can create and manage diary missions for students.

```
┌─────────────┐              ┌─────────────┐                          ┌─────────────┐
│  Create     │              │  Update     │                          │  Delete     │
│  Mission    │ ──────────▶ │  Mission     │ ────────────────▶       │  Mission    │
└─────────────┘              └─────────────┘                          └─────────────┘
       │                            │                                         │
       ▼                            ▼                                         ▼
┌────────────────────┐      ┌───────────────────────────┐        ┌─────────────────────────────┐
│ POST
|/diary/admin/missions      │ PATCH                     │        | DELETE                      │
│                    │      │ /diary/admin/missions/:id │        | /diary/admin/missions/:id   │
│                    │      │                           │        │
└────────────────────┘      └───────────────────────────┘        └─────────────────────────────┘
```

**API Endpoints:**
- `POST /diary/admin/missions` - Create mission (admin only)
- `PATCH /diary/admin/missions/:id` - Update mission (admin only)
- `DELETE /diary/admin/missions/:id` - Delete mission (admin only)
- `GET /diary/admin/missions` - Get all missions (admin only)
- `GET /diary/missions` - Get available missions (student only)
- `GET /diary/missions/today` - Get today's featured mission (student only)

**Create Mission Request Body:**
```json
{
  "title": "Weekly Writing Challenge",
  "description": "Write about your favorite book and why you enjoyed it.",
  "targetWordCount": 200,
  "targetMaxWordCount": 300,
  "publishDate": "2023-07-25T00:00:00Z",
  "expiryDate": "2023-07-31T23:59:59Z",
  "score": 100
}
```

### Mission Entries

Students can create and submit entries for missions.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Create     │      │  Update     │      │  Submit     │
│  Entry      │ ──▶  │  Entry      │ ──▶  │  Entry      │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ POST        │      │ PATCH       │      │ POST        │
│ /student/   │      │ /student/   │      │ /student/   │
│ missions/   │      │ missions/   │      │ missions/   │
│ :id/entries │      │ entries/:id │      │ entries/:id/│
│             │      │             │      │ submit      │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `POST /student/missions/:id/entries` - Create mission entry (student only)
- `PATCH /student/missions/entries/:id` - Update mission entry (student only)
- `POST /student/missions/entries/:id/submit` - Submit mission entry (student only)
- `GET /student/missions/entries/:id` - Get mission entry (student only)
- `GET /student/missions/entries` - Get all mission entries (student only)

**Create Mission Entry Request Body:**
```json
{
  "content": "My favorite book is..."
}
```

### Mission Feedback

Tutors can provide feedback and corrections for mission entries.

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Review     │      │  Add        │      │  Assign     │
│  Entry      │ ──▶  │  Feedback   │ ──▶  │  Score      │
└─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │
       ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /tutor/ │      │ POST /tutor/│      │ POST /tutor/│
│ missions/   │      │ missions/   │      │ missions/   │
│ entries/:id │      │ entries/:id/│      │ entries/:id/│
│             │      │ feedback    │      │ score       │
└─────────────┘      └─────────────┘      └─────────────┘
```

**API Endpoints:**
- `GET /tutor/missions/entries` - Get all mission entries (tutor only)
- `GET /tutor/missions/entries/:id` - Get mission entry (tutor only)
- `POST /tutor/missions/entries/:id/feedback` - Add feedback (tutor only)
- `POST /tutor/missions/entries/:id/correction` - Add correction (tutor only)
- `POST /tutor/missions/entries/:id/score` - Assign score (tutor only)

**Feedback Request Body:**
```json
{
  "feedback": "Great job on your mission entry! Your analysis of the book was insightful.",
  "rating": 4
}
```

**Score Request Body:**
```json
{
  "score": 85
}
```
