import { MigrationInterface, QueryRunner } from "typeorm";

export class StudentEssaySubmissionTable1745736035224 implements MigrationInterface {
    name = 'StudentEssaySubmissionTable1745736035224'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."essay_task_submissions_status_enum" AS ENUM('draft', 'submitted', 'reviewed')`);
        await queryRunner.query(`CREATE TABLE "essay_task_submissions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "status" "public"."essay_task_submissions_status_enum" NOT NULL DEFAULT 'draft', "current_revision" integer NOT NULL DEFAULT '1', "teacher_remarks" text, "is_active" boolean NOT NULL DEFAULT true, "latest_submission_id" uuid, "total_revisions" integer NOT NULL DEFAULT '0', "first_submitted_at" TIMESTAMP, "last_submitted_at" TIMESTAMP, "task_id" uuid, CONSTRAINT "PK_931bc4f39bbfb1578d3ecf40379" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_1cb1730e57651f9168f9b01017" ON "essay_task_submissions" ("task_id", "created_by") `);
        await queryRunner.query(`CREATE TABLE "essay_task_submission_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "content" text NOT NULL, "word_count" integer NOT NULL, "submission_date" TIMESTAMP NOT NULL, "sequence_number" integer NOT NULL, "feedback" text, "reviewed_at" TIMESTAMP, "meta_data" json, "previous_revision_id" uuid, "submission_id" uuid, CONSTRAINT "PK_b080a0661e3ecf1ef572f1ce9fe" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_c4308ae58c4d15d45caccc9893" ON "essay_task_submission_history" ("submission_date") `);
        await queryRunner.query(`CREATE INDEX "IDX_861b3f40f0fc48b637918a6c8a" ON "essay_task_submission_history" ("submission_id", "sequence_number") `);
        await queryRunner.query(`ALTER TABLE "essay_mission_tasks" ALTER COLUMN "word_limit_minumum" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "essay_mission_tasks" ALTER COLUMN "deadline" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_703b42d862207320da43d5144ad" FOREIGN KEY ("task_id") REFERENCES "essay_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "FK_dad1c1d1b97acbc11efc1c9408f" FOREIGN KEY ("submission_id") REFERENCES "essay_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "FK_dad1c1d1b97acbc11efc1c9408f"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_703b42d862207320da43d5144ad"`);
        await queryRunner.query(`ALTER TABLE "essay_mission_tasks" ALTER COLUMN "deadline" SET DEFAULT '1'`);
        await queryRunner.query(`ALTER TABLE "essay_mission_tasks" ALTER COLUMN "word_limit_minumum" SET DEFAULT '1'`);
        await queryRunner.query(`DROP INDEX "public"."IDX_861b3f40f0fc48b637918a6c8a"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c4308ae58c4d15d45caccc9893"`);
        await queryRunner.query(`DROP TABLE "essay_task_submission_history"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_1cb1730e57651f9168f9b01017"`);
        await queryRunner.query(`DROP TABLE "essay_task_submissions"`);
        await queryRunner.query(`DROP TYPE "public"."essay_task_submissions_status_enum"`);
    }

}
