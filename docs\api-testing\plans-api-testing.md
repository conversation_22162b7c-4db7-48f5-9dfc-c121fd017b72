# Plans API Testing Flow

This document outlines the testing flow for the Plans API endpoints.

## Prerequisites

Before testing the Plans API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (admin, student)
3. Set up your API testing tool (<PERSON><PERSON> recommended)

## Plan Listing Testing Flow

### Test Case 1: Get All Plans

1. Authenticate with any valid token
2. Send a GET request to `/api/plans`
3. Verify HTTP status code is 200 OK
4. Verify response contains a list of available plans
5. Verify each plan contains required fields (id, name, price, features)
6. Verify only active plans are returned by default

### Test Case 2: Filter Plans

1. Test filtering by plan type (STARTER, STANDARD, PRO, ULTIMATE)
2. Test filtering by active status
3. Test filtering by price range
4. Verify filtered results match the criteria

### Test Case 3: Get Plan by ID

1. Authenticate with any valid token
2. Send a GET request to `/api/plans/{planId}`
3. Verify HTTP status code is 200 OK
4. Verify response contains complete plan details
5. Test with non-existent plan ID and verify 404 Not Found response

## Plan Subscription Testing Flow

### Test Case 1: Subscribe to Plan (Monthly)

1. Authenticate with a student token
2. Send a POST request to `/api/plans/subscribe` with planId and monthly billing cycle
3. Verify HTTP status code is 200 OK
4. Verify response contains subscription details
5. Verify subscription is created in the database
6. Verify subscription has correct start and end dates (1 month apart)
7. Verify payment is processed correctly
8. Verify new auth tokens are returned with updated plan information

### Test Case 2: Subscribe to Plan (Yearly)

1. Authenticate with a student token
2. Send a POST request to `/api/plans/subscribe` with planId and yearly billing cycle
3. Verify HTTP status code is 200 OK
4. Verify response contains subscription details
5. Verify subscription is created in the database
6. Verify subscription has correct start and end dates (1 year apart)
7. Verify payment is processed correctly
8. Verify yearly discount is applied correctly

### Test Case 3: Subscription Validation

1. Test with invalid plan ID
2. Test with invalid billing cycle
3. Test with invalid payment method
4. Test with insufficient payment details
5. Verify appropriate validation errors are returned for each case

### Test Case 4: Subscription with Existing Active Plan

1. Create an active subscription for a student
2. Attempt to subscribe to another plan
3. Verify previous plan is deactivated
4. Verify new plan is activated
5. Verify appropriate notifications are sent

## User Subscriptions Testing Flow

### Test Case 1: Get User Subscriptions

1. Authenticate with a student token
2. Send a GET request to `/api/plans/subscriptions`
3. Verify HTTP status code is 200 OK
4. Verify response contains active subscription
5. Verify response contains subscription history
6. Verify subscription details match database records

### Test Case 2: Get Active Subscription

1. Authenticate with a student token
2. Send a GET request to `/api/plans/active-subscription`
3. Verify HTTP status code is 200 OK
4. Verify response contains active subscription details
5. Verify included features match the subscribed plan
6. Test with a user having no active subscription and verify appropriate response

## Subscription Management Testing Flow

### Test Case 1: Cancel Subscription

1. Authenticate with a student token
2. Create an active subscription
3. Send a POST request to `/api/plans/cancel-subscription` with subscription ID
4. Verify HTTP status code is 200 OK
5. Verify subscription is marked as inactive in the database
6. Verify cancellation date is recorded
7. Verify user still has access until the end of the billing period

### Test Case 2: Renew Subscription

1. Authenticate with a student token
2. Create a subscription that's about to expire
3. Send a POST request to `/api/plans/renew-subscription` with subscription ID
4. Verify HTTP status code is 200 OK
5. Verify subscription end date is extended appropriately
6. Verify payment is processed correctly

## Tutor Assignment Testing Flow

### Test Case 1: Automatic Tutor Assignment

1. Authenticate with a student token
2. Subscribe to a plan with multiple features
3. Verify tutors are automatically assigned for each active feature
4. Verify assignments are recorded in the database
5. Verify appropriate notifications are sent to assigned tutors

### Test Case 2: Get Assigned Tutors by Feature

1. Authenticate with a student token
2. Send a GET request to `/api/plans/assigned-tutors?feature=diary`
3. Verify HTTP status code is 200 OK
4. Verify response contains tutors assigned to the specified feature
5. Test with different features and verify correct assignments

## Admin Plan Management Testing Flow

### Test Case 1: Create Plan (Admin)

1. Authenticate with an admin token
2. Send a POST request to `/api/admin/plans` with plan details
3. Verify HTTP status code is 201 Created
4. Verify plan is created in the database
5. Verify response contains the created plan details

### Test Case 2: Update Plan (Admin)

1. Authenticate with an admin token
2. Send a PUT request to `/api/admin/plans/{planId}` with updated plan details
3. Verify HTTP status code is 200 OK
4. Verify plan is updated in the database
5. Verify response contains the updated plan details

### Test Case 3: Deactivate/Activate Plan (Admin)

1. Authenticate with an admin token
2. Send a PUT request to `/api/admin/plans/{planId}` with isActive=false
3. Verify HTTP status code is 200 OK
4. Verify plan is deactivated in the database
5. Verify deactivated plan is not returned in general plan listing
6. Repeat with isActive=true to reactivate the plan

## Edge Cases and Security Testing

### Test Case 1: Role-Based Access Control

1. Authenticate with a student token
2. Attempt to access admin plan management endpoints
3. Verify 403 Forbidden responses
4. Authenticate with a tutor token
5. Attempt to access student subscription endpoints
6. Verify 403 Forbidden responses

### Test Case 2: Subscription Edge Cases

1. Test subscription to a deactivated plan
2. Test subscription with expired payment method
3. Test subscription when payment service is unavailable
4. Verify appropriate error handling for each case

### Test Case 3: Concurrent Subscription Attempts

1. Simulate concurrent subscription requests from the same user
2. Verify system handles race conditions correctly
3. Verify only one subscription is created or updated
