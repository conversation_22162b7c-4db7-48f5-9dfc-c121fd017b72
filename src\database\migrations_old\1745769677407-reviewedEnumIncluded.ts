import { MigrationInterface, QueryRunner } from "typeorm";

export class ReviewedEnumIncluded1745769677407 implements MigrationInterface {
    name = 'ReviewedEnumIncluded1745769677407'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."diary_entry_status_enum" RENAME TO "diary_entry_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_status_enum" AS ENUM('new', 'submit', 'reviewed', 'confirm')`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" TYPE "public"."diary_entry_status_enum" USING "status"::"text"::"public"."diary_entry_status_enum"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" SET DEFAULT 'new'`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_status_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_status_enum_old" AS ENUM('new', 'submit', 'confirm')`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" TYPE "public"."diary_entry_status_enum_old" USING "status"::"text"::"public"."diary_entry_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ALTER COLUMN "status" SET DEFAULT 'new'`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."diary_entry_status_enum_old" RENAME TO "diary_entry_status_enum"`);
    }

}
