import { MigrationInterface, QueryRunner } from "typeorm";

export class Altermissiontable1746681145753 implements MigrationInterface {
    name = 'Altermissiontable1746681145753'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "diary_mission" DROP CONSTRAINT "FK_4e9b74e8605cd1c89ab282bc81a"`);
        await queryRunner.query(`ALTER TABLE "diary_mission" RENAME COLUMN "tutor_id" TO "admin_id"`);
        await queryRunner.query(`ALTER TABLE "diary_mission" ADD CONSTRAINT "FK_5eec50cf6462f3d617899b040c5" FOREIGN KEY ("admin_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "diary_mission" DROP CONSTRAINT "FK_5eec50cf6462f3d617899b040c5"`);
        await queryRunner.query(`ALTER TABLE "diary_mission" RENAME COLUMN "admin_id" TO "tutor_id"`);
        await queryRunner.query(`ALTER TABLE "diary_mission" ADD CONSTRAINT "FK_4e9b74e8605cd1c89ab282bc81a" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
