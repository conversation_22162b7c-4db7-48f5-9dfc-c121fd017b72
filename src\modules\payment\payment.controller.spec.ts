import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { PaymentController } from './payment.controller';
import { PaymentService } from './services/payment.service';
import { InitiatePaymentDto, WebhookPayloadDto } from './dto/payment.dto';
import { PaymentTransactionStatus } from '../../database/entities/payment-transaction.entity';
import { KcpPaymentMethod, PurchaseType } from './interfaces/kcp.interface';
import LoggerService from '../../common/services/logger.service';

describe('PaymentController', () => {
  let controller: PaymentController;
  let paymentService: PaymentService;
  let jwtService: JwtService;

  const mockPaymentService = {
    initiatePayment: jest.fn(),
    getPaymentStatus: jest.fn(),
    processWebhook: jest.fn(),
    // refundPayment: jest.fn(), // Not implemented yet
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockReflector = {
    get: jest.fn(),
    getAll: jest.fn(),
    getAllAndOverride: jest.fn(),
    getAllAndMerge: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PaymentController],
      providers: [
        {
          provide: PaymentService,
          useValue: mockPaymentService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    controller = module.get<PaymentController>(PaymentController);
    paymentService = module.get<PaymentService>(PaymentService);
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('initiatePayment', () => {
    const mockInitiatePaymentDto: InitiatePaymentDto = {
      orderId: 'TEST-ORDER-123',
      amount: 10000,
      currency: 'KRW',
      productName: 'Test Product',
      buyerName: 'Test User',
      buyerEmail: '<EMAIL>',
      buyerPhone: '010-1234-5678',
      paymentMethod: KcpPaymentMethod.CARD,
      purchaseType: PurchaseType.SHOP_ITEM,
      referenceId: 'test-reference',
      returnUrl: 'http://localhost:3011/payment/success',
      cancelUrl: 'http://localhost:3011/payment/cancel',
    };

    const mockRequest = {
      user: {
        id: 'user-123',
        sub: 'user-123',
        userId: 'testuser',
        email: '<EMAIL>',
        type: 'student',
      }
    } as any;

    const mockPaymentResponse = {
      success: true,
      transactionId: 'TXN-123',
      paymentUrl: 'http://localhost:3012/payment/kcp/redirect?tno=TXN-123',
      redirectUrl: 'http://localhost:3012/payment/kcp/redirect?tno=TXN-123',
      message: 'Payment initiated successfully',
      expiresAt: new Date(Date.now() + 30 * 60 * 1000),
    };

    beforeEach(() => {
      mockPaymentService.initiatePayment.mockResolvedValue(mockPaymentResponse);
    });

    it('should initiate payment successfully', async () => {
      const result = await controller.initiatePayment(mockInitiatePaymentDto, mockRequest);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPaymentResponse);
      expect(result.message).toBe('Payment initiated successfully');
    });

    it('should call payment service with correct parameters', async () => {
      await controller.initiatePayment(mockInitiatePaymentDto, mockRequest);

      expect(mockPaymentService.initiatePayment).toHaveBeenCalledWith(
        mockRequest.user.id,
        mockInitiatePaymentDto
      );
    });

    it('should handle payment service errors', async () => {
      const error = new Error('Payment service error');
      mockPaymentService.initiatePayment.mockRejectedValue(error);

      await expect(
        controller.initiatePayment(mockInitiatePaymentDto, mockRequest)
      ).rejects.toThrow(error);
    });
  });

  describe('getPaymentStatus', () => {
    const mockTransactionId = 'TXN-123';
    const mockStatusResponse = {
      transactionId: mockTransactionId,
      orderId: 'TEST-ORDER-123',
      status: PaymentTransactionStatus.COMPLETED,
      amount: 10000,
      currency: 'KRW',
      paymentMethod: 'card',
      createdAt: new Date(),
      processedAt: new Date(),
    };

    beforeEach(() => {
      mockPaymentService.getPaymentStatus.mockResolvedValue(mockStatusResponse);
    });

    it('should get payment status successfully', async () => {
      const result = await controller.getPaymentStatus(mockTransactionId);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockStatusResponse);
      expect(result.message).toBe('Payment status retrieved successfully');
    });

    it('should call payment service with correct transaction ID', async () => {
      await controller.getPaymentStatus(mockTransactionId);

      expect(mockPaymentService.getPaymentStatus).toHaveBeenCalledWith(mockTransactionId);
    });

    it('should handle payment service errors', async () => {
      const error = new Error('Transaction not found');
      mockPaymentService.getPaymentStatus.mockRejectedValue(error);

      await expect(
        controller.getPaymentStatus(mockTransactionId)
      ).rejects.toThrow(error);
    });
  });

  describe('handleKcpWebhook', () => {
    const mockWebhookPayload: WebhookPayloadDto = {
      site_cd: 'TEST_SITE',
      res_cd: '0000',
      res_msg: 'SUCCESS',
      tno: 'KCP-TXN-123',
      ordr_idxx: 'TEST-ORDER-123',
      amount: '10000',
      good_name: 'Test Product',
      buyr_name: 'Test User',
      buyr_mail: '<EMAIL>',
      pay_method: '100000000000',
      app_time: new Date().toISOString(),
      app_no: 'APP-123',
    };

    const mockSignature = 'test-signature';
    const mockSourceIp = '127.0.0.1';

    beforeEach(() => {
      mockPaymentService.processWebhook.mockResolvedValue(undefined);
    });

    it('should handle webhook successfully', async () => {
      const result = await controller.handleKcpWebhook(
        mockWebhookPayload,
        mockSignature,
        mockSourceIp
      );

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.data.success).toBe(true);
      expect(result.data.message).toBe('Webhook processed successfully');
      expect(result.message).toBe('Webhook processed successfully');
    });

    it('should call payment service with correct parameters', async () => {
      await controller.handleKcpWebhook(
        mockWebhookPayload,
        mockSignature,
        mockSourceIp
      );

      expect(mockPaymentService.processWebhook).toHaveBeenCalledWith(
        mockWebhookPayload,
        mockSignature,
        mockSourceIp
      );
    });

    it('should handle webhook processing errors', async () => {
      const error = new Error('Webhook processing failed');
      mockPaymentService.processWebhook.mockRejectedValue(error);

      await expect(
        controller.handleKcpWebhook(mockWebhookPayload, mockSignature, mockSourceIp)
      ).rejects.toThrow(error);
    });

    it('should handle missing signature', async () => {
      // The controller may not throw an error for missing signature
      // It depends on the actual implementation
      const result = await controller.handleKcpWebhook(mockWebhookPayload, undefined, mockSourceIp);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
    });
  });

  // Note: refundPayment method may not exist in the actual controller
  // This test is commented out until the method is implemented
  /*
  describe('refundPayment', () => {
    const mockRefundRequest = {
      transactionId: 'TXN-123',
      amount: 5000,
      reason: 'Customer request',
    };

    const mockRequest = {
      user: {
        id: 'user-123',
        sub: 'user-123',
        userId: 'testuser',
        email: '<EMAIL>',
        type: 'admin',
      }
    } as any;

    const mockRefundResponse = {
      success: true,
      refundId: 'REFUND-123',
      refundAmount: 5000,
      message: 'Refund processed successfully',
    };

    beforeEach(() => {
      mockPaymentService.refundPayment.mockResolvedValue(mockRefundResponse);
    });

    it('should process refund successfully', async () => {
      const result = await controller.refundPayment(mockRefundRequest, mockRequest);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockRefundResponse);
      expect(result.message).toBe('Refund processed successfully');
    });

    it('should call payment service with correct parameters', async () => {
      await controller.refundPayment(mockRefundRequest, mockRequest);

      expect(mockPaymentService.refundPayment).toHaveBeenCalledWith(
        mockRefundRequest,
        mockRequest.user.id
      );
    });

    it('should handle refund service errors', async () => {
      const error = new Error('Refund processing failed');
      mockPaymentService.refundPayment.mockRejectedValue(error);

      await expect(
        controller.refundPayment(mockRefundRequest, mockRequest)
      ).rejects.toThrow(error);
    });
  });
  */

  describe('error handling', () => {
    it('should handle validation errors', async () => {
      const invalidPaymentDto = {
        // Missing required fields
        amount: -1000, // Invalid amount
      } as InitiatePaymentDto;

      const mockRequest = {
        user: {
          id: 'user-123',
          sub: 'user-123',
          userId: 'testuser',
          email: '<EMAIL>',
          type: 'student',
        }
      } as any;

      // This would typically be handled by validation pipes
      // but we can test the controller's response to validation errors
      mockPaymentService.initiatePayment.mockRejectedValue(
        new Error('Validation failed')
      );

      await expect(
        controller.initiatePayment(invalidPaymentDto, mockRequest)
      ).rejects.toThrow('Validation failed');
    });
  });
});
