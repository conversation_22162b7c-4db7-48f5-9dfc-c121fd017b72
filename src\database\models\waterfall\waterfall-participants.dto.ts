import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsUUID, IsN<PERSON>ber, Min } from 'class-validator';

/**
 * Enum for participant sort fields
 */
export enum ParticipantsSortField {
  LATEST_PARTICIPATION = 'latest_participation',
  HIGHEST_SCORE = 'highest_score' // Now represents total marks across all sets
}

/**
 * Enum for participation sort fields
 */
export enum ParticipationSortField {
  PARTICIPATED_AT = 'participated_at',
  SCORE = 'score'
}

/**
 * Enum for sort direction
 */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

/**
 * DTO for waterfall participants query
 */
export class WaterfallParticipantsQueryDto {
  @ApiProperty({
    description: 'Page number (1-based)',
    example: 1,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  limit?: number;

  @ApiProperty({
    description: 'Search term for student name or email',
    example: 'john',
    required: false
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Field to sort by',
    enum: ParticipantsSortField,
    example: ParticipantsSortField.LATEST_PARTICIPATION,
    required: false
  })
  @IsOptional()
  @IsEnum(ParticipantsSortField)
  sortBy?: ParticipantsSortField;
}

/**
 * DTO for waterfall participant
 */
export class WaterfallParticipantDto {
  @ApiProperty({
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-426614174001'
  })
  student_id: string;

  @ApiProperty({
    description: 'The name of the student',
    example: 'John Doe'
  })
  name: string;

  @ApiProperty({
    description: 'The email of the student',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'The total number of attempts made by the student across all sets',
    example: 5
  })
  total_attempts: number;

  @ApiProperty({
    description: 'The sum of all marks/scores received by the student across all sets',
    example: 120
  })
  total_marks: number;

  @ApiProperty({
    description: 'The date of the latest participation',
    example: '2023-07-25T12:34:56.789Z'
  })
  last_participation_date: Date;
}

/**
 * DTO for waterfall participants response
 */
export class WaterfallParticipantsResponseDto {
  @ApiProperty({
    description: 'List of participants',
    type: [WaterfallParticipantDto]
  })
  participants: WaterfallParticipantDto[];

  @ApiProperty({
    description: 'Total number of items',
    example: 100
  })
  total_items: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 10
  })
  total_pages: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1
  })
  current_page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10
  })
  page_size: number;
}

/**
 * DTO for waterfall student participation query
 */
export class WaterfallStudentParticipationQueryDto {
  @ApiProperty({
    description: 'Page number (1-based)',
    example: 1,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  limit?: number;

  @ApiProperty({
    description: 'Field to sort by',
    enum: ParticipationSortField,
    example: ParticipationSortField.PARTICIPATED_AT,
    required: false
  })
  @IsOptional()
  @IsEnum(ParticipationSortField)
  sortBy?: ParticipationSortField;

  @ApiProperty({
    description: 'Sort direction',
    enum: SortDirection,
    example: SortDirection.DESC,
    required: false
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection;
}

/**
 * DTO for student participation
 */
export class WaterfallStudentParticipationDto {
  @ApiProperty({
    description: 'The ID of the participation record',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  participation_id: string;

  @ApiProperty({
    description: 'The ID of the waterfall set',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  set_id: string;

  @ApiProperty({
    description: 'The title of the waterfall set',
    example: 'Basic Grammar Set 1'
  })
  set_title: string;

  @ApiProperty({
    description: 'The total number of questions in the set',
    example: 10
  })
  total_questions: number;

  @ApiProperty({
    description: 'The total possible score for the set',
    example: 50
  })
  total_score: number;

  @ApiProperty({
    description: 'The number of correct answers',
    example: 8
  })
  correct_answers: number;

  @ApiProperty({
    description: 'The score achieved',
    example: 40
  })
  score: number;

  @ApiProperty({
    description: 'The date of participation',
    example: '2023-07-25T12:34:56.789Z'
  })
  participated_at: Date;
}

/**
 * DTO for waterfall participation record
 */
export class WaterfallParticipationRecordDto {
  @ApiProperty({
    description: 'The ID of the participation record',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  participation_id: string;

  @ApiProperty({
    description: 'The ID of the waterfall set',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  set_id: string;

  @ApiProperty({
    description: 'The title of the waterfall set',
    example: 'Basic Grammar Set 1'
  })
  set_title: string;

  @ApiProperty({
    description: 'The total number of questions in the set',
    example: 10
  })
  total_questions: number;

  @ApiProperty({
    description: 'The total possible score for the set',
    example: 50
  })
  total_score: number;

  @ApiProperty({
    description: 'The number of correct answers',
    example: 8
  })
  correct_answers: number;

  @ApiProperty({
    description: 'The score achieved',
    example: 40
  })
  score: number;

  @ApiProperty({
    description: 'The date of participation',
    example: '2023-07-25T12:34:56.789Z'
  })
  participated_at: Date;
}

/**
 * DTO for waterfall student participation response
 */
export class WaterfallStudentParticipationResponseDto {
  @ApiProperty({
    description: 'The ID of the student',
    example: '123e4567-e89b-12d3-a456-426614174001'
  })
  student_id: string;

  @ApiProperty({
    description: 'The name of the student',
    example: 'John Doe'
  })
  name: string;

  @ApiProperty({
    description: 'The email of the student',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'List of participation records',
    type: [WaterfallParticipationRecordDto]
  })
  participation_records: WaterfallParticipationRecordDto[];

  @ApiProperty({
    description: 'Total number of items',
    example: 100
  })
  total_items: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 10
  })
  total_pages: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1
  })
  current_page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10
  })
  page_size: number;
}
