import { <PERSON><PERSON><PERSON>, Column, BeforeInsert, BeforeUpdate } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

/**
 * Entity for reward point settings
 * This entity stores settings for reward point conversion rates
 * Only one setting can be active at a time
 */
@Entity({ name: 'reward_point_setting' })
export class RewardPointSetting extends AuditableBaseEntity {
  @Column({ name: 'name', length: 100 })
  name: string;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @Column({ name: 'conversion_rate', type: 'decimal', precision: 10, scale: 2 })
  conversionRate: number;

  @Column({ name: 'is_active', default: false })
  isActive: boolean;

  @BeforeInsert()
  setCreatedAt() {
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }

  @BeforeUpdate()
  setUpdatedAt() {
    this.updatedAt = new Date();
  }
}
