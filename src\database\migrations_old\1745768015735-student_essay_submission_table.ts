import { MigrationInterface, QueryRunner } from "typeorm";

export class StudentEssaySubmissionTable1745768015735 implements MigrationInterface {
    name = 'StudentEssaySubmissionTable1745768015735'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "FK_dad1c1d1b97acbc11efc1c9408f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_861b3f40f0fc48b637918a6c8a"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" RENAME COLUMN "submission_id" TO "submission"`);
        await queryRunner.query(`CREATE INDEX "IDX_af613ccf087d159f7484190092" ON "essay_task_submission_history" ("submission", "sequence_number") `);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "FK_113affd435faf528a6eab27c545" FOREIGN KEY ("submission") REFERENCES "essay_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "FK_113affd435faf528a6eab27c545"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_af613ccf087d159f7484190092"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" RENAME COLUMN "submission" TO "submission_id"`);
        await queryRunner.query(`CREATE INDEX "IDX_861b3f40f0fc48b637918a6c8a" ON "essay_task_submission_history" ("sequence_number", "submission_id") `);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "FK_dad1c1d1b97acbc11efc1c9408f" FOREIGN KEY ("submission_id") REFERENCES "essay_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
