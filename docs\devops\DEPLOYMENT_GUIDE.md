# HEC Backend Deployment Guide

## Overview

This document provides instructions for deploying the HEC backend application to production, staging, and development environments. It covers environment setup, deployment process, and post-deployment verification.

## Prerequisites

Before deploying the HEC backend, ensure you have the following:

- Access to the target server(s)
- Docker and Docker Compose installed on the server
- Access to the Git repository
- Required environment variables and secrets
- Database backup (for production deployments)

## Environment Setup

### Server Requirements

- **CPU**: 2+ cores
- **RAM**: 4GB+ (8GB recommended for production)
- **Storage**: 20GB+ (SSD recommended)
- **OS**: Ubuntu 20.04 LTS or later
- **Network**: Public IP with ports 80, 443, and 3012 accessible

### Required Software

- Docker (20.10.x or later)
- Docker Compose (2.x or later)
- Git
- Node.js 16.x (for local builds only)
- Nginx (for production deployments)

### Directory Structure

Create the following directory structure on the server:

```
/app
├── hec-backend
│   ├── .env
│   ├── docker-compose.yml
│   └── uploads
├── backups
│   ├── database
│   └── uploads
└── logs
    ├── app
    ├── nginx
    └── postgres
```

## Configuration

### Environment Variables

Create a `.env` file in the `/app/hec-backend` directory with the following variables:

```
# Application
NODE_ENV=production
PORT=3012
API_URL=https://api.example.com

# Database
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=hecuser
DB_PASSWORD=your_secure_password
DB_DATABASE=hec_production
DB_SCHEMA=public
DB_SYNCHRONIZE=false
DB_LOGGING=error

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRATION=1h
JWT_REFRESH_EXPIRATION=7d

# Email
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=HEC <<EMAIL>>

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# Socket.io
SOCKET_PORT=3010
SOCKET_CORS_ORIGIN=https://example.com

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Logging
LOG_LEVEL=info
```

Adjust the values according to your environment.

### Docker Compose

Create a `docker-compose.yml` file in the `/app/hec-backend` directory:

```yaml
version: '3.8'

services:
  api:
    image: hec-backend:latest
    container_name: hec-api
    restart: always
    ports:
      - "3012:3012"
      - "3010:3010"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
    networks:
      - hec-network

  postgres:
    image: postgres:14-alpine
    container_name: hec-postgres
    restart: always
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./logs/postgres:/var/log/postgresql
    environment:
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_DATABASE}
    networks:
      - hec-network

  redis:
    image: redis:6-alpine
    container_name: hec-redis
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-data:/data
    networks:
      - hec-network

networks:
  hec-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
```

### Nginx Configuration (Production)

For production deployments, set up Nginx as a reverse proxy:

```nginx
server {
    listen 80;
    server_name api.example.com;
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name api.example.com;

    ssl_certificate /etc/letsencrypt/live/api.example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.example.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;

    # API
    location / {
        proxy_pass http://localhost:3012;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Socket.io
    location /socket.io/ {
        proxy_pass http://localhost:3010;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Documentation
    location /docs/ {
        proxy_pass http://localhost:3012/docs/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Uploads
    location /uploads/ {
        proxy_pass http://localhost:3012/uploads/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

Save this configuration to `/etc/nginx/sites-available/hec-backend` and create a symbolic link to `/etc/nginx/sites-enabled/`.

## Deployment Process

### Building the Docker Image

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/hec-backend.git
   cd hec-backend
   ```

2. Build the Docker image:
   ```bash
   docker build -t hec-backend:latest .
   ```

3. (Optional) Push the image to a registry:
   ```bash
   docker tag hec-backend:latest your-registry/hec-backend:latest
   docker push your-registry/hec-backend:latest
   ```

### Deploying to Production

1. SSH into the production server:
   ```bash
   ssh user@production-server
   ```

2. Navigate to the application directory:
   ```bash
   cd /app/hec-backend
   ```

3. Pull the latest image (if using a registry):
   ```bash
   docker pull your-registry/hec-backend:latest
   docker tag your-registry/hec-backend:latest hec-backend:latest
   ```

4. Back up the database:
   ```bash
   docker exec hec-postgres pg_dump -U hecuser -d hec_production > /app/backups/database/backup-$(date +%Y%m%d%H%M%S).sql
   ```

5. Stop and remove the existing containers:
   ```bash
   docker-compose down
   ```

6. Start the new containers:
   ```bash
   docker-compose up -d
   ```

7. Run database migrations:
   ```bash
   docker exec hec-api npm run migration:run
   ```

8. Verify the deployment:
   ```bash
   docker-compose ps
   docker logs hec-api
   ```

### Deploying to Staging

The process for staging is similar to production, but with different environment variables:

1. SSH into the staging server:
   ```bash
   ssh user@staging-server
   ```

2. Follow steps 2-8 from the production deployment, using staging-specific environment variables.

### Deploying to Development

For development deployments, you can use a simplified process:

1. SSH into the development server:
   ```bash
   ssh user@dev-server
   ```

2. Navigate to the application directory:
   ```bash
   cd /app/hec-backend
   ```

3. Pull the latest code:
   ```bash
   git pull origin develop
   ```

4. Install dependencies:
   ```bash
   npm install
   ```

5. Build the application:
   ```bash
   npm run build
   ```

6. Start the application:
   ```bash
   npm run start:dev
   ```

## Post-Deployment Verification

After deploying, perform the following checks:

1. **API Health Check**:
   ```bash
   curl https://api.example.com/health
   ```
   Expected response: `{"status":"ok","version":"x.y.z"}`

2. **Swagger Documentation**:
   Open `https://api.example.com/api-docs` in a browser and verify that the Swagger UI loads correctly.

3. **Database Connectivity**:
   ```bash
   docker exec hec-api npm run db:check
   ```
   Expected output: `Database connection successful`

4. **Socket.io Connectivity**:
   ```bash
   curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Host: api.example.com" -H "Origin: https://example.com" https://api.example.com/socket.io/
   ```
   Verify that the connection is established.

5. **File Upload**:
   Test file upload functionality through the API.

## Rollback Procedure

If issues are detected after deployment, follow these steps to rollback:

1. Stop the current containers:
   ```bash
   docker-compose down
   ```

2. Restore the database from backup:
   ```bash
   cat /app/backups/database/backup-YYYYMMDDHHMMSS.sql | docker exec -i hec-postgres psql -U hecuser -d hec_production
   ```

3. Start the previous version:
   ```bash
   docker tag hec-backend:previous hec-backend:latest
   docker-compose up -d
   ```

4. Verify the rollback using the post-deployment verification steps.

## Maintenance

### Database Backups

Set up a cron job to regularly back up the database:

```bash
# Add to crontab
0 2 * * * docker exec hec-postgres pg_dump -U hecuser -d hec_production | gzip > /app/backups/database/backup-$(date +\%Y\%m\%d).sql.gz
```

### Log Rotation

Configure log rotation for application logs:

```bash
# /etc/logrotate.d/hec-backend
/app/hec-backend/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 root root
    sharedscripts
    postrotate
        docker exec hec-api kill -USR1 1
    endscript
}
```

### Monitoring

Set up monitoring for the application:

1. **Prometheus**: Configure Prometheus to scrape metrics from the `/metrics` endpoint.
2. **Grafana**: Set up Grafana dashboards to visualize the metrics.
3. **Alerting**: Configure alerts for critical issues (high CPU/memory usage, API errors, etc.).

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Check database credentials in the `.env` file
   - Verify that the database container is running
   - Check network connectivity between containers

2. **File Upload Issues**:
   - Verify that the uploads directory exists and has the correct permissions
   - Check the `UPLOAD_DIR` environment variable
   - Ensure that the container has sufficient disk space

3. **Socket.io Connection Issues**:
   - Verify that the Socket.io port is exposed
   - Check CORS configuration
   - Ensure that the WebSocket upgrade is properly handled by Nginx

### Logs

Access logs for troubleshooting:

```bash
# Application logs
docker logs hec-api

# Database logs
docker logs hec-postgres

# Redis logs
docker logs hec-redis

# Nginx logs (production)
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

## Conclusion

This deployment guide provides a comprehensive approach to deploying the HEC backend application to various environments. By following these instructions, you can ensure a smooth and reliable deployment process.

For any questions or issues, please contact the DevOps team.
