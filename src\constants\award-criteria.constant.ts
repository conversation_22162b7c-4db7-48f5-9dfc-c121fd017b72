import { AwardModule, AwardCriteria } from '../database/entities/award.entity';

export interface AwardCriteriaConfig {
  id: string;
  name: string;
  description: string;
  module: AwardModule;
}

export const AWARD_CRITERIA: AwardCriteriaConfig[] = [
  // Diary Module Criteria
  {
    id: AwardCriteria.DIARY_SCORE,
    name: 'Diary Score',
    description: 'Evaluation based on diary entry scores',
    module: AwardModule.DIARY
  },
  {
    id: AwardCriteria.ATTENDANCE,
    name: 'Attendance',
    description: 'Evaluation based on diary entry submission frequency',
    module: AwardModule.DIARY
  },
  {
    id: AwardCriteria.DIARY_DECORATION,
    name: 'Diary Decoration',
    description: 'Evaluation based on diary decoration and customization',
    module: AwardModule.DIARY
  },
  {
    id: AwardCriteria.DIARY_FRIENDSHIP,
    name: 'Diary Friendship',
    description: 'Evaluation based on diary friendship and social engagement',
    module: AwardModule.DIARY
  },

  // Novel Module Criteria
  {
    id: AwardCriteria.NOVEL_PERFORMANCE,
    name: 'Novel Performance',
    description: 'Overall performance in the novel module',
    module: AwardModule.NOVEL
  },

  // Essay Module Criteria
  {
    id: AwardCriteria.ESSAY_PERFORMANCE,
    name: 'Essay Performance',
    description: 'Overall performance in the essay module',
    module: AwardModule.ESSAY
  }
];

/**
 * Get criteria available for a specific module
 * @param module The award module
 * @returns Array of criteria available for the module
 */
export function getCriteriaForModule(module: AwardModule): AwardCriteriaConfig[] {
  return AWARD_CRITERIA.filter(criteria => criteria.module === module);
}

/**
 * Get all available modules with their criteria
 * @returns Object mapping modules to their available criteria
 */
export function getModuleCriteriaMap(): Record<AwardModule, AwardCriteriaConfig[]> {
  return {
    [AwardModule.DIARY]: getCriteriaForModule(AwardModule.DIARY),
    [AwardModule.NOVEL]: getCriteriaForModule(AwardModule.NOVEL),
    [AwardModule.ESSAY]: getCriteriaForModule(AwardModule.ESSAY)
  };
}
