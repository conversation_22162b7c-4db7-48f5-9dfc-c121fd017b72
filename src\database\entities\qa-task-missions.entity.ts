import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON>To<PERSON>ne, OneToMany, Jo<PERSON><PERSON><PERSON>um<PERSON> } from "typeorm";
import { AuditableBaseEntity } from "./base-entity";
import { QAMissionGoal } from "./qa-mission-goal.entity";
import { QATaskSubmissions } from "./qa-task-submissions.entity";
import { IsUUID } from "class-validator";

@Entity()
export class QATaskMissions extends AuditableBaseEntity {
  @Column({
    name: "title",
    type: "varchar",
    length: 50,
  })
  title: string;

  @Column({
    name: "description",
    type: "text"
  })
  description: string;

  @Column({
    name: "is_active",
    default: true
  })
  isActive?: boolean;

  @Column({
    name: "time_period_unit",
    type: "int",
    default: 1,
    nullable: true
  })
  timePeriodUnit: number;

  @Column({
    name: "word_limit_minumum",
    type: "int",
  })
  wordLimitMinimum: number;

  @Column({
    name: "word_limit_maximum",
    type: "int",
    nullable: true,
  })
  wordLimitMaximum: number;

  @Column({
    name: "deadline",
    type: "int",
    nullable: true
  })
  deadline: number;

  @Column({
    name: "instructions",
    type: "text",
  })
  instructions: string;

  @ManyToOne(() => QAMissionGoal, mission => mission.tasks, { nullable: true })
  @JoinColumn({ name: "mission" })
  mission: QAMissionGoal;

  @Column({
    name: "mission_id",
    type: "uuid",
    nullable: false
  })
  @IsUUID()
  missionId: string;

  @Column({
    name: "meta_data",
    type: "json",
    nullable: true
  })
  metaData: {
    month: string;
    year: string;
    week: string;
  };

  @OneToMany(() => QATaskSubmissions, submission => submission.task)
  submissions: QATaskSubmissions[];
}

// @Entity()
// export class QAMissionTasks extends AuditableBaseEntity {
//   @Column()
//   title: string;

//   @Column("text")
//   description: string;

//   @Column({ name: "word_limit_minimum" })
//   wordLimitMinimum: number;

//   @Column({ name: "word_limit_maximum" })
//   wordLimitMaximum: number;

//   @Column({ name: "time_period_unit" })
//   timePeriodUnit: number;

//   @Column()
//   deadline: number;

//   @Column("text")
//   instructions: string;

//   @Column({ type: "jsonb", nullable: true })
//   metaData: Record<string, any>;

//   @Column({ name: "is_active", default: true })
//   isActive: boolean;

//   @ManyToOne(() => QAMission, mission => mission.tasks)
//   @JoinColumn({ name: "mission_id" })
//   mission: QAMission;

//   @Column({ name: "mission_id" })
//   missionId: string;

//   @OneToMany(() => QATaskSubmissions, submission => submission.task)
//   submissions: QATaskSubmissions[];
// }
