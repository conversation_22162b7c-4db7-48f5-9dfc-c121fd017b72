# HEC API Integration Guide

This guide provides comprehensive instructions for frontend developers on how to integrate with the HEC backend API. It covers authentication, error handling, common patterns, and provides an overview of all available integration guides.

## Table of Contents

1. [Overview](#overview)
2. [Integration Guides](#integration-guides)
3. [Base URL](#base-url)
4. [Authentication](#authentication)
5. [Request/Response Format](#requestresponse-format)
6. [Error Handling](#error-handling)
7. [Pagination](#pagination)
8. [Common API Patterns](#common-api-patterns)
9. [API Versioning](#api-versioning)
10. [Implementation Status](#implementation-status)

## Overview

The HEC backend provides a RESTful API for all frontend operations. The API follows consistent patterns for authentication, error handling, and data formatting to make integration straightforward. This documentation provides clear integration flows for all major features of the HEC platform to minimize the need for verbal communication between backend and frontend teams. These guides focus exclusively on API endpoints, data flows, and integration patterns without prescribing specific frontend implementation approaches.

## Integration Guides

The following integration guides are available for specific modules:

1. [Student Friendship Integration Flow](student-friendship-integration.md)
   - **API Flow Diagrams**: Clear sequence of API calls for each user action
   - **Request/Response Examples**: Complete examples with all required fields
   - **Error Handling**: Common error scenarios and how to handle them
   - **API Endpoints**: Detailed API specifications for friendship management

2. [Diary Module Integration Flow](diary-module-integration.md)
   - **API Flow Diagrams**: Step-by-step API sequences for diary operations
   - **Lifecycle Management**: Clear transitions between diary entry states
   - **Data Models**: Complete data structures for all diary-related entities
   - **API Endpoints**: Detailed API specifications for diary management

3. [Shop Integration Flow](shop-integration-flow.md)
   - **Architecture Overview**: Explanation of the modular shop system architecture
   - **Integration Flows**: Detailed sequence diagrams for shop browsing, cart, checkout, and owned items
   - **API Endpoints**: Detailed API specifications for shop functionality
   - **Code Examples**: Essential code examples for API integration

4. [Promotion Management Integration Flow](promotion-management-integration.md)
   - **API Flow Diagrams**: Step-by-step API sequences for promotion management and application
   - **Category-Based Promotions**: How to implement category-specific discounts
   - **Integration with Shop**: How promotions integrate with shop items, cart, and checkout
   - **API Endpoints**: Detailed API specifications for promotion management

5. [Plan Promotion Integration](plan-promotion-integration.md)
   - **API Flow Diagrams**: Step-by-step API sequences for plan promotion management
   - **Plan-Based Promotions**: How to implement plan-specific discounts
   - **Integration with Plans**: How promotions integrate with subscription plans
   - **API Endpoints**: Detailed API specifications for plan promotion management

6. [Automatic Tutor Assignment Integration Flow](tutor-assignment-integration.md)
   - **API Flow Diagrams**: Step-by-step API sequences for tutor assignment
   - **Notification Integration**: How tutor assignments trigger notifications
   - **Chat Integration**: How tutor assignments enable student-tutor communication
   - **API Endpoints**: Detailed API specifications for tutor assignment

7. [Authentication Integration Guide](../frontend/AUTHENTICATION_INTEGRATION.md)
   - **Authentication Flow**: Detailed authentication process
   - **Token Management**: How to handle JWT tokens
   - **Session Handling**: Managing user sessions

8. [Chat System Integration](../frontend/CHAT_FRONTEND_INTEGRATION.md)
   - **WebSocket Connection**: How to establish real-time connections
   - **Message Exchange**: Sending and receiving messages
   - **Conversation Management**: Managing chat conversations

9. [Notification System Integration](../frontend/NOTIFICATION_SYSTEM_INTEGRATION.md)
   - **Notification Channels**: Different notification delivery methods
   - **Real-time Notifications**: WebSocket-based notification delivery
   - **Notification Preferences**: Managing user notification settings

## Base URL

The base URL for all API requests depends on the environment:

- **Production**: `https://api.hec-education.com`
- **Staging**: `https://api-staging.hec-education.com`
- **Development**: `http://localhost:3012`

All API endpoints are prefixed with `/api`, for example: `https://api.hec-education.com/api/auth/login`

## Authentication

### JWT Authentication

The API uses JWT (JSON Web Token) for authentication. Most endpoints require a valid JWT token in the Authorization header.

To authenticate:

1. Call the login endpoint to obtain a token:

```javascript
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const data = await response.json();
const token = data.data.accessToken;
```

2. Include the token in subsequent requests:

```javascript
const response = await fetch('/api/users/profile', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### Token Refresh

Access tokens expire after a short period (default: 15 minutes). Use the refresh token to obtain a new access token:

```javascript
const response = await fetch('/api/auth/refresh', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    refreshToken: 'your-refresh-token'
  })
});

const data = await response.json();
const newToken = data.data.accessToken;
```

For more detailed information about authentication, refer to the [Authentication Integration Guide](../frontend/AUTHENTICATION_INTEGRATION.md).

## Request/Response Format

### Request Format

- **GET requests**: Parameters should be sent as query parameters
- **POST/PUT/PATCH requests**: Data should be sent as JSON in the request body
- **DELETE requests**: Parameters should be sent as query parameters or URL parameters

### Response Format

All API responses follow a consistent format:

```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data here
  }
}
```

For error responses:

```json
{
  "success": false,
  "message": "Error message",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ]
}
```

## Error Handling

### HTTP Status Codes

The API uses standard HTTP status codes:

- **200 OK**: Request succeeded
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Authentication required or failed
- **403 Forbidden**: Permission denied
- **404 Not Found**: Resource not found
- **422 Unprocessable Entity**: Validation errors
- **500 Internal Server Error**: Server error

### Client-Side Error Handling

Implement consistent error handling in your API requests:

```javascript
async function apiRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getToken()}`,
        ...options.headers
      }
    });

    const data = await response.json();

    if (!response.ok) {
      // Handle error response
      throw {
        status: response.status,
        message: data.message || 'An error occurred',
        errors: data.errors || []
      };
    }

    return data.data;
  } catch (error) {
    // Handle network errors or JSON parsing errors
    if (!error.status) {
      console.error('Network error:', error);
      throw { message: 'Network error. Please check your connection.' };
    }

    // Re-throw the error for handling by the caller
    throw error;
  }
}
```

## Pagination

Many endpoints that return lists of items support pagination.

### Request Parameters

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by
- `sortOrder`: Sort order ('ASC' or 'DESC')

Example:

```javascript
const response = await apiRequest('/api/users?page=2&limit=20&sortBy=createdAt&sortOrder=DESC');
```

### Response Format

Paginated responses include the items and pagination information:

```json
{
  "success": true,
  "message": "Users retrieved successfully",
  "data": {
    "items": [
      // Array of items
    ],
    "totalCount": 100,
    "totalItems": 100,
    "itemsPerPage": 20,
    "currentPage": 2,
    "totalPages": 5
  }
}
```

## Common API Patterns

### CRUD Operations

The API follows RESTful conventions for CRUD operations:

| Operation | HTTP Method | Endpoint | Description |
|-----------|-------------|----------|-------------|
| List | GET | `/api/resource` | Get a list of resources |
| Get | GET | `/api/resource/:id` | Get a single resource |
| Create | POST | `/api/resource` | Create a new resource |
| Update | PUT/PATCH | `/api/resource/:id` | Update a resource |
| Delete | DELETE | `/api/resource/:id` | Delete a resource |

### File Uploads

For file uploads, use `FormData`:

```javascript
async function uploadFile(file) {
  const formData = new FormData();
  formData.append('file', file);

  try {
    const response = await fetch('/api/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getToken()}`
      },
      body: formData
    });

    const data = await response.json();

    if (!response.ok) {
      throw {
        status: response.status,
        message: data.message || 'Upload failed'
      };
    }

    return data.data;
  } catch (error) {
    console.error('Upload error:', error);
    throw error;
  }
}
```

### Real-time Updates

For real-time features, the API uses Socket.io. See the [Chat System Integration](../frontend/CHAT_FRONTEND_INTEGRATION.md) guide for details.

## API Versioning

The API uses URL-based versioning. The current version is v1, which is the default and doesn't need to be specified in the URL.

If you need to use a specific version, include it in the URL:

```
/api/v1/users
/api/v2/users
```

When a new version is released, the previous version will be maintained for a deprecation period to allow for smooth transitions.

## Implementation Status

All the required backend functionality has been implemented, including:

- Student search and friendship management
- Diary follow requests and access control
- Shop management system with categories, items, cart, and checkout
- Student owned items management for purchased shop items
- Skin application system for diary and other features
- Automatic tutor assignment based on subscription plans
- Integration with the chat system for automatic conversation creation
- Integration with the notification system for all relevant events
- QR code generation for diary sharing
- Deep linking for all major features

## Next Steps

1. Frontend team to implement the UI based on these integration guides
2. Testing of the integrated system
3. Deployment to production

## Contact

For any questions or issues with the implementation, please contact the backend team.
