import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseFileRegistry } from './base-file-registry.entity';
import { Diary } from './diary.entity';

@Entity()
export class DiaryCoverRegistry extends BaseFileRegistry {
  @Column({ name: 'diary_id' })
  diaryId: string;

  @ManyToOne(() => Diary, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'diary_id' })
  diary: Diary;

  @Column({ name: 'user_id' })
  userId: string;

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      ...this.toSimpleObject(),
      diaryId: this.diaryId,
      userId: this.userId,
      diary: this.diary ? {
        id: this.diary.id,
        userId: this.diary.userId,
        defaultSkinId: this.diary.defaultSkinId
      } : null
    };
  }
}
