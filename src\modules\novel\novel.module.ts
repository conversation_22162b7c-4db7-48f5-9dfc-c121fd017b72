import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';

// Controllers
import { AdminNovelController } from './controllers/admin-novel.controller';
import { StudentNovelController } from './controllers/student-novel.controller';
import { TutorNovelController } from './controllers/tutor-novel.controller';

// Services
import { NovelService } from './services/novel.service';
import { NovelTopicService } from './services/novel-topic.service';
import { NovelEntryService } from './services/novel-entry.service';
import { NovelReviewService } from './services/novel-review.service';
import { NovelSuggestionService } from './services/novel-suggestion.service';
import { NovelAwardService } from './novel-award.service';

// Entities
import { Novel } from '../../database/entities/novel.entity';
import { NovelTopic } from '../../database/entities/novel-topic.entity';
import { NovelEntry } from '../../database/entities/novel-entry.entity';
import { NovelFeedback } from '../../database/entities/novel-feedback.entity';
import { NovelCorrection } from '../../database/entities/novel-correction.entity';
import { NovelSuggestion } from '../../database/entities/novel-suggestion.entity';
import { NovelModuleSkinPreference } from '../../database/entities/novel-module-skin-preference.entity';

// External entities
import { User } from '../../database/entities/user.entity';
import { DiarySkin } from '../../database/entities/diary-skin.entity';
import { StudentDiarySkin } from '../../database/entities/student-diary-skin.entity';
import { DiarySkinRegistry } from '../../database/entities/diary-skin-registry.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { Diary } from '../../database/entities/diary.entity';
import { StudentTutorMapping } from '../../database/entities/student-tutor-mapping.entity';
import { ShopSkinMapping } from '../../database/entities/shop-skin-mapping.entity';
import { ShopItem } from '../../database/entities/shop-item.entity';
import { StudentOwnedItem } from '../../database/entities/student-owned-item.entity';

// External modules
import { CommonModule } from '../../common/common.module';
import { NotificationModule } from '../notification/notification.module';
import { AwardsModule } from '../awards/awards.module';
import { DiaryModule } from '../diary/diary.module';
import { AuthModule } from '../auth/auth.module';
import { TutorMatchingModule } from '../tutor-matching/tutor-matching.module';

// External services
import { DiarySkinService } from '../diary/diary-skin.service';
import { SubscriptionFeatureGuard } from '../../common/guards/subscription-feature.guard';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Novel entities
      Novel,
      NovelTopic,
      NovelEntry,
      NovelFeedback,
      NovelCorrection,
      NovelSuggestion,
      NovelModuleSkinPreference,
      // External entities
      User,
      DiarySkin,
      StudentDiarySkin,
      DiarySkinRegistry,
      DiaryEntry,
      Diary,
      StudentTutorMapping,
      ShopSkinMapping,
      ShopItem,
      StudentOwnedItem
    ]),
    CommonModule,
    forwardRef(() => NotificationModule),
    forwardRef(() => AwardsModule),
    forwardRef(() => DiaryModule),
    forwardRef(() => AuthModule),
    forwardRef(() => TutorMatchingModule)
  ],
  controllers: [
    AdminNovelController,
    StudentNovelController,
    TutorNovelController
  ],
  providers: [
    NovelService,
    NovelTopicService,
    NovelEntryService,
    NovelReviewService,
    NovelSuggestionService,
    NovelAwardService,
    DiarySkinService,
    SubscriptionFeatureGuard,
    JwtService
  ],
  exports: [
    NovelService,
    NovelTopicService,
    NovelEntryService,
    NovelReviewService,
    NovelSuggestionService,
    NovelAwardService
  ]
})
export class NovelModule {}
