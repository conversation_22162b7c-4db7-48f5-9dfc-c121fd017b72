# HEC Notification System - Frontend Integration Guide

This guide provides instructions for frontend developers on how to integrate with the HEC Notification System, focusing on in-app notifications and real-time updates.

## Table of Contents

1. [Overview](#overview)
2. [Notification Types](#notification-types)
3. [In-App Notification Integration](#in-app-notification-integration)
4. [Notification Preferences](#notification-preferences)
5. [Real-time Notifications](#real-time-notifications)
6. [Handling Notification Actions](#handling-notification-actions)
7. [Testing Notifications](#testing-notifications)

## Overview

The HEC Notification System provides notifications to users through:

- **In-app notifications**: Displayed within the application UI
- **Email notifications**: Sent to the user's registered email address (handled by the backend)
- **Real-time updates**: Delivered via WebSocket for immediate notification

This guide focuses on implementing in-app notifications and real-time updates in the frontend application.

## Notification Types

The system supports various notification types, each with specific content and actions:

| Type | Description | Channels | Action |
|------|-------------|----------|--------|
| `DIARY_COMMENT` | New comment on a diary entry | In-app, Email | Open diary entry |
| `DIARY_REVIEWED` | Diary entry has been reviewed | In-app, Email | Open diary entry |
| `TUTOR_ASSIGNED` | New tutor assigned to student | In-app, Email | Open tutor profile |
| `TUTOR_GREETING` | Student set greeting message | In-app, Email | Open student profile |
| `NEW_MESSAGE` | New chat message received | In-app, Real-time | Open conversation |
| `PLAN_PURCHASED` | Subscription plan purchased | In-app, Email | Open subscription details |
| `ACCOUNT_VERIFIED` | Account email verified | In-app | None |
| `PASSWORD_RESET` | Password reset completed | In-app, Email | None |
| `ESSAY_SUBMITTED` | New essay submitted | In-app, Email | Open essay |
| `ESSAY_REVIEWED` | Essay has been reviewed | In-app, Email | Open essay review |
| `SYSTEM_ANNOUNCEMENT` | System-wide announcement | In-app, Email | Varies |

## In-App Notification Integration

### Notification API Endpoints

#### Get User Notifications

Retrieves notifications for the authenticated user.

```
GET /api/notifications
```

**Query Parameters:**
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 20)
- `read` (boolean, optional): Filter by read status
- `type` (string, optional): Filter by notification type

**Response:**
```json
{
  "success": true,
  "message": "Notifications retrieved successfully",
  "data": {
    "items": [
      {
        "id": "notif-123",
        "type": "DIARY_COMMENT",
        "title": "New comment on your diary",
        "message": "Tutor John has commented on your diary entry",
        "read": false,
        "data": {
          "diaryId": "diary-456",
          "commentId": "comment-789"
        },
        "createdAt": "2023-01-01T12:00:00Z"
      }
    ],
    "meta": {
      "totalItems": 45,
      "itemsPerPage": 20,
      "currentPage": 1,
      "totalPages": 3
    }
  }
}
```

#### Get Unread Count

Gets the count of unread notifications for the authenticated user.

```
GET /api/notifications/unread-count
```

**Response:**
```json
{
  "success": true,
  "data": {
    "count": 3
  }
}
```

#### Mark Notification as Read

Marks a notification as read.

```
PATCH /api/notifications/:id/read
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "notif-123",
    "read": true,
    "updatedAt": "2023-01-02T12:00:00Z"
  }
}
```

#### Mark All Notifications as Read

Marks all notifications as read for the authenticated user.

```
POST /api/notifications/read-all
```

**Response:**
```json
{
  "success": true,
  "data": {
    "count": 5,
    "message": "5 notifications marked as read"
  }
}
```

### Integration Flow

1. **Initial Load**:
   - When the application loads, fetch unread notification count
   - Display the count in the notification bell icon
   - Periodically refresh the count (e.g., every 60 seconds)

2. **Notification List**:
   - When the user clicks the notification bell, fetch the notifications list
   - Display notifications sorted by date (newest first)
   - Visually distinguish between read and unread notifications
   - Implement pagination for scrolling through older notifications

3. **Notification Interaction**:
   - When a user clicks on a notification, mark it as read
   - Navigate to the relevant page based on the notification type and data
   - Provide a way to mark all notifications as read

4. **Notification Badge**:
   - Display a badge with the unread count on the notification icon
   - Update the badge when new notifications arrive or are marked as read



## Notification Preferences

Users can customize which notifications they receive through each channel.

### Get Notification Preferences

```
GET /api/notifications/preferences
```

**Response:**
```json
{
  "success": true,
  "data": {
    "email": {
      "DIARY_COMMENT": true,
      "TUTOR_ASSIGNED": true,
      "NEW_MESSAGE": false
    },
    "push": {
      "DIARY_COMMENT": false,
      "TUTOR_ASSIGNED": true,
      "NEW_MESSAGE": true
    },
    "inApp": {
      "DIARY_COMMENT": true,
      "TUTOR_ASSIGNED": true,
      "NEW_MESSAGE": true
    }
  }
}
```

### Update Notification Preferences

```
PATCH /api/notifications/preferences
```

**Request Body:**
```json
{
  "email": {
    "NEW_MESSAGE": true
  },
  "push": {
    "DIARY_COMMENT": true
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "preferences": {
      "email": {
        "DIARY_COMMENT": true,
        "TUTOR_ASSIGNED": true,
        "NEW_MESSAGE": true
      },
      "push": {
        "DIARY_COMMENT": true,
        "TUTOR_ASSIGNED": true,
        "NEW_MESSAGE": true
      },
      "inApp": {
        "DIARY_COMMENT": true,
        "TUTOR_ASSIGNED": true,
        "NEW_MESSAGE": true
      }
    }
  }
}
```

### Integration Flow

1. **Preferences UI**:
   - Create a notification preferences screen
   - Group preferences by notification type and channel
   - Use toggles to enable/disable each preference

2. **Saving Preferences**:
   - Update preferences when the user changes a toggle
   - Show a success message when preferences are saved
   - Reflect the changes immediately in the UI

## Real-time Notifications

For real-time notifications, the system uses WebSockets via Socket.io. The HEC backend already has a Socket.io server running at `http://**************:3010`.

### WebSocket Connection

Connect to the notification WebSocket endpoint using the existing Socket.io connection:

```
Socket.io Server: http://**************:3010
Socket.io Namespace: /
```

### WebSocket Events

| Event | Direction | Description | Payload |
|-------|-----------|-------------|---------|
| `notification` | Server → Client | New notification received | Notification object |
| `notification_read` | Server → Client | Notification marked as read | `{ notificationId, read, updatedAt }` |
| `notification_count` | Server → Client | Unread count updated | `{ count }` |

### Integration Flow

1. **WebSocket Setup**:
   - Use the existing Socket.io connection that's already established for chat
   - Authentication is handled through the same JWT token used for chat
   - The connection is already configured to reconnect automatically if lost

2. **Event Handling**:
   - Listen for the `notification` event to receive new notifications
   - Update the notification list and badge count
   - Display a toast or alert for important notifications
   - Listen for `notification_read` events to update the UI

## Handling Notification Actions

Each notification type has associated actions that should be triggered when the user interacts with the notification.

### Action Mapping

| Notification Type | Action | Navigation Target |
|-------------------|--------|------------------|
| `DIARY_COMMENT` | Open diary entry | `/diary/:diaryId?commentId=:commentId` |
| `DIARY_REVIEWED` | Open diary entry | `/diary/:diaryId` |
| `TUTOR_ASSIGNED` | Open tutor profile | `/tutors/:tutorId` |
| `TUTOR_GREETING` | Open student profile | `/students/:studentId` |
| `NEW_MESSAGE` | Open conversation | `/messages/:conversationId` |
| `PLAN_PURCHASED` | Open subscription details | `/subscriptions/:subscriptionId` |
| `ESSAY_SUBMITTED` | Open essay | `/essays/:essayId` |
| `ESSAY_REVIEWED` | Open essay review | `/essays/:essayId/review` |

### Integration Flow

1. **Action Handler**:
   - Create a central notification action handler
   - Extract relevant IDs from the notification data
   - Navigate to the appropriate screen based on the notification type
   - Mark the notification as read

2. **Deep Linking**:
   - Support opening the app from push notifications
   - Handle deep links to navigate to the correct screen
   - Pass notification data through the deep link

## Testing Notifications

### Test Notification Endpoint

For development and testing purposes, you can use the test notification endpoint:

```
POST /api/admin/notifications/send
```

**Request Body:**
```json
{
  "userIds": ["user-123"],
  "type": "DIARY_COMMENT",
  "title": "Test Notification",
  "message": "This is a test notification",
  "data": {
    "diaryId": "diary-123",
    "commentId": "comment-456"
  },
  "channels": ["inApp", "email"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "sent": 1,
    "failed": 0,
    "notifications": [
      {
        "id": "notif-789",
        "userId": "user-123",
        "type": "DIARY_COMMENT",
        "title": "Test Notification",
        "createdAt": "2023-01-01T12:00:00Z"
      }
    ]
  }
}
```

### Testing Flow

1. **Development Testing**:
   - Use the admin notification endpoint to generate test notifications
   - Test different notification types and channels
   - Verify that notifications appear correctly in the UI
   - Check that real-time notifications are received via Socket.io

2. **Integration Testing**:
   - Test the complete notification flow from trigger to display
   - Verify that clicking the notification navigates to the correct screen
   - Test notification badge updates and read status synchronization

## Best Practices

1. **User Experience**:
   - Don't overwhelm users with too many notifications
   - Group similar notifications when possible
   - Provide clear and concise notification messages
   - Make it easy to manage notification preferences

2. **Performance**:
   - Fetch notifications efficiently with pagination
   - Update the UI incrementally when new notifications arrive
   - Minimize unnecessary API calls

3. **Reliability**:
   - Handle offline scenarios gracefully
   - Implement retry mechanisms for failed API calls
   - Sync notification state when the app comes online

4. **Security**:
   - Validate notification data before taking action
   - Don't include sensitive information in push notifications
   - Secure WebSocket connections with proper authentication

## Conclusion

The HEC Notification System provides a comprehensive solution for delivering timely updates to users across multiple channels. By following this integration guide, frontend developers can implement a robust notification experience that keeps users informed and engaged with the platform.

For any questions or issues, please contact the backend development team.
