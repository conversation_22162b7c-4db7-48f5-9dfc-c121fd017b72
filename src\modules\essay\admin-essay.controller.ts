import {
  Controller,
  Get,
  Post,
  Delete,
  Patch,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpCode
} from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiTags } from "@nestjs/swagger";
import { JwtAuthGuard } from "src/common/guards/jwt.guard";
import { AdminGuard } from "src/common/guards/admin.guard";
import { ApiResponse } from "src/common/dto/api-response.dto";
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from 'src/common/decorators/api-response.decorator';
import { PagedListDto } from "src/common/models/paged-list.dto";
import { EssayMissionService } from "./admin-essay.service";
import { PaginationDto } from "src/common/models/pagination.dto";


import {
  MissionResponseDto,
  MissionPaginationDto,
  CreateMissionDto,
  UpdateMissionDto,
  EssayTaskSubmissionDto,
  TaskSkinInfoResponseDto
} from "src/database/models/mission.dto";
import { EssaySubmissionService } from "./student-essay.service";

@ApiTags('admin-essay')
@ApiBearerAuth('JWT-auth')
@Controller('admin-essay')
export class MissionController {
  constructor(
    private readonly missionService: EssayMissionService,
    private readonly essaySubmissionService: EssaySubmissionService
  ){}

  @Get('missions')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: 'Get all missions' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    enum: ['ASC', 'DESC'],
    description: 'Sort order (asc or desc)',
  })
  @ApiOkResponseWithPagedListType(MissionResponseDto, 'Retrieved all missions')
  async findAll( @Query() paginationDto?: MissionPaginationDto): Promise<ApiResponse<PagedListDto<MissionResponseDto>>> {
    const result = await this.missionService.findAll(paginationDto);
    return ApiResponse.success(result, 'Retrieved all missions');
  }

  @Get('list')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: 'Get all submitted first essays list' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    enum: ['ASC', 'DESC'],
    description: 'Sort order (asc or desc)',
  })
  @ApiOkResponseWithPagedListType(EssayTaskSubmissionDto, 'Retrieved all submitted first submitted essays list')
  async findAllSubmittedEssays(
    @Query() paginationDto?: PaginationDto
  ): Promise<ApiResponse<PagedListDto<EssayTaskSubmissionDto>>> {
    const result = await this.missionService.findAllSubmittedEssays(paginationDto);
    return ApiResponse.success(result, 'Retrieved all submitted first essays list');
  }

  @Get('list/:id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: 'Get a specific submitted essay by ID' })
  @ApiParam({ name: 'id', description: 'ID of the submitted essay to retrieve', type: String })
  @ApiOkResponseWithType(EssayTaskSubmissionDto, 'Retrieved submitted essay successfully')
  @ApiErrorResponse(404, 'Submitted essay not found')
  async findOneSubmittedEssay(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponse<EssayTaskSubmissionDto>> {
    const result = await this.missionService.findSubmittedEssayById(id);
    return ApiResponse.success(result, 'Retrieved submitted essay successfully');
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: 'Get a specific essay mission by ID' })
  @ApiParam({ name: 'id', description: 'ID of the mission to retrieve', type: String })
  @ApiOkResponseWithType(MissionResponseDto, 'Retrieved mission successfully')
  @ApiErrorResponse(404, 'Mission not found')
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<MissionResponseDto>> {
    const result = await this.missionService.findById(id);
    return ApiResponse.success(result, 'Retrieved mission successfully');
  }

  @Post('create')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: 'Create a new essay mission with tasks' })
  @ApiBody({
    type: CreateMissionDto,
    description: 'Essay mission creation data with tasks',
    examples: {
      example1: {
        value: {
          timeFrequency: 'weekly',
          tasks: [
            {
              title: 'Writing Task 1',
              description: 'Write an essay about your favorite book',
              wordLimitMinimum: 500,
              wordLimitMaximum: 1000,
              timePeriodUnit: 1,
              deadline: 7,
              instructions: 'Follow MLA format and include citations',
              metaData: {
                week: '1',
                month: '4',
                year: '2025'
              }
            }
          ]
        }
      }
    }
  })
  @ApiOkResponseWithType(MissionResponseDto, 'Essay mission created successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async create(
    @Body() createMissionDto: CreateMissionDto
  ): Promise<ApiResponse<MissionResponseDto>> {
    const result = await this.missionService.create(createMissionDto);
    return ApiResponse.success(
      result,
      'Essay mission created successfully',
      201
    );
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({ summary: 'Update an existing essay mission' })
  @ApiParam({ name: 'id', description: 'ID of the mission to update', type: String })
  @ApiBody({ type: UpdateMissionDto, description: 'Data to update the mission. Include task IDs for updating existing tasks.' })
  @ApiOkResponseWithType(MissionResponseDto, 'Essay mission updated successfully')
  @ApiErrorResponse(400, 'Invalid input data or validation error')
  @ApiErrorResponse(404, 'Mission not found')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMissionDto: UpdateMissionDto
  ): Promise<ApiResponse<MissionResponseDto>> {
    const result = await this.missionService.update(id, updateMissionDto);
    return ApiResponse.success(
      result,
      'Essay mission updated successfully'
    );
  }

  @Delete(':id/soft')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @HttpCode(204)
  @ApiOperation({ summary: 'Soft delete an essay mission and its tasks' })
  @ApiErrorResponse(400, 'Failed to soft delete mission')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Mission not found')
  async softDelete(@Param('id') id: string): Promise<ApiResponse<void>> {
    await this.missionService.softDelete(id);
    return ApiResponse.success(null, 'Mission soft deleted successfully');
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @HttpCode(204)
  @ApiOperation({ summary: 'Permanently delete an essay mission and its tasks' })
  @ApiErrorResponse(400, 'Failed to delete mission')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Mission not found')
  async hardDelete(@Param('id') id: string): Promise<ApiResponse<void>> {
    await this.missionService.hardDelete(id);
    return ApiResponse.success(null, 'Mission deleted successfully');
  }

  @Get('skins/:taskId/:userId')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiOperation({
    summary: 'Get skin information for a specific task',
    description: 'Retrieves the skin configuration for a task, including whether it uses default or task-specific skin.'
  })
  @ApiParam({
    name: 'taskId',
    type: String,
    description: 'The ID of the task to get skin information for',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiParam({
    name: 'userId',
    type: String,
    description: 'The ID of the user to get skin information for',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @ApiOkResponseWithType(TaskSkinInfoResponseDto, 'Task skin information retrieved successfully')
  @ApiErrorResponse(400, 'Invalid task ID')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Task not found')
  @ApiErrorResponse(500, 'Internal server error')
  async getTaskSkinInfo(
    @Param('taskId') taskId: string,
    @Param('userId') userId: string
  ): Promise<ApiResponse<TaskSkinInfoResponseDto>> {
    const result = await this.essaySubmissionService.getTaskSkinInfo(taskId, userId);
    return ApiResponse.success(result, 'Task skin information retrieved successfully');
  }

}