import { Injectable } from '@nestjs/common';
import CommonEmailService from '../../common/services/email.service';

@Injectable()
export class EmailService {
  constructor(private readonly emailServiceCommon: CommonEmailService) {}

  async sendVerificationLink(to: string, token: string): Promise<boolean> {
    return this.emailServiceCommon.sendVerificationLink(to, token);
  }

  async sendPasswordResetLink(to: string, token: string): Promise<boolean> {
    return this.emailServiceCommon.sendPasswordResetLink(to, token);
  }

  async sendUserId(to: string, userId: string): Promise<boolean> {
    return this.emailServiceCommon.sendUserId(to, userId);
  }

  async sendTutorApprovalEmail(to: string, name: string): Promise<boolean> {
    return this.emailServiceCommon.sendTutorApprovalEmail(to, name);
  }

  async sendUserCreationEmail(to: string, userId: string, temporaryPassword: string, userType: string): Promise<boolean> {
    return this.emailServiceCommon.sendUserCreationEmail(to, userId, temporaryPassword, userType);
  }

  async sendTutorRejectionEmail(to: string, name: string, reason: string): Promise<boolean> {
    return this.emailServiceCommon.sendTutorRejectionEmail(to, name, reason);
  }

  async sendPasswordChangeNotification(to: string, name: string, changeTime: Date, ipAddress?: string, userAgent?: string): Promise<boolean> {
    return this.emailServiceCommon.sendPasswordChangeNotification(to, name, changeTime, ipAddress, userAgent);
  }

  async sendGenericNotificationEmail(
    to: string,
    name: string,
    title: string,
    message: string,
    htmlContent?: string
  ): Promise<boolean> {
    const subject = title;
    const text = message;
    const html = htmlContent || `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="color: #333;">${title}</h2>
        </div>
        <div style="margin-bottom: 20px;">
          <p>Hello ${name || 'there'},</p>
          <p>${message}</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
          <p>This is an automated message from the HEC system.</p>
          <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
        </div>
      </div>
    `;

    return this.emailServiceCommon.sendEmail(to, subject, text, html);
  }

  async sendDiarySubmissionEmail(
    to: string,
    name: string,
    title: string,
    message: string,
    htmlContent?: string
  ): Promise<boolean> {
    const subject = title || 'New Diary Submission';
    const text = message || 'A new diary entry has been submitted for review.';
    const html = htmlContent || `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="color: #333;">${title || 'New Diary Submission'}</h2>
        </div>
        <div style="margin-bottom: 20px;">
          <p>Hello ${name || 'there'},</p>
          <p>${message || 'A new diary entry has been submitted for review.'}</p>
          <p>Please log in to the HEC system to review the submission.</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
          <p>This is an automated message from the HEC system.</p>
          <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
        </div>
      </div>
    `;

    return this.emailServiceCommon.sendEmail(to, subject, text, html);
  }

  async sendDiaryReviewEmail(
    to: string,
    name: string,
    title: string,
    message: string,
    htmlContent?: string
  ): Promise<boolean> {
    const subject = title || 'Your Diary Entry Has Been Reviewed';
    const text = message || 'Your diary entry has been reviewed by a tutor.';
    const html = htmlContent || `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="color: #333;">${title || 'Your Diary Entry Has Been Reviewed'}</h2>
        </div>
        <div style="margin-bottom: 20px;">
          <p>Hello ${name || 'there'},</p>
          <p>${message || 'Your diary entry has been reviewed by a tutor.'}</p>
          <p>Please log in to the HEC system to view the feedback.</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
          <p>This is an automated message from the HEC system.</p>
          <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
        </div>
      </div>
    `;

    return this.emailServiceCommon.sendEmail(to, subject, text, html);
  }

  async sendTutorAssignmentEmail(
    to: string,
    name: string,
    title: string,
    message: string,
    htmlContent?: string
  ): Promise<boolean> {
    const subject = title || 'Tutor Assignment Notification';
    const text = message || 'You have been assigned a tutor in the HEC system.';
    const html = htmlContent || `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 20px;">
          <h2 style="color: #333;">${title || 'Tutor Assignment Notification'}</h2>
        </div>
        <div style="margin-bottom: 20px;">
          <p>Hello ${name || 'there'},</p>
          <p>${message || 'You have been assigned a tutor in the HEC system.'}</p>
          <p>Please log in to the HEC system to view your tutor's details.</p>
        </div>
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
          <p>This is an automated message from the HEC system.</p>
          <p>&copy; ${new Date().getFullYear()} HEC. All rights reserved.</p>
        </div>
      </div>
    `;

    return this.emailServiceCommon.sendEmail(to, subject, text, html);
  }
}
