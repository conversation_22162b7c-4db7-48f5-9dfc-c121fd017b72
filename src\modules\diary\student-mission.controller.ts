import { Controller, Get, Post, Put, Body, Param, Query, UseGuards, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { SubscriptionFeatureGuard } from '../../common/guards/subscription-feature.guard';
import { RequireFeature } from '../../common/decorators/require-feature.decorator';
import { FeatureType } from '../../database/entities/plan-feature.entity';
import { DiaryMissionService } from './diary-mission.service';
import { MissionDiaryEntryService } from './mission-diary-entry.service';
import {
  DiaryMissionResponseDto,
  MissionFilterDto
} from '../../database/models/diary-mission.dto';
import {
  CreateMissionDiaryEntryDto,
  UpdateMissionDiaryEntryDto,
  MissionDiaryEntryResponseDto,
  MissionEntryFilterDto
} from '../../database/models/mission-diary-entry.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PaginationDto } from '../../common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';

@ApiTags('Diary Missions')
@Controller('diary/missions')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, StudentGuard, SubscriptionFeatureGuard)
@RequireFeature(FeatureType.HEC_USER_DIARY)
export class StudentMissionController {
  constructor(
    private readonly diaryMissionService: DiaryMissionService,
    private readonly missionDiaryEntryService: MissionDiaryEntryService
  ) {}

  // ===== Mission Viewing Endpoints =====

  @Get('/entries')
  @ApiOperation({
    summary: 'Get all mission diary entries for the student',
    description: 'Retrieves a paginated list of all diary entries created by the student for missions. Supports filtering by mission, status, and date range.'
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'missionId', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'createdAtFrom', required: false, type: String })
  @ApiQuery({ name: 'createdAtTo', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'] })
  @ApiOkResponseWithPagedListType(MissionDiaryEntryResponseDto, 'Entries retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  async getStudentMissionEntries(
    @Req() req: any,
    @Query() params: any
  ): Promise<ApiResponse<PagedListDto<MissionDiaryEntryResponseDto>>> {
    // Extract pagination parameters
    const paginationDto: PaginationDto = {
      page: params.page ? parseInt(params.page) : 1,
      limit: params.limit ? parseInt(params.limit) : 10,
      sortBy: params.sortBy,
      sortDirection: params.sortDirection
    };

    // Extract filter parameters
    const filterDto: MissionEntryFilterDto = {
      missionId: params.missionId,
      status: params.status,
      createdAtFrom: params.createdAtFrom,
      createdAtTo: params.createdAtTo
    };

    const entries = await this.missionDiaryEntryService.getStudentMissionEntries(req.user.id, filterDto, paginationDto);
    return ApiResponse.success(entries, 'Entries retrieved successfully');
  }

  @Get('/today')
  @ApiOperation({ summary: "Get today's featured mission" })
  @ApiOkResponseWithType(DiaryMissionResponseDto, "Today's mission retrieved successfully")
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'No active missions available')
  async getTodaysMission(
    @Req() req: any
  ): Promise<ApiResponse<DiaryMissionResponseDto>> {
    const mission = await this.diaryMissionService.getTodaysMission(req.user.id);
    return ApiResponse.success(mission, "Today's mission retrieved successfully");
  }

  @Get()
  @ApiOperation({ summary: 'Get all available diary missions for the student' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'publishDateFrom', required: false, type: String })
  @ApiQuery({ name: 'publishDateTo', required: false, type: String })
  @ApiQuery({ name: 'createdBy', required: false, type: String, description: 'Filter by tutor ID' })
  @ApiQuery({ name: 'isActive', required: false, type: Boolean })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'] })
  @ApiOkResponseWithPagedListType(DiaryMissionResponseDto, 'Missions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  async getStudentMissions(
    @Req() req: any,
    @Query() params: any
  ): Promise<ApiResponse<PagedListDto<DiaryMissionResponseDto>>> {
    // Extract pagination parameters
    const paginationDto: PaginationDto = {
      page: params.page ? parseInt(params.page) : 1,
      limit: params.limit ? parseInt(params.limit) : 10,
      sortBy: params.sortBy,
      sortDirection: params.sortDirection
    };

    // Extract filter parameters
    const filterDto: MissionFilterDto = {
      isActive: params.isActive === 'true' ? true : params.isActive === 'false' ? false : undefined,
      publishDateFrom: params.publishDateFrom,
      publishDateTo: params.publishDateTo,
      createdBy: params.createdBy
    };

    const missions = await this.diaryMissionService.getStudentMissions(req.user.id, filterDto, paginationDto);
    return ApiResponse.success(missions, 'Missions retrieved successfully');
  }

  @Get('/:id')
  @ApiOperation({ summary: 'Get a specific diary mission' })
  @ApiParam({ name: 'id', description: 'Mission ID' })
  @ApiOkResponseWithType(DiaryMissionResponseDto, 'Mission retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Mission not found')
  async getMission(
    @Req() req: any,
    @Param('id') id: string
  ): Promise<ApiResponse<DiaryMissionResponseDto>> {
    const mission = await this.diaryMissionService.getStudentMission(id, req.user.id);
    return ApiResponse.success(mission, 'Mission retrieved successfully');
  }

  // ===== Mission Entry Management Endpoints =====

  @Get('/entries/mission/:missionId')
  @ApiOperation({
    summary: 'Get or create a mission diary entry by mission ID',
    description: 'Gets an existing mission entry or creates a new empty one if it does not exist'
  })
  @ApiParam({ name: 'missionId', description: 'Mission ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Entry retrieved or created successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Mission not found')
  async getOrCreateMissionEntry(
    @Req() req: any,
    @Param('missionId') missionId: string
  ): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.getOrCreateMissionEntry(missionId, req.user.id);
    return ApiResponse.success(entry, 'Entry retrieved or created successfully');
  }

  @Post('/entries')
  @ApiOperation({ summary: 'Create a new mission diary entry' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Entry created successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  async createMissionEntry(
    @Req() req: any,
    @Body() createDto: CreateMissionDiaryEntryDto
  ): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.createMissionEntry(req.user.id, createDto);
    return ApiResponse.success(entry, 'Entry created successfully');
  }

  @Put('/entries/:id')
  @ApiOperation({ summary: 'Update a mission diary entry' })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Entry updated successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async updateMissionEntry(
    @Req() req: any,
    @Param('id') id: string,
    @Body() updateDto: UpdateMissionDiaryEntryDto
  ): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.updateMissionEntry(id, req.user.id, updateDto);
    return ApiResponse.success(entry, 'Entry updated successfully');
  }

  @Get('/entries/:id')
  @ApiOperation({ summary: 'Get a specific mission diary entry' })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Entry retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async getMissionEntry(
    @Param('id') id: string
  ): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.getMissionEntry(id);
    return ApiResponse.success(entry, 'Entry retrieved successfully');
  }

  @Post('/entries/:id/submit')
  @ApiOperation({
    summary: 'Submit a mission diary entry for review',
    description: 'Changes the status of a diary entry from draft to submitted, making it available for tutor review. No request body is needed as validation is performed using the saved entry data. The entry must meet word count requirements (minimum and maximum if specified) to be submitted.'
  })
  @ApiParam({ name: 'id', description: 'Entry ID', type: 'string', format: 'uuid' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Entry submitted successfully')
  @ApiErrorResponse(400, 'Bad request - Entry must meet word count requirements')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - You can only submit your own entries')
  @ApiErrorResponse(404, 'Entry not found')
  async submitMissionEntry(
    @Req() req: any,
    @Param('id') id: string,
    @Body() submitDto: UpdateMissionDiaryEntryDto
  ): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.submitMissionEntry(id, req.user.id, submitDto);
    return ApiResponse.success(entry, 'Entry submitted successfully');
  }
}
