import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveDiscountCategory1746600000000 implements MigrationInterface {
    name = 'RemoveDiscountCategory1746600000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // Drop the discount_category column
            await queryRunner.query(`ALTER TABLE "promotion" DROP COLUMN "discount_category"`);
            
            console.log('Successfully removed discount_category column');
        } catch (error) {
            console.error('Migration error:', error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        try {
            // Add back the discount_category column
            await queryRunner.query(`ALTER TABLE "promotion" ADD "discount_category" character varying`);
            
            console.log('Successfully added back discount_category column');
        } catch (error) {
            console.error('Migration rollback error:', error);
            throw error;
        }
    }
}
