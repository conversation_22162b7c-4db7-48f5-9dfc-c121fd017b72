import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne, ManyToOne } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { DiaryEntry } from './diary-entry.entity';
import { DiarySkin } from './diary-skin.entity';
import { DiaryCoverRegistry } from './diary-cover-registry.entity';

@Entity()
export class Diary extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @OneToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'default_skin_id' })
  defaultSkinId: string;

  @ManyToOne(() => DiarySkin)
  @JoinColumn({ name: 'default_skin_id' })
  defaultSkin: DiarySkin;

  @OneToMany(() => DiaryEntry, entry => entry.diary)
  entries: DiaryEntry[];

  @OneToMany(() => DiaryCoverRegistry, cover => cover.diary)
  coverPhotos: DiaryCoverRegistry[];

  @Column({ name: 'tutor_greeting', nullable: true, type: 'text' })
  tutorGreeting: string;
}
