import { Controller, Get, Post, Body, Param, Put, Query, UseGuards, ValidationPipe, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { RewardPointSettingService } from './reward-point-setting.service';
import { CreateRewardPointSettingDto, UpdateRewardPointSettingDto, RewardPointSettingResponseDto } from '../../database/models/reward-point-setting.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { AdminGuard } from '../../common/guards/admin.guard';

@ApiTags('Reward Point Settings')
@Controller('admin/reward-point-settings')
@ApiBearerAuth('JWT-auth')
@UseGuards(AdminGuard)
export class RewardPointSettingController {
  constructor(private readonly rewardPointSettingService: RewardPointSettingService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a reward point setting (Admin only)',
    description: 'Creates a new reward point setting.'
  })
  @ApiBody({
    type: CreateRewardPointSettingDto,
    description: 'Reward point setting creation data'
  })
  @ApiOkResponseWithType(RewardPointSettingResponseDto, 'Reward point setting created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async createRewardPointSetting(
    @Body() createRewardPointSettingDto: CreateRewardPointSettingDto
  ): Promise<ApiResponse<RewardPointSettingResponseDto>> {
    try {
      console.log('Received request to create reward point setting:', createRewardPointSettingDto);

      // Manual validation and conversion
      if (!createRewardPointSettingDto.name) {
        throw new BadRequestException('Name is required');
      }

      if (createRewardPointSettingDto.conversionRate === undefined || createRewardPointSettingDto.conversionRate === null) {
        throw new BadRequestException('Conversion rate is required');
      }

      // Convert conversionRate to number if it's a string
      if (typeof createRewardPointSettingDto.conversionRate === 'string') {
        const parsedRate = parseFloat(createRewardPointSettingDto.conversionRate);
        if (isNaN(parsedRate)) {
          throw new BadRequestException('Conversion rate must be a valid number');
        }
        createRewardPointSettingDto.conversionRate = parsedRate;
      }

      // Convert isActive to boolean if it's a string
      if (typeof createRewardPointSettingDto.isActive === 'string') {
        const isActiveStr = String(createRewardPointSettingDto.isActive).toLowerCase();
        createRewardPointSettingDto.isActive = isActiveStr === 'true';
      }

      const rewardPointSetting = await this.rewardPointSettingService.createRewardPointSetting(createRewardPointSettingDto);
      return ApiResponse.success(rewardPointSetting, 'Reward point setting created successfully');
    } catch (error) {
      console.error('Error creating reward point setting:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Failed to create reward point setting: ${error.message}`);
    }
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update a reward point setting (Admin only)',
    description: 'Updates an existing reward point setting.'
  })
  @ApiBody({
    type: UpdateRewardPointSettingDto,
    description: 'Reward point setting update data'
  })
  @ApiOkResponseWithType(RewardPointSettingResponseDto, 'Reward point setting updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Reward point setting not found')
  async updateRewardPointSetting(
    @Param('id') id: string,
    @Body() updateRewardPointSettingDto: UpdateRewardPointSettingDto
  ): Promise<ApiResponse<RewardPointSettingResponseDto>> {
    const rewardPointSetting = await this.rewardPointSettingService.updateRewardPointSetting(id, updateRewardPointSettingDto);
    return ApiResponse.success(rewardPointSetting, 'Reward point setting updated successfully');
  }

  @Put(':id/activate')
  @ApiOperation({
    summary: 'Set a reward point setting as active (Admin only)',
    description: 'Sets a reward point setting as active and deactivates all others.'
  })
  @ApiOkResponseWithType(RewardPointSettingResponseDto, 'Reward point setting activated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Reward point setting not found')
  async setRewardPointSettingActive(@Param('id') id: string): Promise<ApiResponse<RewardPointSettingResponseDto>> {
    const rewardPointSetting = await this.rewardPointSettingService.setRewardPointSettingActive(id);
    return ApiResponse.success(rewardPointSetting, 'Reward point setting activated successfully');
  }

  @Get()
  @ApiOperation({
    summary: 'Get all reward point settings (Admin only)',
    description: 'Gets a list of all reward point settings with pagination.'
  })
  @ApiOkResponseWithPagedListType(RewardPointSettingResponseDto, 'Reward point settings retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getRewardPointSettings(
    @Query(new ValidationPipe({
      transform: true,
      transformOptions: { enableImplicitConversion: true },
      forbidNonWhitelisted: false,
      whitelist: true
    })) paginationDto: PaginationDto
  ): Promise<ApiResponse<PagedListDto<RewardPointSettingResponseDto>>> {
    const rewardPointSettings = await this.rewardPointSettingService.getRewardPointSettings(paginationDto);
    return ApiResponse.success(rewardPointSettings, 'Reward point settings retrieved successfully');
  }

  @Get('active')
  @ApiOperation({
    summary: 'Get the active reward point setting (Admin only)',
    description: 'Gets the currently active reward point setting.'
  })
  @ApiOkResponseWithType(RewardPointSettingResponseDto, 'Active reward point setting retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'No active reward point setting found')
  async getActiveRewardPointSetting(): Promise<ApiResponse<RewardPointSettingResponseDto>> {
    const rewardPointSetting = await this.rewardPointSettingService.getActiveRewardPointSetting();
    return ApiResponse.success(rewardPointSetting, 'Active reward point setting retrieved successfully');
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a reward point setting by ID (Admin only)',
    description: 'Gets a reward point setting by its ID.'
  })
  @ApiOkResponseWithType(RewardPointSettingResponseDto, 'Reward point setting retrieved successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Reward point setting not found')
  async getRewardPointSettingById(@Param('id') id: string): Promise<ApiResponse<RewardPointSettingResponseDto>> {
    const rewardPointSetting = await this.rewardPointSettingService.getRewardPointSettingById(id);
    return ApiResponse.success(rewardPointSetting, 'Reward point setting retrieved successfully');
  }
}
