import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

export enum NovelSuggestionStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

@Entity()
export class NovelSuggestion extends AuditableBaseEntity {
  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({ name: 'student_id' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @Column({
    name: 'status',
    type: 'enum',
    enum: NovelSuggestionStatus,
    default: NovelSuggestionStatus.PENDING
  })
  status: NovelSuggestionStatus;
}
