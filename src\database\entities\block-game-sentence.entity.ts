import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { BlockGame } from './block-game.entity';

@Entity()
export class BlockGameSentence extends AuditableBaseEntity {
  @Column({ name: 'block_game_id' })
  blockGameId: string;

  @Column({ name: 'starting_part', type: 'text' })
  startingPart: string;

  @Column({ name: 'expanding_part', type: 'text' })
  expandingPart: string;

  @Column({ name: 'sentence_order' })
  sentenceOrder: number;

  // Relationships
  @ManyToOne(() => BlockGame, (blockGame) => blockGame.sentences, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'block_game_id' })
  blockGame: BlockGame;
}
