import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPriceEquivalentToRewardPointColumn1746200000000 implements MigrationInterface {
    name = 'AddPriceEquivalentToRewardPointColumn1746200000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shop_item" ADD "price_equivalent_to_reward_point" decimal(10,2) NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shop_item" DROP COLUMN "price_equivalent_to_reward_point"`);
    }
}
