# HEC Backend Backlog Summary

This document provides a summary of all epics, features, and tasks identified in the API documentation. Use this to populate your backlog with properly organized items.

## Epics

### Authentication Module
1. **User Authentication**
2. **User Registration**
3. **Account Recovery**
4. **Email Verification**

### Users Module
1. **User Management**
2. **Profile Management**
3. **Role Management**

### Plans Module
1. **Plan Management**
2. **Plan Feature Management**
3. **User Subscription Management**

### Diary Module
1. **Diary Management**
2. **Diary Entry Management**
3. **Diary Skin Management**
4. **Tutor Feedback and Evaluation**
5. **Diary Awards**

### Tutor Approval Module
1. **Tutor Approval Workflow**
2. **Tutor Approval Management**

### Common Services
1. **Logging and Monitoring**
2. **Email Services**
3. **File Management**
4. **Security and Authorization**

## Features

### Authentication Module
1. **User Registration**
   - Register as student or tutor
   - Email verification
   - Tutor approval workflow

2. **User Login**
   - Unified login for all user types
   - Role selection
   - Remember me functionality
   - Return URL support

3. **Account Recovery**
   - Forgot password
   - Forgot user ID
   - Password reset

4. **Email Verification**
   - Email verification on registration
   - Resend verification email

### Users Module
1. **User Management**
   - Create admin users
   - Create tutor users
   - Create student users
   - List all users (admin only)

2. **Profile Management**
   - View user profile
   - Update user profile
   - Upload profile picture
   - Calculate age from date of birth

3. **Role Management**
   - Assign roles to users
   - View user roles

### Plans Module
1. **Plan Management**
   - Create subscription plans
   - Update subscription plans
   - Delete subscription plans
   - List all plans
   - View plan details

2. **Plan Feature Management**
   - Create plan features
   - List all plan features
   - Associate features with plans

3. **User Subscription Management**
   - Subscribe to plans
   - View user subscriptions
   - Auto-renewal of subscriptions
   - Subscription expiry handling

### Diary Module
1. **Diary Management**
   - Create and retrieve student diaries
   - Set default diary skin

2. **Diary Entry Management**
   - Create diary entries
   - Update diary entries
   - Submit entries for review
   - View entry history

3. **Diary Skin Management**
   - Create global skins (admin)
   - Create student-specific skins
   - Apply skins to diary entries

4. **Tutor Feedback and Evaluation**
   - Review diary entries
   - Provide feedback
   - Assign scores
   - Lock entries during review

5. **Diary Awards**
   - Monthly/weekly awards for top scorers
   - Award calculation and distribution

### Tutor Approval Module
1. **Tutor Approval Workflow**
   - Automatic creation of approval request on tutor registration
   - Email notification to admin on new tutor registration
   - Email notification to tutor on approval/rejection

2. **Tutor Approval Management**
   - View all approval requests
   - View pending approval requests
   - Approve tutor with notes
   - Reject tutor with reason

### Common Services
1. **Logging and Monitoring**
   - Centralized logging
   - Audit logging
   - Operation logging
   - Error handling

2. **Email Services**
   - Transactional emails
   - Email templates
   - Email verification
   - Password reset

3. **File Management**
   - File uploads
   - File validation
   - File storage
   - File serving

4. **Security and Authorization**
   - JWT authentication
   - Role-based access control
   - Request validation
   - Response transformation

## Tasks

### Authentication Module
1. **Implement User Registration API**
   - Create registration endpoint
   - Validate input data
   - Handle email verification
   - Implement role assignment

2. **Implement User Login API**
   - Create login endpoint
   - Implement JWT token generation
   - Handle role selection
   - Implement remember me functionality

3. **Implement Account Recovery APIs**
   - Create forgot password endpoint
   - Create reset password endpoint
   - Create forgot user ID endpoint

4. **Implement Email Verification**
   - Create verify email endpoint
   - Create resend verification endpoint
   - Implement token generation and validation

### Users Module
1. **Implement User Management APIs**
   - Create endpoints for creating different user types
   - Implement user listing with pagination
   - Add role assignment functionality

2. **Implement Profile Management APIs**
   - Create profile retrieval endpoint
   - Create profile update endpoint
   - Implement profile picture upload
   - Add age calculation functionality

3. **Implement Role Management**
   - Create role assignment endpoint
   - Implement role validation
   - Add role-based access control

### Plans Module
1. **Implement Plan Management APIs**
   - Create endpoints for CRUD operations on plans
   - Implement plan listing and retrieval
   - Add validation for plan operations

2. **Implement Plan Feature Management APIs**
   - Create endpoints for CRUD operations on plan features
   - Implement feature listing
   - Add association of features with plans

3. **Implement User Subscription Management**
   - Create subscription endpoint
   - Implement subscription listing
   - Add auto-renewal functionality
   - Implement subscription expiry handling

### Diary Module
1. **Implement Diary Management APIs**
   - Create diary creation and retrieval endpoints
   - Implement default skin assignment
   - Add diary ownership validation

2. **Implement Diary Entry Management APIs**
   - Create entry CRUD endpoints
   - Implement entry submission workflow
   - Add entry status management

3. **Implement Diary Skin Management APIs**
   - Create skin CRUD endpoints for admins
   - Create student skin endpoints
   - Implement skin application to entries

4. **Implement Tutor Feedback and Evaluation APIs**
   - Create review workflow endpoints
   - Implement feedback submission
   - Add scoring functionality
   - Implement review locking mechanism

5. **Implement Diary Awards System**
   - Create award calculation logic
   - Implement scheduled award distribution
   - Add award notification

### Tutor Approval Module
1. **Implement Tutor Approval Workflow**
   - Create approval request on tutor registration
   - Implement email notifications
   - Add approval status check on login

2. **Implement Tutor Approval Management APIs**
   - Create endpoints for listing approval requests
   - Implement approval endpoint
   - Implement rejection endpoint
   - Add admin notes and rejection reason

### Common Services
1. **Implement Logging Services**
   - Create logger service
   - Implement audit logging
   - Add operation logging
   - Configure log levels and formats

2. **Implement Email Services**
   - Create email service
   - Implement email templates
   - Add email sending functionality
   - Configure email provider

3. **Implement File Management**
   - Create file upload service
   - Implement file validation
   - Add file storage functionality
   - Configure file serving

4. **Implement Security and Authorization**
   - Create JWT auth service
   - Implement guards
   - Add interceptors
   - Configure security settings
