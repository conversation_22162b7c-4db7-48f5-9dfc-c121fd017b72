import * as bcrypt from 'bcrypt';

async function testBcrypt() {
  const plainPassword = 'Admin@123';

  console.log('Testing bcrypt hashing and comparison...');

  // Hash the password
  const hashedPassword = await bcrypt.hash(plainPassword, 10);
  console.log('Hashed password:', hashedPassword);

  // Compare the plain password with the hash (should be true)
  const isMatch1 = await bcrypt.compare(plainPassword, hashedPassword);
  console.log('Plain password matches hash:', isMatch1);

  // Compare a wrong password with the hash (should be false)
  const isMatch2 = await bcrypt.compare('WrongPassword', hashedPassword);
  console.log('Wrong password matches hash:', isMatch2);

  // Test with a known hash from the database
  const knownHash = '$2b$10$2Qqz3Kpe88Dv6wAqdpXsSeJreu0lf9eoVtYmt8xurNIYTT1r8NR2y';
  const isMatch3 = await bcrypt.compare(plainPassword, knownHash);
  console.log('Plain password matches known hash:', isMatch3);

  // Generate multiple hashes for the same password to show they're different
  console.log('\nGenerating multiple hashes for the same password:');
  for (let i = 0; i < 5; i++) {
    const hash = await bcrypt.hash(plainPassword, 10);
    console.log(`Hash ${i+1}:`, hash);
    const matches = await bcrypt.compare(plainPassword, hash);
    console.log(`Verification result ${i+1}:`, matches);
  }
}

testBcrypt().catch(console.error);
