import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as path from 'path';
import LoggerService from './logger.service';

interface SignedUrlPayload {
  filePath: string;     // Relative path to the file
  userId: string;       // User ID who owns the file
  iat?: number;         // Issued at timestamp (added by JWT)
  exp?: number;         // Expiration timestamp (added by JWT)
}

@Injectable()
export class SignedUrlService {
  private readonly jwtSecret: string;
  private readonly baseUrl: string;
  
  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly logger: LoggerService,
  ) {
    // Get JWT secret from config
    this.jwtSecret = this.configService.get<string>('JWT_SECRET');
    if (!this.jwtSecret) {
      this.logger.error('JWT_SECRET is not defined in environment variables');
      throw new Error('JWT_SECRET is not defined');
    }
    
    // Get base URL from config
    this.baseUrl = this.configService.get<string>('API_URL') || 'http://localhost:3000';
  }

  /**
   * Generate a signed URL for accessing a protected file
   * @param filePath Relative path to the file
   * @param userId User ID who owns the file
   * @param expiresIn Token expiration time (e.g., '15m', '1h', '1d')
   * @returns Signed URL for accessing the file
   */
  generateSignedImageUrl(filePath: string, userId: string, expiresIn: string = '1h'): string {
    try {
      // Create payload
      const payload: SignedUrlPayload = {
        filePath,
        userId,
      };

      // Sign payload with JWT
      const token = this.jwtService.sign(payload, {
        secret: this.jwtSecret,
        expiresIn,
      });

      // Return signed URL
      return `${this.baseUrl}/media/images/${token}`;
    } catch (error) {
      this.logger.error(`Failed to generate signed URL for file ${filePath}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Verify a signed URL token
   * @param token JWT token from the signed URL
   * @returns Payload containing file path and user ID
   * @throws UnauthorizedException if token is invalid
   */
  verifySignedImageUrl(token: string): SignedUrlPayload {
    try {
      // Verify token
      const payload = this.jwtService.verify<SignedUrlPayload>(token, {
        secret: this.jwtSecret,
      });

      // Return payload
      return payload;
    } catch (error) {
      this.logger.warn(`Invalid signed URL token: ${error.message}`);
      throw new UnauthorizedException('Invalid or expired image URL');
    }
  }

  /**
   * Get the absolute file path from a verified payload
   * @param payload Verified payload from signed URL
   * @returns Absolute path to the file
   */
  getAbsoluteFilePath(payload: SignedUrlPayload): string {
    const uploadDir = this.configService.get<string>('UPLOAD_DIR') || 'uploads';
    return path.resolve(process.cwd(), uploadDir, payload.filePath);
  }
}
