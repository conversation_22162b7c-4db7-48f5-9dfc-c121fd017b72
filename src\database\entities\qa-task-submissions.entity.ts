import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, ManyTo<PERSON>ne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToOne } from "typeorm";
import { AuditableBaseEntity } from "./base-entity";
import { QATaskMissions } from "./qa-task-missions.entity";
import { QATaskSubmissionHistory } from "./qa-task-submission-history.entity";
import { QATaskSubmissionMarking } from "./qa-task-submission-marking.entity";
import { IsUUID } from "class-validator";
import { QAMissionTasks } from "./qa-mission-tasks.entity";

export enum QASubmissionStatus {
  DRAFT = "draft",
  SUBMITTED = "submitted",
  REVIEWED = "reviewed",
  DISCARDED = "discarded",
}

@Entity()
// export class QATaskSubmissions extends AuditableBaseEntity {
//   @Column({ name: "status", type: "enum", enum: QASubmissionStatus, default: QASubmissionStatus.DRAFT })
//   status: QASubmissionStatus;

//   @ManyToOne(() => QATaskMissions, task => task.submissions)
//   @JoinColumn({ name: "task_id" })
//   task: QATaskMissions;

//   @Column({ name: "task_id" })
//   taskId: string;

//   @Column({
//     name: "latest_submission_id",
//     type: "uuid",
//     nullable: true
//   })
//   latestSubmissionId?: string;

//   @Column({ name: "current_revision", default: 1 })
//   currentRevision: number;

//   @Column({ name: "total_revisions", default: 1 })
//   totalRevisions: number;

//   @Column({ name: "is_first_revision", default: true })
//   isFirstRevision: boolean;

//   @OneToMany(() => QATaskSubmissionHistory, history => history.submission)
//   submissionHistory: QATaskSubmissionHistory[];

//   @Column({ name: "is_active", default: true })
//   isActive: boolean;
// }


export class QATaskSubmissions extends AuditableBaseEntity {
  @Column({ name: "status", type: "enum", enum: QASubmissionStatus, default: QASubmissionStatus.DRAFT })
  status: QASubmissionStatus;

  @Column({ name: "current_revision", type: "int", default: 1 })
  currentRevision: number;

  @ManyToOne(() => QAMissionTasks, task => task.submissions)
  @JoinColumn({ name: "task_id" })
  task: QAMissionTasks;

  @Column({
    name: "task_id",
    type: "uuid",
    nullable: false
  })
  @IsUUID()
  taskId: string;

  @Column({ name: "is_active", default: true })
  isActive: boolean;

  @OneToMany(() => QATaskSubmissionHistory, history => history.submission)
  submissionHistory: QATaskSubmissionHistory[];

  @Column({
    name: "latest_submission_id",
    type: "uuid",
    nullable: true
  })
  latestSubmissionId?: string;

  @Column({ name: "total_revisions", type: "int", default: 1 })
  totalRevisions: number;

  @Column({
    name: "first_submitted_at",
    type: "timestamp",
    nullable: true
  })
  firstSubmittedAt?: Date;

  @Column({
    name: "last_submitted_at",
    type: "timestamp",
    nullable: true
  })
  lastSubmittedAt?: Date;

  @Column({
    name: "first_revision_progress",
    type: "float",
    default: 0
  })
  firstRevisionProgress: number;

  @Column({ name: "is_first_revision", type: "boolean", default: true })
  isFirstRevision: boolean;

  // @OneToOne('QATaskSubmissionMarking', (marking: QATaskSubmissionMarking) => marking.submission)
  // @JoinColumn({ name: "submission_mark_id" })
  // submissionMark: QATaskSubmissionMarking;

  @OneToOne(() => QATaskSubmissionMarking, marking => marking.submission, { nullable: true })
  @JoinColumn({ name: "submission_mark_id" })
  submissionMark: QATaskSubmissionMarking;
}