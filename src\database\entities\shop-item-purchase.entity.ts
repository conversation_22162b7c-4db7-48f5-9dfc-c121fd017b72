import { <PERSON>ti<PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';
import { ShopItem } from './shop-item.entity';
import { ShopCategory } from './shop-category.entity';

/**
 * Payment method for shop item purchases
 * @enum {string}
 */
export enum PaymentMethod {
  /** Reward points */
  REWARD_POINTS = 'reward_points',
  /** Credit card */
  CREDIT_CARD = 'credit_card',
  /** Free */
  FREE = 'free',
  /** KCP Credit Card */
  KCP_CARD = 'kcp_card',
  /** KCP Bank Transfer */
  KCP_BANK = 'kcp_bank',
  /** KCP Mobile Payment */
  KCP_MOBILE = 'kcp_mobile'
}

/**
 * Status of the purchase
 * @enum {string}
 */
export enum PurchaseStatus {
  /** Purchase completed successfully */
  COMPLETED = 'completed',
  /** Purchase is pending */
  PENDING = 'pending',
  /** Purchase failed */
  FAILED = 'failed',
  /** Purchase was refunded */
  REFUNDED = 'refunded',
  /** Payment is being processed */
  PAYMENT_PENDING = 'payment_pending',
  /** Payment is in progress */
  PAYMENT_PROCESSING = 'payment_processing',
  /** Payment confirmed by gateway */
  PAYMENT_CONFIRMED = 'payment_confirmed'
}

@Entity()
export class ShopItemPurchase extends AuditableBaseEntity {
  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @Column({ name: 'shop_item_id' })
  shopItemId: string;

  @ManyToOne(() => ShopItem)
  @JoinColumn({ name: 'shop_item_id' })
  shopItem: ShopItem;

  @Column({ name: 'original_price', type: 'decimal', precision: 10, scale: 2 })
  originalPrice: number;

  @Column({ name: 'final_price', type: 'decimal', precision: 10, scale: 2 })
  finalPrice: number;

  @Column({ name: 'promotion_id', nullable: true })
  promotionId: string;

  @Column({ name: 'discount_amount', type: 'decimal', precision: 10, scale: 2, nullable: true })
  discountAmount: number;

  @Column({ name: 'reward_points_used', type: 'decimal', precision: 10, scale: 2, default: 0 })
  rewardPointsUsed: number;

  @Column({ name: 'quantity', default: 1 })
  quantity: number;

  @Column({
    name: 'payment_method',
    type: 'enum',
    enum: PaymentMethod
  })
  paymentMethod: PaymentMethod;

  @Column({
    name: 'status',
    type: 'enum',
    enum: PurchaseStatus,
    default: PurchaseStatus.COMPLETED
  })
  status: PurchaseStatus;

  @Column({ name: 'payment_details', nullable: true, type: 'json' })
  paymentDetails: any;

  @Column({ name: 'notes', nullable: true, type: 'text' })
  notes: string;

  @Column({ name: 'category_id', nullable: true })
  categoryId: string;

  @ManyToOne(() => ShopCategory)
  @JoinColumn({ name: 'category_id' })
  category: ShopCategory;

  @Column({ name: 'metadata', nullable: true, type: 'text' })
  metadata: string;

  @Column({ name: 'payment_transaction_id', nullable: true })
  paymentTransactionId: string;
}
