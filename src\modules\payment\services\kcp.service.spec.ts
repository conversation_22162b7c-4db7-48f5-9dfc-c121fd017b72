import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { BadRequestException } from '@nestjs/common';
import { KcpService } from './kcp.service';
import { KcpConfigService } from './kcp-config.service';
import { PaymentInitiationRequest, KcpTradeRegResponse, KcpPaymentResponse, KcpPaymentMethod, PurchaseType } from '../interfaces/kcp.interface';
import { mockKcpResponses } from '../../../../test/utils/test-helpers';

describe('KcpService', () => {
  let service: KcpService;
  let kcpConfigService: KcpConfigService;
  let configService: ConfigService;

  const mockKcpConfigService = {
    getSiteCd: jest.fn(() => 'TEST_SITE_CD'),
    getKcpCertInfo: jest.fn(() => 'TEST_CERT_INFO'),
    getPaymentUrl: jest.fn(() => 'https://test-payment-url.com'),
    generateOrderCheck: jest.fn(() => 'test-order-check'),
    validateWebhookSignature: jest.fn(() => true),
  };

  const mockConfigService = {
    get: jest.fn((key: string, defaultValue?: any) => {
      const config = {
        'API_URL': 'http://localhost:3012',
        'NODE_ENV': 'test',
      };
      return config[key] || defaultValue;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KcpService,
        {
          provide: KcpConfigService,
          useValue: mockKcpConfigService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<KcpService>(KcpService);
    kcpConfigService = module.get<KcpConfigService>(KcpConfigService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('registerTrade', () => {
    const mockRequest: PaymentInitiationRequest = {
      orderId: 'TEST-ORDER-123',
      amount: 10000,
      currency: 'KRW',
      productName: 'Test Product',
      buyerName: 'Test User',
      buyerEmail: '<EMAIL>',
      buyerPhone: '010-1234-5678',
      paymentMethod: KcpPaymentMethod.CARD,
      returnUrl: 'http://localhost:3011/payment/success',
      cancelUrl: 'http://localhost:3011/payment/cancel',
      userId: 'test-user-id',
      purchaseType: PurchaseType.SHOP_ITEM,
      referenceId: 'test-reference',
    };

    it('should register trade successfully', async () => {
      const result = await service.registerTrade(mockRequest);

      expect(result).toBeDefined();
      expect(result.res_cd).toBe('0000');
      expect(result.res_msg).toBe('SUCCESS');
      expect(result.tno).toBeDefined();
      expect(result.amount).toBe(mockRequest.amount.toString());
      expect(result.PayUrl).toBeDefined();
      expect(result.ordr_chk).toBeDefined();
      expect(result.kcp_sign_data).toBeDefined();
    });

    it('should call KCP config service methods', async () => {
      await service.registerTrade(mockRequest);

      expect(kcpConfigService.getSiteCd).toHaveBeenCalled();
      expect(kcpConfigService.getKcpCertInfo).toHaveBeenCalled();
      expect(kcpConfigService.getPaymentUrl).toHaveBeenCalled();
      expect(kcpConfigService.generateOrderCheck).toHaveBeenCalledWith(
        mockRequest.orderId,
        mockRequest.amount
      );
    });

    it('should handle different payment methods', async () => {
      const cardRequest = { ...mockRequest, paymentMethod: KcpPaymentMethod.CARD };
      const bankRequest = { ...mockRequest, paymentMethod: KcpPaymentMethod.BANK };
      const mobileRequest = { ...mockRequest, paymentMethod: KcpPaymentMethod.MOBILE };

      const cardResult = await service.registerTrade(cardRequest);
      const bankResult = await service.registerTrade(bankRequest);
      const mobileResult = await service.registerTrade(mobileRequest);

      expect(cardResult.res_cd).toBe('0000');
      expect(bankResult.res_cd).toBe('0000');
      expect(mobileResult.res_cd).toBe('0000');
    });

    it('should use default currency when not provided', async () => {
      const requestWithoutCurrency = { ...mockRequest };
      delete requestWithoutCurrency.currency;

      const result = await service.registerTrade(requestWithoutCurrency);
      expect(result.res_cd).toBe('0000');
    });
  });

  describe('processPayment', () => {
    const mockRequest: PaymentInitiationRequest = {
      orderId: 'TEST-ORDER-123',
      amount: 10000,
      currency: 'KRW',
      productName: 'Test Product',
      buyerName: 'Test User',
      buyerEmail: '<EMAIL>',
      buyerPhone: '010-1234-5678',
      paymentMethod: KcpPaymentMethod.CARD,
      returnUrl: 'http://localhost:3011/payment/success',
      cancelUrl: 'http://localhost:3011/payment/cancel',
      userId: 'test-user-id',
      purchaseType: PurchaseType.SHOP_ITEM,
      referenceId: 'test-reference',
    };

    const mockTradeRegResponse: KcpTradeRegResponse = {
      res_cd: '0000',
      res_msg: 'SUCCESS',
      tno: 'TXN-123',
      amount: '10000',
      pnt_issue: '0',
      trace_no: 'TRACE-123',
      PayUrl: 'https://test-payment-url.com',
      ordr_chk: 'test-order-check',
      kcp_sign_data: 'test-sign-data',
    };

    it('should process payment successfully', async () => {
      const result = await service.processPayment(mockTradeRegResponse, mockRequest);

      expect(result).toBeDefined();
      expect(result.res_cd).toBe('0000');
      expect(result.res_msg).toBe('SUCCESS');
      expect(result.tno).toBe(mockTradeRegResponse.tno);
      expect(result.amount).toBe(mockRequest.amount.toString());
      expect(result.app_time).toBeDefined();
      expect(result.app_no).toBeDefined();
    });

    it('should include trade registration data in payment request', async () => {
      const result = await service.processPayment(mockTradeRegResponse, mockRequest);

      expect(result.tno).toBe(mockTradeRegResponse.tno);
    });
  });

  describe('initiatePayment', () => {
    const mockRequest: PaymentInitiationRequest = {
      orderId: 'TEST-ORDER-123',
      amount: 10000,
      currency: 'KRW',
      productName: 'Test Product',
      buyerName: 'Test User',
      buyerEmail: '<EMAIL>',
      buyerPhone: '010-1234-5678',
      paymentMethod: KcpPaymentMethod.CARD,
      returnUrl: 'http://localhost:3011/payment/success',
      cancelUrl: 'http://localhost:3011/payment/cancel',
      userId: 'test-user-id',
      purchaseType: PurchaseType.SHOP_ITEM,
      referenceId: 'test-reference',
    };

    it('should initiate payment successfully', async () => {
      const result = await service.initiatePayment(mockRequest);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
      expect(result.paymentUrl).toBeDefined();
      expect(result.redirectUrl).toBeDefined();
      expect(result.message).toBe('Payment initiated successfully');
      expect(result.expiresAt).toBeDefined();
    });

    it('should generate correct payment URL', async () => {
      const result = await service.initiatePayment(mockRequest);

      expect(result.paymentUrl).toContain('/payment/kcp/redirect');
      expect(result.paymentUrl).toContain('tno=');
      expect(result.paymentUrl).toContain('ordr_idxx=');
      expect(result.paymentUrl).toContain('amount=');
    });

    it('should set expiration time to 30 minutes from now', async () => {
      const beforeTime = new Date(Date.now() + 29 * 60 * 1000); // 29 minutes
      const afterTime = new Date(Date.now() + 31 * 60 * 1000); // 31 minutes

      const result = await service.initiatePayment(mockRequest);

      expect(result.expiresAt.getTime()).toBeGreaterThan(beforeTime.getTime());
      expect(result.expiresAt.getTime()).toBeLessThan(afterTime.getTime());
    });
  });

  describe('getPayMethodCode', () => {
    it('should return correct payment method codes', () => {
      expect(service['getPayMethodCode'](KcpPaymentMethod.CARD)).toBe('************');
      expect(service['getPayMethodCode'](KcpPaymentMethod.BANK)).toBe('************');
      expect(service['getPayMethodCode'](KcpPaymentMethod.MOBILE)).toBe('************');
      expect(service['getPayMethodCode']('unknown' as any)).toBe('************'); // default to card
    });
  });

  describe('validateWebhookSignature', () => {
    it('should validate webhook signature', () => {
      const payload = 'test-payload';
      const signature = 'test-signature';

      const result = service.validateWebhookSignature(payload, signature);

      expect(result).toBe(true);
      expect(kcpConfigService.validateWebhookSignature).toHaveBeenCalledWith(payload, signature);
    });
  });

  describe('generatePaymentUrl', () => {
    it('should generate payment URL with correct parameters', () => {
      const tradeRegResponse: KcpTradeRegResponse = {
        res_cd: '0000',
        res_msg: 'SUCCESS',
        tno: 'TXN-123',
        amount: '10000',
        pnt_issue: '0',
        trace_no: 'TRACE-123',
        PayUrl: 'https://test-payment-url.com',
        ordr_chk: 'test-order-check',
        kcp_sign_data: 'test-sign-data',
      };

      const request: PaymentInitiationRequest = {
        orderId: 'TEST-ORDER-123',
        amount: 10000,
        paymentMethod: KcpPaymentMethod.CARD,
        // ... other required fields
      } as PaymentInitiationRequest;

      const url = service['generatePaymentUrl'](tradeRegResponse, request);

      expect(url).toContain('http://localhost:3012/payment/kcp/redirect');
      expect(url).toContain('tno=TXN-123');
      expect(url).toContain('ordr_idxx=TEST-ORDER-123');
      expect(url).toContain('amount=10000');
      expect(url).toContain('pay_method=************');
      expect(url).toContain('ordr_chk=test-order-check');
      expect(url).toContain('kcp_sign_data=test-sign-data');
    });
  });
});
