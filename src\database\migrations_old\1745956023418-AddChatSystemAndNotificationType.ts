import { MigrationInterface, QueryRunner } from "typeorm";

export class AddChatSystemAndNotificationType1745956023418 implements MigrationInterface {
    name = 'AddChatSystemAndNotificationType1745956023418'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."conversation_type_enum" AS ENUM('direct', 'group')`);
        await queryRunner.query(`CREATE TYPE "public"."conversation_status_enum" AS ENUM('active', 'archived', 'blocked')`);
        await queryRunner.query(`CREATE TABLE "conversation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "type" "public"."conversation_type_enum" NOT NULL DEFAULT 'direct', "status" "public"."conversation_status_enum" NOT NULL DEFAULT 'active', "participant1_id" uuid NOT NULL, "participant2_id" uuid NOT NULL, "last_message_at" TIMESTAMP, "last_message_text" text, "last_message_sender_id" character varying, "participant1_unread_count" integer NOT NULL DEFAULT '0', "participant2_unread_count" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_864528ec4274360a40f66c29845" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "message_attachment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "message_id" uuid NOT NULL, "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying NOT NULL, "file_size" integer NOT NULL, "thumbnail_path" character varying, CONSTRAINT "PK_d5bc54379802d99c07cd7ec00e4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."message_type_enum" AS ENUM('text', 'image', 'file', 'quiz', 'system')`);
        await queryRunner.query(`CREATE TYPE "public"."message_status_enum" AS ENUM('sent', 'delivered', 'read', 'deleted')`);
        await queryRunner.query(`CREATE TABLE "message" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "conversation_id" uuid NOT NULL, "sender_id" uuid NOT NULL, "recipient_id" uuid NOT NULL, "type" "public"."message_type_enum" NOT NULL DEFAULT 'text', "content" text NOT NULL, "status" "public"."message_status_enum" NOT NULL DEFAULT 'sent', "read_at" TIMESTAMP, "delivered_at" TIMESTAMP, "metadata" json, CONSTRAINT "PK_ba01f0a3e0123651915008bc578" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "message_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "message_id" character varying, "user_id" uuid, "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying NOT NULL, "file_size" integer NOT NULL, "thumbnail_path" character varying, "is_temporary" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_9c93135ed4f4df5fcc68853d92c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TYPE "public"."notification_type_enum" RENAME TO "notification_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."notification_type_enum" AS ENUM('diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'tutor_verification', 'chat_message', 'system')`);
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum" USING "type"::"text"::"public"."notification_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."notification_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" DROP CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2"`);
        await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum" RENAME TO "user_notification_preference_notification_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."user_notification_preference_notification_type_enum" AS ENUM('diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'tutor_verification', 'chat_message', 'system')`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" ALTER COLUMN "notification_type" TYPE "public"."user_notification_preference_notification_type_enum" USING "notification_type"::"text"::"public"."user_notification_preference_notification_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."user_notification_preference_notification_type_enum_old"`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" ADD CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2" UNIQUE ("user_id", "notification_type", "channel")`);
        await queryRunner.query(`ALTER TABLE "conversation" ADD CONSTRAINT "FK_58c31fcf8372f2b9da76f1771bc" FOREIGN KEY ("participant1_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversation" ADD CONSTRAINT "FK_452dcf5452f8aa5e2e117810051" FOREIGN KEY ("participant2_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message_attachment" ADD CONSTRAINT "FK_9db9a64915214dde2ca1e8db9a7" FOREIGN KEY ("message_id") REFERENCES "message"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message" ADD CONSTRAINT "FK_7fe3e887d78498d9c9813375ce2" FOREIGN KEY ("conversation_id") REFERENCES "conversation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message" ADD CONSTRAINT "FK_c0ab99d9dfc61172871277b52f6" FOREIGN KEY ("sender_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message" ADD CONSTRAINT "FK_3318a3c87e7795d769d5b96e564" FOREIGN KEY ("recipient_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message_registry" ADD CONSTRAINT "FK_db32ff04c2fc2c38f77d298efa0" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "message_registry" DROP CONSTRAINT "FK_db32ff04c2fc2c38f77d298efa0"`);
        await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_3318a3c87e7795d769d5b96e564"`);
        await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_c0ab99d9dfc61172871277b52f6"`);
        await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_7fe3e887d78498d9c9813375ce2"`);
        await queryRunner.query(`ALTER TABLE "message_attachment" DROP CONSTRAINT "FK_9db9a64915214dde2ca1e8db9a7"`);
        await queryRunner.query(`ALTER TABLE "conversation" DROP CONSTRAINT "FK_452dcf5452f8aa5e2e117810051"`);
        await queryRunner.query(`ALTER TABLE "conversation" DROP CONSTRAINT "FK_58c31fcf8372f2b9da76f1771bc"`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" DROP CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2"`);
        await queryRunner.query(`CREATE TYPE "public"."user_notification_preference_notification_type_enum_old" AS ENUM('diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'tutor_verification', 'system')`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" ALTER COLUMN "notification_type" TYPE "public"."user_notification_preference_notification_type_enum_old" USING "notification_type"::"text"::"public"."user_notification_preference_notification_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."user_notification_preference_notification_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."user_notification_preference_notification_type_enum_old" RENAME TO "user_notification_preference_notification_type_enum"`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" ADD CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2" UNIQUE ("user_id", "notification_type", "channel")`);
        await queryRunner.query(`CREATE TYPE "public"."notification_type_enum_old" AS ENUM('diary_submission', 'diary_review', 'diary_feedback', 'tutor_assignment', 'tutor_verification', 'system')`);
        await queryRunner.query(`ALTER TABLE "notification" ALTER COLUMN "type" TYPE "public"."notification_type_enum_old" USING "type"::"text"::"public"."notification_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."notification_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."notification_type_enum_old" RENAME TO "notification_type_enum"`);
        await queryRunner.query(`DROP TABLE "message_registry"`);
        await queryRunner.query(`DROP TABLE "message"`);
        await queryRunner.query(`DROP TYPE "public"."message_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."message_type_enum"`);
        await queryRunner.query(`DROP TABLE "message_attachment"`);
        await queryRunner.query(`DROP TABLE "conversation"`);
        await queryRunner.query(`DROP TYPE "public"."conversation_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."conversation_type_enum"`);
    }

}
