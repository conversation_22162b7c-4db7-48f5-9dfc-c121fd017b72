import { Controller, Get, Post, Body, UseGuards, Query, Param, ParseUUIDPipe, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiParam, ApiBody } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { StudentGuard } from '../../../common/guards/student.guard';
import { StoryMakerService } from './story-maker.service';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../../common/decorators/api-response.decorator';
import { StoryMakerGameListResponseDto, StoryMakerGameDetailDto, StoryMakerSubmissionDto } from '../../../database/models/story-maker/story-maker-student.dto';
import { PaginationDto } from '../../../common/models/pagination.dto';

@ApiTags('Play-StoryMaker')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, StudentGuard)
@Controller('play/story-maker/play')
export class StoryMakerController {
  constructor(private readonly storyMakerService: StoryMakerService) {}

  @Get('list')
  @ApiOperation({
    summary: 'Get available story maker games',
    description: 'Returns a list of active story maker games. Games already played by the student will be marked as played.',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiOkResponseWithType(StoryMakerGameListResponseDto, 'Story maker games retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getAvailableGames(@Query() paginationDto: PaginationDto): Promise<ApiResponse<StoryMakerGameListResponseDto>> {
    const result = await this.storyMakerService.getAvailableGames(paginationDto);

    // Check if any games were found
    const message = result.games.length === 0 ? 'No story maker games are available right now. Please check back later!' : 'Story maker games retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a specific story maker game with enhanced details',
    description: `Returns details of a specific story maker game with enhanced information based on the student's interaction:

    Scenario 1: Student hasn't played the game yet
    - Returns basic story details with is_played: false
    - No submission content, evaluation, or score is returned

    Scenario 2: Student submitted but tutor hasn't evaluated yet
    - Returns story details with is_played: true
    - Returns the student's latest submission content
    - No evaluation details or score yet

    Scenario 3: Student submitted and tutor evaluated with score
    - Returns story details with is_played: true
    - Returns the student's submission content
    - Returns evaluation details (corrections, feedback)
    - Returns the score assigned by the tutor

    Scenario 4: Student submitted again after evaluation
    - Returns story details with is_played: true
    - Returns the student's latest submission content (the new one)
    - No evaluation details for the latest submission
    - Still returns the score from the first submission (which is permanent)`,
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story maker game',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(StoryMakerGameDetailDto, 'Story maker game retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found')
  async getGameById(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<StoryMakerGameDetailDto>> {
    const result = await this.storyMakerService.getGameById(id);
    return ApiResponse.success(result, 'Story game retrieved successfully');
  }

  @Get(':id/submissions')
  @ApiOperation({
    summary: 'Get student submissions for a story',
    description: 'Returns all submissions made by the student for a specific story maker game, including evaluations.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story maker game',
    type: 'string',
    format: 'uuid',
  })
  @ApiOkResponseWithType(Object, 'Submissions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found or no submissions')
  async getSubmissions(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<any>> {
    const result = await this.storyMakerService.getStudentSubmissions(id);

    const message = result.submissions.length === 0 ? "You haven't submitted any stories for this game yet." : 'Your submissions retrieved successfully';

    return ApiResponse.success(result, message);
  }

  @Post(':id/submit')
  @ApiOperation({
    summary: 'Submit a story',
    description:
      'Submit a written story for a specific story maker game. The story will be evaluated by a tutor. Students can submit multiple times, but only after their previous submission has been evaluated.',
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the story maker game',
    type: 'string',
    format: 'uuid',
  })
  @ApiBody({
    type: StoryMakerSubmissionDto,
    description: 'The story content to submit',
  })
  @ApiOkResponseWithType(Object, 'Story submitted successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Game not found')
  async submitStory(@Param('id', ParseUUIDPipe) id: string, @Body() submissionDto: StoryMakerSubmissionDto): Promise<ApiResponse<{ message: string }>> {
    // Validate that the story_maker_id in the DTO matches the ID in the URL
    if (submissionDto.story_maker_id && submissionDto.story_maker_id !== id) {
      throw new BadRequestException("Oops! Something doesn't match. Please try again.");
    }

    const result = await this.storyMakerService.submitStory(id, submissionDto.content);
    return ApiResponse.success(result, result.message);
  }
}
