{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug NestJS App", "runtimeArgs": ["-r", "ts-node/register"], "args": ["${workspaceFolder}/src/main.ts"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "env": {"TS_NODE_PROJECT": "${workspaceFolder}/tsconfig.json", "NODE_PATH": "${workspaceFolder}"}}, {"type": "node", "request": "launch", "name": "Debug via NPM", "runtimeExecutable": "npm", "runtimeArgs": ["run", "start:debug"], "skipFiles": ["<node_internals>/**"], "outFiles": ["${workspaceFolder}/dist/**/*.js"], "console": "integratedTerminal", "sourceMaps": true}, {"type": "node", "request": "launch", "name": "Debug Windows", "runtimeExecutable": "npm", "runtimeArgs": ["run", "debug:windows"], "skipFiles": ["<node_internals>/**"], "outFiles": ["${workspaceFolder}/dist/**/*.js"], "console": "integratedTerminal", "sourceMaps": true, "env": {"NODE_PATH": "${workspaceFolder}", "UPLOAD_DIR": "${workspaceFolder}/uploads"}}]}