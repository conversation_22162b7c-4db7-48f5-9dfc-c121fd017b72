import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDiaryQrAndVisibility1746202751304 implements MigrationInterface {
    name = 'AddDiaryQrAndVisibility1746202751304'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "diary_qr_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "diary_entry_id" uuid NOT NULL, "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying NOT NULL DEFAULT 'image/png', "file_size" integer NOT NULL, "share_url" character varying NOT NULL, CONSTRAINT "PK_3f83ce9c73b24efe0ac0bc4dcbb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_visibility_enum" AS ENUM('private', 'friends_only', 'public')`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ADD "visibility" "public"."diary_entry_visibility_enum" NOT NULL DEFAULT 'private'`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_view_access_enum" AS ENUM('full', 'partial')`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ADD "view_access" "public"."diary_entry_view_access_enum" NOT NULL DEFAULT 'full'`);
        await queryRunner.query(`ALTER TABLE "diary_qr_registry" ADD CONSTRAINT "FK_a29acfaa42930505c29ff2918f8" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "diary_qr_registry" DROP CONSTRAINT "FK_a29acfaa42930505c29ff2918f8"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" DROP COLUMN "view_access"`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_view_access_enum"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" DROP COLUMN "visibility"`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_visibility_enum"`);
        await queryRunner.query(`DROP TABLE "diary_qr_registry"`);
    }

}
