import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, Req, BadRequestException } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { TutorGuard } from '../../common/guards/tutor.guard';
import { MissionDiaryEntryService } from './mission-diary-entry.service';

import {
  MissionDiaryEntryResponseDto,
  MissionEntryFilterDto,
  AddMissionFeedbackDto,
  AddMissionCorrectionDto,
  AssignMissionScoreDto,
  AddMissionCorrectionWithScoreDto,
  MissionFeedbackResponseDto
} from '../../database/models/mission-diary-entry.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { PaginationDto } from '../../common/models/pagination.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';

@ApiTags('Tutor Diary Missions Management')
@Controller('diary/tutor/missions')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TutorGuard)
export class TutorMissionController {
  constructor(
    private readonly missionDiaryEntryService: MissionDiaryEntryService
  ) {}

  // ===== Mission Entry Management Endpoints =====

  @Get('/entries')
  @ApiOperation({ summary: 'Get mission diary entries for the tutor' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'missionId', required: false, type: String })
  @ApiQuery({ name: 'studentId', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'createdAtFrom', required: false, type: String })
  @ApiQuery({ name: 'createdAtTo', required: false, type: String })
  @ApiQuery({ name: 'sortBy', required: false, type: String })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['ASC', 'DESC'] })
  @ApiOkResponseWithPagedListType(MissionDiaryEntryResponseDto, 'Entries retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  async getTutorMissionEntries(
    @Req() req: any,
    @Query() params: any
  ): Promise<ApiResponse<PagedListDto<MissionDiaryEntryResponseDto>>> {
    // Extract pagination parameters
    const paginationDto: PaginationDto = {
      page: params.page ? parseInt(params.page) : 1,
      limit: params.limit ? parseInt(params.limit) : 10,
      sortBy: params.sortBy,
      sortDirection: params.sortDirection
    };

    // Extract filter parameters
    const filterDto: MissionEntryFilterDto = {
      missionId: params.missionId,
      studentId: params.studentId,
      status: params.status,
      createdAtFrom: params.createdAtFrom,
      createdAtTo: params.createdAtTo
    };

    const entries = await this.missionDiaryEntryService.getTutorMissionEntries(req.user.id, filterDto, paginationDto);
    return ApiResponse.success(entries, 'Entries retrieved successfully');
  }

  @Get('/entries/:id')
  @ApiOperation({ summary: 'Get a specific mission diary entry' })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Entry retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async getMissionEntry(
    @Param('id') id: string
  ): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.getMissionEntry(id);
    return ApiResponse.success(entry, 'Entry retrieved successfully');
  }

  @Post('/entries/:id/feedback')
  @ApiOperation({ summary: 'Add feedback to a mission diary entry' })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionFeedbackResponseDto, 'Feedback added successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async addFeedback(
    @Req() req: any,
    @Param('id') id: string,
    @Body() feedbackDto: AddMissionFeedbackDto
  ): Promise<ApiResponse<MissionFeedbackResponseDto>> {
    if (!id || id === 'undefined' || id.trim() === '') {
      throw new BadRequestException('Entry ID is required and cannot be undefined');
    }
    const feedback = await this.missionDiaryEntryService.addFeedback(id, req.user.id, feedbackDto);
    return ApiResponse.success(feedback, 'Feedback added successfully');
  }

  @Post('/entries/:id/correction')
  @ApiOperation({
    summary: 'Add correction with score to a mission diary entry',
    description: 'Provide both correction text and score for a mission diary entry in a single operation. Both fields are required.'
  })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Correction and score added successfully')
  @ApiErrorResponse(400, 'Bad request - Both correction and score are required')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async addCorrectionWithScore(
    @Req() req: any,
    @Param('id') id: string,
    @Body() correctionWithScoreDto: AddMissionCorrectionWithScoreDto
  ): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.addCorrectionWithScore(id, req.user.id, correctionWithScoreDto);
    return ApiResponse.success(entry, 'Correction and score added successfully');
  }

  // Deprecated endpoints - kept for backward compatibility
  @Post('/entries/:id/correction-only')
  @ApiOperation({
    summary: 'Add correction to a mission diary entry (DEPRECATED)',
    deprecated: true,
    description: 'This endpoint is deprecated. Use POST /entries/:id/correction instead which requires both correction and score.'
  })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Correction added successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async addCorrection(
    @Req() req: any,
    @Param('id') id: string,
    @Body() correctionDto: AddMissionCorrectionDto
  ): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.addCorrection(id, req.user.id, correctionDto);
    return ApiResponse.success(entry, 'Correction added successfully');
  }

  @Post('/entries/:id/score')
  @ApiOperation({
    summary: 'Assign a score to a mission diary entry (DEPRECATED)',
    deprecated: true,
    description: 'This endpoint is deprecated. Use POST /entries/:id/correction instead which requires both correction and score.'
  })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Score assigned successfully')
  @ApiErrorResponse(400, 'Bad request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async assignScore(
    @Req() req: any,
    @Param('id') id: string,
    @Body() scoreDto: AssignMissionScoreDto
  ): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.assignScore(id, req.user.id, scoreDto);
    return ApiResponse.success(entry, 'Score assigned successfully');
  }

  @Post('/entries/:id/confirm')
  @ApiOperation({ summary: 'Confirm a mission diary entry review' })
  @ApiParam({ name: 'id', description: 'Entry ID' })
  @ApiOkResponseWithType(MissionDiaryEntryResponseDto, 'Entry confirmed successfully')
  @ApiErrorResponse(400, 'Bad request - Entry must be reviewed and have a score')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Entry not found')
  async confirmEntry(
    @Req() req: any,
    @Param('id') id: string
  ): Promise<ApiResponse<MissionDiaryEntryResponseDto>> {
    const entry = await this.missionDiaryEntryService.confirmMissionEntry(id, req.user.id);
    return ApiResponse.success(entry, 'Entry confirmed successfully');
  }

}
