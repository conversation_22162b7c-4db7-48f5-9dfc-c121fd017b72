/**
 * Upload options for S3 storage
 */
export interface S3UploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
}

/**
 * Upload result from S3 storage
 */
export interface S3UploadResult {
  key: string;
  url: string;
  bucket: string;
  region: string;
  etag?: string;
  versionId?: string;
  storageClass?: string;
  serverSideEncryption?: string;
  size: number;
  metadata?: Record<string, string>;
}
