import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddWordLimitAndDeadlineToStoryMaker1747653309844 implements MigrationInterface {
  name = 'AddWordLimitAndDeadlineToStoryMaker1747653309844';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add word_limit and deadline columns to story_maker table
    await queryRunner.query(`ALTER TABLE "story_maker" ADD "word_limit" integer`);
    await queryRunner.query(`ALTER TABLE "story_maker" ADD "deadline" integer`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove word_limit and deadline columns from story_maker table
    await queryRunner.query(`ALTER TABLE "story_maker" DROP COLUMN "deadline"`);
    await queryRunner.query(`ALTER TABLE "story_maker" DROP COLUMN "word_limit"`);
  }
}
