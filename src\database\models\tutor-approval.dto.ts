import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsOptional, IsUUID } from 'class-validator';
import { TutorApprovalStatus } from '../entities/tutor-approval.entity';

export class TutorApprovalResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the tutor approval request' })
  id: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the user' })
  userId: string;

  @ApiProperty({ example: 'pending', description: 'The status of the tutor approval request', enum: TutorApprovalStatus })
  status: TutorApprovalStatus;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the admin who processed the request', required: false })
  adminId?: string;

  @ApiProperty({ example: 'Approved after verifying credentials', description: 'Admin notes about the approval', required: false })
  adminNotes?: string;

  @ApiProperty({ example: 'Missing required qualifications', description: 'Reason for rejection', required: false })
  rejectionReason?: string;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'When the request was approved', required: false })
  approvedAt?: Date;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'When the request was rejected', required: false })
  rejectedAt?: Date;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'When the request was created' })
  createdAt: Date;

  @ApiProperty({ example: '2023-01-01T00:00:00.000Z', description: 'When the request was last updated' })
  updatedAt: Date;

  @ApiProperty({ example: { name: 'John Doe', email: '<EMAIL>' }, description: 'User details', required: false })
  user?: any;
}

export class ApproveTutorDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the tutor approval request' })
  @IsUUID('4', { message: 'Invalid tutor approval ID' })
  @IsNotEmpty({ message: 'Tutor approval ID is required' })
  approvalId: string;

  @ApiProperty({ example: 'Approved after verifying credentials', description: 'Admin notes about the approval', required: false })
  @IsString()
  @IsOptional()
  adminNotes?: string;
}

export class RejectTutorDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'The ID of the tutor approval request' })
  @IsUUID('4', { message: 'Invalid tutor approval ID' })
  @IsNotEmpty({ message: 'Tutor approval ID is required' })
  approvalId: string;

  @ApiProperty({ example: 'Missing required qualifications', description: 'Reason for rejection' })
  @IsString()
  @IsNotEmpty({ message: 'Rejection reason is required' })
  rejectionReason: string;

  @ApiProperty({ example: 'Additional notes for internal reference', description: 'Admin notes about the rejection', required: false })
  @IsString()
  @IsOptional()
  adminNotes?: string;
}
