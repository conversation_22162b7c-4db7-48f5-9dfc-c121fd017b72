import { Test, TestingModule } from '@nestjs/testing';
import { DiaryShareService } from './diary-share.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { Diary } from '../../database/entities/diary.entity';
import { DiaryShare } from '../../database/entities/diary-share.entity';
import { DiaryQrRegistry } from '../../database/entities/diary-qr-registry.entity';
import { User } from '../../database/entities/user.entity';
import { DeeplinkService } from '../../common/utils/deeplink.service';
import { QrCodeService } from '../../common/services/qr-code.service';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { Repository } from 'typeorm';
import { NotFoundException, ForbiddenException } from '@nestjs/common';
import { DiaryVisibility } from '../../common/enums/diary-visibility.enum';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';

describe('DiaryShareService', () => {
  let service: DiaryShareService;
  let diaryEntryRepository: Repository<DiaryEntry>;
  let diaryRepository: Repository<Diary>;
  let diaryShareRepository: Repository<DiaryShare>;
  let diaryQrRegistryRepository: Repository<DiaryQrRegistry>;
  let userRepository: Repository<User>;
  let deeplinkService: DeeplinkService;
  let qrCodeService: QrCodeService;
  let fileRegistryService: FileRegistryService;

  const mockDiaryEntryRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
  };

  const mockDiaryRepository = {
    findOne: jest.fn(),
  };

  const mockDiaryShareRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockDiaryQrRegistryRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
  };

  const mockUserRepository = {
    findOne: jest.fn(),
  };

  const mockDeeplinkService = {
    getWebLink: jest.fn(),
  };

  const mockQrCodeService = {
    generateQrCode: jest.fn(),
    generateAndSaveQrCode: jest.fn(),
  };

  const mockFileRegistryService = {
    registerFile: jest.fn(),
    getFileUrl: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DiaryShareService,
        {
          provide: getRepositoryToken(DiaryEntry),
          useValue: mockDiaryEntryRepository,
        },
        {
          provide: getRepositoryToken(Diary),
          useValue: mockDiaryRepository,
        },
        {
          provide: getRepositoryToken(DiaryShare),
          useValue: mockDiaryShareRepository,
        },
        {
          provide: getRepositoryToken(DiaryQrRegistry),
          useValue: mockDiaryQrRegistryRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: DeeplinkService,
          useValue: mockDeeplinkService,
        },
        {
          provide: QrCodeService,
          useValue: mockQrCodeService,
        },
        {
          provide: FileRegistryService,
          useValue: mockFileRegistryService,
        },
      ],
    }).compile();

    service = module.get<DiaryShareService>(DiaryShareService);
    diaryEntryRepository = module.get<Repository<DiaryEntry>>(getRepositoryToken(DiaryEntry));
    diaryRepository = module.get<Repository<Diary>>(getRepositoryToken(Diary));
    diaryShareRepository = module.get<Repository<DiaryShare>>(getRepositoryToken(DiaryShare));
    diaryQrRegistryRepository = module.get<Repository<DiaryQrRegistry>>(getRepositoryToken(DiaryQrRegistry));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    deeplinkService = module.get<DeeplinkService>(DeeplinkService);
    qrCodeService = module.get<QrCodeService>(QrCodeService);
    fileRegistryService = module.get<FileRegistryService>(FileRegistryService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('shareDiaryEntry', () => {
    it('should share a diary entry and generate a QR code', async () => {
      // Mock data
      const entryId = 'entry-id';
      const userId = 'user-id';
      const shareDiaryEntryDto = { entryId, expiryDate: null };
      const user = {
        id: userId,
        userRoles: [{ role: { name: 'student' } }],
      };
      const diary = { id: 'diary-id', userId };
      const diaryEntry = { id: entryId, diaryId: diary.id, diary };
      const shareToken = 'share-token';
      const shareUrl = 'https://example.com/share/share-token';
      const qrBuffer = Buffer.from('qr-code');
      const qrResult = { filePath: 'diary-qr/qr-code.png', fileSize: 100 };
      const qrRegistry = { id: 'qr-registry-id' };
      const qrCodeUrl = 'https://example.com/media/diary-qr/entry-id';
      const savedShare = {
        id: 'share-id',
        shareToken,
        expiryDate: null,
        isActive: true,
        createdAt: new Date(),
      };

      // Mock repository methods
      mockUserRepository.findOne.mockResolvedValue(user);
      mockDiaryEntryRepository.findOne.mockResolvedValue(diaryEntry);
      mockDiaryRepository.findOne.mockResolvedValue(diary);
      mockDiaryEntryRepository.save.mockResolvedValue({
        ...diaryEntry,
        isPrivate: false,
        visibility: DiaryVisibility.PUBLIC,
      });
      mockDiaryShareRepository.create.mockReturnValue({
        diaryEntryId: entryId,
        shareToken,
        expiryDate: null,
        isActive: true,
      });
      mockDiaryShareRepository.save.mockResolvedValue(savedShare);
      mockDeeplinkService.getWebLink.mockReturnValue(shareUrl);
      mockQrCodeService.generateQrCode.mockResolvedValue(qrBuffer);
      mockQrCodeService.generateAndSaveQrCode.mockResolvedValue(qrResult);
      mockFileRegistryService.registerFile.mockResolvedValue(qrRegistry);
      mockFileRegistryService.getFileUrl.mockReturnValue(qrCodeUrl);

      // Call the method
      const result = await service.shareDiaryEntry(entryId, userId, shareDiaryEntryDto);

      // Assertions
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({
        where: { id: userId },
        relations: ['userRoles', 'userRoles.role'],
      });
      expect(mockDiaryEntryRepository.findOne).toHaveBeenCalledWith({
        where: { id: entryId },
        relations: ['diary'],
      });
      expect(mockDiaryRepository.findOne).toHaveBeenCalledWith({
        where: { id: diaryEntry.diaryId },
      });
      expect(mockDiaryEntryRepository.save).toHaveBeenCalledWith({
        ...diaryEntry,
        isPrivate: false,
        visibility: DiaryVisibility.PUBLIC,
      });
      expect(mockDiaryShareRepository.create).toHaveBeenCalled();
      expect(mockDiaryShareRepository.save).toHaveBeenCalled();
      expect(mockDeeplinkService.getWebLink).toHaveBeenCalled();
      expect(mockQrCodeService.generateQrCode).toHaveBeenCalledWith(shareUrl);
      expect(mockQrCodeService.generateAndSaveQrCode).toHaveBeenCalled();
      expect(mockFileRegistryService.registerFile).toHaveBeenCalledWith(
        FileEntityType.DIARY_QR,
        entryId,
        qrResult.filePath,
        expect.any(String),
        'image/png',
        qrResult.fileSize,
        userId,
        shareUrl
      );
      expect(mockFileRegistryService.getFileUrl).toHaveBeenCalledWith(FileEntityType.DIARY_QR, entryId);
      expect(result).toEqual({
        id: savedShare.id,
        shareToken: savedShare.shareToken,
        shareUrl,
        qrCodeUrl,
        expiryDate: savedShare.expiryDate,
        isActive: savedShare.isActive,
        createdAt: savedShare.createdAt,
      });
    });

    it('should throw NotFoundException if user not found', async () => {
      // Mock data
      const entryId = 'entry-id';
      const userId = 'user-id';
      const shareDiaryEntryDto = { entryId, expiryDate: null };

      // Mock repository methods
      mockUserRepository.findOne.mockResolvedValue(null);

      // Call the method and expect it to throw
      await expect(service.shareDiaryEntry(entryId, userId, shareDiaryEntryDto)).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if user is not a student', async () => {
      // Mock data
      const entryId = 'entry-id';
      const userId = 'user-id';
      const shareDiaryEntryDto = { entryId, expiryDate: null };
      const user = {
        id: userId,
        userRoles: [{ role: { name: 'admin' } }],
      };

      // Mock repository methods
      mockUserRepository.findOne.mockResolvedValue(user);

      // Call the method and expect it to throw
      await expect(service.shareDiaryEntry(entryId, userId, shareDiaryEntryDto)).rejects.toThrow(ForbiddenException);
    });
  });

  describe('makeEntryPrivate', () => {
    it('should make a diary entry private and deactivate all shares', async () => {
      // Mock data
      const entryId = 'entry-id';
      const userId = 'user-id';
      const diary = { id: 'diary-id', userId };
      const diaryEntry = { id: entryId, diaryId: diary.id, diary };
      const shares = [
        { id: 'share-1', isActive: true },
        { id: 'share-2', isActive: true },
      ];

      // Mock repository methods
      mockDiaryEntryRepository.findOne.mockResolvedValue(diaryEntry);
      mockDiaryShareRepository.find.mockResolvedValue(shares);
      mockDiaryEntryRepository.save.mockResolvedValue({
        ...diaryEntry,
        isPrivate: true,
        visibility: DiaryVisibility.PRIVATE,
      });
      mockDiaryShareRepository.save.mockResolvedValue({ isActive: false });

      // Call the method
      await service.makeEntryPrivate(entryId, userId);

      // Assertions
      expect(mockDiaryEntryRepository.findOne).toHaveBeenCalledWith({
        where: { id: entryId },
        relations: ['diary'],
      });
      expect(mockDiaryEntryRepository.save).toHaveBeenCalledWith({
        ...diaryEntry,
        isPrivate: true,
        visibility: DiaryVisibility.PRIVATE,
      });
      expect(mockDiaryShareRepository.find).toHaveBeenCalledWith({
        where: { diaryEntryId: entryId },
      });
      expect(mockDiaryShareRepository.save).toHaveBeenCalled();
    });

    it('should throw NotFoundException if diary entry not found', async () => {
      // Mock data
      const entryId = 'entry-id';
      const userId = 'user-id';

      // Mock repository methods
      mockDiaryEntryRepository.findOne.mockResolvedValue(null);

      // Call the method and expect it to throw
      await expect(service.makeEntryPrivate(entryId, userId)).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException if user is not the owner', async () => {
      // Mock data
      const entryId = 'entry-id';
      const userId = 'user-id';
      const diary = { id: 'diary-id', userId: 'other-user-id' };
      const diaryEntry = { id: entryId, diaryId: diary.id, diary };

      // Mock repository methods
      mockDiaryEntryRepository.findOne.mockResolvedValue(diaryEntry);

      // Call the method and expect it to throw
      await expect(service.makeEntryPrivate(entryId, userId)).rejects.toThrow(ForbiddenException);
    });
  });
});
