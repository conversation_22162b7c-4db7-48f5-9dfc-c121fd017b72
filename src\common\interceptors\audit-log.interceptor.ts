import { Injectable, NestInterceptor, Execution<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { AuditLogService } from '../services/audit-log.service';
import { Request, Response } from 'express';

// Define a custom interface that extends Express Request
// Explicitly include the standard Express Request properties to ensure TypeScript recognizes them
interface RequestWithUser extends Request {
  user?: { id?: string };
  method: string;
  url: string;
  ip: string;
  headers: any;
  body: any;
}

@Injectable()
export class AuditLogInterceptor implements NestInterceptor {
  constructor(@Inject(AuditLogService) private readonly auditLogService: AuditLogService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const now = Date.now();
    const request = context.switchToHttp().getRequest<RequestWithUser>();
    const { method, url, user, ip, headers } = request;
    const userAgent = headers['user-agent'] || 'unknown';

    // Determine the action based on HTTP method
    let action = method.toLowerCase();
    if (action === 'post') action = 'create';
    else if (action === 'get') action = 'read';
    else if (action === 'put' || action === 'patch') action = 'update';
    else if (action === 'delete') action = 'delete';

    // Extract resource from URL
    const resource = url.split('?')[0];

    // Extract user ID if authenticated
    const userId = user?.id || null;

    // Filter out sensitive data from request body
    const filteredBody = { ...request.body };
    if (filteredBody.password) filteredBody.password = '[REDACTED]';
    if (filteredBody.token) filteredBody.token = '[REDACTED]';

    return next.handle().pipe(
      tap({
        next: (data) => {
          // Log successful operation
          this.auditLogService.log(
            action,
            resource,
            userId,
            ip || '',
            userAgent,
            {
              requestBody: filteredBody,
              responseData: this.filterSensitiveData(data),
              duration: Date.now() - now,
            },
            'success'
          );
        },
        error: (error) => {
          // Log failed operation
          this.auditLogService.log(
            action,
            resource,
            userId,
            ip || '',
            userAgent,
            {
              requestBody: filteredBody,
              duration: Date.now() - now,
            },
            'error',
            error.message
          );
        },
      })
    );
  }

  private filterSensitiveData(data: any): any {
    if (!data) return data;

    // If data is an object with a token or user property
    if (typeof data === 'object') {
      const filtered = { ...data };

      // Redact sensitive fields
      if (filtered.access_token) filtered.access_token = '[REDACTED]';
      if (filtered.refresh_token) filtered.refresh_token = '[REDACTED]';

      // If there's a user object with sensitive data
      if (filtered.user && typeof filtered.user === 'object') {
        filtered.user = { ...filtered.user };
        if (filtered.user.password) filtered.user.password = '[REDACTED]';
      }

      return filtered;
    }

    return data;
  }
}
