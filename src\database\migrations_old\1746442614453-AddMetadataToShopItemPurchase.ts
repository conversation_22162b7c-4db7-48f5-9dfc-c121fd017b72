import { MigrationInterface, QueryRunner } from "typeorm";

export class AddMetadataToShopItemPurchase1746442614453 implements MigrationInterface {
    name = 'AddMetadataToShopItemPurchase1746442614453'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "waterfall_answer" DROP CONSTRAINT "FK_waterfall_answer_question"`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" DROP CONSTRAINT "FK_waterfall_answer_participation"`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" DROP CONSTRAINT "FK_waterfall_participation_set"`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" DROP CONSTRAINT "FK_waterfall_participation_student"`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP CONSTRAINT "FK_shop_item_purchase_category"`);
        await queryRunner.query(`ALTER TABLE "reward_point_setting" ALTER COLUMN "created_at" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "reward_point_setting" ALTER COLUMN "updated_at" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "reward_point_setting" ALTER COLUMN "updated_at" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TYPE "public"."mission_diary_entry_status_enum" RENAME TO "mission_diary_entry_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."mission_diary_entry_status_enum" AS ENUM('NEW', 'SUBMITTED', 'REVIEWED', 'CONFIRMED', 'new', 'submit', 'reviewed', 'confirm')`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ALTER COLUMN "status" TYPE "public"."mission_diary_entry_status_enum" USING "status"::"text"::"public"."mission_diary_entry_status_enum"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ALTER COLUMN "status" SET DEFAULT 'NEW'`);
        await queryRunner.query(`DROP TYPE "public"."mission_diary_entry_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" ADD CONSTRAINT "FK_aaec21ced4bc6bbf331eeb8bb6f" FOREIGN KEY ("participation_id") REFERENCES "waterfall_participation"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" ADD CONSTRAINT "FK_d9104da9519ec2a769ba473f498" FOREIGN KEY ("question_id") REFERENCES "waterfall_question"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" ADD CONSTRAINT "FK_15f9a5110613d2fa261348d7e0b" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" ADD CONSTRAINT "FK_b54f4c72fadee4feb2ca98da56d" FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD CONSTRAINT "FK_5e620f3877939956645a39860f7" FOREIGN KEY ("category_id") REFERENCES "shop_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP CONSTRAINT "FK_5e620f3877939956645a39860f7"`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" DROP CONSTRAINT "FK_b54f4c72fadee4feb2ca98da56d"`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" DROP CONSTRAINT "FK_15f9a5110613d2fa261348d7e0b"`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" DROP CONSTRAINT "FK_d9104da9519ec2a769ba473f498"`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" DROP CONSTRAINT "FK_aaec21ced4bc6bbf331eeb8bb6f"`);
        await queryRunner.query(`CREATE TYPE "public"."mission_diary_entry_status_enum_old" AS ENUM('new', 'submit', 'reviewed', 'confirm')`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ALTER COLUMN "status" TYPE "public"."mission_diary_entry_status_enum_old" USING "status"::"text"::"public"."mission_diary_entry_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ALTER COLUMN "status" SET DEFAULT 'new'`);
        await queryRunner.query(`DROP TYPE "public"."mission_diary_entry_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."mission_diary_entry_status_enum_old" RENAME TO "mission_diary_entry_status_enum"`);
        await queryRunner.query(`ALTER TABLE "reward_point_setting" ALTER COLUMN "updated_at" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "reward_point_setting" ALTER COLUMN "updated_at" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "reward_point_setting" ALTER COLUMN "created_at" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD CONSTRAINT "FK_shop_item_purchase_category" FOREIGN KEY ("category_id") REFERENCES "shop_category"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" ADD CONSTRAINT "FK_waterfall_participation_student" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" ADD CONSTRAINT "FK_waterfall_participation_set" FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" ADD CONSTRAINT "FK_waterfall_answer_participation" FOREIGN KEY ("participation_id") REFERENCES "waterfall_participation"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" ADD CONSTRAINT "FK_waterfall_answer_question" FOREIGN KEY ("question_id") REFERENCES "waterfall_question"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
