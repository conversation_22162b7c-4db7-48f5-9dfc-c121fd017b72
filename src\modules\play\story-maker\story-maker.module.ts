import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { StoryMaker } from '../../../database/entities/story-maker.entity';
import { StoryMakerRegistry } from '../../../database/entities/story-maker-registry.entity';
import { StoryMakerParticipation } from '../../../database/entities/story-maker-participation.entity';
import { StoryMakerSubmission } from '../../../database/entities/story-maker-submission.entity';
import { StoryMakerEvaluation } from '../../../database/entities/story-maker-evaluation.entity';
import { User } from '../../../database/entities/user.entity';
import { StoryMakerAdminController } from './story-maker-admin.controller';
import { StoryMakerController } from './story-maker.controller';
import { StoryMakerTutorController } from './story-maker-tutor.controller';
import { StoryMakerAdminService } from './story-maker-admin.service';
import { StoryMakerTutorService } from './story-maker-tutor.service';
import { StoryMakerService } from './story-maker.service';
import { CommonModule } from '../../../common/common.module';
import { NotificationModule } from '../../../modules/notification/notification.module';
import { UsersModule } from '../../../modules/users/users.module';
import { TutorMatchingModule } from '../../../modules/tutor-matching/tutor-matching.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([StoryMaker, StoryMakerRegistry, StoryMakerParticipation, StoryMakerSubmission, StoryMakerEvaluation, User]),
    MulterModule.register({
      limits: {
        fileSize: 2 * 1024 * 1024, // 2MB
      },
    }),
    CommonModule,
    NotificationModule,
    UsersModule,
    TutorMatchingModule,
  ],
  controllers: [StoryMakerAdminController, StoryMakerController, StoryMakerTutorController],
  providers: [StoryMakerAdminService, StoryMakerTutorService, StoryMakerService],
  exports: [StoryMakerAdminService, StoryMakerTutorService, StoryMakerService],
})
export class StoryMakerModule {}
