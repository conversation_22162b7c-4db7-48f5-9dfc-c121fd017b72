# HEC Backend Environment Configuration with S3 Support
# Copy this file to .env and configure your values

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# Storage Provider: 'local' or 's3'
# Set to 's3' to use AWS S3, 'local' to use local filesystem
# IMPORTANT: Use only ONE provider per environment (no hybrid mode)
STORAGE_PROVIDER=s3

# =============================================================================
# AWS S3 CONFIGURATION
# =============================================================================

# AWS Credentials
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key_here

# S3 Bucket Configuration
AWS_S3_BUCKET_NAME=hec-production-files
AWS_S3_BUCKET_REGION=us-east-1

# CloudFront CDN (optional but recommended for production)
# Leave empty if not using CloudFront
AWS_CLOUDFRONT_DOMAIN=d123456789.cloudfront.net

# S3 Access Control List (ACL)
# Options: private, public-read, public-read-write, authenticated-read,
#          aws-exec-read, bucket-owner-read, bucket-owner-full-control
AWS_S3_ACL=private

# S3 Storage Class
# Options: STANDARD, REDUCED_REDUNDANCY, STANDARD_IA, ONEZONE_IA,
#          INTELLIGENT_TIERING, GLACIER, DEEP_ARCHIVE, GLACIER_IR
AWS_S3_STORAGE_CLASS=STANDARD

# S3 Server-Side Encryption
# Options: AES256, aws:kms, aws:kms:dsse
AWS_S3_SERVER_SIDE_ENCRYPTION=AES256

# Presigned URL expiry time in seconds (default: 3600 = 1 hour)
AWS_S3_PRESIGNED_URL_EXPIRY=3600

# =============================================================================
# EXAMPLE CONFIGURATIONS FOR DIFFERENT ENVIRONMENTS
# =============================================================================

# DEVELOPMENT ENVIRONMENT (Local Storage Only)
# STORAGE_PROVIDER=local

# STAGING ENVIRONMENT (Local Storage)
# STORAGE_PROVIDER=local

# PRODUCTION ENVIRONMENT (S3 Storage)
# STORAGE_PROVIDER=s3
# AWS_S3_BUCKET_NAME=hec-production-files
# AWS_CLOUDFRONT_DOMAIN=d123456789.cloudfront.net

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database connection
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=hec_user
DB_PASSWORD=your_db_password
DB_NAME=hec_database

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application environment
NODE_ENV=production

# Server configuration
PORT=3000
HOST=0.0.0.0

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# =============================================================================


# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. Never commit this file with real credentials to version control
# 2. Use IAM roles in production instead of access keys when possible
# 3. Ensure your S3 bucket has proper access policies
# 4. Enable S3 bucket versioning for data protection
# 5. Consider using AWS KMS for encryption in production
# 6. Set up CloudFront for better performance and security

# =============================================================================
# S3 BUCKET POLICY EXAMPLE
# =============================================================================

# Here's an example S3 bucket policy for your reference:
# {
#   "Version": "2012-10-17",
#   "Statement": [
#     {
#       "Sid": "AllowHECBackendAccess",
#       "Effect": "Allow",
#       "Principal": {
#         "AWS": "arn:aws:iam::YOUR_ACCOUNT_ID:user/hec-backend-user"
#       },
#       "Action": [
#         "s3:GetObject",
#         "s3:PutObject",
#         "s3:DeleteObject",
#         "s3:ListBucket"
#       ],
#       "Resource": [
#         "arn:aws:s3:::hec-production-files",
#         "arn:aws:s3:::hec-production-files/*"
#       ]
#     }
#   ]
# }

# =============================================================================
# IAM POLICY EXAMPLE
# =============================================================================

# Here's an example IAM policy for the HEC backend user:
# {
#   "Version": "2012-10-17",
#   "Statement": [
#     {
#       "Effect": "Allow",
#       "Action": [
#         "s3:GetObject",
#         "s3:PutObject",
#         "s3:DeleteObject",
#         "s3:GetObjectVersion",
#         "s3:DeleteObjectVersion"
#       ],
#       "Resource": "arn:aws:s3:::hec-production-files/*"
#     },
#     {
#       "Effect": "Allow",
#       "Action": [
#         "s3:ListBucket",
#         "s3:GetBucketLocation",
#         "s3:GetBucketVersioning"
#       ],
#       "Resource": "arn:aws:s3:::hec-production-files"
#     }
#   ]
# }

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# Common issues and solutions:

# 1. "Access Denied" errors:
#    - Check AWS credentials are correct
#    - Verify IAM user has proper permissions
#    - Ensure bucket policy allows access

# 2. "Bucket not found" errors:
#    - Verify bucket name is correct
#    - Check bucket region matches AWS_S3_BUCKET_REGION
#    - Ensure bucket exists in your AWS account

# 3. "Invalid storage provider" errors:
#    - Ensure STORAGE_PROVIDER is set to 'local' or 's3'
#    - Check for typos in environment variable names

# 4. Performance issues:
#    - Consider using CloudFront for better global performance
#    - Monitor AWS costs and usage patterns
