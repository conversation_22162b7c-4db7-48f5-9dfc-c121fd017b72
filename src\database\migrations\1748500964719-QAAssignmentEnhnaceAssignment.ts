import { MigrationInterface, QueryRunner } from "typeorm";

export class QAAssignmentEnhnaceAssignment1748500964719 implements MigrationInterface {
    name = 'QAAssignmentEnhnaceAssignment1748500964719'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa-assignment-items" ALTER COLUMN "score" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "qa-assignment-items" ALTER COLUMN "deadline" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa-assignment-items" ALTER COLUMN "deadline" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "qa-assignment-items" ALTER COLUMN "score" SET NOT NULL`);
    }

}
