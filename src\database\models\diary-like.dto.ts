import { ApiProperty } from '@nestjs/swagger';
import { LikerType } from '../entities/diary-entry-like.entity';

export class DiaryLikeResponseDto {
  @ApiProperty({
    description: 'The ID of the like',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'The ID of the diary entry',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  diaryEntryId: string;

  @ApiProperty({
    description: 'The ID of the user who liked the entry',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  likerId: string;

  @ApiProperty({
    description: 'The type of user who liked the entry',
    enum: LikerType,
    example: LikerType.STUDENT
  })
  likerType: LikerType;

  @ApiProperty({
    description: 'When the like was created',
    example: '2025-05-21T10:00:00.000Z'
  })
  createdAt: Date;
}

export class DiaryLikeCountResponseDto {
  @ApiProperty({
    description: 'The total number of likes for the diary entry',
    example: 42
  })
  count: number;

  @ApiProperty({
    description: 'Whether the current user has liked this entry',
    example: true
  })
  hasLiked: boolean;
}
