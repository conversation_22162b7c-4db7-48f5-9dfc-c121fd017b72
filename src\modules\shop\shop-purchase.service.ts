import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { ShopItemPurchase, PaymentMethod, PurchaseStatus } from '../../database/entities/shop-item-purchase.entity';
import { ShopItem, ShopItemType } from '../../database/entities/shop-item.entity';
import { User } from '../../database/entities/user.entity';
import { PurchaseShopItemDto, ShopItemPurchaseResponseDto } from '../../database/models/shop.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { ShopFileService } from './shop-file.service';

@Injectable()
export class ShopPurchaseService {
  private readonly logger = new Logger(ShopPurchaseService.name);

  constructor(
    @InjectRepository(ShopItemPurchase)
    private shopItemPurchaseRepository: Repository<ShopItemPurchase>,
    @InjectRepository(ShopItem)
    private shopItemRepository: Repository<ShopItem>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly shopFileService: ShopFileService,
  ) {}

  /**
   * Purchase a shop item
   * @param userId User ID making the purchase
   * @param purchaseShopItemDto Purchase data
   * @returns Purchase details
   */
  async purchaseShopItem(userId: string, purchaseShopItemDto: PurchaseShopItemDto): Promise<ShopItemPurchaseResponseDto> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOne({
        where: { id: userId }
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Check if shop item exists
      const shopItem = await this.shopItemRepository.findOne({
        where: { id: purchaseShopItemDto.shopItemId },
        relations: ['category']
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${purchaseShopItemDto.shopItemId} not found`);
      }

      // Check if the item is active
      if (!shopItem.isActive) {
        throw new BadRequestException(`Shop item with ID ${purchaseShopItemDto.shopItemId} is not active`);
      }

      // Check if the user has already purchased this item
      const existingPurchase = await this.shopItemPurchaseRepository.findOne({
        where: {
          userId,
          shopItemId: purchaseShopItemDto.shopItemId,
          status: PurchaseStatus.COMPLETED
        }
      });

      if (existingPurchase) {
        throw new BadRequestException(`You have already purchased this item`);
      }

      // Handle free items
      if (shopItem.type === ShopItemType.FREE) {
        purchaseShopItemDto.paymentMethod = PaymentMethod.FREE;
      }

      // Create the purchase
      const purchase = new ShopItemPurchase();
      purchase.userId = userId;
      purchase.shopItemId = purchaseShopItemDto.shopItemId;
      purchase.originalPrice = shopItem.price;
      purchase.finalPrice = shopItem.getFinalPrice();
      purchase.promotionId = shopItem.promotionId;
      purchase.discountAmount = shopItem.isOnSale() ? Number(shopItem.price) - Number(shopItem.discountedPrice) : null;
      purchase.paymentMethod = purchaseShopItemDto.paymentMethod;
      purchase.status = PurchaseStatus.COMPLETED;
      purchase.paymentDetails = purchaseShopItemDto.paymentDetails;
      purchase.notes = purchaseShopItemDto.notes || `Purchase completed via ${purchaseShopItemDto.paymentMethod}`;

      const savedPurchase = await this.shopItemPurchaseRepository.save(purchase);

      // Increment the purchase count for the shop item
      shopItem.purchaseCount += 1;
      await this.shopItemRepository.save(shopItem);

      // Map to response DTO
      return this.mapPurchaseToDto(savedPurchase, shopItem.title);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(`Error purchasing shop item: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to purchase shop item: ${error.message}`);
    }
  }

  /**
   * Get user purchases
   * @param userId User ID
   * @param paginationDto Pagination parameters
   * @returns List of purchases
   */
  async getUserPurchases(userId: string, paginationDto?: PaginationDto): Promise<PagedListDto<ShopItemPurchaseResponseDto>> {
    try {
      const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC' } = paginationDto || {};
      const skip = (page - 1) * limit;

      const [purchases, totalCount] = await this.shopItemPurchaseRepository.findAndCount({
        where: { userId },
        order: { [sortBy]: sortDirection },
        skip,
        take: limit,
        relations: ['shopItem']
      });

      // Map purchases to DTOs with file URLs
      const purchaseDtosPromises = purchases.map(async (purchase) => {
        const dto = this.mapPurchaseToDto(purchase, purchase.shopItem?.title);

        // Add file URL if available
        if (purchase.shopItem?.filePath) {
          try {
            dto.secureFileUrl = await this.shopFileService.getShopItemFileUrl(purchase.shopItemId);
          } catch (error) {
            this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
          }
        }

        return dto;
      });

      const purchaseDtos = await Promise.all(purchaseDtosPromises);
      return new PagedListDto(purchaseDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting user purchases: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get user purchases: ${error.message}`);
    }
  }

  /**
   * Get purchase by ID
   * @param id Purchase ID
   * @returns Purchase details
   */
  async getPurchaseById(id: string): Promise<ShopItemPurchaseResponseDto> {
    try {
      const purchase = await this.shopItemPurchaseRepository.findOne({
        where: { id },
        relations: ['shopItem']
      });

      if (!purchase) {
        throw new NotFoundException(`Purchase with ID ${id} not found`);
      }

      const dto = this.mapPurchaseToDto(purchase, purchase.shopItem?.title);

      // Add file URL if available
      if (purchase.shopItem?.filePath) {
        try {
          dto.secureFileUrl = await this.shopFileService.getShopItemFileUrl(purchase.shopItemId);
        } catch (error) {
          this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
        }
      }

      return dto;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting purchase: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get purchase: ${error.message}`);
    }
  }

  /**
   * Check if a user has purchased a specific item
   * @param userId User ID
   * @param shopItemId Shop item ID
   * @returns Whether the user has purchased the item
   */
  async hasUserPurchasedItem(userId: string, shopItemId: string): Promise<boolean> {
    try {
      const purchase = await this.shopItemPurchaseRepository.findOne({
        where: {
          userId,
          shopItemId,
          status: PurchaseStatus.COMPLETED
        }
      });

      return !!purchase;
    } catch (error) {
      this.logger.error(`Error checking if user purchased item: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Update purchase status (admin only)
   * @param id Purchase ID
   * @param status New status
   * @param notes Optional notes
   * @returns Updated purchase
   */
  async updatePurchaseStatus(
    id: string,
    status: PurchaseStatus,
    notes?: string
  ): Promise<ShopItemPurchaseResponseDto> {
    try {
      const purchase = await this.shopItemPurchaseRepository.findOne({
        where: { id },
        relations: ['shopItem']
      });

      if (!purchase) {
        throw new NotFoundException(`Purchase with ID ${id} not found`);
      }

      // Update status and notes
      purchase.status = status;
      if (notes) {
        purchase.notes = notes;
      }

      const updatedPurchase = await this.shopItemPurchaseRepository.save(purchase);
      return this.mapPurchaseToDto(updatedPurchase, purchase.shopItem?.title);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error updating purchase status: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to update purchase status: ${error.message}`);
    }
  }

  /**
   * Get all purchases (admin only)
   * @param status Optional status filter
   * @param paginationDto Pagination parameters
   * @returns List of purchases
   */
  async getAllPurchases(
    status?: PurchaseStatus,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<ShopItemPurchaseResponseDto>> {
    try {
      const whereConditions: FindOptionsWhere<ShopItemPurchase> = {};

      if (status) {
        whereConditions.status = status;
      }

      const { page = 1, limit = 10, sortBy = 'createdAt', sortDirection = 'DESC' } = paginationDto || {};
      const skip = (page - 1) * limit;

      const [purchases, totalCount] = await this.shopItemPurchaseRepository.findAndCount({
        where: whereConditions,
        order: { [sortBy]: sortDirection },
        skip,
        take: limit,
        relations: ['shopItem', 'user']
      });

      // Map purchases to DTOs
      const purchaseDtos = purchases.map(purchase =>
        this.mapPurchaseToDto(
          purchase,
          purchase.shopItem?.title,
          purchase.user?.name
        )
      );

      return new PagedListDto(purchaseDtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting purchases: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get purchases: ${error.message}`);
    }
  }

  /**
   * Map purchase entity to DTO
   * @param purchase Purchase entity
   * @param shopItemTitle Optional shop item title
   * @param userName Optional user name
   * @returns Purchase DTO
   */
  private mapPurchaseToDto(
    purchase: ShopItemPurchase,
    shopItemTitle?: string,
    userName?: string
  ): ShopItemPurchaseResponseDto {
    return {
      id: purchase.id,
      userId: purchase.userId,
      userName: userName,
      shopItemId: purchase.shopItemId,
      shopItemTitle: shopItemTitle,
      originalPrice: Number(purchase.originalPrice),
      finalPrice: Number(purchase.finalPrice),
      promotionId: purchase.promotionId,
      discountAmount: purchase.discountAmount ? Number(purchase.discountAmount) : null,
      paymentMethod: purchase.paymentMethod,
      status: purchase.status,
      paymentDetails: purchase.paymentDetails,
      notes: purchase.notes,
      createdAt: purchase.createdAt,
      updatedAt: purchase.updatedAt,
    };
  }
}
