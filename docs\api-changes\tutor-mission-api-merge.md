# Tutor Mission API Merge - Implementation Summary

## Overview

This document summarizes the changes made to merge the two separate tutor mission APIs into a single combined endpoint that requires both correction and score fields.

## Previous API Structure

### Separate Endpoints
1. `POST /diary/tutor/missions/entries/{id}/correction` - Add correction only
2. `POST /diary/tutor/missions/entries/{id}/score` - Assign score only

### Issues with Previous Structure
- Required two separate API calls to complete a review
- Possible inconsistent state if one operation failed
- More complex client-side logic
- Potential race conditions

## New API Structure

### Primary Endpoint
- `POST /diary/tutor/missions/entries/{id}/correction` - Add correction with score (combined)

### Request Body
```json
{
  "correction": "Here are some grammar corrections...",
  "score": 85
}
```

### Response
- Returns the updated entry with both correction and score
- Entry status is set to `CONFIRMED` after successful operation
- Single notification sent to student with both correction and score information

## Backward Compatibility

### Deprecated Endpoints (Maintained for Compatibility)
1. `POST /diary/tutor/missions/entries/{id}/correction-only` - Add correction only (DEPRECATED)
2. `POST /diary/tutor/missions/entries/{id}/score` - Assign score only (DEPRECATED)

These endpoints are marked as deprecated in the API documentation and will be removed in a future version.

## Implementation Details

### Files Modified

1. **src/database/models/mission-diary-entry.dto.ts**
   - Added `AddMissionCorrectionWithScoreDto` class
   - Marked existing DTOs as deprecated
   - Both correction and score fields are required with validation

2. **src/modules/diary/mission-diary-entry.service.ts**
   - Added `addCorrectionWithScore()` method
   - Combines validation logic from both separate methods
   - Single database transaction for atomicity
   - Enhanced notification with both correction and score details

3. **src/modules/diary/tutor-mission.controller.ts**
   - Updated primary endpoint to use combined DTO
   - Moved old endpoints to deprecated paths
   - Updated API documentation and descriptions

4. **docs/implementation/diary-mission-management-integration.md**
   - Updated API documentation
   - Added deprecation notices for old endpoints

5. **docs/api-testing/diary-mission-management-testing.md**
   - Updated test cases for combined API
   - Added separate test cases for deprecated endpoints
   - Updated notification testing flow

### Key Features

1. **Atomic Operation**: Both correction and score are saved in a single database transaction
2. **Validation**: Both fields are required and validated
3. **Status Management**: Entry status is set to `CONFIRMED` after successful operation
4. **Notifications**: Single notification sent with both correction and score information
5. **Error Handling**: Comprehensive validation and error messages
6. **Backward Compatibility**: Old endpoints maintained as deprecated

### Validation Rules

- Both `correction` and `score` fields are required
- `correction` must be a non-empty string
- `score` must be a number >= 0 and <= mission.score
- Entry must be in `SUBMITTED` status
- No existing correction or score allowed
- Only tutors can perform this operation

### Business Logic

1. Verify tutor permissions
2. Validate entry exists and is in correct state
3. Check for existing correction or score
4. Validate score range against mission maximum
5. Update entry with both correction and score
6. Set status to `CONFIRMED`
7. Save to database
8. Send notification to student
9. Return updated entry

## Benefits of the New Approach

1. **Simplified Client Logic**: Single API call instead of two
2. **Atomic Operations**: No partial state issues
3. **Better User Experience**: Complete review in one step
4. **Consistent Notifications**: Single notification with all information
5. **Reduced API Calls**: Better performance
6. **Cleaner Code**: Less duplication in service layer

## Migration Guide

### For API Consumers

**Old Approach:**
```javascript
// Step 1: Add correction
await api.post(`/diary/tutor/missions/entries/${id}/correction`, {
  correction: "Grammar corrections..."
});

// Step 2: Add score
await api.post(`/diary/tutor/missions/entries/${id}/score`, {
  score: 85
});
```

**New Approach:**
```javascript
// Single call with both correction and score
await api.post(`/diary/tutor/missions/entries/${id}/correction`, {
  correction: "Grammar corrections...",
  score: 85
});
```

### Timeline

- **Phase 1**: New combined API available (current)
- **Phase 2**: Deprecation notices for old endpoints (current)
- **Phase 3**: Remove deprecated endpoints (future release)

## Testing

Updated test cases cover:
- Combined correction and score submission
- Validation of required fields
- Error handling for various edge cases
- Backward compatibility with deprecated endpoints
- Notification flow for combined operation

## Conclusion

The API merge successfully consolidates the tutor review process into a single, atomic operation while maintaining backward compatibility. This change improves the user experience, reduces complexity, and provides better data consistency.
