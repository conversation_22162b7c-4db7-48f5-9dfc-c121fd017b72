import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsOptional, IsBoolean, IsUUID, IsDateString, Min, Max, IsIn } from 'class-validator';
import { Type } from 'class-transformer';
import { MissionEntryStatus } from '../entities/mission-diary-entry.entity';

// Request DTOs

/**
 * DTO for creating a new diary mission
 */
export class CreateDiaryMissionDto {
  @ApiProperty({ example: 'Weekly Writing Challenge', description: 'Title of the mission' })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ example: 'Write about your favorite book and why you enjoyed it.', description: 'Description of the mission' })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({ example: 200, description: 'Target word count for the mission' })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  targetWordCount: number;

  @ApiProperty({ example: 300, description: 'Maximum target word count for the mission (optional)', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  targetMaxWordCount?: number;

  @ApiProperty({ example: '2023-08-15T00:00:00Z', description: 'Date when the mission becomes available' })
  @IsNotEmpty()
  @IsDateString()
  publishDate: string;

  @ApiProperty({ example: '2023-08-30T23:59:59Z', description: 'Date when the mission expires (optional)', required: false })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @ApiProperty({ example: 100, description: 'Maximum possible score for the mission' })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  score: number;
}

/**
 * DTO for updating a diary mission
 */
export class UpdateDiaryMissionDto {
  @ApiProperty({ example: 'Updated Writing Challenge', description: 'Title of the mission', required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ example: 'Updated description for the mission.', description: 'Description of the mission', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ example: 250, description: 'Target word count for the mission', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  targetWordCount?: number;

  @ApiProperty({ example: 350, description: 'Maximum target word count for the mission', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  targetMaxWordCount?: number;

  @ApiProperty({ example: '2023-08-20T00:00:00Z', description: 'Date when the mission becomes available', required: false })
  @IsOptional()
  @IsDateString()
  publishDate?: string;

  @ApiProperty({ example: '2023-09-05T23:59:59Z', description: 'Date when the mission expires', required: false })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @ApiProperty({ example: true, description: 'Whether the mission is active', required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ example: 120, description: 'Maximum possible score for the mission', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  score?: number;
}

/**
 * DTO for filtering diary missions
 */
export class MissionFilterDto {
  @ApiProperty({ example: true, description: 'Filter by active status', required: false })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiProperty({ example: '2023-08-01', description: 'Filter by publish date (from)', required: false })
  @IsOptional()
  @IsDateString()
  publishDateFrom?: string;

  @ApiProperty({ example: '2023-08-31', description: 'Filter by publish date (to)', required: false })
  @IsOptional()
  @IsDateString()
  publishDateTo?: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Filter by creator ID', required: false })
  @IsOptional()
  @IsUUID()
  createdBy?: string;

  @ApiProperty({ example: 'createdAt', description: 'Field to sort by', required: false })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiProperty({ example: 'DESC', description: 'Sort direction', enum: ['ASC', 'DESC'], required: false })
  @IsOptional()
  @IsString()
  @IsIn(['ASC', 'DESC'])
  sortDirection?: 'ASC' | 'DESC';
}

// Response DTOs

/**
 * DTO for diary mission response
 */
export class DiaryMissionResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Mission ID' })
  id: string;

  @ApiProperty({ example: 'Weekly Writing Challenge', description: 'Title of the mission' })
  title: string;

  @ApiProperty({ example: 'Write about your favorite book and why you enjoyed it.', description: 'Description of the mission' })
  description: string;

  @ApiProperty({ example: 200, description: 'Target word count for the mission' })
  targetWordCount: number;

  @ApiProperty({ example: 300, description: 'Maximum target word count for the mission', required: false })
  targetMaxWordCount?: number;

  @ApiProperty({ example: 75, description: 'Progress of the mission in percent', required: false })
  progress?: number;
  
  @ApiProperty({ example: '2023-08-15T00:00:00Z', description: 'Date when the mission becomes available' })
  publishDate: Date;

  @ApiProperty({ example: '2023-08-30T23:59:59Z', description: 'Date when the mission expires', required: false })
  expiryDate?: Date;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'ID of the admin who created the mission' })
  adminId: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'ID of the admin who created the mission (deprecated, use adminId instead)' })
  createdBy?: string;

  @ApiProperty({ example: true, description: 'Whether the mission is active' })
  isActive: boolean;

  @ApiProperty({ example: 100, description: 'Maximum possible score for the mission' })
  score: number;

  @ApiProperty({ example: '2023-08-10T12:00:00Z', description: 'When the mission was created' })
  createdAt: Date;

  @ApiProperty({ example: '2023-08-11T15:30:00Z', description: 'When the mission was last updated' })
  updatedAt: Date;
}
