import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { DiaryAwardService } from '../diary/diary-award.service';
import { EssayAwardService } from '../essay/essay-award.service';
import { NovelAwardService } from '../novel/novel-award.service';
import { getCurrentUTCDate, addDaysUTC, addMonthsUTC, getStartOfMonthUTC, getEndOfMonthUTC } from '../../common/utils/date-utils';

/**
 * Simplified Award Scheduler
 *
 * This replaces the complex dual-layer scheduling system with a simple, direct approach:
 * - Monthly awards: Generated on the 1st of each month for the previous month
 * - Annual awards: Generated on January 1st for the previous year
 * - No database scheduling overhead - just direct cron jobs
 * - Easy to understand, maintain, and extend
 */
@Injectable()
export class SimplifiedAwardScheduler {
  private readonly logger = new Logger(SimplifiedAwardScheduler.name);
  private isProcessingMonthly = false;
  private isProcessingAnnual = false;

  constructor(
    private readonly diaryAwardService: DiaryAwardService,
    private readonly essayAwardService: EssayAwardService,
    private readonly novelAwardService: NovelAwardService,
  ) {}

  /**
   * Generate monthly awards for all modules on the 1st of each month at 2:00 AM UTC
   * Processes the previous month's data
   */
  @Cron('0 2 1 * *') // At 02:00 on day-of-month 1
  async generateMonthlyAwards() {
    if (this.isProcessingMonthly) {
      this.logger.log('Monthly award generation already in progress, skipping');
      return;
    }

    try {
      this.isProcessingMonthly = true;
      this.logger.log('Starting monthly award generation for all modules');

      const today = getCurrentUTCDate();
      const lastMonth = addMonthsUTC(today, -1);
      const startDate = getStartOfMonthUTC(lastMonth);
      const endDate = getEndOfMonthUTC(lastMonth);

      this.logger.log(`Generating monthly awards for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

      // Generate awards for all modules
      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(startDate, endDate),
        this.essayAwardService.generateAwardsForRange(startDate, endDate),
        this.novelAwardService.generateAwardsForRange(startDate, endDate)
      ]);

      this.logger.log('Monthly awards generated successfully for all modules');
    } catch (error) {
      this.logger.error(`Error generating monthly awards: ${error.message}`, error.stack);
    } finally {
      this.isProcessingMonthly = false;
    }
  }

  /**
   * Generate annual awards for all modules on January 1st at 3:00 AM UTC
   * Processes the previous year's data
   */
  @Cron('0 3 1 1 *') // At 03:00 on January 1st
  async generateAnnualAwards() {
    if (this.isProcessingAnnual) {
      this.logger.log('Annual award generation already in progress, skipping');
      return;
    }

    try {
      this.isProcessingAnnual = true;
      this.logger.log('Starting annual award generation for all modules');

      const today = getCurrentUTCDate();
      const lastYear = today.getUTCFullYear() - 1;
      const startDate = new Date(Date.UTC(lastYear, 0, 1)); // January 1st of last year
      const endDate = new Date(Date.UTC(lastYear, 11, 31, 23, 59, 59, 999)); // December 31st of last year

      this.logger.log(`Generating annual awards for period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

      // Generate awards for all modules
      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(startDate, endDate),
        this.essayAwardService.generateAwardsForRange(startDate, endDate),
        this.novelAwardService.generateAwardsForRange(startDate, endDate)
      ]);

      this.logger.log('Annual awards generated successfully for all modules');
    } catch (error) {
      this.logger.error(`Error generating annual awards: ${error.message}`, error.stack);
    } finally {
      this.isProcessingAnnual = false;
    }
  }

  /**
   * Manual trigger for monthly awards (for testing or manual execution)
   * @param year Optional year (defaults to current year)
   * @param month Optional month (1-12, defaults to previous month)
   */
  async triggerMonthlyAwards(year?: number, month?: number): Promise<void> {
    try {
      const today = getCurrentUTCDate();
      const targetYear = year || today.getUTCFullYear();
      const targetMonth = month || (today.getUTCMonth() === 0 ? 12 : today.getUTCMonth()); // Previous month

      // Adjust year if we're looking at December of previous year
      const actualYear = (month === 12 && today.getUTCMonth() === 0) ? targetYear - 1 : targetYear;

      const startDate = new Date(Date.UTC(actualYear, targetMonth - 1, 1));
      const endDate = getEndOfMonthUTC(startDate);

      this.logger.log(`Manually triggering monthly awards for ${targetMonth}/${actualYear}`);
      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(startDate, endDate),
        this.essayAwardService.generateAwardsForRange(startDate, endDate),
        this.novelAwardService.generateAwardsForRange(startDate, endDate)
      ]);
      this.logger.log(`Manual monthly award generation completed for ${targetMonth}/${actualYear}`);
    } catch (error) {
      this.logger.error(`Error in manual monthly award generation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Manual trigger for annual awards (for testing or manual execution)
   * @param year Optional year (defaults to previous year)
   */
  async triggerAnnualAwards(year?: number): Promise<void> {
    try {
      const today = getCurrentUTCDate();
      const targetYear = year || (today.getUTCFullYear() - 1);

      const startDate = new Date(Date.UTC(targetYear, 0, 1));
      const endDate = new Date(Date.UTC(targetYear, 11, 31, 23, 59, 59, 999));

      this.logger.log(`Manually triggering annual awards for ${targetYear}`);
      await Promise.all([
        this.diaryAwardService.generateAwardsForRange(startDate, endDate),
        this.essayAwardService.generateAwardsForRange(startDate, endDate),
        this.novelAwardService.generateAwardsForRange(startDate, endDate)
      ]);
      this.logger.log(`Manual annual award generation completed for ${targetYear}`);
    } catch (error) {
      this.logger.error(`Error in manual annual award generation: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get scheduler status for monitoring
   */
  getStatus() {
    return {
      monthlyProcessing: this.isProcessingMonthly,
      annualProcessing: this.isProcessingAnnual,
      nextMonthlyRun: 'Every 1st of month at 02:00 UTC (All modules)',
      nextAnnualRun: 'January 1st at 03:00 UTC (All modules)',
      lastMonthlyPeriod: this.getLastMonthlyPeriod(),
      lastAnnualPeriod: this.getLastAnnualPeriod(),
    };
  }

  /**
   * Get the period for last month's awards
   */
  private getLastMonthlyPeriod() {
    const today = getCurrentUTCDate();
    const lastMonth = addMonthsUTC(today, -1);
    const startDate = getStartOfMonthUTC(lastMonth);
    const endDate = getEndOfMonthUTC(lastMonth);

    return {
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      month: lastMonth.getUTCMonth() + 1,
      year: lastMonth.getUTCFullYear(),
    };
  }

  /**
   * Get the period for last year's awards
   */
  private getLastAnnualPeriod() {
    const today = getCurrentUTCDate();
    const lastYear = today.getUTCFullYear() - 1;

    return {
      startDate: new Date(Date.UTC(lastYear, 0, 1)).toISOString(),
      endDate: new Date(Date.UTC(lastYear, 11, 31, 23, 59, 59, 999)).toISOString(),
      year: lastYear,
    };
  }
}
