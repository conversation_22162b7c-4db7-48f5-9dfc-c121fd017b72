import { MigrationInterface, QueryRunner } from "typeorm";

export class QAMissionAllModel1746444686887 implements MigrationInterface {
    name = 'QAMissionAllModel1746444686887'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "qa_task_submission_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "submission_id" character varying NOT NULL, "content" text NOT NULL, "word_count" integer NOT NULL, "submission_date" TIMESTAMP NOT NULL, "sequence_number" integer NOT NULL, "meta_data" json, "submission" uuid, CONSTRAINT "PK_0e129316b58f562367a9846563f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_d24d8afb23a70831c0a86e4595" ON "qa_task_submission_history" ("submission_date") `);
        await queryRunner.query(`CREATE INDEX "IDX_be57aa932722d380a1f9399138" ON "qa_task_submission_history" ("submission", "sequence_number") `);
        await queryRunner.query(`CREATE TABLE "qa_task_submission_marking" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "points" double precision NOT NULL, "submission_feedback" text, "task_remarks" text, "submission_id" uuid NOT NULL, "submission_history_id" uuid NOT NULL, "submission" uuid, "submission_mark_id" uuid, CONSTRAINT "REL_5769bb5bb9b640e0d811df4c4f" UNIQUE ("submission_mark_id"), CONSTRAINT "PK_9164c9fb16da83d479b45bedf81" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_b172bc233df0e2aaf1872c497f" ON "qa_task_submission_marking" ("submission_id", "submission_history_id") `);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ADD "latest_submission_id" uuid`);
        await queryRunner.query(`ALTER TYPE "public"."qa_task_submissions_status_enum" RENAME TO "qa_task_submissions_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."qa_task_submissions_status_enum" AS ENUM('draft', 'submitted', 'reviewed', 'discarded')`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ALTER COLUMN "status" TYPE "public"."qa_task_submissions_status_enum" USING "status"::"text"::"public"."qa_task_submissions_status_enum"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ALTER COLUMN "status" SET DEFAULT 'draft'`);
        await queryRunner.query(`DROP TYPE "public"."qa_task_submissions_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_history" ADD CONSTRAINT "FK_823255a58f1ab55467fb8a4e511" FOREIGN KEY ("submission") REFERENCES "qa_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "FK_c642437d858571413d52240de5f" FOREIGN KEY ("submission") REFERENCES "qa_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "FK_5769bb5bb9b640e0d811df4c4f0" FOREIGN KEY ("submission_mark_id") REFERENCES "essay_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "FK_5769bb5bb9b640e0d811df4c4f0"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "FK_c642437d858571413d52240de5f"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_history" DROP CONSTRAINT "FK_823255a58f1ab55467fb8a4e511"`);
        await queryRunner.query(`CREATE TYPE "public"."qa_task_submissions_status_enum_old" AS ENUM('draft', 'submitted', 'reviewed')`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ALTER COLUMN "status" TYPE "public"."qa_task_submissions_status_enum_old" USING "status"::"text"::"public"."qa_task_submissions_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ALTER COLUMN "status" SET DEFAULT 'draft'`);
        await queryRunner.query(`DROP TYPE "public"."qa_task_submissions_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."qa_task_submissions_status_enum_old" RENAME TO "qa_task_submissions_status_enum"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP COLUMN "latest_submission_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b172bc233df0e2aaf1872c497f"`);
        await queryRunner.query(`DROP TABLE "qa_task_submission_marking"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_be57aa932722d380a1f9399138"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d24d8afb23a70831c0a86e4595"`);
        await queryRunner.query(`DROP TABLE "qa_task_submission_history"`);
        await queryRunner.query(`ALTER TABLE "shop_item" RENAME COLUMN "price_equivalent_to_reward_point" TO "is_purchasable_in_rewardpoint"`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD CONSTRAINT "FK_5e620f3877939956645a39860f7" FOREIGN KEY ("category_id") REFERENCES "shop_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
