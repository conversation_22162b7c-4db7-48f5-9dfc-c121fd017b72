import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Req, HttpCode, HttpStatus, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { PlansService } from './plans.service';
import { CreatePlanDto, UpdatePlanDto, SubscribeToPlanDto, PlanResponseDto, UserPlanResponseDto, PlanFilterDto } from '../../database/models/plans.dto';
import { ApplyPromotionToPlanDto } from '../../database/models/apply-promotion-to-plan.dto';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { ApiResponse } from 'src/common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from 'src/common/decorators/api-response.decorator';
import { PagedListDto } from 'src/common/models/paged-list.dto';
import { PaginationDto } from 'src/common/models/pagination.dto';

@ApiTags('plans')
@ApiBearerAuth('JWT-auth')
@Controller('plans')
export class PlansController {
    constructor(private readonly plansService: PlansService) {}

    @Get()
    @ApiOperation({
        summary: 'Get plans with filtering',
        description: 'Retrieves subscription plans with filtering options. By default returns only active plans. Use status=all to get both active and inactive plans.'
    })
    @ApiQuery({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number for pagination'
    })
    @ApiQuery({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of items per page'
    })
    @ApiQuery({
        name: 'sortBy',
        required: false,
        type: String,
        description: 'Field to sort by'
    })
    @ApiQuery({
        name: 'sortDirection',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Sort direction (ASC or DESC)'
    })
    @ApiQuery({
        name: 'status',
        required: false,
        enum: ['active', 'inactive', 'all'],
        description: 'Filter by plan status: active (default), inactive, or all',
        example: 'active'
    })
    @ApiOkResponseWithPagedListType(PlanResponseDto, 'Returns plans based on filter criteria')
    async findAll(@Query() filterDto?: PlanFilterDto): Promise<ApiResponse<PagedListDto<PlanResponseDto>>> {
        const result = await this.plansService.findAll(filterDto);
        return ApiResponse.success(result, 'Plans retrieved successfully');
    }

    @Get(':id')
    @ApiOperation({
        summary: 'Get a plan by ID',
        description: 'Retrieves a specific subscription plan by its ID.'
    })
    @ApiParam({
        name: 'id',
        description: 'The ID of the plan to retrieve',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiOkResponseWithType(PlanResponseDto, 'Returns the plan')
    @ApiErrorResponse(404, 'Plan not found')
    async findOne(@Param('id') id: string): Promise<ApiResponse<PlanResponseDto>> {
        const result = await this.plansService.findById(id);
        return ApiResponse.success(result, 'Plan retrieved successfully');
    }

    @Post()
    @UseGuards(JwtAuthGuard, AdminGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Create a new plan (Admin only)',
        description: 'Creates a new subscription plan. Only accessible by admins.'
    })
    @ApiBody({
        type: CreatePlanDto,
        description: 'Plan creation data',
        examples: {
            starter: {
                summary: 'Create Starter Plan',
                description: 'Create a new starter plan',
                value: {
                    name: 'Starter Plan',
                    type: 'starter',
                    subscriptionType: 'monthly',
                    price: 9.99,
                    description: 'Basic plan with essential features',
                    features: ['Feature 1', 'Feature 2'],
                    isActive: true
                }
            },
            premium: {
                summary: 'Create Premium Plan',
                description: 'Create a new premium plan',
                value: {
                    name: 'Premium Plan',
                    type: 'premium',
                    subscriptionType: 'yearly',
                    price: 99.99,
                    description: 'Premium plan with all features',
                    features: ['Feature 1', 'Feature 2', 'Feature 3', 'Feature 4'],
                    isActive: true
                }
            }
        }
    })
    @ApiOkResponseWithType(PlanResponseDto, 'Plan created successfully')
    @ApiErrorResponse(400, 'Invalid input')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(409, 'Plan with the same name already exists')
    async create(@Body() createPlanDto: CreatePlanDto): Promise<ApiResponse<PlanResponseDto>> {
        const result = await this.plansService.create(createPlanDto);
        return ApiResponse.success(result, 'Plan created successfully', 201);
    }

    @Patch(':id')
    @UseGuards(JwtAuthGuard, AdminGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Update a plan (Admin only)',
        description: 'Updates an existing subscription plan. Only accessible by admins.'
    })
    @ApiParam({
        name: 'id',
        description: 'The ID of the plan to update',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiBody({
        type: UpdatePlanDto,
        description: 'Plan update data',
        examples: {
            update: {
                summary: 'Update Plan',
                description: 'Update an existing plan',
                value: {
                    name: 'Updated Plan Name',
                    price: 19.99,
                    description: 'Updated plan description',
                    features: ['Feature 1', 'Feature 2', 'New Feature'],
                    isActive: true
                }
            }
        }
    })
    @ApiOkResponseWithType(PlanResponseDto, 'Plan updated successfully')
    @ApiErrorResponse(400, 'Invalid input')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'Plan not found')
    @ApiErrorResponse(409, 'Plan with the same name already exists')
    async update(@Param('id') id: string, @Body() updatePlanDto: UpdatePlanDto): Promise<ApiResponse<PlanResponseDto>> {
        const result = await this.plansService.update(id, updatePlanDto);
        return ApiResponse.success(result, 'Plan updated successfully');
    }

    @Delete(':id')
    @UseGuards(JwtAuthGuard, AdminGuard)
    @ApiBearerAuth('JWT-auth')
    @HttpCode(HttpStatus.NO_CONTENT)
    @ApiOperation({
        summary: 'Delete a plan (Admin only)',
        description: 'Deletes a subscription plan. Only accessible by admins. Cannot delete plans that are in use.'
    })
    @ApiParam({
        name: 'id',
        description: 'The ID of the plan to delete',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiOkResponseWithType(Object, 'Plan deleted successfully')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'Plan not found')
    @ApiErrorResponse(409, 'Cannot delete a plan that is in use')
    async remove(@Param('id') id: string): Promise<ApiResponse<void>> {
        await this.plansService.remove(id);
        return ApiResponse.success(null, 'Plan removed successfully');
    }

    @Post('subscribe')
    @UseGuards(JwtAuthGuard, StudentGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Subscribe to a plan (Student only)',
        description: 'Subscribe the current student to a plan for the first time. Users with existing subscriptions must use the upgrade endpoint. You can enable auto-renewal for automatic subscription renewal. Only available for students.'
    })
    @ApiBody({
        type: SubscribeToPlanDto,
        description: 'Subscription data',
        examples: {
            subscribe: {
                summary: 'Subscribe to Plan',
                description: 'Subscribe to a plan',
                value: {
                    planId: '123e4567-e89b-12d3-a456-426614174000',
                    autoRenew: true
                }
            }
        }
    })
    @ApiOkResponseWithType(UserPlanResponseDto, 'Subscription successful with new authentication token')
    @ApiErrorResponse(400, 'Invalid input - Only students can subscribe to plans')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Student access required')
    @ApiErrorResponse(404, 'Plan not found')
    @ApiErrorResponse(409, 'User already has active subscription - use upgrade endpoint')
    async subscribe(@Body() subscribeToPlanDto: SubscribeToPlanDto, @Req() req: any): Promise<ApiResponse<UserPlanResponseDto>> {
        // Get user ID from JWT token
        const userId = req.user.sub;
        const result = await this.plansService.subscribeToPlan(subscribeToPlanDto, userId);
        return ApiResponse.success(result, 'Successfully subscribed to plan');
    }

    @Post('upgrade')
    @UseGuards(JwtAuthGuard, StudentGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Upgrade or change plan (Student only)',
        description: 'Upgrade or change the current student\'s subscription plan. Preserves existing tutor assignments for unchanged features and intelligently assigns tutors for new features. For downgrades, all tutor assignments are preserved for future upgrades. Only available for students with existing subscriptions.'
    })
    @ApiBody({
        type: SubscribeToPlanDto,
        description: 'Plan upgrade data',
        examples: {
            upgrade: {
                summary: 'Upgrade to Premium Plan',
                description: 'Upgrade from current plan to premium',
                value: {
                    planId: '123e4567-e89b-12d3-a456-426614174001',
                    autoRenew: true
                }
            },
            downgrade: {
                summary: 'Downgrade to Basic Plan',
                description: 'Downgrade from current plan to basic (tutors preserved)',
                value: {
                    planId: '123e4567-e89b-12d3-a456-426614174002',
                    autoRenew: false
                }
            }
        }
    })
    @ApiOkResponseWithType(UserPlanResponseDto, 'Plan upgrade successful with new authentication token')
    @ApiErrorResponse(400, 'Invalid input - User has no active subscription to upgrade')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Student access required')
    @ApiErrorResponse(404, 'Plan not found')
    @ApiErrorResponse(409, 'User already subscribed to this plan')
    async upgradePlan(@Body() upgradePlanDto: SubscribeToPlanDto, @Req() req: any): Promise<ApiResponse<UserPlanResponseDto>> {
        // Get user ID from JWT token
        const userId = req.user.sub;
        const result = await this.plansService.upgradePlan(userId, upgradePlanDto);
        return ApiResponse.success(result, 'Successfully upgraded plan');
    }

    @Get('user/active')
    @UseGuards(JwtAuthGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Get current user active plan',
        description: 'Retrieves the currently active subscription plan for the authenticated user.'
    })
    @ApiOkResponseWithType(UserPlanResponseDto, 'Returns the active plan')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(404, 'No active plan found')
    async getActiveUserPlan(@Req() req: any): Promise<ApiResponse<UserPlanResponseDto>> {
        // Get user ID from JWT token
        const userId = req.user.sub;
        const result = await this.plansService.getActiveUserPlan(userId);
        return ApiResponse.success(result, 'Active plan retrieved successfully');
    }

    @Get('user/history')
    @UseGuards(JwtAuthGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Get subscription history for current user',
        description: 'Retrieves the subscription history for the authenticated user.'
    })
    @ApiQuery({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number for pagination'
    })
    @ApiQuery({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of items per page'
    })
    @ApiQuery({
        name: 'sortBy',
        required: false,
        type: String,
        description: 'Field to sort by'
    })
    @ApiQuery({
        name: 'sortDirection',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Sort direction (ASC or DESC)'
    })
    @ApiOkResponseWithPagedListType(UserPlanResponseDto, 'Returns the subscription history')
    @ApiErrorResponse(401, 'Unauthorized')
    async getUserPlans(
        @Req() req: any,
        @Query() paginationDto?: PaginationDto
    ): Promise<ApiResponse<PagedListDto<UserPlanResponseDto>>> {
        // Get user ID from JWT token
        const userId = req.user.sub;
        const result = await this.plansService.getUserPlans(userId, paginationDto);
        return ApiResponse.success(result, 'Subscription history retrieved successfully');
    }

    @Delete('user/cancel/:planId')
    @UseGuards(JwtAuthGuard)
    @ApiBearerAuth('JWT-auth')
    @HttpCode(HttpStatus.NO_CONTENT)
    @ApiOperation({
        summary: 'Cancel subscription to a plan',
        description: 'Cancels the current user\'s subscription to a specific plan.'
    })
    @ApiParam({
        name: 'planId',
        description: 'The ID of the plan to cancel subscription for',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiOkResponseWithType(Object, 'Subscription cancelled successfully')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(404, 'Subscription not found')
    async cancelSubscription(@Param('planId') planId: string, @Req() req: any): Promise<ApiResponse<void>> {
        // Get user ID from JWT token
        const userId = req.user.sub;
        await this.plansService.cancelUserPlan(userId, planId);
        return ApiResponse.success(null, 'Subscription cancelled successfully');
    }

    // Admin endpoints for managing user subscriptions
    @Get('admin/user/:userId/plans')
    @UseGuards(JwtAuthGuard, AdminGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Get subscription history for a user (Admin only)',
        description: 'Retrieves the subscription history for a specific user. Only accessible by admins.'
    })
    @ApiParam({
        name: 'userId',
        description: 'The ID of the user to get subscription history for',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiQuery({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number for pagination'
    })
    @ApiQuery({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of items per page'
    })
    @ApiQuery({
        name: 'sortBy',
        required: false,
        type: String,
        description: 'Field to sort by'
    })
    @ApiQuery({
        name: 'sortDirection',
        required: false,
        enum: ['ASC', 'DESC'],
        description: 'Sort direction (ASC or DESC)'
    })
    @ApiOkResponseWithPagedListType(UserPlanResponseDto, 'Returns the subscription history')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'User not found')
    async getUserPlansAdmin(
        @Param('userId') userId: string,
        @Query() paginationDto?: PaginationDto
    ): Promise<ApiResponse<PagedListDto<UserPlanResponseDto>>> {
        const result = await this.plansService.getUserPlans(userId, paginationDto);
        return ApiResponse.success(result, 'User subscription history retrieved successfully');
    }

    @Post('admin/user/:userId/subscribe')
    @UseGuards(JwtAuthGuard, AdminGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Subscribe a student to a plan (Admin only)',
        description: 'Subscribes a specific student to a plan. Only accessible by admins. Note: Only student users can be subscribed to plans.'
    })
    @ApiParam({
        name: 'userId',
        description: 'The ID of the user to subscribe',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiBody({
        type: SubscribeToPlanDto,
        description: 'Subscription data',
        examples: {
            subscribe: {
                summary: 'Subscribe User to Plan',
                description: 'Subscribe a user to a plan',
                value: {
                    planId: '123e4567-e89b-12d3-a456-426614174000',
                    autoRenew: true
                }
            }
        }
    })
    @ApiOkResponseWithType(UserPlanResponseDto, 'User subscribed successfully with new authentication token')
    @ApiErrorResponse(400, 'Invalid input - Only students can subscribe to plans')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'User or plan not found')
    @ApiErrorResponse(409, 'User already subscribed to this plan')
    async subscribeUserAdmin(
        @Param('userId') userId: string,
        @Body() subscribeToPlanDto: SubscribeToPlanDto
    ): Promise<ApiResponse<UserPlanResponseDto>> {
        const result = await this.plansService.subscribeToPlan(subscribeToPlanDto, userId, true);
        return ApiResponse.success(result, 'User successfully subscribed to plan');
    }

    @Delete('admin/user/:userId/cancel/:planId')
    @UseGuards(JwtAuthGuard, AdminGuard)
    @ApiBearerAuth('JWT-auth')
    @HttpCode(HttpStatus.NO_CONTENT)
    @ApiOperation({
        summary: 'Cancel a user subscription (Admin only)',
        description: 'Cancels a specific user\'s subscription to a plan. Only accessible by admins.'
    })
    @ApiParam({
        name: 'userId',
        description: 'The ID of the user to cancel subscription for',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiParam({
        name: 'planId',
        description: 'The ID of the plan to cancel subscription for',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiOkResponseWithType(Object, 'User subscription cancelled successfully')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'Subscription not found')
    async cancelUserSubscriptionAdmin(
        @Param('userId') userId: string,
        @Param('planId') planId: string
    ): Promise<ApiResponse<void>> {
        await this.plansService.cancelUserPlan(userId, planId);
        return ApiResponse.success(null, 'User subscription cancelled successfully');
    }

    @Post('admin/apply-promotion')
    @UseGuards(JwtAuthGuard, AdminGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Apply a promotion to plans (Admin only)',
        description: 'Applies a promotion to multiple plans. Only accessible by admins.'
    })
    @ApiBody({
        type: ApplyPromotionToPlanDto,
        description: 'Promotion application data',
        examples: {
            apply: {
                summary: 'Apply Promotion to Plans',
                description: 'Apply a promotion to multiple plans',
                value: {
                    promotionId: '123e4567-e89b-12d3-a456-426614174001',
                    planIds: [
                        '123e4567-e89b-12d3-a456-426614174002',
                        '123e4567-e89b-12d3-a456-426614174003'
                    ]
                }
            }
        }
    })
    @ApiOkResponseWithType(Object, 'Promotion applied successfully')
    @ApiErrorResponse(400, 'Invalid input')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'No plans found with the provided IDs')
    async applyPromotionToPlans(@Body() applyPromotionToPlanDto: ApplyPromotionToPlanDto): Promise<ApiResponse<{ success: boolean; message: string }>> {
        const result = await this.plansService.applyPromotionToPlans(applyPromotionToPlanDto);
        return ApiResponse.success(result, result.message);
    }

    @Post('admin/user/:userId/fix-tutors')
    @UseGuards(JwtAuthGuard, AdminGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Fix missing tutor assignments for a student (Admin only)',
        description: 'Checks all features in the student\'s active plan and assigns tutors for any missing assignments. This is useful for resolving issues where students don\'t have tutors due to previous assignment failures.'
    })
    @ApiParam({
        name: 'userId',
        description: 'The ID of the student to fix tutor assignments for',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiOkResponseWithType(Object, 'Tutor assignment fix completed')
    @ApiErrorResponse(400, 'Invalid input - User must be a student with an active plan')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'Student or active plan not found')
    async fixMissingTutorAssignments(
        @Param('userId') userId: string
    ): Promise<ApiResponse<{
        assignmentsCreated: number;
        featuresChecked: number;
        missingFeatures: string[];
        errors: string[];
    }>> {
        const result = await this.plansService.fixMissingTutorAssignments(userId);
        return ApiResponse.success(result, `Fixed missing tutor assignments: ${result.assignmentsCreated} assignments created`);
    }

    @Delete('admin/:id/promotion')
    @UseGuards(JwtAuthGuard, AdminGuard)
    @ApiBearerAuth('JWT-auth')
    @ApiOperation({
        summary: 'Remove promotion from a plan (Admin only)',
        description: 'Remove the promotion from a specific plan. Only accessible by admins.'
    })
    @ApiParam({
        name: 'id',
        description: 'The ID of the plan to remove promotion from',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    @ApiOkResponseWithType(Object, 'Promotion removed successfully')
    @ApiErrorResponse(400, 'Invalid input')
    @ApiErrorResponse(401, 'Unauthorized')
    @ApiErrorResponse(403, 'Forbidden - Admin access required')
    @ApiErrorResponse(404, 'Plan not found')
    async removePromotionFromPlan(@Param('id') id: string): Promise<ApiResponse<{ success: boolean; message: string }>> {
        const result = await this.plansService.removePromotionFromPlan(id);
        return ApiResponse.success(result, result.message);
    }
}
