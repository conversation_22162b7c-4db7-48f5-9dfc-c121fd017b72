# Promotion Management

This document outlines the promotion management system in the HEC backend application, including its features, API endpoints, and integration with other modules.

## Overview

The promotion management system allows administrators to create and manage promotions that can be applied to shop items and plans. Promotions can offer percentage or fixed amount discounts, can be restricted to specific categories or plans, and can have various limitations such as usage limits and valid date ranges.

## Promotion Attributes

Each promotion has the following attributes:

| Attribute | Type | Description | Required |
|-----------|------|-------------|----------|
| name | string | Name of the promotion | Yes |
| description | string | Description of the promotion | Yes |
| promotionType | enum | Type of promotion (percentage/fixed_amount) | Yes |
| discountType | enum | Type of discount (percentage/fixed_amount) | Yes |
| discountValue | number | Value of the discount (percentage or fixed amount) | Yes |
| applicableType | enum | Type of items the promotion applies to (ALL/SHOP_ITEM/PLAN) | Yes |
| applicableCategoryIds | string[] | Category IDs the promotion applies to (for shop items) | No |
| applicablePlanIds | string[] | Plan IDs the promotion applies to (for plans) | No |
| appliedToPlan | boolean | Whether the promotion is applied to a plan | No |
| promotionCode | string | Code that can be entered to apply the promotion | No |
| startDate | Date | Start date of the promotion | No |
| endDate | Date | End date of the promotion | No |
| isActive | boolean | Whether the promotion is active | Yes |
| usageLimit | number | Maximum number of times the promotion can be used | No |
| minimumPurchaseAmount | number | Minimum purchase amount required to apply the promotion | No |
| maximumPurchaseAmount | number | Maximum purchase amount eligible for the promotion | No |
| maximumDiscountAmount | number | Maximum discount amount that can be applied | No |

> **Note**: The `discountAmount` is a calculated field and is not stored in the database. It is calculated at runtime based on the `promotionType`, `discountValue`, and the price of the item. This field should not be included in creation or update operations.

## Promotion Status

A promotion can have one of the following statuses:

- **ACTIVE**: The promotion is active and can be applied
- **INACTIVE**: The promotion has been manually deactivated
- **SCHEDULED**: The promotion is scheduled to start in the future
- **EXPIRED**: The promotion has ended or reached its usage limit

## API Endpoints

### Admin Endpoints

#### Create Promotion

- **Endpoint**: `POST /promotions`
- **Description**: Creates a new promotion
- **Access**: Admin only
- **Request Body**:
  ```json
  {
    "name": "Summer Sale",
    "description": "20% off all items",
    "promotionType": "percentage",
    "discountType": "percentage",
    "discountValue": 20,
    "applicableType": "SHOP_ITEM",
    "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
    "promotionCode": "SUMMER20",
    "startDate": "2023-06-01",
    "endDate": "2023-08-31",
    "isActive": true,
    "usageLimit": 1000,
    "minimumPurchaseAmount": 50,
    "maximumPurchaseAmount": 1000,
    "maximumDiscountAmount": 100
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Promotion created successfully",
    "data": {
      "id": "123e4567-e89b-12d3-a456-426614174001",
      "name": "Summer Sale",
      "description": "20% off all items",
      "promotionType": "percentage",
      "discountType": "percentage",
      "discountValue": 20,
      "applicableType": "SHOP_ITEM",
      "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
      "promotionCode": "SUMMER20",
      "startDate": "2023-06-01T00:00:00.000Z",
      "endDate": "2023-08-31T23:59:59.999Z",
      "isActive": true,
      "usageLimit": 1000,
      "usageCount": 0,
      "minimumPurchaseAmount": 50,
      "maximumPurchaseAmount": 1000,
      "maximumDiscountAmount": 100,
      "status": "ACTIVE",
      "createdAt": "2023-05-15T10:30:00.000Z",
      "updatedAt": "2023-05-15T10:30:00.000Z"
    }
  }
  ```

#### Get All Promotions

- **Endpoint**: `GET /promotions/admin`
- **Description**: Gets all promotions with optional filtering and pagination
- **Access**: Admin only
- **Query Parameters**:
  - `status`: Filter by promotion status (ACTIVE/INACTIVE/SCHEDULED/EXPIRED)
  - `applicableType`: Filter by applicable type (ALL/SHOP_ITEM/PLAN)
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10)
  - `sortBy`: Field to sort by
  - `sortDirection`: Sort direction (ASC/DESC)
- **Response**:
  ```json
  {
    "success": true,
    "message": "Promotions retrieved successfully",
    "data": {
      "items": [
        {
          "id": "123e4567-e89b-12d3-a456-426614174001",
          "name": "Summer Sale",
          "description": "20% off all items",
          "promotionType": "percentage",
          "discountType": "percentage",
          "discountValue": 20,
          "applicableType": "SHOP_ITEM",
          "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
          "promotionCode": "SUMMER20",
          "startDate": "2023-06-01T00:00:00.000Z",
          "endDate": "2023-08-31T23:59:59.999Z",
          "isActive": true,
          "usageLimit": 1000,
          "usageCount": 0,
          "minimumPurchaseAmount": 50,
          "maximumPurchaseAmount": 1000,
          "maximumDiscountAmount": 100,
          "status": "ACTIVE",
          "createdAt": "2023-05-15T10:30:00.000Z",
          "updatedAt": "2023-05-15T10:30:00.000Z"
        }
      ],
      "totalItems": 1,
      "itemsPerPage": 10,
      "currentPage": 1,
      "totalPages": 1
    }
  }
  ```

#### Get Promotion by ID

- **Endpoint**: `GET /promotions/{id}`
- **Description**: Gets a promotion by ID
- **Access**: Admin only
- **Response**:
  ```json
  {
    "success": true,
    "message": "Promotion retrieved successfully",
    "data": {
      "id": "123e4567-e89b-12d3-a456-426614174001",
      "name": "Summer Sale",
      "description": "20% off all items",
      "promotionType": "percentage",
      "discountType": "percentage",
      "discountValue": 20,
      "applicableType": "SHOP_ITEM",
      "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
      "promotionCode": "SUMMER20",
      "startDate": "2023-06-01T00:00:00.000Z",
      "endDate": "2023-08-31T23:59:59.999Z",
      "isActive": true,
      "usageLimit": 1000,
      "usageCount": 0,
      "minimumPurchaseAmount": 50,
      "maximumPurchaseAmount": 1000,
      "maximumDiscountAmount": 100,
      "status": "ACTIVE",
      "createdAt": "2023-05-15T10:30:00.000Z",
      "updatedAt": "2023-05-15T10:30:00.000Z"
    }
  }
  ```

#### Update Promotion

- **Endpoint**: `PATCH /promotions/{id}`
- **Description**: Updates a promotion
- **Access**: Admin only
- **Request Body**:
  ```json
  {
    "name": "Updated Summer Sale",
    "description": "25% off all items",
    "discountValue": 25,
    "isActive": true
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Promotion updated successfully",
    "data": {
      "id": "123e4567-e89b-12d3-a456-426614174001",
      "name": "Updated Summer Sale",
      "description": "25% off all items",
      "promotionType": "percentage",
      "discountType": "percentage",
      "discountValue": 25,
      "applicableType": "SHOP_ITEM",
      "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
      "promotionCode": "SUMMER20",
      "startDate": "2023-06-01T00:00:00.000Z",
      "endDate": "2023-08-31T23:59:59.999Z",
      "isActive": true,
      "usageLimit": 1000,
      "usageCount": 0,
      "minimumPurchaseAmount": 50,
      "maximumPurchaseAmount": 1000,
      "maximumDiscountAmount": 100,
      "status": "ACTIVE",
      "createdAt": "2023-05-15T10:30:00.000Z",
      "updatedAt": "2023-05-15T11:15:00.000Z"
    }
  }
  ```

#### Delete Promotion

- **Endpoint**: `DELETE /promotions/{id}`
- **Description**: Deletes a promotion
- **Access**: Admin only
- **Response**:
  ```json
  {
    "success": true,
    "message": "Promotion deleted successfully",
    "data": null
  }
  ```

#### Generate Promotion Code

- **Endpoint**: `POST /promotions/generate-code`
- **Description**: Generates a unique promotion code
- **Access**: Admin only
- **Request Body**:
  ```json
  {
    "prefix": "SUMMER"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Promotion code generated successfully",
    "data": {
      "promotionCode": "SUMMER123ABC"
    }
  }
  ```

#### Apply Promotion to Shop Items

- **Endpoint**: `POST /shop/admin/apply-promotion`
- **Description**: Applies a promotion to multiple shop items
- **Access**: Admin only
- **Request Body**:
  ```json
  {
    "promotionId": "123e4567-e89b-12d3-a456-426614174001",
    "itemIds": [
      "123e4567-e89b-12d3-a456-426614174002",
      "123e4567-e89b-12d3-a456-426614174003"
    ]
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Promotion applied to 2 shop items",
    "data": {
      "success": true,
      "message": "Promotion applied to 2 shop items"
    }
  }
  ```

> **Note**: When applying a promotion to shop items, the system checks if the items' categories are in the promotion's `applicableCategoryIds`. If a promotion has category restrictions, it will only be applied to items in those categories. Items in non-applicable categories will be skipped with a clear message.

### Student Endpoints

#### Apply Promotion Code

- **Endpoint**: `POST /promotions/apply-code`
- **Description**: Applies a promotion code to calculate discount
- **Access**: Student only
- **Request Body**:
  ```json
  {
    "promotionCode": "SUMMER20",
    "originalPrice": 100,
    "itemType": "SHOP_ITEM",
    "category": "123e4567-e89b-12d3-a456-426614174000"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Promotion code applied successfully",
    "data": {
      "promotionId": "123e4567-e89b-12d3-a456-426614174001",
      "promotionName": "Summer Sale",
      "originalPrice": 100,
      "discountAmount": 20,
      "finalPrice": 80,
      "discountType": "percentage",
      "discountValue": 20,
      "isApplied": true,
      "message": "Promotion applied successfully"
    }
  }
  ```

#### Get Applicable Promotions

- **Endpoint**: `GET /promotions/applicable`
- **Description**: Gets promotions applicable to an item
- **Access**: Student only
- **Query Parameters**:
  - `itemType`: Type of item (SHOP_ITEM/PLAN)
  - `category`: Category ID (for shop items)
  - `planType`: Plan type (for plans)
  - `page`: Page number (default: 1)
  - `limit`: Items per page (default: 10)
- **Response**:
  ```json
  {
    "success": true,
    "message": "Applicable promotions retrieved successfully",
    "data": {
      "items": [
        {
          "id": "123e4567-e89b-12d3-a456-426614174001",
          "name": "Summer Sale",
          "description": "20% off all items",
          "promotionType": "percentage",
          "discountType": "percentage",
          "discountValue": 20,
          "applicableType": "SHOP_ITEM",
          "applicableCategoryIds": ["123e4567-e89b-12d3-a456-426614174000"],
          "promotionCode": "SUMMER20",
          "startDate": "2023-06-01T00:00:00.000Z",
          "endDate": "2023-08-31T23:59:59.999Z",
          "isActive": true,
          "usageLimit": 1000,
          "usageCount": 0,
          "minimumPurchaseAmount": 50,
          "maximumPurchaseAmount": 1000,
          "maximumDiscountAmount": 100,
          "status": "ACTIVE",
          "createdAt": "2023-05-15T10:30:00.000Z",
          "updatedAt": "2023-05-15T10:30:00.000Z"
        }
      ],
      "totalItems": 1,
      "itemsPerPage": 10,
      "currentPage": 1,
      "totalPages": 1
    }
  }
  ```

## Integration with Other Modules

### Shop Module

The promotion management system integrates with the shop module in the following ways:

1. **Shop Items**: Promotions can be applied to shop items to offer discounts. When a promotion is applied to a shop item, the item's `promotionId` field is set to the promotion's ID.

2. **Shopping Cart**: When items are added to the cart, the system calculates the final price based on any applied promotions. The cart response includes information about the original price, discounted price, and discount percentage.

3. **Checkout**: During checkout, the system preserves promotion information in purchase records, including the original price, final price, promotion ID, and discount amount.

### Plan Module

The promotion management system integrates with the plan module in the following ways:

1. **Plans**: Promotions can be applied to plans to offer discounts. When a promotion is applied to a plan, the plan's `promotionId` field is set to the promotion's ID.

2. **Plan Subscription**: When a user subscribes to a plan, the system calculates the final price based on any applied promotions. The subscription response includes information about the original price, discounted price, and discount percentage.

## Promotion Validation

When applying a promotion, the system performs the following validations:

1. **Active Status**: The promotion must be active.

2. **Date Range**: If start and end dates are specified, the current date must be within that range.

3. **Usage Limit**: If a usage limit is specified, the promotion must not have exceeded that limit.

4. **Applicable Type**: The promotion must be applicable to the item type (ALL, SHOP_ITEM, or PLAN).

5. **Applicable Categories**: For shop items, if applicable category IDs are specified, the item's category must be in that list.

6. **Applicable Plans**: For plans, if applicable plan IDs are specified, the plan must be in that list.

7. **Purchase Amount**: If minimum or maximum purchase amounts are specified, the purchase amount must be within that range.

## Discount Calculation

The system calculates discounts based on the promotion type:

1. **Percentage**: The discount is calculated as a percentage of the original price.
   ```
   discountAmount = originalPrice * (discountValue / 100)
   ```

2. **Fixed Amount**: The discount is a fixed amount.
   ```
   discountAmount = discountValue
   ```

If a maximum discount amount is specified, the discount cannot exceed that amount:
```
if (discountAmount > maximumDiscountAmount) {
  discountAmount = maximumDiscountAmount;
}
```

The discount amount is also limited to the original price to prevent negative prices:
```
if (discountAmount > originalPrice) {
  discountAmount = originalPrice;
}
```

The final price is calculated by subtracting the discount amount from the original price:
```
finalPrice = originalPrice - discountAmount
```

> **Important**: The `discountAmount` is calculated at runtime and is not stored as a field in the promotion entity. It is derived from the `promotionType`, `discountValue`, and the price of the item being discounted.

## Best Practices

1. **Promotion Codes**: Use unique, easy-to-remember promotion codes. The system provides a code generation API to help with this.

2. **Date Ranges**: Set appropriate start and end dates for promotions to control when they are active.

3. **Usage Limits**: Set usage limits for promotions to control how many times they can be used.

4. **Category Restrictions**: Use applicable category IDs to restrict promotions to specific categories.

5. **Purchase Amount Restrictions**: Use minimum and maximum purchase amounts to control when promotions can be applied.

6. **Maximum Discount Amount**: Set a maximum discount amount to limit the discount that can be applied.

7. **Promotion Types**: Choose the appropriate promotion type (percentage or fixed amount) based on the desired discount.

## Error Handling

The promotion management system handles errors in the following ways:

1. **Not Found**: If a promotion is not found, the system returns a 404 Not Found error.

2. **Bad Request**: If the request is invalid (e.g., missing required fields), the system returns a 400 Bad Request error.

3. **Conflict**: If a promotion code already exists, the system returns a 409 Conflict error.

4. **Unauthorized**: If the user is not authorized to access the endpoint, the system returns a 401 Unauthorized error.

5. **Forbidden**: If the user is not allowed to access the endpoint, the system returns a 403 Forbidden error.

## Conclusion

The promotion management system provides a flexible way to create and manage promotions for shop items and plans. It supports percentage and fixed amount discounts, can be restricted to specific categories or plans, and can have various limitations such as usage limits and valid date ranges.
