import { <PERSON>tity, Column, OneToMany, ManyToMany, JoinTable } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { UserPlan } from './user-plan.entity';
import { PlanFeature } from './plan-feature.entity';

export enum PlanType {
  STARTER = 'starter',
  STANDARD = 'standard',
  PRO = 'pro',
  ULTIMATE = 'ultimate'
}

export enum SubscriptionType {
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}

@Entity()
export class Plan extends AuditableBaseEntity {
  @Column({ name: 'name', unique: true })
  name: string;

  @Column({
    name: 'type',
    type: 'enum',
    enum: PlanType,
    default: PlanType.STARTER
  })
  type: PlanType;

  @Column({
    name: 'subscription_type',
    type: 'enum',
    enum: SubscriptionType,
    default: SubscriptionType.MONTHLY
  })
  subscriptionType: SubscriptionType;

  @Column({ name: 'description', type: 'text' })
  description: string;

  @Column({ name: 'price', type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ name: 'duration_days', type: 'int' })
  durationDays: number;

  @Column({ name: 'auto_renew', default: false })
  autoRenew: boolean;

  @Column({ name: 'legacy_features', type: 'simple-json', nullable: true })
  legacyFeatures: any;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'is_applicable_for_promotion', default: false })
  isApplicableForPromotion: boolean;

  @Column({ name: 'promotion_id', nullable: true })
  promotionId: string;

  @OneToMany(() => UserPlan, userPlan => userPlan.plan)
  userPlans: UserPlan[];

  @ManyToMany(() => PlanFeature, planFeature => planFeature.plans)
  @JoinTable({
    name: 'plan_feature_map',
    joinColumn: {
      name: 'plan_id',
      referencedColumnName: 'id'
    },
    inverseJoinColumn: {
      name: 'feature_id',
      referencedColumnName: 'id'
    }
  })
  planFeatures: PlanFeature[];

  // Helper method to get a simple representation of this plan
  toSimpleObject() {
    // Format legacy features
    let formattedLegacyFeatures = [];

    if (this.legacyFeatures) {
      // If it's already an array, use it
      if (Array.isArray(this.legacyFeatures)) {
        formattedLegacyFeatures = this.legacyFeatures;
      }
      // If it's an object with boolean values, convert it
      else if (typeof this.legacyFeatures === 'object') {
        formattedLegacyFeatures = [
          {
            type: 'hec_user_diary',
            name: 'Hec User Diary',
            description: 'Access to the HEC User Diary platform',
            isActive: !!this.legacyFeatures.hecUserDiary
          },
          {
            type: 'hec_play',
            name: 'HEC Play',
            description: 'Access to the HEC Play platform',
            isActive: !!this.legacyFeatures.hecPlay
          },
          {
            type: 'english_qa_writing',
            name: 'English Q&A Writing Platform',
            description: 'Access to the English Q&A Writing Platform',
            isActive: !!this.legacyFeatures.englishQA
          },
          {
            type: 'english_essay',
            name: 'English Essay Platform',
            description: 'Access to the English Essay Platform',
            isActive: !!this.legacyFeatures.englishEssay
          },
          {
            type: 'english_novel',
            name: 'English Novel Platform',
            description: 'Access to the English Novel Platform',
            isActive: !!this.legacyFeatures.englishNovel
          }
        ];
      }
    }

    return {
      id: this.id,
      name: this.name,
      type: this.type,
      subscriptionType: this.subscriptionType,
      description: this.description,
      price: this.price,
      durationDays: this.durationDays,
      autoRenew: this.autoRenew,
      isActive: this.isActive,
      isApplicableForPromotion: this.isApplicableForPromotion,
      promotionId: this.promotionId,
      features: this.planFeatures ? this.planFeatures.map(feature => feature.toSimpleObject()) : [],
      planFeatures: this.planFeatures ? this.planFeatures.map(feature => feature.toSimpleObject()) : [],
      legacyFeatures: formattedLegacyFeatures
    };
  }
}
