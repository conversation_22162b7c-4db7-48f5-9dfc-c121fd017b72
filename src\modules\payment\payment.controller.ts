import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Req,
  Res,
  HttpStatus,
  UseGuards,
  Logger,
  Headers,
  Ip,
  HttpCode
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { PaymentService } from './services/payment.service';
import {
  InitiatePaymentDto,
  PaymentInitiationResponseDto,
  PaymentStatusResponseDto,
  ProcessPaymentDto,
  WebhookPayloadDto,
  RefundRequestDto,
  RefundResponseDto,
  PaymentTransactionDto,
  PaymentHealthResponseDto,
  WebhookResponseDto
} from './dto/payment.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiOkResponseWithArrayType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';

interface RequestWithUser extends Request {
  user: { id: string; sub: string; [key: string]: any };
}

@ApiTags('Payment')
@ApiBearerAuth('JWT-auth')
@Controller('payment')
export class PaymentController {
  private readonly logger = new Logger(PaymentController.name);

  constructor(private readonly paymentService: PaymentService) {}

  @Post('initiate')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({
    summary: 'Initiate payment process',
    description: 'Initiates a payment process for shop items or plan subscriptions using KCP payment gateway.'
  })
  @ApiOkResponseWithType(PaymentInitiationResponseDto, 'Payment initiated successfully')
  @ApiErrorResponse(400, 'Invalid payment data')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(500, 'Payment initiation failed')
  async initiatePayment(
    @Body() initiatePaymentDto: InitiatePaymentDto,
    @Req() req: RequestWithUser
  ): Promise<ApiResponse<PaymentInitiationResponseDto>> {
    const userId = req.user.sub || req.user.id;
    this.logger.log(`Payment initiation request from user: ${userId}`);

    const result = await this.paymentService.initiatePayment(userId, initiatePaymentDto);
    return ApiResponse.success(result, 'Payment initiated successfully');
  }

  @Post('process')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Process payment completion',
    description: 'Processes payment completion after user returns from KCP payment gateway.'
  })
  @ApiOkResponseWithType(PaymentStatusResponseDto, 'Payment processed successfully')
  @ApiErrorResponse(400, 'Invalid payment data')
  @ApiErrorResponse(404, 'Transaction not found')
  @ApiErrorResponse(500, 'Payment processing failed')
  async processPayment(
    @Body() processPaymentDto: ProcessPaymentDto
  ): Promise<ApiResponse<PaymentStatusResponseDto>> {
    this.logger.log(`Processing payment: ${processPaymentDto.transactionId}`);

    const result = await this.paymentService.processPayment(processPaymentDto);
    return ApiResponse.success(result, 'Payment processed successfully');
  }

  @Get('status/:transactionId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get payment status',
    description: 'Retrieves the current status of a payment transaction by transaction ID.'
  })
  @ApiParam({ name: 'transactionId', description: 'Payment transaction ID' })
  @ApiOkResponseWithType(PaymentStatusResponseDto, 'Payment status retrieved successfully')
  @ApiErrorResponse(404, 'Transaction not found')
  @ApiErrorResponse(500, 'Failed to get payment status')
  async getPaymentStatus(
    @Param('transactionId') transactionId: string
  ): Promise<ApiResponse<PaymentStatusResponseDto>> {
    this.logger.log(`Getting payment status: ${transactionId}`);

    const result = await this.paymentService.getPaymentStatus(transactionId);
    return ApiResponse.success(result, 'Payment status retrieved successfully');
  }

  @Post('webhook/kcp')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Handle KCP webhook notifications',
    description: 'Processes webhook notifications from KCP payment gateway for payment status updates.'
  })
  @ApiOkResponseWithType(WebhookResponseDto, 'Webhook processed successfully')
  @ApiErrorResponse(400, 'Invalid webhook data')
  @ApiErrorResponse(500, 'Webhook processing failed')
  async handleKcpWebhook(
    @Body() payload: WebhookPayloadDto,
    @Headers('x-kcp-signature') signature: string,
    @Ip() sourceIp: string
  ): Promise<ApiResponse<WebhookResponseDto>> {
    this.logger.log(`Received KCP webhook for order: ${payload.ordr_idxx}`);

    await this.paymentService.processWebhook(payload, signature, sourceIp);

    const result: WebhookResponseDto = {
      success: true,
      message: 'Webhook processed successfully'
    };

    return ApiResponse.success(result, 'Webhook processed successfully');
  }

  @Get('kcp/redirect')
  @ApiOperation({
    summary: 'Handle KCP payment redirect',
    description: 'Handles redirect from KCP payment gateway after payment completion or cancellation.'
  })
  @ApiOkResponseWithType(Object, 'Redirect handled successfully')
  async handleKcpRedirect(
    @Req() req: Request,
    @Res() res: Response
  ): Promise<void> {
    const { tno, ordr_idxx, res_cd, res_msg } = req.query;

    this.logger.log(`KCP redirect for transaction: ${tno}, order: ${ordr_idxx}`);

    // Process the redirect based on result
    if (res_cd === '0000') {
      // Success - redirect to success page
      const successUrl = `${process.env.FRONTEND_URL}/payment/success?transaction=${tno}&order=${ordr_idxx}`;
      res.redirect(successUrl);
    } else {
      // Failure - redirect to failure page
      const failureUrl = `${process.env.FRONTEND_URL}/payment/failure?transaction=${tno}&order=${ordr_idxx}&error=${res_msg}`;
      res.redirect(failureUrl);
    }
  }

  @Post('refund')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Process payment refund',
    description: 'Processes a refund request for a completed payment transaction.'
  })
  @ApiOkResponseWithType(RefundResponseDto, 'Refund processed successfully')
  @ApiErrorResponse(400, 'Invalid refund request')
  @ApiErrorResponse(404, 'Transaction not found')
  @ApiErrorResponse(500, 'Refund processing failed')
  async processRefund(
    @Body() refundRequestDto: RefundRequestDto
  ): Promise<ApiResponse<RefundResponseDto>> {
    this.logger.log(`Processing refund for transaction: ${refundRequestDto.transactionId}`);

    // Note: Refund functionality would be implemented here
    // For now, return a placeholder response
    const result: RefundResponseDto = {
      success: true,
      transactionId: refundRequestDto.transactionId,
      refundAmount: refundRequestDto.amount || 0,
      message: 'Refund request received and will be processed',
      refundId: `REF-${Date.now()}`
    };

    return ApiResponse.success(result, 'Refund processed successfully');
  }

  @Get('transactions')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get user payment transactions',
    description: 'Retrieves all payment transactions for the authenticated user.'
  })
  @ApiOkResponseWithArrayType(PaymentTransactionDto, 'Transactions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(500, 'Failed to get transactions')
  async getUserTransactions(
    @Req() req: RequestWithUser
  ): Promise<ApiResponse<PaymentTransactionDto[]>> {
    const userId = req.user.sub || req.user.id;
    this.logger.log(`Getting transactions for user: ${userId}`);

    // Note: This would be implemented to return user's payment transactions
    // For now, return empty array
    const transactions = [];

    return ApiResponse.success(transactions, 'Transactions retrieved successfully');
  }

  @Get('health')
  @ApiOperation({
    summary: 'Payment service health check',
    description: 'Checks the health status of the payment service.'
  })
  @ApiOkResponseWithType(PaymentHealthResponseDto, 'Payment service is healthy')
  async healthCheck(): Promise<ApiResponse<PaymentHealthResponseDto>> {
    const result: PaymentHealthResponseDto = {
      status: 'healthy',
      timestamp: new Date().toISOString()
    };

    return ApiResponse.success(result, 'Payment service is healthy');
  }
}
