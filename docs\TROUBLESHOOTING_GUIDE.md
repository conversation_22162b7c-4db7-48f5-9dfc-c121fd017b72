# KCP Payment Gateway - Troubleshooting Guide

This guide provides solutions to common issues encountered with the KCP payment gateway integration.

## Common Issues and Solutions

### 1. Payment Initiation Failures

#### Issue: "Payment initiation failed" error
**Symptoms:**
- Users cannot start payment process
- Error message: "Failed to initiate payment"
- Backend logs show KCP connection errors

**Possible Causes & Solutions:**

```typescript
// Check KCP configuration
const kcpConfig = {
  siteCd: process.env.KCP_SITE_CD,
  siteKey: process.env.KCP_SITE_KEY,
  apiUrl: process.env.KCP_API_URL,
};

// Verify all required fields are present
if (!kcpConfig.siteCd || !kcpConfig.siteKey) {
  console.error('Missing KCP credentials');
}
```

**Solutions:**
1. **Verify KCP Credentials**
   ```bash
   # Check environment variables
   echo $KCP_SITE_CD
   echo $KCP_SITE_KEY
   echo $KCP_API_URL
   ```

2. **Test KCP Connectivity**
   ```bash
   # Test KCP API endpoint
   curl -X POST https://stg-spl.kcp.co.kr/std/tradeReg/register \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "site_cd=TEST&kcp_cert_info=TEST"
   ```

3. **Check Network Configuration**
   - Verify firewall rules allow outbound HTTPS traffic
   - Ensure DNS resolution works for KCP domains
   - Check proxy settings if applicable

#### Issue: "Invalid payment method" error
**Symptoms:**
- Specific payment methods fail
- Error occurs during method selection

**Solutions:**
1. **Verify Payment Method Configuration**
   ```typescript
   // Check if payment method is properly mapped
   const payMethodMap = {
     'kcp_card': '************',
     'kcp_bank': '************',
     'kcp_mobile': '************',
   };
   ```

2. **Enable Payment Methods in KCP Portal**
   - Log into KCP merchant portal
   - Enable required payment methods
   - Configure method-specific settings

### 2. Webhook Processing Issues

#### Issue: Webhooks not being received
**Symptoms:**
- Payment status not updating automatically
- Manual status checks show completed payments
- No webhook entries in database

**Diagnostic Steps:**
```bash
# Check webhook endpoint accessibility
curl -X POST https://your-domain.com/payment/webhook/kcp \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook"}'

# Check server logs for webhook requests
tail -f /var/log/your-app/app.log | grep webhook

# Verify webhook URL in KCP portal
```

**Solutions:**
1. **Verify Webhook URL Configuration**
   - Ensure webhook URL is publicly accessible
   - Check SSL certificate validity
   - Verify URL format: `https://your-domain.com/payment/webhook/kcp`

2. **Check Firewall and Security Settings**
   ```bash
   # Allow KCP IP ranges (check KCP documentation for current ranges)
   iptables -A INPUT -s kcp-ip-range -p tcp --dport 443 -j ACCEPT
   ```

3. **Test Webhook Signature Verification**
   ```typescript
   // Debug webhook signature validation
   const crypto = require('crypto');
   const expectedSignature = crypto
     .createHmac('sha256', process.env.KCP_WEBHOOK_SECRET)
     .update(JSON.stringify(payload))
     .digest('hex');
   
   console.log('Expected:', expectedSignature);
   console.log('Received:', receivedSignature);
   ```

#### Issue: Webhook signature validation fails
**Symptoms:**
- Webhooks received but marked as invalid
- Error: "Invalid webhook signature"

**Solutions:**
1. **Verify Webhook Secret**
   ```typescript
   // Ensure webhook secret matches KCP configuration
   const webhookSecret = process.env.KCP_WEBHOOK_SECRET;
   if (!webhookSecret) {
     console.error('Webhook secret not configured');
   }
   ```

2. **Check Payload Format**
   ```typescript
   // Ensure payload is processed correctly
   const rawPayload = JSON.stringify(req.body);
   const signature = req.headers['x-kcp-signature'];
   ```

### 3. Database Issues

#### Issue: Migration fails
**Symptoms:**
- Error during database migration
- New tables not created
- Foreign key constraint errors

**Solutions:**
1. **Check Database Permissions**
   ```sql
   -- Verify user has necessary permissions
   GRANT CREATE, ALTER, DROP ON DATABASE your_db TO your_user;
   ```

2. **Run Migration Manually**
   ```bash
   # Check migration status
   npm run migration:show
   
   # Run specific migration
   npm run migration:run -- --transaction=each
   ```

3. **Handle Enum Value Conflicts**
   ```sql
   -- Check existing enum values
   SELECT enumlabel FROM pg_enum 
   WHERE enumtypid = (
     SELECT oid FROM pg_type 
     WHERE typname = 'shop_item_purchase_payment_method_enum'
   );
   ```

#### Issue: Payment transaction not found
**Symptoms:**
- Error: "Payment transaction not found"
- Status check fails

**Solutions:**
1. **Verify Transaction ID Format**
   ```typescript
   // Ensure transaction ID is properly generated
   const transactionId = `TXN-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
   ```

2. **Check Database Connection**
   ```typescript
   // Test database connectivity
   const result = await dataSource.query('SELECT NOW()');
   console.log('Database time:', result[0].now);
   ```

### 4. Frontend Integration Issues

#### Issue: Payment redirect not working
**Symptoms:**
- Users not redirected to KCP payment page
- Blank page or error after payment initiation

**Solutions:**
1. **Check Payment URL Generation**
   ```typescript
   // Verify payment URL is valid
   const paymentUrl = response.data.paymentUrl;
   if (!paymentUrl || !paymentUrl.startsWith('https://')) {
     console.error('Invalid payment URL:', paymentUrl);
   }
   ```

2. **Test Redirect Logic**
   ```typescript
   // Ensure redirect works correctly
   if (paymentUrl) {
     console.log('Redirecting to:', paymentUrl);
     window.location.href = paymentUrl;
   }
   ```

#### Issue: Payment status not updating in UI
**Symptoms:**
- Payment completed but UI shows pending
- Status polling not working

**Solutions:**
1. **Check Status Polling Logic**
   ```typescript
   // Implement proper status polling
   const pollPaymentStatus = async (transactionId) => {
     const maxAttempts = 10;
     const interval = 2000;
     
     for (let i = 0; i < maxAttempts; i++) {
       const status = await checkPaymentStatus(transactionId);
       if (status.status === 'completed' || status.status === 'failed') {
         return status;
       }
       await new Promise(resolve => setTimeout(resolve, interval));
     }
   };
   ```

2. **Verify API Response Format**
   ```typescript
   // Check API response structure
   const response = await fetch(`/payment/status/${transactionId}`);
   const data = await response.json();
   console.log('Status response:', data);
   ```

### 5. Performance Issues

#### Issue: Slow payment processing
**Symptoms:**
- Payment initiation takes too long
- Timeouts during payment process

**Solutions:**
1. **Optimize Database Queries**
   ```sql
   -- Add indexes for better performance
   CREATE INDEX idx_payment_transaction_user_id ON payment_transaction(user_id);
   CREATE INDEX idx_payment_transaction_status ON payment_transaction(status);
   ```

2. **Implement Connection Pooling**
   ```typescript
   // Configure database connection pool
   const dataSource = new DataSource({
     type: 'postgres',
     host: 'localhost',
     port: 5432,
     username: 'user',
     password: 'password',
     database: 'database',
     extra: {
       max: 20,
       min: 5,
       acquire: 30000,
       idle: 10000,
     },
   });
   ```

3. **Add Caching**
   ```typescript
   // Cache payment method configurations
   const paymentMethodCache = new Map();
   
   const getPaymentMethods = async () => {
     if (paymentMethodCache.has('methods')) {
       return paymentMethodCache.get('methods');
     }
     
     const methods = await fetchPaymentMethods();
     paymentMethodCache.set('methods', methods);
     return methods;
   };
   ```

## Debugging Tools

### 1. Payment Transaction Debugger

```typescript
// utils/payment-debugger.ts
export class PaymentDebugger {
  static logTransaction(transaction: any) {
    console.log('=== Payment Transaction Debug ===');
    console.log('Transaction ID:', transaction.transactionId);
    console.log('Status:', transaction.status);
    console.log('Amount:', transaction.amount);
    console.log('Payment Method:', transaction.paymentMethod);
    console.log('Created:', transaction.createdAt);
    console.log('Request Data:', JSON.stringify(transaction.requestData, null, 2));
    console.log('Response Data:', JSON.stringify(transaction.responseData, null, 2));
    console.log('================================');
  }

  static validatePaymentData(data: any) {
    const required = ['orderId', 'amount', 'paymentMethod', 'buyerEmail'];
    const missing = required.filter(field => !data[field]);
    
    if (missing.length > 0) {
      console.error('Missing required fields:', missing);
      return false;
    }
    
    return true;
  }
}
```

### 2. KCP API Tester

```typescript
// utils/kcp-tester.ts
export class KcpTester {
  static async testConnection() {
    try {
      const response = await fetch(process.env.KCP_API_URL + '/health');
      console.log('KCP API Status:', response.status);
      return response.ok;
    } catch (error) {
      console.error('KCP API Connection Failed:', error);
      return false;
    }
  }

  static async testTradeRegistration(testData: any) {
    try {
      const response = await fetch(process.env.KCP_API_URL + '/std/tradeReg/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(testData),
      });
      
      const result = await response.text();
      console.log('Trade Registration Test:', result);
      return result;
    } catch (error) {
      console.error('Trade Registration Test Failed:', error);
      return null;
    }
  }
}
```

### 3. Database Health Checker

```typescript
// utils/db-health-checker.ts
export class DatabaseHealthChecker {
  static async checkPaymentTables() {
    const tables = ['payment_transaction', 'payment_webhook'];
    const results = {};
    
    for (const table of tables) {
      try {
        const count = await dataSource.query(`SELECT COUNT(*) FROM ${table}`);
        results[table] = { exists: true, count: count[0].count };
      } catch (error) {
        results[table] = { exists: false, error: error.message };
      }
    }
    
    return results;
  }

  static async checkIndexes() {
    const query = `
      SELECT indexname, tablename 
      FROM pg_indexes 
      WHERE tablename LIKE 'payment%'
    `;
    
    return await dataSource.query(query);
  }
}
```

## Monitoring and Alerting

### 1. Payment Metrics Dashboard

```typescript
// monitoring/payment-metrics.ts
export class PaymentMetrics {
  static async getSuccessRate(timeframe: string = '24h') {
    const query = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful
      FROM payment_transaction 
      WHERE created_at > NOW() - INTERVAL '${timeframe}'
    `;
    
    const result = await dataSource.query(query);
    const { total, successful } = result[0];
    return total > 0 ? (successful / total) * 100 : 0;
  }

  static async getAverageProcessingTime() {
    const query = `
      SELECT AVG(EXTRACT(EPOCH FROM (processed_at - created_at))) as avg_time
      FROM payment_transaction 
      WHERE status = 'completed' 
      AND processed_at IS NOT NULL
      AND created_at > NOW() - INTERVAL '24h'
    `;
    
    const result = await dataSource.query(query);
    return result[0].avg_time;
  }
}
```

### 2. Error Alerting

```typescript
// monitoring/error-alerts.ts
export class ErrorAlerts {
  static async checkFailureRate() {
    const failureRate = await PaymentMetrics.getFailureRate();
    
    if (failureRate > 5) { // Alert if failure rate > 5%
      await this.sendAlert({
        type: 'HIGH_FAILURE_RATE',
        message: `Payment failure rate is ${failureRate}%`,
        severity: 'HIGH',
      });
    }
  }

  static async sendAlert(alert: any) {
    // Implement your alerting mechanism (email, Slack, etc.)
    console.error('PAYMENT ALERT:', alert);
  }
}
```

## Emergency Procedures

### 1. Disable Payment Gateway

```typescript
// emergency/disable-payments.ts
export const disableKcpPayments = async () => {
  // Update configuration to disable KCP
  await dataSource.query(`
    UPDATE app_config 
    SET value = 'false' 
    WHERE key = 'kcp_payments_enabled'
  `);
  
  console.log('KCP payments disabled');
};
```

### 2. Rollback to Previous Version

```bash
#!/bin/bash
# emergency/rollback.sh

echo "Starting emergency rollback..."

# Stop current application
pm2 stop hec-backend

# Restore previous version
git checkout previous-stable-tag

# Restore database if needed
# pg_restore -d hec_backend backup_before_payment_integration.sql

# Restart application
npm install
npm run build
pm2 start hec-backend

echo "Rollback completed"
```

This troubleshooting guide provides comprehensive solutions for common issues and tools for debugging the KCP payment gateway integration.
