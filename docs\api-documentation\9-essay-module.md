# Essay Module

## Overview

The Essay Module provides functionality for students to submit essays, tutors to review and provide feedback on essays, and administrators to manage the essay review process.

## Entities

### Essay

Represents an essay submitted by a student.

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier |
| title | string | Essay title |
| content | string | Essay content |
| prompt | string | The essay prompt or question |
| wordCount | number | Number of words in the essay |
| status | EssayStatus | Current status of the essay |
| studentId | string | ID of the student who submitted the essay |
| tutorId | string | ID of the tutor assigned to review the essay |
| submittedAt | Date | When the essay was submitted |
| reviewedAt | Date | When the essay was reviewed |
| createdAt | Date | When the essay was created |
| updatedAt | Date | When the essay was last updated |

### EssayFeedback

Represents feedback provided by a tutor on an essay.

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier |
| essayId | string | ID of the essay |
| tutorId | string | ID of the tutor providing feedback |
| overallFeedback | string | General feedback on the essay |
| score | number | Numerical score (e.g., 1-10) |
| grammarFeedback | string | Feedback on grammar |
| structureFeedback | string | Feedback on essay structure |
| contentFeedback | string | Feedback on essay content |
| suggestions | string | Suggestions for improvement |
| createdAt | Date | When the feedback was created |
| updatedAt | Date | When the feedback was last updated |

### EssayComment

Represents inline comments on specific parts of an essay.

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique identifier |
| essayId | string | ID of the essay |
| tutorId | string | ID of the tutor making the comment |
| startIndex | number | Starting character index in the essay content |
| endIndex | number | Ending character index in the essay content |
| comment | string | The comment text |
| type | CommentType | Type of comment (e.g., grammar, structure, content) |
| createdAt | Date | When the comment was created |
| updatedAt | Date | When the comment was last updated |

## Enums

### EssayStatus

```typescript
enum EssayStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  REVIEWED = 'REVIEWED',
  REVISION_REQUESTED = 'REVISION_REQUESTED'
}
```

### CommentType

```typescript
enum CommentType {
  GRAMMAR = 'GRAMMAR',
  STRUCTURE = 'STRUCTURE',
  CONTENT = 'CONTENT',
  SUGGESTION = 'SUGGESTION',
  GENERAL = 'GENERAL'
}
```

## API Endpoints

### Student Endpoints

#### Create Essay

Creates a new essay (initially in DRAFT status).

```
POST /api/essays
```

**Request Body:**
```json
{
  "title": "The Impact of Climate Change",
  "prompt": "Discuss the environmental and social impacts of climate change",
  "content": "Climate change is one of the most pressing issues of our time...",
  "isDraft": true
}
```

**Response:**
```json
{
  "id": "essay-123",
  "title": "The Impact of Climate Change",
  "prompt": "Discuss the environmental and social impacts of climate change",
  "content": "Climate change is one of the most pressing issues of our time...",
  "wordCount": 250,
  "status": "DRAFT",
  "studentId": "student-456",
  "createdAt": "2023-01-01T12:00:00Z",
  "updatedAt": "2023-01-01T12:00:00Z"
}
```

#### Update Essay Draft

Updates an essay in DRAFT status.

```
PUT /api/essays/:id
```

**Request Body:**
```json
{
  "title": "The Impact of Climate Change on Society",
  "prompt": "Discuss the environmental and social impacts of climate change",
  "content": "Climate change is one of the most pressing issues of our time. It affects not only the environment but also society in numerous ways..."
}
```

**Response:**
```json
{
  "id": "essay-123",
  "title": "The Impact of Climate Change on Society",
  "prompt": "Discuss the environmental and social impacts of climate change",
  "content": "Climate change is one of the most pressing issues of our time. It affects not only the environment but also society in numerous ways...",
  "wordCount": 300,
  "status": "DRAFT",
  "studentId": "student-456",
  "createdAt": "2023-01-01T12:00:00Z",
  "updatedAt": "2023-01-01T13:00:00Z"
}
```

#### Submit Essay

Submits an essay for review.

```
POST /api/essays/:id/submit
```

**Response:**
```json
{
  "id": "essay-123",
  "title": "The Impact of Climate Change on Society",
  "status": "SUBMITTED",
  "studentId": "student-456",
  "submittedAt": "2023-01-01T14:00:00Z",
  "updatedAt": "2023-01-01T14:00:00Z"
}
```

#### Get Student Essays

Gets all essays for the authenticated student.

```
GET /api/essays
```

**Query Parameters:**
- `status` (EssayStatus, optional): Filter by status
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 20)

**Response:**
```json
{
  "items": [
    {
      "id": "essay-123",
      "title": "The Impact of Climate Change on Society",
      "prompt": "Discuss the environmental and social impacts of climate change",
      "wordCount": 300,
      "status": "SUBMITTED",
      "submittedAt": "2023-01-01T14:00:00Z",
      "createdAt": "2023-01-01T12:00:00Z",
      "updatedAt": "2023-01-01T14:00:00Z"
    }
  ],
  "meta": {
    "totalItems": 5,
    "itemsPerPage": 20,
    "currentPage": 1,
    "totalPages": 1
  }
}
```

#### Get Essay Details

Gets detailed information about an essay, including feedback and comments.

```
GET /api/essays/:id
```

**Response:**
```json
{
  "essay": {
    "id": "essay-123",
    "title": "The Impact of Climate Change on Society",
    "prompt": "Discuss the environmental and social impacts of climate change",
    "content": "Climate change is one of the most pressing issues of our time...",
    "wordCount": 300,
    "status": "REVIEWED",
    "studentId": "student-456",
    "tutorId": "tutor-789",
    "submittedAt": "2023-01-01T14:00:00Z",
    "reviewedAt": "2023-01-02T10:00:00Z",
    "createdAt": "2023-01-01T12:00:00Z",
    "updatedAt": "2023-01-02T10:00:00Z"
  },
  "feedback": {
    "id": "feedback-123",
    "essayId": "essay-123",
    "tutorId": "tutor-789",
    "overallFeedback": "Good essay with clear arguments, but needs improvement in structure.",
    "score": 8,
    "grammarFeedback": "Few grammatical errors, mostly in complex sentences.",
    "structureFeedback": "Introduction is strong, but conclusion needs more development.",
    "contentFeedback": "Good use of examples and evidence to support arguments.",
    "suggestions": "Consider adding more counterarguments to strengthen your position.",
    "createdAt": "2023-01-02T10:00:00Z"
  },
  "comments": [
    {
      "id": "comment-123",
      "essayId": "essay-123",
      "tutorId": "tutor-789",
      "startIndex": 150,
      "endIndex": 180,
      "comment": "This sentence is unclear. Consider rephrasing.",
      "type": "STRUCTURE",
      "createdAt": "2023-01-02T09:30:00Z"
    }
  ]
}
```

### Tutor Endpoints

#### Get Assigned Essays

Gets essays assigned to the authenticated tutor.

```
GET /api/tutor/essays
```

**Query Parameters:**
- `status` (EssayStatus, optional): Filter by status
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 20)

**Response:**
```json
{
  "items": [
    {
      "id": "essay-123",
      "title": "The Impact of Climate Change on Society",
      "studentId": "student-456",
      "studentName": "John Doe",
      "wordCount": 300,
      "status": "SUBMITTED",
      "submittedAt": "2023-01-01T14:00:00Z"
    }
  ],
  "meta": {
    "totalItems": 3,
    "itemsPerPage": 20,
    "currentPage": 1,
    "totalPages": 1
  }
}
```

#### Start Essay Review

Marks an essay as under review by the tutor.

```
POST /api/tutor/essays/:id/start-review
```

**Response:**
```json
{
  "id": "essay-123",
  "title": "The Impact of Climate Change on Society",
  "status": "UNDER_REVIEW",
  "studentId": "student-456",
  "tutorId": "tutor-789",
  "submittedAt": "2023-01-01T14:00:00Z",
  "updatedAt": "2023-01-02T09:00:00Z"
}
```

#### Add Essay Comment

Adds an inline comment to an essay.

```
POST /api/tutor/essays/:id/comments
```

**Request Body:**
```json
{
  "startIndex": 150,
  "endIndex": 180,
  "comment": "This sentence is unclear. Consider rephrasing.",
  "type": "STRUCTURE"
}
```

**Response:**
```json
{
  "id": "comment-123",
  "essayId": "essay-123",
  "tutorId": "tutor-789",
  "startIndex": 150,
  "endIndex": 180,
  "comment": "This sentence is unclear. Consider rephrasing.",
  "type": "STRUCTURE",
  "createdAt": "2023-01-02T09:30:00Z"
}
```

#### Submit Essay Feedback

Submits overall feedback for an essay and marks it as reviewed.

```
POST /api/tutor/essays/:id/feedback
```

**Request Body:**
```json
{
  "overallFeedback": "Good essay with clear arguments, but needs improvement in structure.",
  "score": 8,
  "grammarFeedback": "Few grammatical errors, mostly in complex sentences.",
  "structureFeedback": "Introduction is strong, but conclusion needs more development.",
  "contentFeedback": "Good use of examples and evidence to support arguments.",
  "suggestions": "Consider adding more counterarguments to strengthen your position."
}
```

**Response:**
```json
{
  "essay": {
    "id": "essay-123",
    "title": "The Impact of Climate Change on Society",
    "status": "REVIEWED",
    "reviewedAt": "2023-01-02T10:00:00Z"
  },
  "feedback": {
    "id": "feedback-123",
    "essayId": "essay-123",
    "tutorId": "tutor-789",
    "overallFeedback": "Good essay with clear arguments, but needs improvement in structure.",
    "score": 8,
    "grammarFeedback": "Few grammatical errors, mostly in complex sentences.",
    "structureFeedback": "Introduction is strong, but conclusion needs more development.",
    "contentFeedback": "Good use of examples and evidence to support arguments.",
    "suggestions": "Consider adding more counterarguments to strengthen your position.",
    "createdAt": "2023-01-02T10:00:00Z"
  }
}
```

### Admin Endpoints

#### Get All Essays

Gets all essays with filtering options.

```
GET /api/admin/essays
```

**Query Parameters:**
- `status` (EssayStatus, optional): Filter by status
- `studentId` (string, optional): Filter by student
- `tutorId` (string, optional): Filter by tutor
- `startDate` (Date, optional): Filter by submission date range start
- `endDate` (Date, optional): Filter by submission date range end
- `page` (number, optional): Page number (default: 1)
- `limit` (number, optional): Items per page (default: 20)

**Response:**
```json
{
  "items": [
    {
      "id": "essay-123",
      "title": "The Impact of Climate Change on Society",
      "studentId": "student-456",
      "studentName": "John Doe",
      "tutorId": "tutor-789",
      "tutorName": "Jane Smith",
      "status": "REVIEWED",
      "wordCount": 300,
      "score": 8,
      "submittedAt": "2023-01-01T14:00:00Z",
      "reviewedAt": "2023-01-02T10:00:00Z"
    }
  ],
  "meta": {
    "totalItems": 50,
    "itemsPerPage": 20,
    "currentPage": 1,
    "totalPages": 3
  }
}
```

#### Reassign Essay

Reassigns an essay to a different tutor.

```
POST /api/admin/essays/:id/reassign
```

**Request Body:**
```json
{
  "tutorId": "tutor-456",
  "reason": "Original tutor is on leave"
}
```

**Response:**
```json
{
  "id": "essay-123",
  "title": "The Impact of Climate Change on Society",
  "status": "SUBMITTED",
  "studentId": "student-456",
  "tutorId": "tutor-456",
  "previousTutorId": "tutor-789",
  "reassignedAt": "2023-01-02T11:00:00Z",
  "reassignedBy": "admin-123",
  "reassignmentReason": "Original tutor is on leave"
}
```

## Workflow

1. Student creates an essay (status: DRAFT)
2. Student can edit the essay while it's in DRAFT status
3. Student submits the essay (status: SUBMITTED)
4. System assigns the essay to a tutor based on availability and subject expertise
5. Tutor starts reviewing the essay (status: UNDER_REVIEW)
6. Tutor adds inline comments and overall feedback
7. Tutor submits the review (status: REVIEWED)
8. Student receives notification that the essay has been reviewed
9. Student can view the feedback and comments

## Notifications

The Essay Module integrates with the Notification Module to send notifications at various stages:

1. When an essay is submitted, the assigned tutor receives a notification
2. When an essay is reviewed, the student receives a notification
3. When an essay is reassigned, both the new tutor and the previous tutor receive notifications

## Conclusion

The Essay Module provides a comprehensive system for students to submit essays and receive detailed feedback from tutors. It supports a structured workflow and integrates with other modules like Notifications to provide a seamless experience.
