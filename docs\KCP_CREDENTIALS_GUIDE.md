# KCP Payment Gateway - Credentials and Configuration Guide

This document provides a comprehensive guide to all credentials and configuration required for the KCP payment gateway integration.

## <PERSON><PERSON> Credentials Overview

The KCP payment gateway requires several key credentials and configuration parameters to function properly. These credentials are obtained from KCP after merchant registration and approval.

## Required KCP Credentials

### 1. Primary KCP Credentials

#### **KCP_SITE_CD** (Site Code)
- **Description**: Unique merchant identifier assigned by KCP
- **Format**: Alphanumeric string (typically 8-10 characters)
- **Example**: `T0000001` (test), `A1234567` (production)
- **Where to get**: KCP merchant portal after account approval
- **Usage**: Identifies your merchant account in all API calls

#### **KCP_SITE_KEY** (Site Key)
- **Description**: Secret key for authentication and encryption
- **Format**: Long alphanumeric string (32-64 characters)
- **Example**: `a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6`
- **Where to get**: KCP merchant portal (keep this secret!)
- **Usage**: Used for API authentication and data encryption

#### **KCP_WEBHOOK_SECRET** (Webhook Secret)
- **Description**: Secret key for webhook signature verification
- **Format**: Random string (recommended 32+ characters)
- **Example**: `webhook_secret_key_for_signature_verification`
- **Where to set**: You generate this and configure it in KCP portal
- **Usage**: Verifies webhook authenticity

### 2. API Endpoint Configuration

#### **KCP_API_URL** (API Base URL)
- **Test Environment**: `https://stg-spl.kcp.co.kr`
- **Production Environment**: `https://spl.kcp.co.kr`
- **Usage**: Base URL for all KCP API calls

#### **KCP_TRADE_REG_URL** (Trade Registration Endpoint)
- **Default**: `/std/tradeReg/register`
- **Full URL**: `{KCP_API_URL}/std/tradeReg/register`
- **Usage**: Endpoint for initiating payment transactions

#### **KCP_PAYMENT_URL** (Payment Processing Endpoint)
- **Default**: `/gw/enc/v1/payment`
- **Full URL**: `{KCP_API_URL}/gw/enc/v1/payment`
- **Usage**: Endpoint for processing payments

## How to Obtain KCP Credentials

### Step 1: KCP Merchant Registration

1. **Visit KCP Website**
   - Go to [KCP Official Website](https://www.kcp.co.kr)
   - Navigate to merchant registration section

2. **Submit Application**
   - Business registration documents
   - Bank account information
   - Business license
   - Representative identification

3. **Document Review**
   - KCP reviews your application (typically 3-5 business days)
   - May request additional documentation

4. **Account Approval**
   - Receive approval notification
   - Get access to KCP merchant portal

### Step 2: Access Merchant Portal

1. **Login to KCP Merchant Portal**
   - Use provided credentials to access portal
   - Navigate to API settings section

2. **Generate API Credentials**
   - Find "API Management" or "Integration" section
   - Generate or view your Site Code and Site Key
   - Download or copy credentials securely

3. **Configure Webhook Settings**
   - Set webhook URL: `https://your-domain.com/payment/webhook/kcp`
   - Generate and set webhook secret key
   - Configure webhook events (payment completion, failure, etc.)

### Step 3: Test Environment Setup

1. **Request Test Credentials**
   - KCP provides separate test environment credentials
   - Test Site Code and Site Key for development

2. **Configure Test Environment**
   - Use staging API URL
   - Enable test mode in your application
   - Test with provided test card numbers

## Environment Configuration

### Development Environment (.env.development)

```env
# KCP Test Environment Credentials
KCP_SITE_CD=T0000001
KCP_SITE_KEY=test_site_key_from_kcp_portal
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_TRADE_REG_URL=/std/tradeReg/register
KCP_PAYMENT_URL=/gw/enc/v1/payment
KCP_WEBHOOK_SECRET=your_generated_webhook_secret_dev
KCP_ENVIRONMENT=development
KCP_TEST_MODE=true

# Payment Configuration
PAYMENT_PROVIDER=kcp
PAYMENT_CURRENCY=KRW
PAYMENT_TIMEOUT=30000
PAYMENT_RETRY_ATTEMPTS=3

# Additional KCP Settings
KCP_CARD_QUOTA_OPTION=12
KCP_SHOW_CARD_LIST=Y
KCP_ESCROW_USED=N
KCP_TAX_FLAG=TG03
KCP_CURRENCY_CODE=410
```

### Staging Environment (.env.staging)

```env
# KCP Staging Environment Credentials
KCP_SITE_CD=S1234567
KCP_SITE_KEY=staging_site_key_from_kcp_portal
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_TRADE_REG_URL=/std/tradeReg/register
KCP_PAYMENT_URL=/gw/enc/v1/payment
KCP_WEBHOOK_SECRET=your_generated_webhook_secret_staging
KCP_ENVIRONMENT=staging
KCP_TEST_MODE=true

# Same payment configuration as development
PAYMENT_PROVIDER=kcp
PAYMENT_CURRENCY=KRW
PAYMENT_TIMEOUT=30000
PAYMENT_RETRY_ATTEMPTS=3
```

### Production Environment (.env.production)

```env
# KCP Production Environment Credentials
KCP_SITE_CD=A1234567
KCP_SITE_KEY=production_site_key_from_kcp_portal
KCP_API_URL=https://spl.kcp.co.kr
KCP_TRADE_REG_URL=/std/tradeReg/register
KCP_PAYMENT_URL=/gw/enc/v1/payment
KCP_WEBHOOK_SECRET=your_generated_webhook_secret_production
KCP_ENVIRONMENT=production
KCP_TEST_MODE=false

# Production payment configuration
PAYMENT_PROVIDER=kcp
PAYMENT_CURRENCY=KRW
PAYMENT_TIMEOUT=30000
PAYMENT_RETRY_ATTEMPTS=3

# Production-specific settings
KCP_CARD_QUOTA_OPTION=12
KCP_SHOW_CARD_LIST=Y
KCP_ESCROW_USED=N
KCP_TAX_FLAG=TG03
KCP_CURRENCY_CODE=410
```

## Credential Security Best Practices

### 1. Environment Variable Management

```typescript
// config/kcp-config.ts
export const getKcpConfig = () => {
  const requiredEnvVars = [
    'KCP_SITE_CD',
    'KCP_SITE_KEY',
    'KCP_WEBHOOK_SECRET'
  ];

  // Validate all required environment variables are present
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Missing required KCP environment variables: ${missingVars.join(', ')}`);
  }

  return {
    siteCd: process.env.KCP_SITE_CD!,
    siteKey: process.env.KCP_SITE_KEY!,
    apiUrl: process.env.KCP_API_URL || 'https://stg-spl.kcp.co.kr',
    webhookSecret: process.env.KCP_WEBHOOK_SECRET!,
    testMode: process.env.KCP_TEST_MODE === 'true',
  };
};
```

### 2. Credential Encryption

```typescript
// utils/credential-encryption.ts
import * as crypto from 'crypto';

export class CredentialManager {
  private static readonly ENCRYPTION_KEY = process.env.ENCRYPTION_KEY!;

  static encryptCredential(credential: string): string {
    const cipher = crypto.createCipher('aes-256-cbc', this.ENCRYPTION_KEY);
    let encrypted = cipher.update(credential, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  static decryptCredential(encryptedCredential: string): string {
    const decipher = crypto.createDecipher('aes-256-cbc', this.ENCRYPTION_KEY);
    let decrypted = decipher.update(encryptedCredential, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}
```

### 3. Access Control

```typescript
// middleware/credential-access.ts
export const restrictCredentialAccess = (req: Request, res: Response, next: NextFunction) => {
  // Only allow access to KCP credentials from authorized services
  const authorizedServices = ['payment-service', 'webhook-processor'];
  const serviceId = req.headers['x-service-id'];

  if (!authorizedServices.includes(serviceId as string)) {
    return res.status(403).json({ error: 'Unauthorized access to credentials' });
  }

  next();
};
```

## Credential Validation

### 1. Startup Validation

```typescript
// services/credential-validator.ts
export class CredentialValidator {
  static async validateKcpCredentials(): Promise<boolean> {
    try {
      const config = getKcpConfig();

      // Test API connectivity with credentials
      const testRequest = {
        site_cd: config.siteCd,
        kcp_cert_info: `${config.siteCd}:${config.siteKey}`,
        ordr_idxx: `TEST-${Date.now()}`,
        good_name: 'Test Product',
        good_mny: 1000,
        buyr_name: 'Test User',
        buyr_mail: '<EMAIL>',
        buyr_tel1: '010-0000-0000',
      };

      const response = await fetch(`${config.apiUrl}/std/tradeReg/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams(testRequest),
      });

      const result = await response.text();

      // Check if credentials are valid (should not return authentication error)
      return !result.includes('authentication failed') && !result.includes('invalid site');
    } catch (error) {
      console.error('KCP credential validation failed:', error);
      return false;
    }
  }
}
```

### 2. Runtime Monitoring

```typescript
// monitoring/credential-monitor.ts
export class CredentialMonitor {
  static async monitorCredentialHealth(): Promise<void> {
    const isValid = await CredentialValidator.validateKcpCredentials();

    if (!isValid) {
      // Alert system administrators
      await this.sendCredentialAlert({
        type: 'CREDENTIAL_VALIDATION_FAILED',
        message: 'KCP credentials validation failed',
        severity: 'CRITICAL',
        timestamp: new Date().toISOString(),
      });
    }
  }

  private static async sendCredentialAlert(alert: any): Promise<void> {
    // Implement your alerting mechanism
    console.error('CREDENTIAL ALERT:', alert);

    // Example: Send to monitoring service
    // await monitoringService.sendAlert(alert);
  }
}
```

## Test Credentials and Data

### KCP Test Environment

```typescript
// config/test-credentials.ts
export const KCP_TEST_CONFIG = {
  // These are example test credentials - replace with actual test credentials from KCP
  SITE_CD: 'T0000001',
  SITE_KEY: 'test_site_key_provided_by_kcp',
  API_URL: 'https://stg-spl.kcp.co.kr',

  // Test card numbers provided by KCP
  TEST_CARDS: {
    VISA: '****************',
    MASTERCARD: '****************',
    AMEX: '***************',
  },

  // Test bank codes
  TEST_BANKS: {
    KB: '004',
    SHINHAN: '088',
    WOORI: '020',
  },
};
```

## Credential Rotation

### 1. Automated Rotation

```typescript
// services/credential-rotation.ts
export class CredentialRotation {
  static async rotateWebhookSecret(): Promise<void> {
    // Generate new webhook secret
    const newSecret = crypto.randomBytes(32).toString('hex');

    // Update in KCP portal (API call or manual process)
    await this.updateKcpWebhookSecret(newSecret);

    // Update environment configuration
    await this.updateEnvironmentVariable('KCP_WEBHOOK_SECRET', newSecret);

    // Restart application services
    await this.restartServices();
  }

  private static async updateKcpWebhookSecret(newSecret: string): Promise<void> {
    // Implementation depends on KCP API availability for webhook management
    console.log('Update webhook secret in KCP portal:', newSecret);
  }
}
```

## Troubleshooting Credential Issues

### Common Credential Problems

1. **Invalid Site Code/Key**
   - Error: "Authentication failed"
   - Solution: Verify credentials in KCP portal

2. **Webhook Secret Mismatch**
   - Error: "Invalid webhook signature"
   - Solution: Ensure webhook secret matches KCP configuration

3. **Environment Mismatch**
   - Error: "Invalid API endpoint"
   - Solution: Use correct API URL for environment

4. **Expired Credentials**
   - Error: "Credential expired"
   - Solution: Renew credentials through KCP portal

### Credential Testing Script

```bash
#!/bin/bash
# scripts/test-kcp-credentials.sh

echo "Testing KCP Credentials..."

# Test environment variables
if [ -z "$KCP_SITE_CD" ]; then
  echo "ERROR: KCP_SITE_CD not set"
  exit 1
fi

if [ -z "$KCP_SITE_KEY" ]; then
  echo "ERROR: KCP_SITE_KEY not set"
  exit 1
fi

# Test API connectivity
curl -X POST "$KCP_API_URL/std/tradeReg/register" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "site_cd=$KCP_SITE_CD&kcp_cert_info=$KCP_SITE_CD:$KCP_SITE_KEY" \
  --max-time 10

echo "Credential test completed"
```

## Credential Flow Diagram

### KCP Credential Usage Flow

```mermaid
graph TD
    A[KCP Merchant Portal] -->|Provides| B[Site Code & Site Key]
    A -->|Configure| C[Webhook URL & Secret]

    B --> D[Backend Application]
    C --> D

    D -->|API Calls| E[KCP Payment Gateway]
    E -->|Webhooks| F[Webhook Endpoint]

    F -->|Verify| G[Webhook Secret]
    G -->|Valid| H[Process Payment]
    G -->|Invalid| I[Reject Request]

    D -->|Store Encrypted| J[Environment Variables]
    J -->|Load at Runtime| K[KCP Config Service]

    K -->|Authenticate| E
    K -->|Generate| L[Payment URLs]
    K -->|Verify| M[Signatures]
```

### Credential Security Architecture

```mermaid
graph LR
    A[Environment Variables] -->|Encrypted| B[Application Memory]
    B -->|Validated| C[KCP Config Service]
    C -->|Secure API Calls| D[KCP Gateway]

    E[Webhook Requests] -->|Signature Check| F[Webhook Validator]
    F -->|Valid| G[Payment Processor]
    F -->|Invalid| H[Security Log]

    I[Credential Monitor] -->|Health Check| C
    I -->|Alert on Failure| J[Admin Notification]
```

## Summary of All Required Credentials

### Essential KCP Credentials (Must Have)
1. **KCP_SITE_CD** - Your unique merchant identifier
2. **KCP_SITE_KEY** - Secret authentication key
3. **KCP_WEBHOOK_SECRET** - Webhook signature verification key
4. **KCP_API_URL** - Environment-specific API endpoint

### Optional Configuration (Recommended)
1. **KCP_TIMEOUT** - API request timeout (default: 30000ms)
2. **KCP_RETRY_ATTEMPTS** - Failed request retry count (default: 3)
3. **KCP_CARD_QUOTA_OPTION** - Installment options (default: 12)
4. **KCP_CURRENCY_CODE** - Currency code (default: 410 for KRW)

### Application-Specific Settings
1. **PAYMENT_PROVIDER** - Set to 'kcp'
2. **PAYMENT_CURRENCY** - Set to 'KRW'
3. **FRONTEND_URL** - For return/cancel URLs
4. **API_URL** - Your backend API base URL

This guide provides comprehensive information about all credentials and configuration needed for the KCP payment gateway integration.
