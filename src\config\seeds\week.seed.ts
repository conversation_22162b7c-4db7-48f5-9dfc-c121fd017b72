import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { QAMissionWeek as Week } from '../../database/entities/qa-mission-week.entity';
@Injectable()
export class WeekSeed {
  private readonly logger = new Logger(WeekSeed.name);

  constructor(
    @InjectRepository(Week)
    private readonly weekRepository: Repository<Week>,
  ) {}

  async seed(): Promise<void> {
    this.logger.log('Seeding weeks...');

    // Define weeks for 2025
    const weeks2025 = [
      // January
      { title: 'Week-01', sequence: 1, month: 'January', startDate: new Date('2025-01-01'), endDate: new Date('2025-01-07') },
      { title: 'Week-02', sequence: 2, month: 'January', startDate: new Date('2025-01-08'), endDate: new Date('2025-01-14') },
      { title: 'Week-03', sequence: 3, month: 'January', startDate: new Date('2025-01-15'), endDate: new Date('2025-01-21') },
      { title: 'Week-04', sequence: 4, month: 'January', startDate: new Date('2025-01-22'), endDate: new Date('2025-01-28') },
      { title: 'Week-05', sequence: 5, month: 'January', startDate: new Date('2025-01-29'), endDate: new Date('2025-02-04') },
    
      // February
      { title: 'Week-06', sequence: 6, month: 'February', startDate: new Date('2025-02-05'), endDate: new Date('2025-02-11') },
      { title: 'Week-07', sequence: 7, month: 'February', startDate: new Date('2025-02-12'), endDate: new Date('2025-02-18') },
      { title: 'Week-08', sequence: 8, month: 'February', startDate: new Date('2025-02-19'), endDate: new Date('2025-02-25') },
      { title: 'Week-09', sequence: 9, month: 'February', startDate: new Date('2025-02-26'), endDate: new Date('2025-03-04') },
    
      // March
      { title: 'Week-10', sequence: 10, month: 'March', startDate: new Date('2025-03-05'), endDate: new Date('2025-03-11') },
      { title: 'Week-11', sequence: 11, month: 'March', startDate: new Date('2025-03-12'), endDate: new Date('2025-03-18') },
      { title: 'Week-12', sequence: 12, month: 'March', startDate: new Date('2025-03-19'), endDate: new Date('2025-03-25') },
      { title: 'Week-13', sequence: 13, month: 'March', startDate: new Date('2025-03-26'), endDate: new Date('2025-04-01') },
    
      // April
      { title: 'Week-14', sequence: 14, month: 'April', startDate: new Date('2025-04-02'), endDate: new Date('2025-04-08') },
      { title: 'Week-15', sequence: 15, month: 'April', startDate: new Date('2025-04-09'), endDate: new Date('2025-04-15') },
      { title: 'Week-16', sequence: 16, month: 'April', startDate: new Date('2025-04-16'), endDate: new Date('2025-04-22') },
      { title: 'Week-17', sequence: 17, month: 'April', startDate: new Date('2025-04-23'), endDate: new Date('2025-04-29') },
      { title: 'Week-18', sequence: 18, month: 'April', startDate: new Date('2025-04-30'), endDate: new Date('2025-05-06') },
    
      // May
      { title: 'Week-19', sequence: 19, month: 'May', startDate: new Date('2025-05-07'), endDate: new Date('2025-05-13') },
      { title: 'Week-20', sequence: 20, month: 'May', startDate: new Date('2025-05-14'), endDate: new Date('2025-05-20') },
      { title: 'Week-21', sequence: 21, month: 'May', startDate: new Date('2025-05-21'), endDate: new Date('2025-05-27') },
      { title: 'Week-22', sequence: 22, month: 'May', startDate: new Date('2025-05-28'), endDate: new Date('2025-06-03') },
    
      // June
      { title: 'Week-23', sequence: 23, month: 'June', startDate: new Date('2025-06-04'), endDate: new Date('2025-06-10') },
      { title: 'Week-24', sequence: 24, month: 'June', startDate: new Date('2025-06-11'), endDate: new Date('2025-06-17') },
      { title: 'Week-25', sequence: 25, month: 'June', startDate: new Date('2025-06-18'), endDate: new Date('2025-06-24') },
      { title: 'Week-26', sequence: 26, month: 'June', startDate: new Date('2025-06-25'), endDate: new Date('2025-07-01') },
    
      // July
      { title: 'Week-27', sequence: 27, month: 'July', startDate: new Date('2025-07-02'), endDate: new Date('2025-07-08') },
      { title: 'Week-28', sequence: 28, month: 'July', startDate: new Date('2025-07-09'), endDate: new Date('2025-07-15') },
      { title: 'Week-29', sequence: 29, month: 'July', startDate: new Date('2025-07-16'), endDate: new Date('2025-07-22') },
      { title: 'Week-30', sequence: 30, month: 'July', startDate: new Date('2025-07-23'), endDate: new Date('2025-07-29') },
      { title: 'Week-31', sequence: 31, month: 'July', startDate: new Date('2025-07-30'), endDate: new Date('2025-08-05') },
    
      // August
      { title: 'Week-32', sequence: 32, month: 'August', startDate: new Date('2025-08-06'), endDate: new Date('2025-08-12') },
      { title: 'Week-33', sequence: 33, month: 'August', startDate: new Date('2025-08-13'), endDate: new Date('2025-08-19') },
      { title: 'Week-34', sequence: 34, month: 'August', startDate: new Date('2025-08-20'), endDate: new Date('2025-08-26') },
      { title: 'Week-35', sequence: 35, month: 'August', startDate: new Date('2025-08-27'), endDate: new Date('2025-09-02') },
    
      // September
      { title: 'Week-36', sequence: 36, month: 'September', startDate: new Date('2025-09-03'), endDate: new Date('2025-09-09') },
      { title: 'Week-37', sequence: 37, month: 'September', startDate: new Date('2025-09-10'), endDate: new Date('2025-09-16') },
      { title: 'Week-38', sequence: 38, month: 'September', startDate: new Date('2025-09-17'), endDate: new Date('2025-09-23') },
      { title: 'Week-39', sequence: 39, month: 'September', startDate: new Date('2025-09-24'), endDate: new Date('2025-09-30') },
    
      // October
      { title: 'Week-40', sequence: 40, month: 'October', startDate: new Date('2025-10-01'), endDate: new Date('2025-10-07') },
      { title: 'Week-41', sequence: 41, month: 'October', startDate: new Date('2025-10-08'), endDate: new Date('2025-10-14') },
      { title: 'Week-42', sequence: 42, month: 'October', startDate: new Date('2025-10-15'), endDate: new Date('2025-10-21') },
      { title: 'Week-43', sequence: 43, month: 'October', startDate: new Date('2025-10-22'), endDate: new Date('2025-10-28') },
      { title: 'Week-44', sequence: 44, month: 'October', startDate: new Date('2025-10-29'), endDate: new Date('2025-11-04') },
    
      // November
      { title: 'Week-45', sequence: 45, month: 'November', startDate: new Date('2025-11-05'), endDate: new Date('2025-11-11') },
      { title: 'Week-46', sequence: 46, month: 'November', startDate: new Date('2025-11-12'), endDate: new Date('2025-11-18') },
      { title: 'Week-47', sequence: 47, month: 'November', startDate: new Date('2025-11-19'), endDate: new Date('2025-11-25') },
      { title: 'Week-48', sequence: 48, month: 'November', startDate: new Date('2025-11-26'), endDate: new Date('2025-12-02') },
    
      // December
      { title: 'Week-49', sequence: 49, month: 'December', startDate: new Date('2025-12-03'), endDate: new Date('2025-12-09') },
      { title: 'Week-50', sequence: 50, month: 'December', startDate: new Date('2025-12-10'), endDate: new Date('2025-12-16') },
      { title: 'Week-51', sequence: 51, month: 'December', startDate: new Date('2025-12-17'), endDate: new Date('2025-12-23') },
      { title: 'Week-52', sequence: 52, month: 'December', startDate: new Date('2025-12-24'), endDate: new Date('2025-12-30') }
    ];

    // Seed weeks for 2025
    for (const weekData of weeks2025) {
      // Check if week already exists
      const existingWeek = await this.weekRepository.findOne({
        where: {
          title: weekData.title,
          month: weekData.month,
          startDate: weekData.startDate
        }
      });

      if (!existingWeek) {
        // Create and save the week
        const week = this.weekRepository.create({
          ...weekData,
          year: 2025
        });
        await this.weekRepository.save(week);
        this.logger.log(`Created week: ${week.title} ${week.month} 2025`);
      } else {
        this.logger.log(`Week already exists: ${existingWeek.title} ${existingWeek.month} 2025`);
      }
    }

    this.logger.log('Week seeding completed');
  }
}
