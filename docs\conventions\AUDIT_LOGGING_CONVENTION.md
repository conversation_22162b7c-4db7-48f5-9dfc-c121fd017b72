# Audit Logging Convention

This document provides detailed guidelines for implementing and using the audit logging system in the HEC Backend project.

## Table of Contents

1. [Overview](#overview)
2. [Audit Fields](#audit-fields)
3. [Automatic Audit Field Population](#automatic-audit-field-population)
4. [Implementation](#implementation)
5. [Usage](#usage)

## Overview

The audit logging system automatically tracks when entities are created or modified and by whom. This provides a complete history of changes to the database, which is essential for debugging, security, and compliance purposes.

## Audit Fields

All entities that need auditing should include the following fields:

- `CreatedAt`: Automatically populated with the current timestamp when the entity is created
- `UpdatedAt`: Automatically updated with the current timestamp when the entity is modified
- `CreatedById`: Populated with the ID of the user who created the entity
- `UpdatedById`: Populated with the ID of the user who last modified the entity

The `UpdatedAt` and `UpdatedById` fields should be nullable since they won't have values when the entity is first created.

Example entity with audit fields:

```typescript
@Entity('user')
export class User {
  @PrimaryGeneratedColumn('uuid')
  Id: string;

  // Entity-specific fields...

  @CreateDateColumn()
  CreatedAt: Date;

  @UpdateDateColumn({ nullable: true })
  UpdatedAt: Date;

  @Column({ nullable: true })
  CreatedById: string;

  @Column({ nullable: true })
  UpdatedById: string;
}
```

## Automatic Audit Field Population

The system automatically populates audit fields using TypeORM subscribers or a unit of work pattern:

1. `CreatedAt` and `UpdatedAt` are handled by TypeORM's `@CreateDateColumn()` and `@UpdateDateColumn()` decorators
2. `CreatedById` and `UpdatedById` are populated by a custom subscriber that extracts the current user ID from the request context

## Implementation

### TypeORM Subscriber

The audit logging system is implemented using a TypeORM subscriber that listens for entity events:

```typescript
@Injectable()
export class AuditSubscriber implements EntitySubscriberInterface {
  constructor(private readonly requestService: RequestContextService) {}

  beforeInsert(event: InsertEvent<any>) {
    if (this.isAuditable(event.entity)) {
      const userId = this.requestService.getCurrentUserId();
      if (userId) {
        event.entity.CreatedById = userId;
      }
    }
  }

  beforeUpdate(event: UpdateEvent<any>) {
    if (this.isAuditable(event.entity)) {
      const userId = this.requestService.getCurrentUserId();
      if (userId) {
        event.entity.UpdatedById = userId;
      }
    }
  }

  private isAuditable(entity: any): boolean {
    return entity && 
           'CreatedById' in entity && 
           'UpdatedById' in entity;
  }
}
```

### Request Context Service

The `RequestContextService` uses AsyncLocalStorage or a similar mechanism to store the current user ID throughout the request lifecycle:

```typescript
@Injectable()
export class RequestContextService {
  private readonly asyncLocalStorage = new AsyncLocalStorage<Map<string, any>>();

  getStore(): Map<string, any> | undefined {
    return this.asyncLocalStorage.getStore();
  }

  run(context: Map<string, any>, callback: () => any) {
    return this.asyncLocalStorage.run(context, callback);
  }

  setCurrentUserId(userId: string) {
    const store = this.getStore();
    if (store) {
      store.set('userId', userId);
    }
  }

  getCurrentUserId(): string | undefined {
    const store = this.getStore();
    return store ? store.get('userId') : undefined;
  }
}
```

### Authentication Middleware

The authentication middleware sets the current user ID in the request context:

```typescript
@Injectable()
export class AuthMiddleware implements NestMiddleware {
  constructor(
    private readonly jwtService: JwtService,
    private readonly requestContextService: RequestContextService
  ) {}

  use(req: Request, res: Response, next: NextFunction) {
    const token = this.extractTokenFromHeader(req);
    if (token) {
      try {
        const payload = this.jwtService.verify(token);
        this.requestContextService.setCurrentUserId(payload.sub);
      } catch (error) {
        // Token validation failed, but we'll continue without a user ID
      }
    }
    next();
  }

  private extractTokenFromHeader(req: Request): string | undefined {
    const [type, token] = req.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
```

## Usage

To make an entity auditable, simply add the audit fields to your entity definition:

```typescript
@Entity('diary_entry')
export class DiaryEntry {
  @PrimaryGeneratedColumn('uuid')
  Id: string;

  @Column()
  Title: string;

  @Column()
  Content: string;

  // Audit fields
  @CreateDateColumn()
  CreatedAt: Date;

  @UpdateDateColumn({ nullable: true })
  UpdatedAt: Date;

  @Column({ nullable: true })
  CreatedById: string;

  @Column({ nullable: true })
  UpdatedById: string;
}
```

The audit fields will be automatically populated when the entity is created or updated.

### Transactions

When using transactions, the audit fields are still automatically populated:

```typescript
async createDiaryEntry(userId: string, createDiaryEntryDto: CreateDiaryEntryDto): Promise<DiaryEntry> {
  // Start a transaction
  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Create the diary entry
    const diaryEntry = queryRunner.manager.create(DiaryEntry, {
      Title: createDiaryEntryDto.title,
      Content: createDiaryEntryDto.content,
      // No need to set CreatedById or CreatedAt - they are automatically populated
    });

    // Save the diary entry
    const savedEntry = await queryRunner.manager.save(diaryEntry);

    // Commit the transaction
    await queryRunner.commitTransaction();
    return savedEntry;
  } catch (error) {
    // Rollback the transaction in case of error
    await queryRunner.rollbackTransaction();
    throw error;
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
}
```

---

Following these conventions ensures a consistent and reliable audit trail throughout the application.
