# HEC Backend Development

This document provides an overview of the backend development process and implementation details for the HEC platform. It focuses on the currently implemented features and functionality.

## Current Backend Implementation

The HEC backend is built with the following technologies and features:

1. **NestJS Framework**: Used for the entire backend architecture with modules for auth, users, diary, tutor matching, notifications, shop, and essay
2. **TypeORM with PostgreSQL**: Handles all database operations with custom naming strategy (camelCase for DTOs/entities with snake_case database columns)
3. **JWT Authentication**: Supports Admin, Tutor, and Student user types with role-based access control
4. **Socket.io**: Implements real-time chat between students, tutors, and admins
5. **File Management**: Handles file uploads with entity-specific registries storing metadata

## Implementation Guides

### File Upload Implementation

The File Upload Implementation guide covers:

- File upload architecture
- File metadata storage
- File serving strategies
- Security considerations
- Implementation examples

[Read more about File Upload Implementation](FILE_UPLOAD_INTEGRATION.md)

### Notification System Implementation

The Notification System Implementation guide covers:

- Notification types and channels
- Notification delivery mechanisms
- Notification storage and retrieval
- Notification retry mechanism
- Implementation examples

[Read more about Notification System Implementation](NOTIFICATION_INTEGRATION.md)

### Deeplink Service Implementation

The Deeplink Service Implementation guide covers:

- Deeplink generation
- QR code generation
- Deeplink resolution
- Security considerations
- Implementation examples

[Read more about Deeplink Service Implementation](../implementation/deeplink-service-implementation.md)

## Current Development Workflow

The HEC backend team follows this development workflow:

1. **Feature Implementation**: Features are implemented chunk by chunk rather than all at once
2. **Targeted Fixes**: Minimal targeted fixes are preferred over large-scale changes
3. **Database Handling**:
   - Automatic handling of audit fields (createdAt/updatedAt, createdBy/updatedBy)
   - Database transactions with rollback for operations involving multiple entities
4. **Testing**:
   - Unit tests for services
   - Integration tests for key workflows
   - Manual testing before deployment
5. **Documentation**:
   - API documentation for each endpoint
   - Frontend integration guides to minimize verbal communication
   - Implementation documentation for backend developers

For more detailed guidance, refer to the [Implementation Guide](../implementation/implementation-guide.md).

## Current Best Practices

Based on the existing implementation, these best practices are followed:

1. **Module Organization**: Clear separation of modules (auth, users, diary, etc.)
2. **Data Transfer Objects**: Consistent use of DTOs for request/response handling
3. **Service Layer**: Business logic encapsulated in services
4. **Repository Pattern**: Database access through repositories
5. **Error Handling**: Consistent error responses with appropriate HTTP status codes
6. **Pagination**: Standard pagination for all list endpoints
7. **File Management**: Centralized file handling with proper metadata tracking
