import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { UserType } from 'src/database/entities/user.entity';

@Injectable()
export class AdminGuard implements CanActivate {
    constructor(private reflector: Reflector) {}

    canActivate(
        context: ExecutionContext,
    ): boolean | Promise<boolean> | Observable<boolean> {
        const request = context.switchToHttp().getRequest();
        const user = request.user;

        if (!user) {
            return false;
        }

        // Check if user has admin type
        if (user.type === UserType.ADMIN) {
            return true;
        }

        // Check if user has admin role
        if (user.roles && Array.isArray(user.roles)) {
            return user.roles.includes('admin');
        }

        return false;
    }
}