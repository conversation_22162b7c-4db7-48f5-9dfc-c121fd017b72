import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiBody, ApiQuery, ApiParam } from '@nestjs/swagger';
import { AdminGuard } from '../../common/guards/admin.guard';
import { AwardScheduleService } from './award-schedule.service';
import { CreateAwardScheduleDto, AwardScheduleResponseDto, UpdateAwardScheduleStatusDto } from '../../database/models/award-schedule.dto';
import { AwardModule } from '../../database/entities/award.entity';
import { ScheduleStatus } from '../../database/entities/award-schedule.entity';
import { JwtAuthGuard } from 'src/common/guards/jwt.guard';
import { ApiResponse } from 'src/common/dto/api-response.dto';
import { ApiErrorResponse, ApiOkResponseWithType } from 'src/common/decorators/api-response.decorator';

@ApiTags('award-schedules')
@Controller('award-schedules')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class AwardScheduleController {
  constructor(private readonly awardScheduleService: AwardScheduleService) {}

  @Post()
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Create a new award schedule (Admin only)',
    description: 'Creates a new award schedule with the specified details.'
  })
  @ApiBody({
    type: CreateAwardScheduleDto,
    description: 'Award schedule creation data'
  })
  @ApiOkResponseWithType(AwardScheduleResponseDto, 'Award schedule created successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async createSchedule(
    @Body() createScheduleDto: CreateAwardScheduleDto
  ): Promise<ApiResponse<AwardScheduleResponseDto>> {
    const schedule = await this.awardScheduleService.createManualSchedule(createScheduleDto);
    return ApiResponse.success(schedule, 'Award schedule created successfully');
  }

  @Get(':id')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Get an award schedule by ID (Admin only)',
    description: 'Get details of a specific award schedule.'
  })
  @ApiParam({
    name: 'id',
    description: 'Award schedule ID',
    type: String
  })
  @ApiOkResponseWithType(AwardScheduleResponseDto, 'Award schedule retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Award schedule not found')
  async getScheduleById(@Param('id') id: string): Promise<ApiResponse<AwardScheduleResponseDto>> {
    const schedule = await this.awardScheduleService.getScheduleById(id);
    return ApiResponse.success(schedule, 'Award schedule retrieved successfully');
  }

  @Get()
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Get all award schedules (Admin only)',
    description: 'Get a list of all award schedules with optional filters.'
  })
  @ApiQuery({
    name: 'module',
    required: false,
    type: 'enum',
    enum: AwardModule,
    description: 'Filter by award module'
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: 'enum',
    enum: ScheduleStatus,
    description: 'Filter by schedule status'
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: Date,
    description: 'Filter by schedule date range start'
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: Date,
    description: 'Filter by schedule date range end'
  })
  @ApiOkResponseWithType(AwardScheduleResponseDto, 'Award schedules retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async getAllSchedules(
    @Query('module') module?: AwardModule,
    @Query('status') status?: ScheduleStatus,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ): Promise<ApiResponse<AwardScheduleResponseDto[]>> {
    const schedules = await this.awardScheduleService.getAllSchedules(
      module,
      status,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined
    );
    return ApiResponse.success(schedules, 'Award schedules retrieved successfully');
  }

  @Post(':id/active-status')
  @UseGuards(AdminGuard)
  @ApiOperation({
    summary: 'Update award schedule active status (Admin only)',
    description: 'Activate or deactivate an award schedule.'
  })
  @ApiParam({
    name: 'id',
    description: 'Award schedule ID',
    type: String
  })
  @ApiBody({
    type: UpdateAwardScheduleStatusDto,
    description: 'Award schedule active status update data'
  })
  @ApiOkResponseWithType(AwardScheduleResponseDto, 'Award schedule updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Award schedule not found')
  async updateActiveStatus(
    @Param('id') id: string,
    @Body() updateDto: UpdateAwardScheduleStatusDto
  ): Promise<ApiResponse<AwardScheduleResponseDto>> {
    const schedule = await this.awardScheduleService.updateActiveStatus(id, updateDto.isActive);
    return ApiResponse.success(
      schedule, 
      `Award schedule ${updateDto.isActive ? 'activated' : 'deactivated'} successfully`
    );
  }
}
