import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

/**
 * Entity representing a tutor's education information
 */
@Entity('tutor_education')
export class TutorEducation extends AuditableBaseEntity {
  @Column({ name: 'tutor_id' })
  tutorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @Column({ name: 'degree', length: 100 })
  degree: string;

  @Column({ name: 'institution', length: 255 })
  institution: string;

  @Column({ name: 'field_of_study', length: 255 })
  fieldOfStudy: string;

  @Column({ name: 'start_date', type: 'date' })
  startDate: Date;

  @Column({ name: 'end_date', type: 'date', nullable: true })
  endDate: Date;

  @Column({ name: 'is_current', default: false })
  isCurrent: boolean;

  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  @Column({ name: 'location', length: 255, nullable: true })
  location: string;

  @Column({ name: 'grade', length: 50, nullable: true })
  grade: string;

  @Column({ name: 'activities', type: 'text', nullable: true })
  activities: string;

  /**
   * Convert the entity to a plain object for API responses
   * @returns A plain object representation of the entity
   */
  toResponseObject() {
    return {
      id: this.id,
      tutorId: this.tutorId,
      degree: this.degree,
      institution: this.institution,
      fieldOfStudy: this.fieldOfStudy,
      startDate: this.startDate,
      endDate: this.endDate,
      isCurrent: this.isCurrent,
      description: this.description,
      location: this.location,
      grade: this.grade,
      activities: this.activities,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}
