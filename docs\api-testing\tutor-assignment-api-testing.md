# Tutor Assignment API Testing Flow

This document outlines the testing flow for the Tutor Assignment API endpoints.

## Prerequisites

Before testing the Tutor Assignment API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (admin, tutor, student)
3. Set up your API testing tool (<PERSON><PERSON> recommended)
4. Ensure there are active subscription plans with different features

## Automatic Assignment Testing Flow

### Test Case 1: Automatic Assignment on Plan Subscription

1. Authenticate with a student token
2. Subscribe to a plan with multiple features
3. Verify tutors are automatically assigned for each active feature
4. Verify assignments are recorded in the database
5. Verify appropriate notifications are sent to assigned tutors
6. Verify the student can see the assigned tutors

### Test Case 2: Assignment Based on Feature Availability

1. Authenticate with a student token
2. Subscribe to a plan with some inactive features
3. Verify tutors are only assigned for active features
4. Verify no assignments are created for inactive features
5. Activate a previously inactive feature
6. Verify a tutor is now assigned for the newly activated feature

### Test Case 3: Assignment Based on Tutor Availability

1. Authenticate with an admin token
2. Set tutor availability limits (max students per tutor)
3. Create multiple students and subscribe them to plans
4. Verify assignments are distributed based on tutor availability
5. Verify tutors don't exceed their maximum student limit
6. Verify appropriate fallback mechanism when all tutors reach capacity

## Manual Assignment Testing Flow

### Test Case 1: Admin Manual Assignment

1. Authenticate with an admin token
2. Send a POST request to `/api/admin/tutor-assignments` with student ID, tutor ID, and feature
3. Verify HTTP status code is 200 OK
4. Verify assignment is created in the database
5. Verify appropriate notifications are sent to the tutor and student
6. Verify the assignment is visible to both the tutor and student

### Test Case 2: Update Assignment

1. Authenticate with an admin token
2. Create a tutor assignment
3. Send a PUT request to `/api/admin/tutor-assignments/{assignmentId}` with updated data
4. Verify HTTP status code is 200 OK
5. Verify assignment is updated in the database
6. Verify appropriate notifications are sent to affected users

### Test Case 3: Remove Assignment

1. Authenticate with an admin token
2. Create a tutor assignment
3. Send a DELETE request to `/api/admin/tutor-assignments/{assignmentId}`
4. Verify HTTP status code is 200 OK
5. Verify assignment is removed from the database
6. Verify appropriate notifications are sent to affected users

## Assignment Retrieval Testing Flow

### Test Case 1: Get Student's Assigned Tutors

1. Authenticate with a student token
2. Have tutors assigned for different features
3. Send a GET request to `/api/students/assigned-tutors`
4. Verify HTTP status code is 200 OK
5. Verify response contains list of assigned tutors
6. Verify each assignment includes the feature and tutor details

### Test Case 2: Get Tutor's Assigned Students

1. Authenticate with a tutor token
2. Have students assigned to this tutor for different features
3. Send a GET request to `/api/tutors/assigned-students` with pagination parameters
4. Verify HTTP status code is 200 OK
5. Verify response contains paginated list of assigned students
6. Verify each assignment includes the feature and student details

### Test Case 3: Filter Assignments

1. Authenticate with appropriate token (admin, tutor, or student)
2. Send a GET request to the assignment endpoint with filter parameters
3. Test filtering by feature
4. Test filtering by assignment date
5. Test filtering by active status
6. Verify filtered results match the criteria

## Assignment Metrics Testing Flow

### Test Case 1: Get Tutor Assignment Metrics

1. Authenticate with an admin or tutor token
2. Create multiple assignments for different tutors
3. Send a GET request to `/api/tutor-assignments/metrics`
4. Verify HTTP status code is 200 OK
5. Verify response contains assignment metrics (students per tutor, assignments per feature, etc.)
6. Verify metrics match the actual data in the database

### Test Case 2: Get Feature Assignment Coverage

1. Authenticate with an admin token
2. Create assignments for different features
3. Send a GET request to `/api/admin/tutor-assignments/coverage`
4. Verify HTTP status code is 200 OK
5. Verify response contains coverage metrics for each feature
6. Verify metrics match the actual data in the database

## Assignment Notification Testing Flow

### Test Case 1: Assignment Notification to Tutor

1. Authenticate with an admin token
2. Create a new assignment
3. Verify notification is sent to the assigned tutor
4. Verify notification contains correct assignment details
5. Verify notification includes links to view the assigned student

### Test Case 2: Assignment Notification to Student

1. Authenticate with an admin token
2. Create a new assignment
3. Verify notification is sent to the student
4. Verify notification contains correct assignment details
5. Verify notification includes links to view the assigned tutor

## Edge Cases and Security Testing

### Test Case 1: Role-Based Access Control

1. Authenticate with a student token
2. Attempt to access tutor assignment management endpoints
3. Verify 403 Forbidden responses
4. Authenticate with a tutor token
5. Attempt to access admin assignment endpoints
6. Verify 403 Forbidden responses

### Test Case 2: Assignment Conflicts

1. Authenticate with an admin token
2. Assign a tutor to a student for a specific feature
3. Attempt to assign another tutor to the same student for the same feature
4. Verify appropriate error response or automatic replacement
5. Verify only one active assignment exists per student per feature

### Test Case 3: Feature Access Validation

1. Authenticate with an admin token
2. Attempt to assign a tutor for a feature not included in the student's plan
3. Verify appropriate error response
4. Attempt to assign a tutor for a feature that is inactive in the student's plan
5. Verify appropriate error response

### Test Case 4: Tutor Qualification Validation

1. Authenticate with an admin token
2. Attempt to assign a tutor to a feature they are not qualified for
3. Verify appropriate error response or warning
4. Update tutor qualifications
5. Verify assignment can now be created successfully

### Test Case 5: Plan Expiration Handling

1. Authenticate with a student token
2. Subscribe to a plan and get tutors assigned
3. Let the plan expire or cancel it
4. Verify tutor assignments are handled appropriately (marked inactive or removed)
5. Subscribe to a new plan
6. Verify new tutors are assigned based on the new plan features
