import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAuditColumnsToDiaryFriendShare1704067300000 implements MigrationInterface {
  name = 'AddAuditColumnsToDiaryFriendShare1704067300000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the columns already exist
    const table = await queryRunner.getTable('diary_entry_friend_share');
    if (!table) {
      console.log('Table diary_entry_friend_share does not exist, skipping audit columns addition');
      return;
    }

    const createdByColumn = table.findColumnByName('created_by');
    const updatedByColumn = table.findColumnByName('updated_by');

    if (createdByColumn && updatedByColumn) {
      console.log('Audit columns already exist in diary_entry_friend_share table, skipping');
      return;
    }

    // Add created_by column if it doesn't exist
    if (!createdByColumn) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry_friend_share" 
        ADD COLUMN "created_by" character varying(36)
      `);
      console.log('Added created_by column to diary_entry_friend_share table');
    }

    // Add updated_by column if it doesn't exist
    if (!updatedByColumn) {
      await queryRunner.query(`
        ALTER TABLE "diary_entry_friend_share" 
        ADD COLUMN "updated_by" character varying(36)
      `);
      console.log('Added updated_by column to diary_entry_friend_share table');
    }

    console.log('Successfully added audit columns to diary_entry_friend_share table');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if table exists
    const table = await queryRunner.getTable('diary_entry_friend_share');
    if (!table) {
      console.log('Table diary_entry_friend_share does not exist, skipping audit columns removal');
      return;
    }

    // Drop updated_by column if it exists
    const updatedByColumn = table.findColumnByName('updated_by');
    if (updatedByColumn) {
      await queryRunner.query(`ALTER TABLE "diary_entry_friend_share" DROP COLUMN IF EXISTS "updated_by"`);
      console.log('Dropped updated_by column from diary_entry_friend_share table');
    }

    // Drop created_by column if it exists
    const createdByColumn = table.findColumnByName('created_by');
    if (createdByColumn) {
      await queryRunner.query(`ALTER TABLE "diary_entry_friend_share" DROP COLUMN IF EXISTS "created_by"`);
      console.log('Dropped created_by column from diary_entry_friend_share table');
    }

    console.log('Successfully removed audit columns from diary_entry_friend_share table');
  }
}
