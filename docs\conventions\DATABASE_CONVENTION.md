# Database Convention

This document provides detailed guidelines for database design, entity modeling, and data access in the HEC Backend project.

## Table of Contents

1. [Database Design](#database-design)
2. [Entity Modeling](#entity-modeling)
3. [Relationships](#relationships)
4. [Data Access](#data-access)
5. [Migrations](#migrations)
6. [Seeding](#seeding)
7. [File Upload and Registry System](#file-upload-and-registry-system)
8. [Performance Optimization](#performance-optimization)

## Database Design

### Naming Conventions

#### Entity and Property Naming
- Use **PascalCase** for entity class names
- Use **camelCase** for entity properties
- Entity class names should be singular (e.g., `User`, not `Users`)
- Primary keys should be named `id` in the entity
- Foreign keys should follow the pattern `entityNameId` (e.g., `userId`, `diaryId`)

#### Database Naming
- Table names: Use snake_case: `user`, `diary_entry`
- Column names: Use snake_case: `first_name`, `created_at`
- Primary keys: Use `id` as the primary key name in the database
- Foreign keys: Use `entity_name_id`: `user_id`, `plan_id`
- Junction tables: Combine both table names: `user_role`
- Indexes: Use `idx_table_column`: `idx_users_email`
- Constraints: Use descriptive names: `pk_users`, `fk_user_roles_user_id`

#### Naming Strategy

Use TypeORM's custom naming strategy to automatically convert camelCase to snake_case for database columns:

```typescript
// snake-naming.strategy.ts
export class SnakeNamingStrategy extends DefaultNamingStrategy implements NamingStrategyInterface {
  tableName(targetName: string, userSpecifiedName: string | undefined): string {
    return userSpecifiedName ? userSpecifiedName : snakeCase(targetName);
  }

  columnName(
    propertyName: string,
    customName: string | undefined,
    embeddedPrefixes: string[],
  ): string {
    return (
      snakeCase(embeddedPrefixes.join('_')) +
      (customName ? customName : snakeCase(propertyName))
    );
  }

  // Other methods...
}
```

This naming strategy ensures that entity properties written in camelCase (e.g., `userId`) are automatically converted to snake_case in the database (`user_id`).

### Data Types

- Use appropriate data types for columns:
  - Text: `varchar` for short text, `text` for long text
  - Numbers: `int` for integers, `decimal` for precise decimals, `float` for approximate decimals
  - Dates: `timestamp` for date and time, `date` for date only
  - Booleans: `boolean`
  - UUIDs: `uuid`

### Constraints

- Use primary key constraints for all tables
- Use foreign key constraints for relationships
- Use unique constraints for unique columns
- Use check constraints for data validation
- Use not null constraints for required columns

## Entity Modeling

### Audit Fields

All entities that need auditing should include the following fields:

- `createdAt`: Automatically populated with the current timestamp when the entity is created
- `updatedAt`: Automatically updated with the current timestamp when the entity is modified (nullable)
- `createdBy`: Populated with the ID of the user who created the entity (nullable)
- `updatedBy`: Populated with the ID of the user who last modified the entity (nullable)

```typescript
@Entity('user')
export class User extends AuditableBaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Entity-specific fields...

  // Audit fields are inherited from AuditableBaseEntity:
  // createdAt: Date;
  // updatedAt: Date;
  // createdBy: string;
  // updatedBy: string;
}
```

The `updatedAt` and `updatedBy` fields should be nullable since they won't have values when the entity is first created.

We use a base entity class to provide these audit fields to all entities that need them:

```typescript
// base-entity.ts
export abstract class NonAuditableBaseEntity {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;
}

export abstract class AuditableBaseEntity extends NonAuditableBaseEntity {
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', nullable: true })
  updatedAt: Date;

  @Column({ name: 'created_by', nullable: true, length: 36 })
  createdBy: string;

  @Column({ name: 'updated_by', nullable: true, length: 36 })
  updatedBy: string;
}
```

This base entity class is automatically populated with audit information through a global subscriber that intercepts entity creation and update operations.

### Entity Structure

```typescript
@Entity('user')
export class User extends AuditableBaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'name' })
  name: string;

  @Column({ name: 'user_id', unique: true })
  userId: string;

  @Column({ name: 'email', unique: true })
  email: string;

  @Column({ name: 'phone_number', nullable: true })
  phoneNumber: string;

  @Column({ name: 'gender', nullable: true })
  gender: string;

  @Column({ name: 'password' })
  password: string;

  @Column({ name: 'is_active', default: false })
  isActive: boolean;

  @Column({ name: 'verification_token', nullable: true })
  verificationToken: string;

  @Column({ name: 'reset_password_token', nullable: true })
  resetPasswordToken: string;

  @Column({ name: 'reset_password_expires', nullable: true })
  resetPasswordExpires: Date;

  // Relationships
  @OneToMany(() => UserRole, userRole => userRole.user)
  userRoles: UserRole[];

  @OneToMany(() => UserPlan, userPlan => userPlan.user)
  userPlans: UserPlan[];

  // Methods
  toDto(): UserResponseDto {
    // Implementation
  }

  verifyPassword(password: string): boolean {
    // Implementation
  }
}
```

### Entity Methods

- Add methods to entities for business logic related to the entity
- Use `toDto()` methods to transform entities to DTOs
- Use methods for entity-specific operations like password verification

## Relationships

### One-to-One

```typescript
// User entity
@OneToOne(() => Profile, profile => profile.user)
@JoinColumn({ name: 'profile_id' })
profile: Profile;

// Profile entity
@OneToOne(() => User, user => user.profile)
user: User;
```

### One-to-Many

```typescript
// User entity
@OneToMany(() => DiaryEntry, diaryEntry => diaryEntry.user)
diaryEntries: DiaryEntry[];

// DiaryEntry entity
@ManyToOne(() => User, user => user.diaryEntries)
@JoinColumn({ name: 'user_id' })
user: User;

@Column({ name: 'user_id' })
userId: string;
```

### Many-to-Many

```typescript
// User entity
@ManyToMany(() => Role, role => role.users)
@JoinTable({
  name: 'user_role',
  joinColumn: { name: 'user_id', referencedColumnName: 'id' },
  inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' }
})
roles: Role[];

// Role entity
@ManyToMany(() => User, user => user.roles)
users: User[];
```

## Data Access

### Repositories

- Use TypeORM repositories for data access
- Inject repositories into services using `@InjectRepository()`
- Use repository methods for CRUD operations

```typescript
@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}

  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['userRoles', 'userRoles.role']
    });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }
}
```

### Query Building

- Use TypeORM query builder for complex queries
- Use eager loading to avoid N+1 query problems
- Use pagination for large result sets

```typescript
async findAllPaginated(paginationDto: PaginationDto): Promise<{ items: User[], totalItems: number }> {
  const { page, limit, sortBy, sortOrder } = paginationDto;
  const skip = (page - 1) * limit;

  const [items, totalItems] = await this.userRepository.findAndCount({
    skip,
    take: limit,
    order: { [sortBy]: sortOrder },
    relations: ['userRoles', 'userRoles.role']
  });

  return { items, totalItems };
}
```

### Transactions

- Use transactions for operations that modify multiple entities to ensure data consistency
- Use the query runner approach for explicit transaction control with proper error handling and rollback

```typescript
async createUserWithRoles(createUserDto: CreateUserDto, roles: string[]): Promise<User> {
  // Start a transaction
  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Create user
    const user = queryRunner.manager.create(User, {
      name: createUserDto.name,
      email: createUserDto.email,
      password: await bcrypt.hash(createUserDto.password, 10),
      isActive: true
    });
    await queryRunner.manager.save(user);

    // Assign roles
    for (const roleName of roles) {
      const role = await queryRunner.manager.findOne(Role, { where: { name: roleName } });
      if (role) {
        const userRole = queryRunner.manager.create(UserRole, {
          userId: user.id,
          roleId: role.id
        });
        await queryRunner.manager.save(userRole);
      }
    }

    // Commit the transaction
    await queryRunner.commitTransaction();
    return user;
  } catch (error) {
    // Rollback the transaction in case of error
    await queryRunner.rollbackTransaction();
    throw error;
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
}
```

## Migrations

### Creating Migrations

- Use TypeORM CLI to generate migrations
- Name migrations descriptively: `CreateUsersTable`, `AddEmailVerificationToUsers`
- Review and modify generated migrations as needed

```bash
npm run typeorm migration:generate -- -n CreateUsersTable
```

### Running Migrations

- Run migrations during application startup
- Use TypeORM CLI to run migrations manually

```typescript
// main.ts
async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Run migrations
  const connection = app.get(Connection);
  await connection.runMigrations();

  await app.listen(3000);
}
```

### Rolling Back Migrations

- Create down methods in migrations for rollback
- Use TypeORM CLI to revert migrations

```bash
npm run typeorm migration:revert
```

## Seeding

### Seed Service

- Create a seed service for initializing the database with test data
- Use environment variables to control seeding behavior
- Check for existing data before seeding

```typescript
@Injectable()
export class SeedService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>
  ) {}

  async seed() {
    await this.seedRoles();
    await this.seedUsers();
  }

  private async seedRoles() {
    const existingRoles = await this.roleRepository.count();
    if (existingRoles > 0) {
      return;
    }

    const roles = [
      { name: 'admin' },
      { name: 'tutor' },
      { name: 'student' }
    ];

    await this.roleRepository.save(roles);
  }

  private async seedUsers() {
    const existingUsers = await this.userRepository.count();
    if (existingUsers > 0) {
      return;
    }

    // Seed admin user
    const adminUser = this.userRepository.create({
      userId: 'admin',
      email: '<EMAIL>',
      password: await bcrypt.hash('Admin@123', 10),
      isActive: true,
      isConfirmed: true,
      type: UserType.ADMIN
    });

    await this.userRepository.save(adminUser);
  }
}
```

### Running Seeds

- Run seeds during application startup in development environment
- Provide a separate command for running seeds in production

```typescript
// main.ts
async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Run seeds in development
  if (process.env.NODE_ENV === 'development') {
    const seedService = app.get(SeedService);
    await seedService.seed();
  }

  await app.listen(3000);
}
```

## File Upload and Registry System

### File Upload Strategy

- Files are uploaded to a configurable directory (`UPLOAD_DIR` environment variable, defaults to 'uploads')
- Files are organized in subdirectories based on their type (e.g., 'profile-pictures', 'shop-items', 'diary-skins')
- File names are generated using a combination of entity information and timestamps to ensure uniqueness
- File metadata is stored in registry entities

### Registry Entities

For each file type, there is a corresponding registry entity:

- `ProfilePictureRegistry`: For user profile pictures
- `ShopItemRegistry`: For shop item images
- `DiarySkinRegistry`: For diary skin preview images

Each registry entity contains:

```typescript
@Entity('diary_skin_registry')
export class DiarySkinRegistry extends AuditableBaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'diary_skin_id' })
  diarySkinId: string;

  @Column({ name: 'file_path' })
  filePath: string;

  @Column({ name: 'file_name' })
  fileName: string;

  @Column({ name: 'mime_type' })
  mimeType: string;

  @Column({ name: 'file_size' })
  fileSize: number;

  @Column({ name: 'user_id', nullable: true })
  userId: string;
}
```

### File Registry Service

The `FileRegistryService` provides methods for:

- Uploading files
- Registering files in the registry
- Retrieving file information
- Generating URLs for accessing files

### URL Generation

The `getFileUrlWithFallback` method provides a simplified way to generate URLs for files:

```typescript
// Get a URL for a file with automatic fallback
const fileUrl = await fileRegistryService.getFileUrlWithFallback(
  FileEntityType.DIARY_SKIN,
  skinId
);
```

This method:
1. First tries to find a registry entry for the entity
2. If found, generates a URL using the registry entry ID
3. If not found, looks up the entity and uses its file path to generate a direct URL
4. Returns null if no file exists

### Media Controller

The `MediaController` provides endpoints for serving files:

- `/media/profile-pictures/:userId`: Serves profile pictures
- `/media/shop-items/:itemId`: Serves shop item images
- `/media/diary-skins/:skinId`: Serves diary skin preview images

These endpoints:
- Retrieve the file information from the registry
- Set appropriate content type headers
- Serve the file directly to the client

## Performance Optimization

### Indexing

- Add indexes to frequently queried columns
- Add composite indexes for columns that are queried together
- Add unique indexes for unique constraints

```typescript
@Entity('users')
@Index('idx_users_email', ['email'])
@Index('idx_users_user_id', ['userId'])
export class User extends AuditableBaseEntity {
  // ...
}
```

### Query Optimization

- Use `SELECT` with specific columns instead of `SELECT *`
- Use pagination for large result sets
- Use eager loading to avoid N+1 query problems
- Use query caching for frequently accessed data

```typescript
async findByEmail(email: string): Promise<User> {
  return this.userRepository.findOne({
    where: { email },
    select: ['id', 'userId', 'email', 'isActive', 'isConfirmed', 'type'],
    cache: true
  });
}
```

### Connection Pooling

- Configure connection pooling for optimal performance
- Set appropriate pool size based on server resources

```typescript
// app.module.ts
@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT, 10),
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_DATABASE,
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: false,
      migrationsRun: true,
      migrations: [__dirname + '/migrations/**/*{.ts,.js}'],
      cli: {
        migrationsDir: 'src/migrations',
      },
      // Connection pool settings
      extra: {
        max: 20, // Maximum number of connections in the pool
        min: 5,  // Minimum number of connections in the pool
        idle: 10000, // Maximum time (ms) that a connection can be idle before being released
      }
    }),
  ],
})
export class AppModule {}
```

---

Following these conventions ensures a well-designed, efficient, and maintainable database layer for the application.
