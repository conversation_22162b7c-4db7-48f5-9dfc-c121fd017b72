# KCP Payment Gateway Testing Guide

This document provides comprehensive guidance for testing the KCP payment gateway integration in the HEC backend system.

## Overview

The payment testing suite covers all aspects of the KCP payment gateway integration:

- **Unit Tests**: Individual component testing with mocked dependencies
- **Integration Tests**: Component interaction and database operation testing
- **End-to-End Tests**: Complete payment flow testing
- **API Tests**: HTTP endpoint testing with authentication

## Test Structure

```
test/
├── utils/
│   ├── test-database.config.ts    # Test database configuration
│   └── test-helpers.ts            # Test utilities and helpers
├── payment-integration.test.ts    # Integration and E2E tests
├── run-payment-tests.ts          # Test runner script
└── .env.test                     # Test environment configuration

src/modules/payment/
├── services/
│   ├── kcp.service.spec.ts       # KCP service unit tests
│   ├── payment.service.spec.ts   # Payment service unit tests
│   └── kcp-config.service.spec.ts # Config service unit tests
└── payment.controller.spec.ts    # Controller unit tests
```

## Running Tests

### Quick Start

```bash
# Run all payment tests
npm run test:payment

# Or use the custom test runner
npx ts-node test/run-payment-tests.ts
```

### Individual Test Suites

```bash
# Unit tests only
npm run test -- --testPathPattern="payment.*spec.ts"

# Integration tests only
npm run test:e2e -- --testPathPattern="payment-integration"

# Specific service tests
npm run test -- src/modules/payment/services/kcp.service.spec.ts
```

### With Coverage

```bash
# Generate coverage report
npm run test:cov -- --testPathPattern="payment"
```

## Test Configuration

### Environment Setup

Create `.env.test` with test-specific configuration:

```env
NODE_ENV=test
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USER=test
DATABASE_PASSWORD=test
DATABASE_NAME=:memory:

# KCP Test Configuration
KCP_SITE_CD=test_site_cd
KCP_WEBHOOK_SECRET=test_webhook_secret
MOCK_PAYMENT_GATEWAY=true

# JWT Configuration
JWT_SECRET=test_jwt_secret_key_for_testing_only
```

### Database Configuration

Tests use SQLite in-memory database for fast, isolated testing:

```typescript
// test/utils/test-database.config.ts
export const getTestDatabaseConfig = (): DataSourceOptions => ({
  type: 'sqlite',
  database: ':memory:',
  entities: [__dirname + '/../../src/database/entities/*.entity{.ts,.js}'],
  synchronize: true,
  dropSchema: true,
  logging: false,
});
```

## Test Categories

### 1. Unit Tests

#### KCP Service Tests (`kcp.service.spec.ts`)
- ✅ Trade registration with KCP
- ✅ Payment processing simulation
- ✅ Payment URL generation
- ✅ Payment method code mapping
- ✅ Webhook signature validation
- ✅ Error handling for failed requests

#### Payment Service Tests (`payment.service.spec.ts`)
- ✅ Payment initiation workflow
- ✅ Transaction database operations
- ✅ User validation
- ✅ KCP service integration
- ✅ Webhook processing
- ✅ Transaction status updates
- ✅ Error handling and rollbacks

#### KCP Config Service Tests (`kcp-config.service.spec.ts`)
- ✅ Configuration loading
- ✅ Environment-specific settings
- ✅ Order check generation
- ✅ Webhook signature validation
- ✅ Default value handling

#### Payment Controller Tests (`payment.controller.spec.ts`)
- ✅ Payment initiation endpoint
- ✅ Payment status retrieval
- ✅ Webhook handling
- ✅ Refund processing
- ✅ Authentication validation
- ✅ Request/response formatting

### 2. Integration Tests

#### Payment Flow Tests (`payment-integration.test.ts`)
- ✅ Complete payment initiation flow
- ✅ Database transaction creation
- ✅ KCP service integration
- ✅ Payment status tracking
- ✅ Webhook processing
- ✅ Error scenarios

#### API Endpoint Tests
- ✅ POST `/payment/initiate` - Payment initiation
- ✅ GET `/payment/status/:id` - Status retrieval
- ✅ POST `/payment/webhook/kcp` - Webhook processing
- ✅ POST `/payment/refund` - Refund processing

### 3. Error Handling Tests

- ✅ Invalid payment data validation
- ✅ Unauthorized access attempts
- ✅ KCP service failures
- ✅ Database operation failures
- ✅ Network timeout scenarios
- ✅ Invalid webhook signatures

## Test Data Management

### Test Helpers

The test suite includes comprehensive helpers for:

```typescript
// Create test users
const testUser = await createTestUser(userRepository, {
  type: UserType.STUDENT,
  email: '<EMAIL>'
});

// Generate auth tokens
const authToken = generateTestToken(testUser, jwtService);

// Create test transactions
const transaction = await createTestPaymentTransaction(
  transactionRepository, 
  testUser
);

// Mock KCP responses
const mockResponse = mockKcpResponses.tradeRegSuccess;
```

### Database Cleanup

Tests automatically clean up data between runs:

```typescript
afterEach(async () => {
  await paymentTransactionRepository.clear();
  await paymentWebhookRepository.clear();
});
```

## Mocking Strategy

### External Services

KCP API calls are mocked to ensure:
- Fast test execution
- Predictable responses
- No external dependencies
- Cost-free testing

### Database Operations

- Unit tests use mocked repositories
- Integration tests use in-memory SQLite
- Automatic cleanup between tests

## Coverage Requirements

The payment module maintains high test coverage:

- **Unit Tests**: 90%+ coverage for all services
- **Integration Tests**: 100% coverage for critical payment flows
- **API Tests**: 100% coverage for all endpoints

## Continuous Integration

### GitHub Actions

```yaml
name: Payment Tests
on: [push, pull_request]
jobs:
  payment-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run payment tests
        run: npx ts-node test/run-payment-tests.ts
```

## Debugging Tests

### Common Issues

1. **Database Connection Errors**
   ```bash
   # Check test database configuration
   cat .env.test
   ```

2. **Mock Service Failures**
   ```typescript
   // Verify mock setup in beforeEach
   expect(mockKcpService.initiatePayment).toBeDefined();
   ```

3. **Authentication Errors**
   ```typescript
   // Check JWT token generation
   const token = generateTestToken(testUser, jwtService);
   expect(token).toBeDefined();
   ```

### Debug Mode

```bash
# Run tests with debug output
DEBUG=* npm run test -- --testPathPattern="payment"

# Run specific test with verbose output
npm run test -- --verbose src/modules/payment/services/kcp.service.spec.ts
```

## Best Practices

1. **Test Isolation**: Each test should be independent
2. **Descriptive Names**: Use clear, descriptive test names
3. **Arrange-Act-Assert**: Follow AAA pattern
4. **Mock External Dependencies**: Don't rely on external services
5. **Clean Up**: Always clean up test data
6. **Error Testing**: Test both success and failure scenarios

## Security Testing

The test suite includes security-focused tests:

- ✅ Webhook signature validation
- ✅ Authentication token verification
- ✅ Input sanitization
- ✅ SQL injection prevention
- ✅ XSS protection

## Performance Testing

Basic performance tests are included:

- ✅ Payment initiation response time
- ✅ Database query optimization
- ✅ Memory usage monitoring
- ✅ Concurrent request handling

## Troubleshooting

### Test Failures

1. Check environment configuration
2. Verify database connectivity
3. Ensure all dependencies are installed
4. Check for port conflicts
5. Review test logs for specific errors

### Getting Help

- Review test documentation
- Check existing test patterns
- Consult the testing convention guide
- Ask the development team for assistance
