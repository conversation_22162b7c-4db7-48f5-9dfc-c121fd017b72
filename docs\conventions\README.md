# HEC Backend Development Conventions

This document provides an overview of the development conventions used in the HEC backend project. These conventions ensure consistency, maintainability, and quality across the codebase.

## Core Conventions

The HEC backend follows these key conventions:

### 1. General Development Conventions

- **Naming**: camelCase for variables, PascalCase for classes, kebab-case for files
- **Code Organization**: Modular architecture with clear separation of concerns
- **Error Handling**: Consistent error responses with appropriate HTTP status codes
- **Documentation**: All public APIs and methods should be documented with JSDoc comments

[Read more about General Development Conventions](DEVELOPMENT_CONVENTION.md)

### 2. Database Conventions

- **Naming**: snake_case for database columns with custom TypeORM NamingStrategy
- **Audit Fields**: Automatic handling of createdAt/updatedAt, createdBy/updatedBy
- **Transactions**: Database transactions with rollback for operations involving multiple entities
- **Migrations**: All database schema changes should be done through migrations

[Read more about Database Conventions](DATABASE_CONVENTION.md)

### 3. API Response Conventions

- **Format**: All API responses follow a consistent format with success, message, and data fields
- **Pagination**: All list responses use a pagedlist DTO with items, totalCount, totalItems, itemsPerPage, currentPage, and totalPages
- **Error Responses**: Standardized error response format with error codes and messages

[Read more about API Response Conventions](API_RESPONSE_CONVENTION.md)

### 4. Authentication Conventions

- **JWT**: Authentication using JWT tokens with appropriate expiration
- **Role-Based Access Control**: Access control based on user roles
- **Password Security**: Secure password hashing and validation

[Read more about Authentication Conventions](AUTHENTICATION_CONVENTION.md)

### 5. Testing Conventions

- **Unit Tests**: All services should have unit tests with appropriate mocking
- **Integration Tests**: Key workflows should have integration tests
- **Test Coverage**: Aim for high test coverage, especially for critical paths

[Read more about Testing Conventions](TESTING_CONVENTION.md)

## Core Services

The following core services have their own conventions:

- [File Upload Service](FILE_UPLOAD_CONVENTION.md) - Guidelines for file uploads, storage, and serving
- [Audit Logging Service](AUDIT_LOGGING_CONVENTION.md) - Guidelines for tracking entity creation and modification

## Advanced Topics

For more advanced topics, please refer to:

- [API Versioning](API_VERSIONING_CONVENTION.md) - Guidelines for API versioning and evolution
- [Feature Flags](FEATURE_FLAG_CONVENTION.md) - Guidelines for controlled feature rollout
- [Stability Strategy](STABILITY_STRATEGY.md) - Comprehensive strategy for maintaining API stability

## Purpose & Benefits

Following these conventions ensures:

1. Consistent code style and structure
2. Standardized API responses
3. Secure authentication and authorization
4. Efficient database design and access
5. Comprehensive documentation
6. Maintainable and scalable codebase

## Contributing

These conventions are living documents that should evolve with the project. If you have suggestions for improving the conventions or notice inconsistencies, please discuss the proposed changes with the team before updating the documentation.
