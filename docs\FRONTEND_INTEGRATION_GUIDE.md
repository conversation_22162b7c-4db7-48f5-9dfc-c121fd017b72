# Frontend Integration Guide - KCP Payment Gateway

This document provides a comprehensive guide for integrating the KCP payment gateway with your NextJS frontend application.

## Overview

The frontend integration supports two main payment flows:
1. **Shop Item Purchase Flow** - For purchasing individual shop items
2. **Plan Subscription Flow** - For subscribing to premium plans

Both flows follow a similar pattern but have different endpoints and data structures.

## Architecture Overview

```
Frontend (NextJS) ↔ Backend API ↔ KCP Payment Gateway
     ↓                    ↓              ↓
User Interface ←→ Payment Service ←→ KCP Servers
```

## Payment Flow Types

### 1. Shop Item Purchase Flow

#### Sequence Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend API
    participant K as KCP Gateway

    U->>F: Add items to cart
    U->>F: Click "Checkout"
    F->>F: Show payment method selection
    U->>F: Select KCP payment method
    F->>B: POST /shop/cart/checkout
    Note over F,B: {paymentMethod: "kcp_card", returnUrl, cancelUrl}

    B->>B: Create temporary purchase
    B->>K: Initiate payment
    K-->>B: Return payment URL
    B-->>F: Return checkout response
    Note over B,F: {success: true, paymentUrl, transactionId}

    F->>F: Redirect to payment URL
    U->>K: Complete payment on KCP
    K->>B: Send webhook notification
    B->>B: Process payment confirmation
    K->>F: Redirect to return URL
    F->>B: GET /payment/status/{transactionId}
    B-->>F: Return payment status
    F->>F: Show success/failure page
```

### 2. Plan Subscription Flow

#### Sequence Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend API
    participant K as KCP Gateway

    U->>F: Browse plans
    U->>F: Select plan
    F->>F: Show payment method selection
    U->>F: Select KCP payment method
    F->>B: POST /plans/subscribe
    Note over F,B: {planId, paymentMethod: "kcp_card", returnUrl, cancelUrl}

    B->>B: Create temporary user plan
    B->>K: Initiate payment
    K-->>B: Return payment URL
    B-->>F: Return subscription response
    Note over B,F: {success: true, paymentUrl, transactionId}

    F->>F: Redirect to payment URL
    U->>K: Complete payment on KCP
    K->>B: Send webhook notification
    B->>B: Activate user plan
    K->>F: Redirect to return URL
    F->>B: GET /payment/status/{transactionId}
    B-->>F: Return payment status
    F->>F: Show success page & update user state
```

## Frontend Implementation

### 1. Payment Method Selection Component

```typescript
// components/PaymentMethodSelector.tsx
import React, { useState } from 'react';

interface PaymentMethodSelectorProps {
  onMethodSelect: (method: string) => void;
  selectedMethod: string;
}

export const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({
  onMethodSelect,
  selectedMethod
}) => {
  const paymentMethods = [
    { id: 'reward_points', name: 'Reward Points', icon: '🎯' },
    { id: 'kcp_card', name: 'Credit/Debit Card', icon: '💳' },
    { id: 'kcp_bank', name: 'Bank Transfer', icon: '🏦' },
    { id: 'kcp_mobile', name: 'Mobile Payment', icon: '📱' },
  ];

  return (
    <div className="payment-method-selector">
      <h3>Select Payment Method</h3>
      {paymentMethods.map((method) => (
        <div
          key={method.id}
          className={`payment-method ${selectedMethod === method.id ? 'selected' : ''}`}
          onClick={() => onMethodSelect(method.id)}
        >
          <span className="icon">{method.icon}</span>
          <span className="name">{method.name}</span>
        </div>
      ))}
    </div>
  );
};
```

### 2. Shop Checkout Integration

```typescript
// hooks/useShopCheckout.ts
import { useState } from 'react';
import { useRouter } from 'next/router';
import { apiClient } from '../lib/api-client';

interface CheckoutData {
  paymentMethod: string;
  useRewardPoints?: boolean;
  promoCode?: string;
  returnUrl?: string;
  cancelUrl?: string;
}

export const useShopCheckout = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const processCheckout = async (checkoutData: CheckoutData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.post('/shop/cart/checkout', {
        ...checkoutData,
        returnUrl: checkoutData.returnUrl || `${window.location.origin}/payment/success`,
        cancelUrl: checkoutData.cancelUrl || `${window.location.origin}/payment/cancel`,
      });

      if (response.data.success) {
        const { paymentUrl, paymentTransactionId } = response.data.data;

        if (paymentUrl) {
          // Store transaction ID for later verification
          localStorage.setItem('pendingTransactionId', paymentTransactionId);

          // Redirect to KCP payment page
          window.location.href = paymentUrl;
        } else {
          // Payment completed immediately (e.g., reward points)
          router.push('/shop/purchase-success');
        }
      } else {
        setError(response.data.message || 'Checkout failed');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'An error occurred during checkout');
    } finally {
      setLoading(false);
    }
  };

  return { processCheckout, loading, error };
};
```

### 3. Plan Subscription Integration

```typescript
// hooks/usePlanSubscription.ts
import { useState } from 'react';
import { useRouter } from 'next/router';
import { apiClient } from '../lib/api-client';

interface SubscriptionData {
  planId: string;
  paymentMethod: string;
  autoRenew?: boolean;
  returnUrl?: string;
  cancelUrl?: string;
}

export const usePlanSubscription = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const subscribeToPlan = async (subscriptionData: SubscriptionData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.post('/plans/subscribe', {
        ...subscriptionData,
        returnUrl: subscriptionData.returnUrl || `${window.location.origin}/payment/success`,
        cancelUrl: subscriptionData.cancelUrl || `${window.location.origin}/payment/cancel`,
      });

      if (response.data.success) {
        const { paymentUrl, paymentTransactionId, access_token } = response.data.data;

        if (paymentUrl) {
          // Store transaction ID and new token for later verification
          localStorage.setItem('pendingTransactionId', paymentTransactionId);
          if (access_token) {
            localStorage.setItem('pendingAccessToken', access_token);
          }

          // Redirect to KCP payment page
          window.location.href = paymentUrl;
        } else {
          // Free plan or immediate activation
          if (access_token) {
            localStorage.setItem('accessToken', access_token);
          }
          router.push('/plans/subscription-success');
        }
      } else {
        setError(response.data.message || 'Subscription failed');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'An error occurred during subscription');
    } finally {
      setLoading(false);
    }
  };

  return { subscribeToPlan, loading, error };
};
```

### 4. Payment Status Verification

```typescript
// hooks/usePaymentVerification.ts
import { useState, useEffect } from 'react';
import { apiClient } from '../lib/api-client';

interface PaymentStatus {
  transactionId: string;
  status: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  completedAt?: string;
  errorMessage?: string;
}

export const usePaymentVerification = (transactionId: string | null) => {
  const [status, setStatus] = useState<PaymentStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const verifyPayment = async (txnId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.get(`/payment/status/${txnId}`);

      if (response.data.success) {
        setStatus(response.data.data);
        return response.data.data;
      } else {
        setError(response.data.message || 'Failed to verify payment');
        return null;
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Payment verification failed');
      return null;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (transactionId) {
      verifyPayment(transactionId);
    }
  }, [transactionId]);

  return { status, loading, error, verifyPayment };
};
```

### 5. Payment Success/Failure Pages

```typescript
// pages/payment/success.tsx
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { usePaymentVerification } from '../../hooks/usePaymentVerification';

const PaymentSuccessPage: React.FC = () => {
  const router = useRouter();
  const [transactionId, setTransactionId] = useState<string | null>(null);
  const { status, loading, error } = usePaymentVerification(transactionId);

  useEffect(() => {
    // Get transaction ID from URL params or localStorage
    const txnId = router.query.transaction as string ||
                  localStorage.getItem('pendingTransactionId');

    if (txnId) {
      setTransactionId(txnId);
    } else {
      router.push('/');
    }
  }, [router]);

  useEffect(() => {
    if (status && status.status === 'completed') {
      // Payment successful - update user state
      const pendingToken = localStorage.getItem('pendingAccessToken');
      if (pendingToken) {
        localStorage.setItem('accessToken', pendingToken);
        localStorage.removeItem('pendingAccessToken');
      }

      // Clean up
      localStorage.removeItem('pendingTransactionId');

      // Redirect based on payment type
      setTimeout(() => {
        if (router.query.type === 'plan') {
          router.push('/plans/subscription-success');
        } else {
          router.push('/shop/purchase-success');
        }
      }, 3000);
    }
  }, [status, router]);

  if (loading) {
    return (
      <div className="payment-verification">
        <div className="spinner">Verifying payment...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="payment-error">
        <h2>Payment Verification Failed</h2>
        <p>{error}</p>
        <button onClick={() => router.push('/')}>Go Home</button>
      </div>
    );
  }

  if (status?.status === 'completed') {
    return (
      <div className="payment-success">
        <div className="success-icon">✅</div>
        <h2>Payment Successful!</h2>
        <p>Transaction ID: {status.transactionId}</p>
        <p>Amount: {status.amount} {status.currency}</p>
        <p>Redirecting...</p>
      </div>
    );
  }

  return (
    <div className="payment-processing">
      <div className="spinner">Processing payment...</div>
    </div>
  );
};

export default PaymentSuccessPage;
```

```typescript
// pages/payment/cancel.tsx
import React, { useEffect } from 'react';
import { useRouter } from 'next/router';

const PaymentCancelPage: React.FC = () => {
  const router = useRouter();

  useEffect(() => {
    // Clean up pending transaction data
    localStorage.removeItem('pendingTransactionId');
    localStorage.removeItem('pendingAccessToken');
  }, []);

  return (
    <div className="payment-cancel">
      <div className="cancel-icon">❌</div>
      <h2>Payment Cancelled</h2>
      <p>Your payment was cancelled. You can try again or choose a different payment method.</p>
      <div className="actions">
        <button onClick={() => router.back()}>Go Back</button>
        <button onClick={() => router.push('/')}>Go Home</button>
      </div>
    </div>
  );
};

export default PaymentCancelPage;
```

### 6. API Client Configuration

```typescript
// lib/api-client.ts
import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3012';

export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('accessToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);
```

## Error Handling Strategy

### 1. Payment Errors

```typescript
// utils/payment-errors.ts
export const getPaymentErrorMessage = (errorCode: string): string => {
  const errorMessages: Record<string, string> = {
    'PAYMENT_INIT_ERROR': 'Failed to initialize payment. Please try again.',
    'INSUFFICIENT_FUNDS': 'Insufficient funds. Please check your account balance.',
    'INVALID_CARD': 'Invalid card information. Please check your card details.',
    'PAYMENT_TIMEOUT': 'Payment timed out. Please try again.',
    'PAYMENT_CANCELLED': 'Payment was cancelled by user.',
    'NETWORK_ERROR': 'Network error. Please check your connection and try again.',
    'SERVER_ERROR': 'Server error. Please try again later.',
  };

  return errorMessages[errorCode] || 'An unexpected error occurred. Please try again.';
};
```

### 2. Retry Logic

```typescript
// utils/retry-logic.ts
export const retryPaymentVerification = async (
  transactionId: string,
  maxRetries: number = 3,
  delay: number = 2000
): Promise<any> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await apiClient.get(`/payment/status/${transactionId}`);
      if (response.data.success) {
        return response.data.data;
      }
    } catch (error) {
      if (attempt === maxRetries) {
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
};
```

## Environment Configuration

### Frontend Environment Variables

```env
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:3012
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3011
NEXT_PUBLIC_PAYMENT_TIMEOUT=300000
NEXT_PUBLIC_ENABLE_PAYMENT_DEBUG=true
```

## Testing Strategy

### 1. Unit Tests

```typescript
// __tests__/hooks/useShopCheckout.test.ts
import { renderHook, act } from '@testing-library/react-hooks';
import { useShopCheckout } from '../../hooks/useShopCheckout';

jest.mock('../../lib/api-client');

describe('useShopCheckout', () => {
  it('should process checkout successfully', async () => {
    const { result } = renderHook(() => useShopCheckout());

    await act(async () => {
      await result.current.processCheckout({
        paymentMethod: 'kcp_card',
        useRewardPoints: false,
      });
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
  });
});
```

### 2. Integration Tests

```typescript
// __tests__/integration/payment-flow.test.ts
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import PaymentPage from '../../pages/payment/index';

describe('Payment Flow Integration', () => {
  it('should complete payment flow successfully', async () => {
    render(<PaymentPage />);

    // Select payment method
    fireEvent.click(screen.getByText('Credit/Debit Card'));

    // Submit payment
    fireEvent.click(screen.getByText('Pay Now'));

    // Wait for redirect
    await waitFor(() => {
      expect(window.location.href).toContain('payment.kcp.co.kr');
    });
  });
});
```

## Performance Optimization

### 1. Code Splitting

```typescript
// Dynamic imports for payment components
const PaymentMethodSelector = dynamic(
  () => import('../components/PaymentMethodSelector'),
  { loading: () => <div>Loading payment methods...</div> }
);
```

### 2. Caching Strategy

```typescript
// utils/payment-cache.ts
export const cachePaymentMethods = (methods: any[]) => {
  sessionStorage.setItem('paymentMethods', JSON.stringify(methods));
};

export const getCachedPaymentMethods = (): any[] | null => {
  const cached = sessionStorage.getItem('paymentMethods');
  return cached ? JSON.parse(cached) : null;
};
```

## Security Considerations

### 1. Data Sanitization

```typescript
// utils/sanitize.ts
export const sanitizePaymentData = (data: any) => {
  // Remove sensitive fields before logging
  const { cardNumber, cvv, ...safeData } = data;
  return safeData;
};
```

### 2. HTTPS Enforcement

```typescript
// utils/security.ts
export const enforceHTTPS = () => {
  if (typeof window !== 'undefined' &&
      window.location.protocol !== 'https:' &&
      process.env.NODE_ENV === 'production') {
    window.location.href = window.location.href.replace('http:', 'https:');
  }
};
```

## Additional Sequence Diagrams

### 3. Error Handling Flow

#### Payment Failure Scenario

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend API
    participant K as KCP Gateway

    U->>F: Initiate payment
    F->>B: POST /shop/cart/checkout
    B->>K: Initiate payment
    K-->>B: Payment initiation failed
    B-->>F: Return error response
    Note over B,F: {success: false, message: "Payment failed"}

    F->>F: Show error message
    F->>F: Allow retry or method change

    alt User retries
        U->>F: Retry payment
        F->>B: POST /shop/cart/checkout (retry)
    else User changes method
        U->>F: Select different method
        F->>B: POST /shop/cart/checkout (new method)
    else User cancels
        U->>F: Cancel checkout
        F->>F: Return to cart
    end
```

### 4. Webhook Processing Flow

#### Real-time Payment Status Updates

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend API
    participant K as KCP Gateway
    participant W as WebSocket/SSE

    Note over U,K: User completes payment on KCP
    U->>K: Submit payment
    K->>K: Process payment

    par Webhook Processing
        K->>B: POST /payment/webhook/kcp
        Note over K,B: Payment completion notification
        B->>B: Verify webhook signature
        B->>B: Update payment status
        B->>B: Complete purchase/subscription
        B-->>K: Acknowledge webhook
    and Real-time Updates
        B->>W: Broadcast payment status
        W->>F: Send status update
        F->>F: Update UI in real-time
    end

    K->>F: Redirect to return URL
    F->>B: GET /payment/status/{transactionId}
    B-->>F: Return final status
    F->>F: Show success page
```

### 5. Mobile Payment Flow

#### Mobile-Optimized Payment Process

```mermaid
sequenceDiagram
    participant U as User (Mobile)
    participant F as Frontend (PWA)
    participant B as Backend API
    participant K as KCP Mobile
    participant A as Mobile App

    U->>F: Select mobile payment
    F->>B: POST /shop/cart/checkout
    Note over F,B: {paymentMethod: "kcp_mobile"}

    B->>K: Initiate mobile payment
    K-->>B: Return mobile payment URL
    B-->>F: Return payment response

    F->>F: Detect mobile environment
    alt Native App Available
        F->>A: Launch payment app
        A->>K: Process payment
        K->>A: Payment result
        A->>F: Return to web app
    else Web-based Mobile Payment
        F->>K: Redirect to mobile web
        U->>K: Complete payment
        K->>F: Redirect back
    end

    F->>B: Verify payment status
    B-->>F: Return status
    F->>F: Show result
```

### 6. Subscription Renewal Flow

#### Automatic Plan Renewal Process

```mermaid
sequenceDiagram
    participant S as System Scheduler
    participant B as Backend API
    participant K as KCP Gateway
    participant F as Frontend
    participant U as User

    S->>B: Check expiring subscriptions
    B->>B: Find auto-renewal plans

    loop For each renewal
        B->>K: Process renewal payment
        alt Payment Successful
            K-->>B: Payment confirmed
            B->>B: Extend subscription
            B->>B: Send renewal notification
        else Payment Failed
            K-->>B: Payment failed
            B->>B: Mark for retry
            B->>B: Notify user of failure
        end
    end

    Note over F,U: User receives notification
    U->>F: Check subscription status
    F->>B: GET /plans/user-plans
    B-->>F: Return updated plans
    F->>F: Update subscription UI
```

This comprehensive frontend integration guide provides everything needed to implement the KCP payment gateway on the frontend, including sequence diagrams, code examples, error handling, and testing strategies.
