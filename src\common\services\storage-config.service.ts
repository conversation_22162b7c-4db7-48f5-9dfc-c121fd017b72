import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  StorageProvider,
  StorageDefaults,
  S3StorageClass,
  S3ServerSideEncryption,
  S3ACL
} from '../enums/storage.enum';

/**
 * AWS S3 configuration interface
 */
export interface S3Config {
  region: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
  bucketRegion: string;
  cloudFrontDomain?: string;
  acl: S3ACL;
  storageClass: S3StorageClass;
  serverSideEncryption: S3ServerSideEncryption;
  presignedUrlExpiry: number;
}

@Injectable()
export class StorageConfigService {
  constructor(private readonly configService: ConfigService) {}

  /**
   * Get storage provider from environment
   */
  getStorageProvider(): StorageProvider {
    const provider = this.configService.get<string>('STORAGE_PROVIDER', StorageDefaults.STORAGE_PROVIDER);

    if (!Object.values(StorageProvider).includes(provider as StorageProvider)) {
      throw new Error(`Invalid storage provider: ${provider}. Must be one of: ${Object.values(StorageProvider).join(', ')}`);
    }

    return provider as StorageProvider;
  }

  /**
   * Check if current provider is S3
   */
  isS3Provider(): boolean {
    return this.getStorageProvider() === StorageProvider.S3;
  }

  /**
   * Check if current provider is local
   */
  isLocalProvider(): boolean {
    return this.getStorageProvider() === StorageProvider.LOCAL;
  }

  /**
   * Get AWS S3 configuration
   */
  getS3Config(): S3Config {
    const region = this.configService.get<string>('AWS_REGION');
    const accessKeyId = this.configService.get<string>('AWS_ACCESS_KEY_ID');
    const secretAccessKey = this.configService.get<string>('AWS_SECRET_ACCESS_KEY');
    const bucketName = this.configService.get<string>('AWS_S3_BUCKET_NAME');

    if (!region || !accessKeyId || !secretAccessKey || !bucketName) {
      throw new Error('Missing required AWS S3 configuration. Please set AWS_REGION, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, and AWS_S3_BUCKET_NAME');
    }

    return {
      region,
      accessKeyId,
      secretAccessKey,
      bucketName,
      bucketRegion: this.configService.get<string>('AWS_S3_BUCKET_REGION', region),
      cloudFrontDomain: this.configService.get<string>('AWS_CLOUDFRONT_DOMAIN'),
      acl: this.getS3ACL(),
      storageClass: this.getS3StorageClass(),
      serverSideEncryption: this.getS3ServerSideEncryption(),
      presignedUrlExpiry: this.getS3PresignedUrlExpiry()
    };
  }

  /**
   * Check if S3 is configured
   */
  isS3Configured(): boolean {
    try {
      this.getS3Config();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get S3 ACL setting
   */
  private getS3ACL(): S3ACL {
    const acl = this.configService.get<string>('AWS_S3_ACL', StorageDefaults.AWS_S3_ACL);

    if (!Object.values(S3ACL).includes(acl as S3ACL)) {
      throw new Error(`Invalid S3 ACL: ${acl}. Must be one of: ${Object.values(S3ACL).join(', ')}`);
    }

    return acl as S3ACL;
  }

  /**
   * Get S3 storage class
   */
  private getS3StorageClass(): S3StorageClass {
    const storageClass = this.configService.get<string>('AWS_S3_STORAGE_CLASS', StorageDefaults.AWS_S3_STORAGE_CLASS);

    if (!Object.values(S3StorageClass).includes(storageClass as S3StorageClass)) {
      throw new Error(`Invalid S3 storage class: ${storageClass}. Must be one of: ${Object.values(S3StorageClass).join(', ')}`);
    }

    return storageClass as S3StorageClass;
  }

  /**
   * Get S3 server-side encryption
   */
  private getS3ServerSideEncryption(): S3ServerSideEncryption {
    const encryption = this.configService.get<string>('AWS_S3_SERVER_SIDE_ENCRYPTION', StorageDefaults.AWS_S3_SERVER_SIDE_ENCRYPTION);

    if (!Object.values(S3ServerSideEncryption).includes(encryption as S3ServerSideEncryption)) {
      throw new Error(`Invalid S3 server-side encryption: ${encryption}. Must be one of: ${Object.values(S3ServerSideEncryption).join(', ')}`);
    }

    return encryption as S3ServerSideEncryption;
  }

  /**
   * Get S3 presigned URL expiry
   */
  private getS3PresignedUrlExpiry(): number {
    return parseInt(
      this.configService.get<string>('AWS_S3_PRESIGNED_URL_EXPIRY', StorageDefaults.AWS_S3_PRESIGNED_URL_EXPIRY.toString()),
      10
    );
  }

}
