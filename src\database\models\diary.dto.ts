import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsDate, IsOptional, IsBoolean, IsUUID, IsEnum, IsNumber, Min, Max, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';
import { DiaryEntryStatus } from '../entities/diary-entry.entity';
import { DiaryEntrySettingsResponseDto } from './diary-settings.dto';
import { DiaryCorrectionResponseDto } from './diary-correction.dto';

// Request DTOs
/**
 * DTO for creating a new diary entry with minimal required fields
 * @example
 * {
 *   "entryDate": "2023-07-25",
 *   "title": "My First Diary Entry",
 *   "content": "Today I learned about...",
 *   "skinId": "123e4567-e89b-12d3-a456-************",
 *   "backgroundColor": "#f5f5f5",
 *   "isPrivate": false,
 *   "settingsTemplateId": "123e4567-e89b-12d3-a456-************"
 * }
 */
export class CreateDiaryEntryDto {
  @ApiProperty({ example: '2023-07-25', description: 'Date of the diary entry' })
  @IsNotEmpty()
  @IsDateString()
  entryDate: string;

  @ApiProperty({ example: 'My First Diary Entry', description: 'Title of the diary entry', required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ example: 'Today I learned about...', description: 'Content of the diary entry', required: false })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'ID of the skin to use for this entry', required: false })
  @IsOptional()
  @IsUUID()
  skinId?: string;

  @ApiProperty({ example: '#f5f5f5', description: 'Background color for the diary entry', required: false })
  @IsOptional()
  @IsString()
  backgroundColor?: string;

  @ApiProperty({ example: false, description: 'Whether the entry is private', required: false })
  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'ID of the diary settings template',
    required: false
  })
  @IsOptional()
  @IsUUID()
  settingsTemplateId?: string;
}

/**
 * DTO for updating a diary entry
 * Note: The entry date cannot be modified as each entry is strictly bound to a specific date
 * @example
 * {
 *   "title": "Updated Title",
 *   "content": "Updated content..."
 * }
 */
export class UpdateDiaryEntryDto {
  @ApiProperty({ example: 'Updated Title', description: 'Updated title of the diary entry', required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ example: 'Updated content...', description: 'Updated content of the diary entry', required: false })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', required: false })
  @IsOptional()
  @IsUUID()
  skinId?: string;

  @ApiProperty({ example: '#f5f5f5', required: false })
  @IsOptional()
  @IsString()
  backgroundColor?: string;

  @ApiProperty({ example: false, required: false })
  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'ID of the diary settings template',
    required: false
  })
  @IsOptional()
  @IsUUID()
  settingsTemplateId?: string;
}

/**
 * DTO for submitting a diary entry for review with all required properties
 * @example
 * {
 *   "title": "My Completed Diary Entry",
 *   "content": "This is the full content of my diary entry...",
 *   "settingsTemplateId": "123e4567-e89b-12d3-a456-************"
 * }
 */
export class SubmitDiaryEntryDto {
  @ApiProperty({ example: 'My Completed Diary Entry', description: 'Updated title of the diary entry' })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ example: 'This is the full content of my diary entry...', description: 'Updated content of the diary entry' })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'ID of the diary settings template. Required if the entry does not already have settings.'
  })
  @IsNotEmpty()
  @IsUUID()
  settingsTemplateId: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'ID of the skin to use for this entry', required: false })
  @IsOptional()
  @IsUUID()
  skinId?: string;

  @ApiProperty({ example: '#f5f5f5', description: 'Background color for the diary entry', required: false })
  @IsOptional()
  @IsString()
  backgroundColor?: string;

  @ApiProperty({ example: false, description: 'Whether the entry should be private', required: false })
  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;
}

/**
 * DTO for creating feedback on a diary entry
 * @example
 * {
 *   "feedback": "Great work on your diary entry! I particularly liked your analysis of the topic.",
 *   "rating": 5,
 *   "award": "Gold Star",
 *   "score": 95
 * }
 */
export class CreateDiaryFeedbackDto {
  @ApiProperty({
    example: 'Great work on your diary entry! I particularly liked your analysis of the topic.',
    description: 'Feedback text for the student'
  })
  @IsNotEmpty()
  @IsString()
  feedback: string;

  @ApiProperty({
    example: 5,
    description: 'Rating from 1-5 stars'
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  @Max(5)
  rating: number;

  @ApiProperty({
    example: 'Gold Star',
    description: 'Optional award to give to the student',
    required: false
  })
  @IsOptional()
  @IsString()
  award?: string;

  @ApiProperty({
    example: 95,
    description: 'Score for the diary entry (0-100)',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  score?: number;
}

/**
 * DTO for sharing a diary entry
 * @example
 * {
 *   "entryId": "123e4567-e89b-12d3-a456-************",
 *   "expiryDate": "2023-08-25T00:00:00.000Z"
 * }
 */
export class ShareDiaryEntryDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'UUID of the diary entry to share'
  })
  @IsNotEmpty()
  @IsUUID()
  entryId: string;

  @ApiProperty({
    example: '2023-08-25T00:00:00.000Z',
    description: 'Optional expiry date for the share link',
    required: false
  })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;
}

export class PickEntryForReviewDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************' })
  @IsNotEmpty()
  @IsUUID()
  entryId: string;
}

// Response DTOs
export class DiaryDetailsDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty({ required: false })
  userName?: string;

  @ApiProperty()
  defaultSkinId: string;

  @ApiProperty({ required: false })
  totalEntries?: number;

  @ApiProperty({ required: false })
  reviewedEntries?: number;

  @ApiProperty({ required: false })
  averageScore?: number;

  @ApiProperty({ required: false, description: 'URL of the diary cover photo' })
  coverPhotoUrl?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class DiarySkinResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty({
    required: false,
    description: 'Registry URL to access the preview image',
    example: 'http://example.com/media/registry/123e4567-e89b-12d3-a456-************'
  })
  previewImagePath?: string;

  @ApiProperty({ default: true })
  isActive: boolean;

  @ApiProperty({ default: true })
  isGlobal: boolean;

  @ApiProperty({ required: false })
  studentId?: string;

  @ApiProperty({ required: false })
  createdById?: string;

  @ApiProperty()
  templateContent: string;

  @ApiProperty({
    description: 'Whether this skin is used in any diary entry or as anyone\'s default skin',
    default: false,
    required: false
  })
  isUsedIn?: boolean;

  @ApiProperty({
    description: 'Whether this admin skin is free (no purchase required)',
    default: true,
    required: false
  })
  isFree?: boolean;

  @ApiProperty({
    description: 'Whether this admin skin has been purchased by the current student',
    default: false,
    required: false
  })
  isPurchased?: boolean;

  @ApiProperty({
    description: 'Whether this skin is the user default diary skin',
    default: false,
    required: false
  })
  isUserDefaultDiary?: boolean;

  @ApiProperty({
    description: 'Whether this skin is the user default novel skin',
    default: false,
    required: false
  })
  isUserDefaultNovel?: boolean;
}

export class DiaryFeedbackResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  tutorId: string;

  @ApiProperty()
  tutorName: string;

  @ApiProperty()
  feedback: string;

  @ApiProperty()
  rating: number;

  @ApiProperty({ required: false })
  award?: string;

  @ApiProperty()
  createdAt: Date;
}

export class DiaryEntryResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  content: string;

  @ApiProperty()
  entryDate: Date;

  @ApiProperty()
  status: DiaryEntryStatus;

  @ApiProperty({ required: false })
  backgroundColor?: string;

  @ApiProperty()
  isPrivate: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ type: DiarySkinResponseDto })
  skin: DiarySkinResponseDto;

  @ApiProperty({ required: false })
  score?: number;

  @ApiProperty({ required: false })
  evaluatedAt?: Date;

  @ApiProperty({ type: DiaryDetailsDto, required: false })
  diary?: DiaryDetailsDto;

  @ApiProperty({ required: false })
  evaluatedBy?: string;

  @ApiProperty({ type: [DiaryFeedbackResponseDto], required: false })
  feedbacks?: DiaryFeedbackResponseDto[];

  @ApiProperty({
    description: 'Whether the tutor greeting has been set by the student',
    required: false,
    default: false
  })
  hasGreeting?: boolean;

  @ApiProperty({ type: DiaryEntrySettingsResponseDto, required: false })
  settings?: DiaryEntrySettingsResponseDto;

  @ApiProperty({ type: DiaryCorrectionResponseDto, required: false })
  correction?: DiaryCorrectionResponseDto;

  @ApiProperty({ required: false })
  thanksMessage?: string;
  @ApiProperty({ description: 'Number of likes this entry has received', example: 5 })
  likeCount: number;

  @ApiProperty({ description: 'Whether the current user has liked this entry', example: true })
  hasLiked: boolean;

  @ApiProperty({
    description: 'URL for sharing this diary entry',
    required: false,
    example: 'https://example.com/shared/diary/abc123'
  })
  shareUrl?: string;

  @ApiProperty({
    description: 'URL for the QR code image that links to this diary entry',
    required: false,
    example: 'https://example.com/media/diary-qr/abc123.png'
  })
  qrCodeUrl?: string;
}

export class DiaryShareResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  shareToken: string;

  @ApiProperty()
  shareUrl: string;

  @ApiProperty({ required: false })
  qrCodeUrl?: string;

  @ApiProperty({ required: false })
  expiryDate?: Date;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  createdAt: Date;
}

export class PendingReviewEntryDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  entryDate: Date;

  @ApiProperty()
  studentName: string;

  @ApiProperty()
  studentId: string;

  @ApiProperty()
  submittedAt: Date;

  @ApiProperty({ enum: DiaryEntryStatus, enumName: 'DiaryEntryStatus' })
  status: DiaryEntryStatus;

  @ApiProperty({ description: 'Whether the entry has been reviewed by the current tutor' })
  reviewedByCurrentTutor: boolean;

  @ApiProperty({ description: 'Whether the entry is being reviewed by another tutor' })
  underReviewByOtherTutor: boolean;

  @ApiProperty({ description: 'Name of the tutor who reviewed the entry', required: false })
  reviewedByTutorName?: string;

  @ApiProperty({ description: 'Score given to the entry', required: false })
  score?: number;

  @ApiProperty({ description: 'Date when the entry was evaluated', required: false })
  evaluatedAt?: Date;

  @ApiProperty({ description: 'Module title from the diary settings template', required: false })
  moduleTitle?: string;

  @ApiProperty({ description: 'Module level from the diary settings template', required: false })
  moduleLevel?: number;

  @ApiProperty({ description: 'Word limit from the diary settings template', required: false })
  wordLimit?: number;

  @ApiProperty({ description: 'ID of the settings template', required: false })
  settingsTemplateId?: string;
}

export class TutorProfileDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  profilePicture?: string;

  @ApiProperty({ required: false })
  bio?: string;
}

export class StudentTutorListResponseDto {
  @ApiProperty({ type: [TutorProfileDto] })
  tutors: TutorProfileDto[];
}

export class DiaryAwardSummaryDto {
  @ApiProperty()
  awardName: string;

  @ApiProperty()
  count: number;
}

export class DiaryAwardsResponseDto {
  @ApiProperty({ type: [DiaryAwardSummaryDto] })
  awards: DiaryAwardSummaryDto[];

  @ApiProperty()
  totalAwards: number;
}

export class DiaryTopScorersDto {
  @ApiProperty()
  userId: string;

  @ApiProperty()
  userName: string;

  @ApiProperty({ required: false })
  profilePicture?: string;

  @ApiProperty()
  totalScore: number;

  @ApiProperty()
  entriesCount: number;

  @ApiProperty()
  averageScore: number;
}

export class DiaryTopScorersResponseDto {
  @ApiProperty({ type: [DiaryTopScorersDto] })
  topScorers: DiaryTopScorersDto[];

  @ApiProperty()
  period: string;

  @ApiProperty()
  startDate: Date;

  @ApiProperty()
  endDate: Date;
}

export class DiaryPeriodAwardDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  userName: string;

  @ApiProperty()
  period: string;

  @ApiProperty()
  periodStartDate: Date;

  @ApiProperty()
  periodEndDate: Date;

  @ApiProperty()
  totalScore: number;

  @ApiProperty({ required: false })
  awardTitle?: string;

  @ApiProperty({ required: false })
  awardDescription?: string;

  @ApiProperty()
  createdAt: Date;
}

export class DiaryPeriodAwardsResponseDto {
  @ApiProperty({ type: [DiaryPeriodAwardDto] })
  awards: DiaryPeriodAwardDto[];
}

/**
 * Filter parameters for diary entries
 * @example
 * {
 *   "date": "2023-07-25",
 *   "subject": "Math",
 *   "status": "reviewed"
 * }
 */
export class DiaryEntryFilterDto {
  @ApiProperty({
    required: false,
    example: '2023-07-25',
    description: 'Filter entries by specific date (YYYY-MM-DD)',
    type: String
  })
  @IsOptional()
  @IsDateString()
  @Type(() => String)
  date?: string;

  @ApiProperty({
    required: false,
    example: 'Math',
    description: 'Filter by subject or title of diary entries',
    type: String
  })
  @IsOptional()
  @IsString()
  subject?: string;

  @ApiProperty({
    required: false,
    enum: DiaryEntryStatus,
    enumName: 'DiaryEntryStatus',
    description: 'Filter by entry status (draft, submitted, under_review, reviewed)',
    example: 'reviewed'
  })
  @IsOptional()
  @IsEnum(DiaryEntryStatus)
  @Type(() => String)
  status?: DiaryEntryStatus;
}

/**
 * DTO for updating the tutor greeting message
 * @example
 * {
 *   "greeting": "Hello tutor! I'm excited to share my diary entries with you."
 * }
 */
export class UpdateTutorGreetingDto {
  @ApiProperty({
    example: 'Hello tutor! I\'m excited to share my diary entries with you.',
    description: 'Greeting message for the tutor'
  })
  @IsNotEmpty({ message: 'Greeting message is required' })
  @IsString({ message: 'Greeting message must be a string' })
  greeting: string;
}

export class CreateDiarySkinDto {
  @ApiProperty({ example: 'Modern Blue', description: 'Name of the diary skin' })
  @IsNotEmpty({ message: 'Skin name is required' })
  @IsString({ message: 'Skin name must be a string' })
  name: string;

  @ApiProperty({ example: 'A modern blue theme with clean typography', description: 'Description of the diary skin' })
  @IsNotEmpty({ message: 'Description is required' })
  @IsString({ message: 'Description must be a string' })
  description: string;

  @ApiProperty({ example: '<div class="diary-template">{{content}}</div>', description: 'HTML template content for the skin' })
  @IsNotEmpty({ message: 'Template content is required' })
  @IsString({ message: 'Template content must be a string' })
  templateContent: string;

  @ApiProperty({ example: true, default: true, description: 'Whether the skin is active and available for use' })
  @IsOptional()
  @IsBoolean({ message: 'isActive must be a boolean value' })
  isActive?: boolean;
}

export class UpdateDiarySkinDto {
  @ApiProperty({ example: 'Modern Blue Updated', description: 'Name of the diary skin', required: false })
  @IsOptional()
  @IsString({ message: 'Skin name must be a string' })
  name?: string;

  @ApiProperty({ example: 'An updated modern blue theme with clean typography', description: 'Description of the diary skin', required: false })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  @ApiProperty({ example: '<div class="diary-template-updated">{{content}}</div>', description: 'HTML template content for the skin', required: false })
  @IsOptional()
  @IsString({ message: 'Template content must be a string' })
  templateContent?: string;

  @ApiProperty({ example: false, description: 'Whether the skin is active and available for use', required: false })
  @IsOptional()
  @IsBoolean({ message: 'isActive must be a boolean value' })
  isActive?: boolean;
}

export class CreateStudentDiarySkinDto {
  @ApiProperty({ example: 'My Custom Skin', description: 'Name of the student diary skin' })
  @IsNotEmpty({ message: 'Skin name is required' })
  @IsString({ message: 'Skin name must be a string' })
  name: string;

  @ApiProperty({ example: 'My personal theme with custom colors', description: 'Description of the student diary skin' })
  @IsNotEmpty({ message: 'Description is required' })
  @IsString({ message: 'Description must be a string' })
  description: string;

  @ApiProperty({ example: '<div class="student-diary-template">{{content}}</div>', description: 'HTML template content for the skin' })
  @IsNotEmpty({ message: 'Template content is required' })
  @IsString({ message: 'Template content must be a string' })
  templateContent: string;

  @ApiProperty({ example: true, default: true, description: 'Whether the skin is active and available for use' })
  @IsOptional()
  @IsBoolean({ message: 'isActive must be a boolean value' })
  isActive?: boolean;
}

export class UpdateStudentDiarySkinDto {
  @ApiProperty({ example: 'My Updated Custom Skin', description: 'Name of the student diary skin', required: false })
  @IsOptional()
  @IsString({ message: 'Skin name must be a string' })
  name?: string;

  @ApiProperty({ example: 'My updated personal theme with custom colors', description: 'Description of the student diary skin', required: false })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  @ApiProperty({ example: '<div class="updated-student-diary-template">{{content}}</div>', description: 'HTML template content for the skin', required: false })
  @IsOptional()
  @IsString({ message: 'Template content must be a string' })
  templateContent?: string;

  @ApiProperty({ example: false, description: 'Whether the skin is active and available for use', required: false })
  @IsOptional()
  @IsBoolean({ message: 'isActive must be a boolean value' })
  isActive?: boolean;
}

/**
 * DTO for updating a student's default diary skin
 */
export class UpdateDefaultSkinDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'ID of the skin to set as default' })
  @IsNotEmpty({ message: 'Skin ID is required' })
  @IsUUID(undefined, { message: 'Skin ID must be a valid UUID' })
  skinId: string;
}

/**
 * DTO for updating today's diary entry skin
 */
export class UpdateTodaysDiarySkinDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'ID of the skin to apply to today\'s diary entry' })
  @IsNotEmpty({ message: 'Skin ID is required' })
  @IsUUID(undefined, { message: 'Skin ID must be a valid UUID' })
  skinId: string;
}

// Diary Cover Photo DTOs
export class DiaryCoverPhotoResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  diaryId: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  fileName: string;

  @ApiProperty()
  mimeType: string;

  @ApiProperty()
  fileSize: number;

  @ApiProperty()
  formattedFileSize: string;

  @ApiProperty()
  storageProvider: string;

  @ApiProperty()
  fileUrl: string;

  @ApiProperty()
  isImage: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
