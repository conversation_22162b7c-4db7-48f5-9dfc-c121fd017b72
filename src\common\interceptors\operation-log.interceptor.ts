import { Injectable, NestInterceptor, ExecutionContext, CallH<PERSON>ler, Inject } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import LoggerService from '../services/logger.service';

@Injectable()
export class OperationLogInterceptor implements NestInterceptor {
  constructor(@Inject(LoggerService) private readonly logger: LoggerService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const now = Date.now();
    const request = context.switchToHttp().getRequest();
    const { method, url, user, body, headers, ip } = request;
    const userAgent = headers['user-agent'] || 'unknown';
    const requestId = this.generateRequestId();

    // Add request ID to the request object for tracking
    request.requestId = requestId;

    // Log the incoming request with more details
    this.logger.request(method, url, user?.id, {
      requestId,
      ip,
      userAgent,
      body: this.sanitizeRequestBody(body)
    });

    return next.handle().pipe(
      tap({
        next: (data) => {
          const responseTime = Date.now() - now;
          // Log the successful response with request ID for correlation
          this.logger.info(`[REQ-ID:${requestId}] Response success: ${method} ${url} - ${responseTime}ms - User: ${user?.id || 'anonymous'}`, {
            status: 200,
            responseTime,
            // Include a sanitized version of the response data for debugging
            responseData: this.sanitizeResponseData(data)
          });
        },
        error: (error) => {
          const responseTime = Date.now() - now;
          const status = error.status || 500;

          // Generate a unique error reference ID
          const errorRefId = `ERR-${Date.now().toString(36)}-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`.toUpperCase();

          // Add error reference ID to the error object for the exception filters
          error.refId = errorRefId;

          // Log the error response with detailed information
          this.logger.error(
            `[REQ-ID:${requestId}] [ERROR-REF:${errorRefId}] Request failed: ${method} ${url} - ${status} - ${error.message}`,
            error.stack,
            JSON.stringify({
              method,
              url,
              userId: user?.id,
              ip,
              userAgent,
              requestBody: this.sanitizeRequestBody(body),
              responseTime
            })
          );
        },
      })
    );
  }

  /**
   * Generate a unique request ID for tracking
   */
  private generateRequestId(): string {
    return `REQ-${Date.now().toString(36)}-${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`.toUpperCase();
  }

  private sanitizeRequestBody(body: any): any {
    if (!body) return {};

    const sanitized = { ...body };

    // Remove sensitive information
    if (sanitized.password) sanitized.password = '[REDACTED]';
    if (sanitized.confirmPassword) sanitized.confirmPassword = '[REDACTED]';
    if (sanitized.token) sanitized.token = '[REDACTED]';
    if (sanitized.accessToken) sanitized.accessToken = '[REDACTED]';
    if (sanitized.refreshToken) sanitized.refreshToken = '[REDACTED]';

    return sanitized;
  }

  /**
   * Sanitize response data to remove sensitive information and limit size
   */
  private sanitizeResponseData(data: any): any {
    if (!data) return {};

    // For primitive types, return as is
    if (typeof data !== 'object') return data;

    // Handle arrays
    if (Array.isArray(data)) {
      // If array is too large, truncate it
      if (data.length > 10) {
        return {
          type: 'array',
          length: data.length,
          sample: data.slice(0, 3).map(item => this.sanitizeResponseData(item)),
          truncated: true
        };
      }
      return data.map(item => this.sanitizeResponseData(item));
    }

    // Handle objects
    try {
      const sanitized = { ...data };

      // Remove sensitive fields
      if (sanitized.password) sanitized.password = '[REDACTED]';
      if (sanitized.token) sanitized.token = '[REDACTED]';
      if (sanitized.access_token) sanitized.access_token = '[REDACTED]';
      if (sanitized.refresh_token) sanitized.refresh_token = '[REDACTED]';

      // Convert to string and check size
      const jsonString = JSON.stringify(sanitized);
      if (jsonString.length > 1000) {
        // If too large, return a summary
        return {
          type: 'object',
          keys: Object.keys(sanitized),
          size: jsonString.length,
          truncated: true
        };
      }

      return sanitized;
    } catch (error) {
      return { type: 'object', error: 'Failed to sanitize response data' };
    }
  }
}
