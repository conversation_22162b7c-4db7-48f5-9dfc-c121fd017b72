# File Upload and Registry Convention

This document provides detailed guidelines for implementing and using the file upload and registry system in the HEC Backend project.

> **Note on Sequence Diagrams**: This document includes sequence diagrams created with Mermaid. The diagrams are provided as image links that will display in most Markdown viewers. If you want to view or edit the diagrams interactively, click on any diagram to open it in the Mermaid Live Editor.

## Table of Contents

1. [Overview](#overview)
2. [File Upload Strategy](#file-upload-strategy)
3. [Registry Entities](#registry-entities)
4. [File Registry Service](#file-registry-service)
5. [URL Generation](#url-generation)
6. [Media Controller](#media-controller)
7. [Fallback Mechanisms](#fallback-mechanisms)
8. [Error Handling](#error-handling)
9. [Transaction Management](#transaction-management)
10. [Usage Examples](#usage-examples)
11. [Best Practices](#best-practices)

## Overview

The file upload and registry system provides a standardized way to handle file uploads, storage, and retrieval across the application. It ensures that files are properly organized, securely stored, and efficiently served to clients.

### File Upload Process

The file upload process follows this sequence:

1. Client uploads a file with entity data to the Controller
2. Controller calls FileRegistryService.uploadFile() with entity type, file, name, and options
3. FileRegistryService validates the file and generates a file path
4. File is saved to disk in the appropriate directory
5. Entity is created in the database
6. Registry entry is created with file metadata
7. File metadata is returned to the Controller
8. Success response with file URL is returned to the Client

**File Upload Sequence:**

![File Upload Process](https://mermaid.ink/img/pako:eNqNkk1vgzAMhv9KlHMrFUg_oBdOk3bYpE3qdtplcsBtIkKCEtOtVcV_XwJlWy-b5pPtx6_tN5lRZVmCJJJqbLTbKYsGXzXCGNyBRmvhHlbgUFvYgFYNWGEcPMEWDLzAK2wUNPAEL6BhDc_wDDuHVsEDvMGTQyPgHt7hCVUDK3iDDWoHW1jBu0MtYQlbUOjgHu7gQ6GBJaxhj9bBEu7gUzlYwD18oXKwgHtQzqKBBWxAO4cSFrBD7WDhv3-Hb6gcLGEJX6gdLOAOvpVzfgFr2KJ1MIcFfKB2MIcVKGfRwBxWcHDo_Pf38KkcnMEcvtE4OIMZHJzz3-ewgb1D57-_hW_l4BRmcEDj4BRmcHTOf38KG9g5dP77G_hRDk5gCkfUDk5gCr_O-e9PYAt758_5H_ipHBzDBI6oHBzDBP6c89-P4QD_NZzAGDRqB8cwBuOcPz-GMRwcnMEYftE4OIUxWOe8P4YR_Pz7fwFtK-Wd?type=png)](https://mermaid.live/edit#pako:eNqNkk1vgzAMhv9KlHMrFUg_oBdOk3bYpE3qdtplcsBtIkKCEtOtVcV_XwJlWy-b5pPtx6_tN5lRZVmCJJJqbLTbKYsGXzXCGNyBRmvhHlbgUFvYgFYNWGEcPMEWDLzAK2wUNPAEL6BhDc_wDDuHVsEDvMGTQyPgHt7hCVUDK3iDDWoHW1jBu0MtYQlbUOjgHu7gQ6GBJaxhj9bBEu7gUzlYwD18oXKwgHtQzqKBBWxAO4cSFrBD7WDhv3-Hb6gcLGEJX6gdLOAOvpVzfgFr2KJ1MIcFfKB2MIcVKGfRwBxWcHDo_Pf38KkcnMEcvtE4OIMZHJzz3-ewgb1D57-_hW_l4BRmcEDj4BRmcHTOf38KG9g5dP77G_hRDk5gCkfUDk5gCr_O-e9PYAt758_5H_ipHBzDBI6oHBzDBP6c89-P4QD_NZzAGDRqB8cwBuOcPz-GMRwcnMEYftE4OIUxWOe8P4YR_Pz7fwFtK-Wd)

### File Retrieval Process

The file retrieval process follows this sequence:

1. Client sends a GET request to MediaController for a specific file
2. MediaController calls FileRegistryService to get file information
3. FileRegistryService queries the registry by ID
4. If registry entry is found, it's returned; otherwise, the entity is queried
5. If entity is found, a registry-like object is created from its data
6. File information is returned to MediaController
7. MediaController reads the file from disk
8. Appropriate content type headers are set
9. File is streamed to the client with proper headers

**File Retrieval Sequence:**

![File Retrieval Process](https://mermaid.ink/img/pako:eNqNkstugzAQRX9l5HUrFUgeoJtupXbRTdWq7aYbZMBtIoKNbKZNFfHvNYZ0kW6qeGHP8Zl7Z5iQMiwBE0YV1srtpEGNrwphDO5AoTFwD0twqAxsQMkarDAWnmALGl7gFTYSaniCF1CwgmdYwc6hkfAAb_Dk0HC4h3d4QlnDEt5gjdrCFpbw7tAwWMAWJFq4hzv4kKhhAWvYozGwgDv4lBbmcA9fKC3M4R6ktWjgHjagncUJzGGH2sLcf_8O31JamMMCvlBZmMMdfEvr_BzWsEVjYQZz-EBlYQZLkNaigRksYe_Q-e_v4VNaOIUZfKO2cAozOFjnvz-DDewdOv_9LXxLC6cwgwNqC6cwg6Nz_vtT2MDOofPf38CPtHACUziitHACU_h1zn9_AlvYO3_O_8CPtHAMEziitHAME_hzzn8_hgP813AMY1CoLBzDGLRz_vwYxnBwcApj-EVt4RTGoKzz_hhG8PPv_wWnXeWh?type=png)](https://mermaid.live/edit#pako:eNqNkstugzAQRX9l5HUrFUgeoJtupXbRTdWq7aYbZMBtIoKNbKZNFfHvNYZ0kW6qeGHP8Zl7Z5iQMiwBE0YV1srtpEGNrwphDO5AoTFwD0twqAxsQMkarDAWnmALGl7gFTYSaniCF1CwgmdYwc6hkfAAb_Dk0HC4h3d4QlnDEt5gjdrCFpbw7tAwWMAWJFq4hzv4kKhhAWvYozGwgDv4lBbmcA9fKC3M4R6ktWjgHjagncUJzGGH2sLcf_8O31JamMMCvlBZmMMdfEvr_BzWsEVjYQZz-EBlYQZLkNaigRksYe_Q-e_v4VNaOIUZfKO2cAozOFjnvz-DDewdOv_9LXxLC6cwgwNqC6cwg6Nz_vtT2MDOofPf38CPtHACUziitHACU_h1zn9_AlvYO3_O_8CPtHAMEziitHAME_hzzn8_hgP813AMY1CoLBzDGLRz_vwYxnBwcApj-EVt4RTGoKzz_hhG8PPv_wWnXeWh)

## File Upload Strategy

### Directory Structure

- Files are uploaded to a configurable directory (`UPLOAD_DIR` environment variable, defaults to 'uploads')
- Files are organized in subdirectories based on their type:
  - `profile-pictures`: For user profile pictures
  - `shop-items`: For shop item images
  - `diary-skins`: For diary skin preview images
  - `student-diary-skins`: For student-created diary skin images

### File Naming

- File names are generated using a combination of entity information and timestamps to ensure uniqueness
- Example: `profile-picture-user123-1234567890.jpg`
- Example: `shop-item-GR001-1234567890.png`
- Example: `diary-skin-modern-blue-1234567890.jpg`

### File Storage

- Files are stored on the local filesystem in the configured upload directory
- In production, this directory should be mounted as a volume in Docker
- The file paths are stored in the database as relative paths from the upload directory

## Registry Entities

For each file type, there is a corresponding registry entity:

### ProfilePictureRegistry

```typescript
@Entity('profile_picture_registry')
export class ProfilePictureRegistry {
  @PrimaryGeneratedColumn('uuid')
  Id: string;

  @Column()
  ProfilePictureId: string;

  @Column()
  FilePath: string;

  @Column()
  FileName: string;

  @Column()
  MimeType: string;

  @Column()
  FileSize: number;

  @Column({ nullable: true })
  UserId: string;

  @CreateDateColumn()
  CreatedAt: Date;

  @UpdateDateColumn({ nullable: true })
  UpdatedAt: Date;
}
```

### ShopItemRegistry

```typescript
@Entity('shop_item_registry')
export class ShopItemRegistry {
  @PrimaryGeneratedColumn('uuid')
  Id: string;

  @Column()
  ShopItemId: string;

  @Column()
  FilePath: string;

  @Column()
  FileName: string;

  @Column()
  MimeType: string;

  @Column()
  FileSize: number;

  @Column({ nullable: true })
  UserId: string;

  @CreateDateColumn()
  CreatedAt: Date;

  @UpdateDateColumn({ nullable: true })
  UpdatedAt: Date;
}
```

### DiarySkinRegistry

```typescript
@Entity('diary_skin_registry')
export class DiarySkinRegistry {
  @PrimaryGeneratedColumn('uuid')
  Id: string;

  @Column()
  DiarySkinId: string;

  @Column()
  FilePath: string;

  @Column()
  FileName: string;

  @Column()
  MimeType: string;

  @Column()
  FileSize: number;

  @Column({ nullable: true })
  UserId: string;

  @CreateDateColumn()
  CreatedAt: Date;

  @UpdateDateColumn({ nullable: true })
  UpdatedAt: Date;
}
```

## File Registry Service

The `FileRegistryService` provides methods for:

### Uploading Files

```typescript
/**
 * Upload a file
 * @param entityType Type of entity (profile picture, shop item, diary skin)
 * @param file File to upload
 * @param referenceId Reference ID (userId for profile pictures, itemNumber for shop items, skinName for diary skins)
 * @param options Additional options for the upload
 * @returns Path to the uploaded file and the registry entry
 */
async uploadFile(
  entityType: FileEntityType,
  file: any,
  referenceId: string,
  options?: any,
): Promise<{ filePath: string; registry: any }>
```

### Registering Files

```typescript
/**
 * Register a file in the registry
 * @param entityType Type of entity (profile picture, shop item, diary skin)
 * @param entityId Entity ID
 * @param filePath Path to the file
 * @param fileName Original file name
 * @param mimeType MIME type of the file
 * @param fileSize Size of the file in bytes
 * @param userId User ID who uploaded the file (optional)
 * @returns The created registry entry
 */
async registerFile(
  entityType: FileEntityType,
  entityId: string,
  filePath: string,
  fileName: string,
  mimeType: string,
  fileSize: number,
  userId?: string,
): Promise<any>
```

### Retrieving Files

```typescript
/**
 * Get a file from the registry
 * @param entityType Type of entity (profile picture, shop item, diary skin)
 * @param entityId Entity ID
 * @returns The registry entry or null if not found
 */
async getFile(entityType: FileEntityType, entityId: string): Promise<any>
```

### Generating URLs

```typescript
/**
 * Get a URL for a file
 * @param entityType Type of entity (profile picture, shop item, diary skin)
 * @param entityId Entity ID
 * @returns URL for accessing the file
 */
getFileUrl(entityType: FileEntityType, entityId: string): string
```

## URL Generation

The `getFileUrlWithFallback` method provides a simplified way to generate URLs for files:

```typescript
/**
 * Get a file URL with automatic fallback based on entity type and ID
 * @param entityType Type of entity
 * @param entityId ID of the entity (can be either registry ID or entity ID)
 * @returns URL for the file or null if no file exists
 */
async getFileUrlWithFallback(entityType: FileEntityType, entityId: string): Promise<string | null>
```

This method implements a robust fallback mechanism:

1. First tries to find a registry entry by registry ID
2. If not found, tries to find by entity ID
3. If a registry entry is found, generates a URL using the registry ID
4. If no registry entry is found but the entity has a file path, generates a URL using the entity ID
5. Returns null if no file is found

The implementation includes detailed logging to help diagnose issues:

```typescript
// First try to get the registry entry by registry ID
let registry = await this.getFileByRegistryId(entityType, entityId);

// If not found by registry ID, try by entity ID
if (!registry) {
  this.logger.log(`Registry not found by ID, trying by entity ID: ${entityId}`);
  registry = await this.getFile(entityType, entityId);
}

// If we found a registry entry with an ID, use that for the URL
if (registry && registry.id) {
  const url = this.getFileUrl(entityType, registry.id);
  this.logger.log(`Generated URL using registry ID: ${url}`);
  return url;
}

// If we found a registry-like object without an ID but with a file path
if (registry && (registry.filePath || registry.FilePath)) {
  // Generate URL using entity ID
  const url = this.getFileUrl(entityType, entityId);
  this.logger.log(`Generated URL using entity ID directly: ${url}`);
  return url;
}
```

Example usage:

```typescript
// Get a URL for a diary skin preview image
const previewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(
  FileEntityType.DIARY_SKIN,
  diarySkin.id
);

// Use the URL in a response DTO
return {
  id: diarySkin.id,
  name: diarySkin.name,
  previewImagePath: previewImageUrl,
  // other properties...
};
```

## Media Controller

The `MediaController` provides endpoints for serving files with appropriate content types:

- `/media/profile/:id`: Serves profile pictures
- `/media/shop/:id`: Serves shop item images
- `/media/diary-skin/:id`: Serves diary skin preview images
- `/media/registry/:entityType/:id`: Serves files directly from registry entries

These endpoints handle both registry IDs and entity IDs, making them flexible and robust.

### Implementation Details

Each endpoint follows a similar pattern:

1. Get the file information using the FileRegistryService
2. Check if the file exists and has a valid path
3. Determine the content type (from registry or file extension)
4. Set appropriate headers
5. Stream the file to the client

Example implementation:

```typescript
@Get('diary-skin/:id')
@ApiOperation({ summary: 'Get a diary skin file by ID' })
@ApiParam({ name: 'id', description: 'Diary skin ID or registry ID' })
@ApiResponse({ status: 200, description: 'Diary skin file' })
@ApiResponse({ status: 404, description: 'File not found' })
async getDiarySkin(@Param('id') id: string, @Res() res: Response) {
  try {
    this.logger.log(`Getting diary skin file for ID: ${id}`);

    // Get file information using the enhanced FileRegistryService
    const file = await this.fileRegistryService.getDiarySkinFile(id);

    if (!file || !file.filePath) {
      this.logger.warn(`Diary skin file not found for ID: ${id}`);
      return res.status(404).send('File not found');
    }

    // Construct absolute file path
    const filePath = path.join(this.uploadsDir, file.filePath);

    // Check if file exists on disk
    if (!fs.existsSync(filePath)) {
      this.logger.warn(`Diary skin file not found on disk: ${filePath}`);
      return res.status(404).send('File not found on disk');
    }

    // Determine content type from registry or file extension
    const contentType = file.mimeType || this.getMimeTypeFromPath(filePath);

    // Set appropriate headers
    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'max-age=3600'); // Cache for 1 hour
    res.setHeader('Content-Disposition', 'inline');

    // Stream the file to the client
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    this.logger.error(`Error serving diary skin file: ${error.message}`);
    res.status(500).send('Error serving file');
  }
}
```

### Content Type Determination

The system determines content types in multiple ways:

```typescript
private getMimeTypeFromPath(filePath: string): string {
  if (!filePath) return 'application/octet-stream';

  const ext = path.extname(filePath).toLowerCase();
  switch (ext) {
    case '.jpg':
    case '.jpeg':
      return 'image/jpeg';
    case '.png':
      return 'image/png';
    case '.gif':
      return 'image/gif';
    case '.webp':
      return 'image/webp';
    case '.svg':
      return 'image/svg+xml';
    case '.pdf':
      return 'application/pdf';
    default:
      return 'application/octet-stream';
  }
}
```

## Fallback Mechanisms

The file registry system implements robust fallback mechanisms to handle various scenarios:

### Fallback Process

The fallback process follows this sequence:

1. Client requests an entity with a file URL from Service
2. Service calls FileRegistryService.getFileUrlWithFallback() with entity type and ID
3. FileRegistryService queries the registry by ID
4. If registry entry is found, URL is generated with registry ID
5. If registry entry is not found, entity is queried by ID
6. If entity has a file path, URL is generated with entity ID
7. If entity has no file path, additional fallbacks are attempted
8. If all fallbacks fail, null is returned
9. Service returns the entity with file URL to Client

**Fallback Sequence:**

![Fallback Process]()](https://www.mermaidchart.com/raw/7f02c7cc-4fa9-4647-b2cc-0b04f96a7c3e?theme=light&version=v0.1&format=svg)

### Entity-Specific File Retrieval

For each entity type, specialized methods retrieve files with fallback logic:

```typescript
// Example for diary skins
private async getDiarySkinFile(diarySkinId: string): Promise<DiarySkinRegistry | null> {
  try {
    // Try to find registry entry
    const registry = await this.diarySkinRegistryRepository.findOne({
      where: { diarySkinId: diarySkinId },
      relations: ['diarySkin'],
    });

    if (registry) return registry;

    // Fallback: get entity directly and create registry-like object
    const diarySkin = await this.diarySkinRepository.findOne({
      where: { id: diarySkinId }
    });

    if (diarySkin && diarySkin.previewImagePath) {
      return {
        id: null,
        diarySkinId: diarySkin.id,
        diarySkin: diarySkin,
        filePath: diarySkin.previewImagePath,
        fileName: path.basename(diarySkin.previewImagePath),
        mimeType: this.getMimeTypeFromPath(diarySkin.previewImagePath),
        fileSize: null,
        userId: null,
        createdAt: diarySkin.createdAt,
        updatedAt: diarySkin.updatedAt
      } as DiarySkinRegistry;
    }

    return null;
  } catch (error) {
    this.logger.error(`Error getting diary skin file: ${error.message}`);
    return null;
  }
}
```

### Case-Insensitive Property Access

The system handles both camelCase and PascalCase property names:

```typescript
// Get file path from registry, handling both camelCase and PascalCase
const filePath = file.filePath || file.FilePath;

// Get mime type from registry, handling both camelCase and PascalCase
const mimeType = file.mimeType || file.MimeType || this.getMimeTypeFromPath(filePath);
```

## Error Handling

The file registry system includes comprehensive error handling:

### Logging

Detailed logging is used throughout the system to help diagnose issues:

```typescript
try {
  this.logger.log(`Getting file for ${entityType} with ID: ${entityId}`);
  // File operations...
  this.logger.log(`Generated URL: ${url}`);
  return url;
} catch (error) {
  this.logger.error(`Error in file operation: ${error.message}`, error.stack);
  return null;
}
```

### Graceful Degradation

The system is designed to gracefully handle errors and provide fallbacks:

```typescript
// Try to get file from registry
const file = await this.fileRegistryService.getFile(entityType, entityId);

// If not found, try fallback mechanisms
if (!file) {
  this.logger.warn(`File not found in registry, trying fallbacks`);
  // Fallback logic...
}

// If all fallbacks fail, return a default or null
if (!filePath) {
  this.logger.warn(`No file path found for ${entityType} with ID ${entityId}`);
  return null;
}
```

## Transaction Management

File operations use database transactions to ensure data consistency. This is especially important when creating both an entity and its associated registry entry, as they must be created together or not at all.

### Transaction Process

The transaction process follows this sequence:

1. Controller calls Service to create an entity with a file
2. Service creates and starts a transaction using QueryRunner
3. Service calls FileRegistryService to upload the file
4. File is saved to disk and metadata is returned
5. Service creates the entity through QueryRunner
6. Entity is saved to the database
7. Service creates a registry entry through QueryRunner
8. Registry entry is saved to the database
9. If all operations succeed, transaction is committed
10. If any operation fails, transaction is rolled back
11. QueryRunner is released
12. Result is returned to Controller

**Transaction Sequence:**

![Transaction Process](https://mermaid.ink/img/pako:eNqNk8tugzAQRX9l5HUrFUgeoJtupXbRTdWq7aYbZMBtIoKNbKZNFfHvNYZ0kW6qeGHP8Zl7Z5iQMiwBE0YV1srtpEGNrwphDO5AoTFwD0twqAxsQMkarDAWnmALGl7gFTYSaniCF1CwgmdYwc6hkfAAb_Dk0HC4h3d4QlnDEt5gjdrCFpbw7tAwWMAWJFq4hzv4kKhhAWvYozGwgDv4lBbmcA9fKC3M4R6ktWjgHjagncUJzGGH2sLcf_8O31JamMMCvlBZmMMdfEvr_BzWsEVjYQZz-EBlYQZLkNaigRksYe_Q-e_v4VNaOIUZfKO2cAozOFjnvz-DDewdOv_9LXxLC6cwgwNqC6cwg6Nz_vtT2MDOofPf38CPtHACUziitHACU_h1zn9_AlvYO3_O_8CPtHAMEziitHAME_hzzn8_hgP813AMY1CoLBzDGLRz_vwYxnBwcApj-EVt4RTGoKzz_hhG8PPv_wWnXeWh?type=png)](https://mermaid.live/edit#pako:eNqNk8tugzAQRX9l5HUrFUgeoJtupXbRTdWq7aYbZMBtIoKNbKZNFfHvNYZ0kW6qeGHP8Zl7Z5iQMiwBE0YV1srtpEGNrwphDO5AoTFwD0twqAxsQMkarDAWnmALGl7gFTYSaniCF1CwgmdYwc6hkfAAb_Dk0HC4h3d4QlnDEt5gjdrCFpbw7tAwWMAWJFq4hzv4kKhhAWvYozGwgDv4lBbmcA9fKC3M4R6ktWjgHjagncUJzGGH2sLcf_8O31JamMMCvlBZmMMdfEvr_BzWsEVjYQZz-EBlYQZLkNaigRksYe_Q-e_v4VNaOIUZfKO2cAozOFjnvz-DDewdOv_9LXxLC6cwgwNqC6cwg6Nz_vtT2MDOofPf38CPtHACUziitHACU_h1zn9_AlvYO3_O_8CPtHAMEziitHAME_hzzn8_hgP813AMY1CoLBzDGLRz_vwYxnBwcApj-EVt4RTGoKzz_hhG8PPv_wWnXeWh)

### Transaction Implementation

```typescript
// Start a transaction
const queryRunner = this.dataSource.createQueryRunner();
await queryRunner.connect();
await queryRunner.startTransaction();

try {
  // Upload file and create entity
  const result = await this.fileRegistryService.uploadFile(...);
  const entity = queryRunner.manager.create(EntityType, {...});
  const savedEntity = await queryRunner.manager.save(entity);

  // Create registry entry
  const registryEntry = queryRunner.manager.create(RegistryType, {
    entityId: savedEntity.id,
    filePath: result.filePath,
    fileName: file.originalname,
    mimeType: file.mimetype,
    fileSize: file.size,
    userId: userId
  });
  await queryRunner.manager.save(registryEntry);

  // Commit the transaction
  await queryRunner.commitTransaction();

  return savedEntity;
} catch (error) {
  // Rollback on error
  await queryRunner.rollbackTransaction();
  throw error;
} finally {
  // Always release the query runner
  await queryRunner.release();
}
```

## Usage Examples

### Uploading a Profile Picture

```typescript
async uploadProfilePicture(userId: string, file: any): Promise<string> {
  try {
    // Upload the file using the FileRegistryService
    const result = await this.fileRegistryService.uploadFile(
      FileEntityType.PROFILE_PICTURE,
      file,
      userId
    );

    // Return the URL to the uploaded file
    return this.fileRegistryService.getFileUrl(FileEntityType.PROFILE_PICTURE, userId);
  } catch (error) {
    this.logger.error(`Error uploading profile picture: ${error.message}`);
    throw new BadRequestException('Failed to upload profile picture');
  }
}
```

### Creating a Diary Skin with Preview Image

```typescript
async createDiarySkin(adminId: string, createDiarySkinDto: CreateDiarySkinDto, previewImage: any): Promise<DiarySkinResponseDto> {
  // Start a transaction
  const queryRunner = this.dataSource.createQueryRunner();
  await queryRunner.connect();
  await queryRunner.startTransaction();

  try {
    // Upload the preview image using the FileRegistryService
    const result = await this.fileRegistryService.uploadFile(
      FileEntityType.DIARY_SKIN,
      previewImage,
      createDiarySkinDto.name,
      {
        isStudentSkin: false,
        userId: adminId
      }
    );

    // Create the new skin with the file path from the result
    const newSkin = this.diarySkinRepository.create({
      Name: createDiarySkinDto.name,
      Description: createDiarySkinDto.description,
      TemplateContent: createDiarySkinDto.templateContent,
      PreviewImagePath: result.filePath,
      IsActive: createDiarySkinDto.isActive !== undefined ? createDiarySkinDto.isActive : true,
      IsGlobal: true,
      CreatedById: adminId
    });

    // Save the new skin using the transaction
    const savedSkin = await queryRunner.manager.save(newSkin);

    // Create a registry entry for the file
    const diarySkinRegistry = new DiarySkinRegistry();
    diarySkinRegistry.DiarySkinId = savedSkin.Id;
    diarySkinRegistry.FilePath = result.filePath;
    diarySkinRegistry.FileName = previewImage.originalname;
    diarySkinRegistry.MimeType = previewImage.mimetype;
    diarySkinRegistry.FileSize = previewImage.size;
    diarySkinRegistry.UserId = adminId;

    // Save the registry entry
    await queryRunner.manager.save(diarySkinRegistry);

    // Commit the transaction
    await queryRunner.commitTransaction();

    // Generate registry URL for preview image using the helper method
    const registryPreviewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(
      FileEntityType.DIARY_SKIN,
      savedSkin.Id
    );

    // Return the DTO
    return {
      id: savedSkin.Id,
      name: savedSkin.Name,
      description: savedSkin.Description,
      previewImagePath: registryPreviewImageUrl,
      isActive: savedSkin.IsActive,
      isGlobal: savedSkin.IsGlobal,
      createdById: savedSkin.CreatedById,
      templateContent: savedSkin.TemplateContent
    };
  } catch (error) {
    // Rollback the transaction in case of error
    await queryRunner.rollbackTransaction();
    throw error;
  } finally {
    // Release the query runner
    await queryRunner.release();
  }
}
```

### Getting a List of Diary Skins with Preview Images

```typescript
async getDiarySkins(includeInactive: boolean = false, studentId?: string): Promise<{ globalSkins: DiarySkinResponseDto[], studentSkins: DiarySkinResponseDto[] }> {
  // Define the where clause based on includeInactive
  const where = includeInactive ? {} : { IsActive: true };

  // Get global skins (admin created)
  const globalSkins = await this.diarySkinRepository.find({ where });
  const globalSkinDtos = await Promise.all(globalSkins.map(async skin => {
    // Generate registry URL for preview image using the helper method
    const registryPreviewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(
      FileEntityType.DIARY_SKIN,
      skin.Id
    );

    return {
      id: skin.Id,
      name: skin.Name,
      description: skin.Description,
      previewImagePath: registryPreviewImageUrl,
      isActive: skin.IsActive,
      isGlobal: skin.IsGlobal,
      createdById: skin.CreatedById,
      templateContent: skin.TemplateContent
    };
  }));

  // Get student skins if studentId is provided
  let studentSkinDtos: DiarySkinResponseDto[] = [];
  if (studentId) {
    const studentWhere = includeInactive ? { StudentId: studentId } : { StudentId: studentId, IsActive: true };
    const studentSkins = await this.studentDiarySkinRepository.find({ where: studentWhere });

    studentSkinDtos = await Promise.all(studentSkins.map(async skin => {
      // Generate registry URL for preview image using the helper method
      const registryPreviewImageUrl = await this.fileRegistryService.getFileUrlWithFallback(
        FileEntityType.DIARY_SKIN,
        skin.Id
      );

      return {
        id: skin.Id,
        name: skin.Name,
        description: skin.Description,
        previewImagePath: registryPreviewImageUrl,
        isActive: skin.IsActive,
        isGlobal: false,
        studentId: skin.StudentId,
        templateContent: skin.TemplateContent
      };
    }));
  }

  return { globalSkins: globalSkinDtos, studentSkins: studentSkinDtos };
}
```

## Best Practices

### 1. Always Use Transactions

When operations involve multiple entities (e.g., creating an entity and its registry entry), always use transactions:

```typescript
const queryRunner = this.dataSource.createQueryRunner();
await queryRunner.connect();
await queryRunner.startTransaction();

try {
  // Multiple database operations...
  await queryRunner.commitTransaction();
} catch (error) {
  await queryRunner.rollbackTransaction();
  throw error;
} finally {
  await queryRunner.release();
}
```

### 2. Validate Files Before Uploading

Always validate files before uploading them:

```typescript
if (!file) {
  throw new BadRequestException('File is required');
}

if (file.size > this.maxFileSize) {
  throw new BadRequestException(`File size exceeds the maximum allowed size of ${this.maxFileSize / 1024 / 1024}MB`);
}

const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif'];
if (!allowedMimeTypes.includes(file.mimetype)) {
  throw new BadRequestException(`File type not allowed. Allowed types: ${allowedMimeTypes.join(', ')}`);
}
```

### 3. Use the getFileUrlWithFallback Method

Always use the `getFileUrlWithFallback` method for consistent URL generation:

```typescript
// Good practice
const imageUrl = await this.fileRegistryService.getFileUrlWithFallback(
  FileEntityType.SHOP_ITEM,
  shopItem.id
);

// Avoid direct URL construction
// const imageUrl = `${baseUrl}/media/shop/${shopItem.id}`;
```

### 4. Include Proper Error Handling

Implement comprehensive error handling with detailed logging:

```typescript
try {
  // File operations...
} catch (error) {
  this.logger.error(`Error in file operation: ${error.message}`, error.stack);

  // Specific error handling
  if (error.code === 'ENOENT') {
    throw new NotFoundException('File not found on disk');
  }

  throw new InternalServerErrorException('File operation failed');
}
```

### 5. Set Appropriate Content Types

Always set the correct content type when serving files:

```typescript
const contentType = file.mimeType || this.getMimeTypeFromPath(filePath);
res.setHeader('Content-Type', contentType);
```

### 6. Check for File Existence

Always check if a file exists before attempting to serve it:

```typescript
if (!fs.existsSync(filePath)) {
  this.logger.warn(`File not found on disk: ${filePath}`);
  return res.status(404).send('File not found on disk');
}
```

### 7. Use Stream Piping

Use stream piping for efficient file serving:

```typescript
const fileStream = fs.createReadStream(filePath);
fileStream.pipe(res);
```

### 8. Implement Fallback Mechanisms

Implement fallback mechanisms for robust file retrieval:

```typescript
// Try registry first
let file = await this.getFileByRegistryId(entityType, entityId);

// If not found, try entity
if (!file) {
  file = await this.getFileByEntityId(entityType, entityId);
}

// If still not found, try fallback
if (!file) {
  file = await this.getFallbackFile(entityType);
}
```

---

Following these conventions and best practices ensures a consistent, reliable, and robust file upload and registry system throughout the application.
