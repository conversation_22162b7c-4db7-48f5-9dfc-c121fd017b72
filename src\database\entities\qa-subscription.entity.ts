import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { User } from './user.entity';

export enum QASubscriptionStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

@Entity()
export class QASubscription extends AuditableBaseEntity {
  @Column({ name: 'student_id' })
  studentId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'student_id' })
  student: User;

  @Column({ name: 'tutor_id' })
  tutorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @Column({
    type: 'enum',
    enum: QASubscriptionStatus,
    default: QASubscriptionStatus.ACTIVE
  })
  status: QASubscriptionStatus;

  @Column({ name: 'subscription_dates', type: 'jsonb', nullable: true })
  subscriptionDates: {
    start: Date;
    end?: Date;
  };
}