import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  UseGuards,
  Param,
  Parse<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>
} from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiOperation, ApiTags, ApiQuery, ApiParam } from "@nestjs/swagger";
import { JwtAuthGuard } from "src/common/guards/jwt.guard";
import { TutorGuard } from "src/common/guards/tutor.guard";
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from 'src/common/decorators/api-response.decorator';
import { EssayTaskSubmissionMarkingDto, CreateEssayTaskSubmissionMarkingDto, EssayTaskSubmissionHistoryUpdate } from 'src/database/models/mission.dto';
import { TutorEssayService } from "./tutor-essay.service";
import { ApiResponse } from "src/common/dto/api-response.dto";
import { EssayTaskSubmissionDto, MissionResponseDto, MissionPaginationDto } from "src/database/models/mission.dto";
import { PaginationDto } from "src/common/models/pagination.dto";
import { PagedListDto } from "src/common/models/paged-list.dto";
import { EssayMissionService } from "./admin-essay.service";
import { CreateMissionDto } from "src/database/models/mission.dto";


@ApiTags('tutor-essay')
@ApiBearerAuth('JWT-auth')
@Controller('tutor-essay')
export class TutorEssayController {
  constructor(
    private readonly tutorEssayService: TutorEssayService,
    private readonly missionService: EssayMissionService,
  ){}

  @Get('missions')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Get all missions' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    enum: ['ASC', 'DESC'],
    description: 'Sort order (asc or desc)',
  })
  @ApiOkResponseWithPagedListType(MissionResponseDto, 'Retrieved all missions')
  async findAll( @Query() paginationDto?: MissionPaginationDto): Promise<ApiResponse<PagedListDto<MissionResponseDto>>> {
    const result = await this.missionService.findAll(paginationDto);
    return ApiResponse.success(result, 'Retrieved all missions');
  }

  @Get('list')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Get all submitted first essays list' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    enum: ['ASC', 'DESC'],
    description: 'Sort order (asc or desc)',
  })
  @ApiOkResponseWithPagedListType(EssayTaskSubmissionDto, 'Retrieved all submitted first submitted essays list')
  async findAllSubmittedEssays(
    @Query() paginationDto?: PaginationDto
  ): Promise<ApiResponse<PagedListDto<EssayTaskSubmissionDto>>> {
    const result = await this.missionService.findAllSubmittedEssays(paginationDto);
    return ApiResponse.success(result, 'Retrieved all submitted first essays list');
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Get a specific submitted essay by ID' })
  @ApiParam({ name: 'id', description: 'ID of the submitted essay to retrieve', type: String })
  @ApiOkResponseWithType(EssayTaskSubmissionDto, 'Retrieved submitted essay successfully')
  @ApiErrorResponse(404, 'Submitted essay not found')
  async findOneSubmittedEssay(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponse<EssayTaskSubmissionDto>> {
    const result = await this.missionService.findSubmittedEssayById(id);
    return ApiResponse.success(result, 'Retrieved submitted essay successfully');
  }

  @Post('markEssay')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Mark essay' })
  @ApiBody({
    type: CreateEssayTaskSubmissionMarkingDto,
    description: 'Essay task submission marking data',
    examples: {
      'example1': {
        value: {
          submissionId: '123-456-789-abc-def-ghi',
          points: 85,
          submissionFeedback: 'Great job!',
          taskRemarks: 'Well done on the introduction and conclusion.',
        },
      },
  }})
  @ApiOkResponseWithType(EssayTaskSubmissionMarkingDto)
  @ApiErrorResponse(400, 'Bad Request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Not Found')
  async markEssayTaskSubmission(
    @Body() essaySubmissionMarkDto: CreateEssayTaskSubmissionMarkingDto
  ): Promise<ApiResponse<EssayTaskSubmissionMarkingDto>> {
    const result = await this.tutorEssayService.markEssayTaskSubmission(essaySubmissionMarkDto);
    return ApiResponse.success(result, "Essay task submission marked successfully");
  }

  @Patch('markEssay')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Update essay task submission marking' })
  @ApiBody({
    type: EssayTaskSubmissionHistoryUpdate,
    description: 'Essay task submission marking update data',
    examples: {
      'example1': {
        value: {
          submissionId: '123-456-789-abc-def-ghi',
          submissionFeedback: 'Excellent work!',
          taskRemarks: 'The essay was well-structured and insightful.',
        },
      },
    }
  })
  @ApiOkResponseWithType(EssayTaskSubmissionMarkingDto, 'Essay task submission marking updated successfully')
  @ApiErrorResponse(400, 'Bad Request')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiErrorResponse(404, 'Not Found')
  async updateEssayTaskSubmissionMarking(
    @Body() essaySubmissionMarkDto: EssayTaskSubmissionHistoryUpdate
  ): Promise<ApiResponse<EssayTaskSubmissionMarkingDto>> {
    const result = await this.tutorEssayService.updateEssayTaskSubmissionMarking(essaySubmissionMarkDto);
    return ApiResponse.success(result, "Essay task submission marking updated successfully");
  }

  @Post('mission/create')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Create a new essay mission with tasks' })
  @ApiBody({
    type: CreateMissionDto,
    description: 'Essay mission creation data with tasks',
    examples: {
      example1: {
        value: {
          timeFrequency: 'weekly',
          tasks: [
            {
              title: 'Writing Task 1',
              description: 'Write an essay about your favorite book',
              wordLimitMinimum: 500,
              wordLimitMaximum: 1000,
              timePeriodUnit: 1,
              deadline: 7,
              instructions: 'Follow MLA format and include citations',
              metaData: {
                week: '1',
                month: '4',
                year: '2025'
              }
            }
          ]
        }
      }
    }
  })
  @ApiOkResponseWithType(MissionResponseDto, 'Essay mission created successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async create(
    @Body() createMissionDto: CreateMissionDto
  ): Promise<ApiResponse<MissionResponseDto>> {
    const result = await this.missionService.create(createMissionDto);
    return ApiResponse.success(
      result,
      'Essay mission created successfully',
      201
    );
  }

  @Get('missions')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Get all missions' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    enum: ['ASC', 'DESC'],
    description: 'Sort order (asc or desc)',
  })
  @ApiOkResponseWithPagedListType(MissionResponseDto, 'Retrieved all missions')
  async findAllMissions( @Query() paginationDto?: MissionPaginationDto): Promise<ApiResponse<PagedListDto<MissionResponseDto>>> {
    const result = await this.missionService.findAll(paginationDto);
    return ApiResponse.success(result, 'Retrieved all missions');
  }

  @Get('missions/:id')
  @UseGuards(JwtAuthGuard, TutorGuard)
  @ApiOperation({ summary: 'Get a specific essay mission task by ID' })
  @ApiParam({ name: 'id', description: 'ID of the mission task to retrieve', type: String })
  @ApiOkResponseWithType(MissionResponseDto, 'Retrieved mission successfully')
  @ApiErrorResponse(404, 'Mission not found')
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<ApiResponse<MissionResponseDto>> {
    const result = await this.missionService.findByIdWithTasks(id);
    return ApiResponse.success(result, 'Retrieved mission task successfully');
  }
}