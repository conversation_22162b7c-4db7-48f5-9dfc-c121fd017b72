# Diary API Testing Flow

This document outlines the testing flow for the Diary API endpoints.

## Prerequisites

Before testing the Diary API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (student, tutor)
3. Set up your API testing tool (<PERSON><PERSON> recommended)

## Diary Entry Creation Testing Flow

### Test Case 1: Create New Diary Entry

1. Authenticate with a student token
2. Send a POST request to `/api/diary/entries` with valid entry data:
   ```json
   {
     "entryDate": "2023-07-25",
     "title": "My First Diary Entry",
     "content": "Today I learned about...",
     "skinId": "123e4567-e89b-12d3-a456-426614174000",
     "backgroundColor": "#f5f5f5",
     "isPrivate": false,
     "settingsTemplateId": "123e4567-e89b-12d3-a456-426614174000"
   }
   ```
3. Verify HTTP status code is 201 Created
4. Verify response contains the created entry details
5. Verify entry is created in the database with status "NEW"
6. Verify entry is associated with the authenticated student

### Test Case 2: Entry Creation Validation

1. Test with missing required fields (title, content)
2. Test with invalid skinId
3. Test with invalid status value
4. Verify appropriate validation errors are returned for each case

### Test Case 3: Create Entry with Skin

1. Authenticate with a student token
2. Send a POST request to `/api/diary/entries` with a valid skinId
3. Verify HTTP status code is 201 Created
4. Verify response contains the skin details
5. Verify entry is created with the specified skin

## Diary Entry Update Testing Flow

### Test Case 1: Update Diary Entry

1. Authenticate with a student token
2. Create a new diary entry
3. Send a PUT request to `/api/diary/entries/{entryId}` with updated data:
   ```json
   {
     "title": "Updated Title",
     "content": "Updated content...",
     "skinId": "123e4567-e89b-12d3-a456-426614174000",
     "backgroundColor": "#f5f5f5",
     "isPrivate": false,
     "settingsTemplateId": "123e4567-e89b-12d3-a456-426614174000"
   }
   ```
4. Verify HTTP status code is 200 OK
5. Verify response contains the updated entry details
6. Verify changes are persisted in the database

### Test Case 2: Update Entry Validation

1. Test updating an entry that doesn't belong to the authenticated student
2. Test updating an entry that is being reviewed
3. Test updating an entry that has been reviewed
4. Verify appropriate error responses for each case

### Test Case 3: Update Entry Status

1. Authenticate with a student token
2. Create a new diary entry
3. Send a PUT request to update the entry status to "SUBMIT"
4. Verify HTTP status code is 200 OK
5. Verify entry status is updated in the database
6. Verify appropriate notifications are sent

## Diary Entry Submission Testing Flow

### Test Case 1: Submit Entry for Review

1. Authenticate with a student token
2. Create a new diary entry
3. Send a POST request to `/api/diary/entries/{entryId}/submit`
4. Verify HTTP status code is 200 OK
5. Verify entry status is changed to "SUBMIT"
6. Verify entry is available for tutor review
7. Verify appropriate notifications are sent to assigned tutors

### Test Case 2: Submit Entry Validation

1. Test submitting an entry that is already submitted
2. Test submitting an entry that is being reviewed
3. Test submitting an entry that has been reviewed
4. Verify appropriate error responses for each case

## Diary Entry Retrieval Testing Flow

### Test Case 1: Get Student's Diary Entries

1. Authenticate with a student token
2. Create multiple diary entries with different statuses
3. Send a GET request to `/api/diary/entries` with pagination parameters
4. Verify HTTP status code is 200 OK
5. Verify response contains paginated list of entries
6. Verify only entries belonging to the authenticated student are returned

### Test Case 2: Filter and Sort Entries

1. Test filtering by status (NEW, SUBMIT, REVIEWED, CONFIRM)
2. Test filtering by date range
3. Test filtering by privacy setting
4. Test sorting by different fields (createdAt, title, etc.)
5. Verify filtered and sorted results match the criteria

### Test Case 3: Get Entry by ID

1. Authenticate with a student token
2. Create a diary entry
3. Send a GET request to `/api/diary/entries/{entryId}`
4. Verify HTTP status code is 200 OK
5. Verify response contains the complete entry details
6. Test with non-existent entry ID and verify 404 Not Found response
7. Test with entry ID belonging to another student and verify 403 Forbidden response

## Tutor Review Testing Flow

### Test Case 1: Get Entries for Review

1. Authenticate with a tutor token
2. Send a GET request to `/api/diary/entries/for-review` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of entries for review
5. Verify only entries assigned to the authenticated tutor are returned
6. Verify only entries with status "SUBMIT" are returned

### Test Case 2: Start Reviewing Entry

1. Authenticate with a tutor token
2. Send a POST request to `/api/diary/entries/{entryId}/start-review`
3. Verify HTTP status code is 200 OK
4. Verify entry is locked for review
5. Verify reviewStartTime, reviewExpiryTime, and reviewingTutorId are set
6. Verify entry remains in "SUBMIT" status but is locked

### Test Case 3: Review Lock Validation

1. Authenticate with a student token
2. Attempt to update an entry that is being reviewed
3. Verify 409 Conflict response with lock information
4. Authenticate with another tutor token
5. Attempt to start reviewing an entry that is already being reviewed
6. Verify 409 Conflict response with lock information

### Test Case 4: Review Lock Expiry

1. Authenticate with a tutor token
2. Start reviewing an entry
3. Wait for the review lock to expire (or simulate expiration)
4. Verify entry is no longer locked
5. Verify another tutor can start reviewing the entry

## Diary Entry Correction Testing Flow

### Test Case 1: Add Corrections to Entry

1. Authenticate with a tutor token
2. Start reviewing an entry
3. Send a POST request to `/api/diary/entries/{entryId}/corrections` with correction data
4. Verify HTTP status code is 200 OK
5. Verify corrections are saved in the database
6. Verify entry status is changed to "REVIEWED"
7. Verify appropriate notifications are sent to the student

### Test Case 2: Correction Validation

1. Test adding corrections to an entry that is not being reviewed by the authenticated tutor
2. Test adding corrections with invalid correction data
3. Test adding corrections to an entry that is already in "REVIEWED" status
4. Verify appropriate error responses for each case

## Diary Entry Confirmation Testing Flow

### Test Case 1: Confirm Entry Review

1. Authenticate with a tutor token
2. Review an entry and add corrections
3. Send a POST request to `/api/diary/entries/{entryId}/confirm`
4. Verify HTTP status code is 200 OK
5. Verify entry status is changed to "CONFIRM"
6. Verify confirmedAt timestamp is set
7. Verify appropriate notifications are sent to the student

### Test Case 2: Confirmation Validation

1. Test confirming an entry that is not in "REVIEWED" status
2. Test confirming an entry that was not reviewed by the authenticated tutor
3. Test confirming an entry that is already confirmed
4. Verify appropriate error responses for each case

## Diary Entry Sharing Testing Flow

### Test Case 1: Share Diary Entry

1. Authenticate with a student token
2. Create a diary entry
3. Send a POST request to `/api/diary/entries/{entryId}/share`
4. Verify HTTP status code is 200 OK
5. Verify response contains share link or QR code
6. Verify share information is saved in the database

### Test Case 2: Access Shared Entry

1. Create a shared diary entry
2. Send a GET request to the share link without authentication
3. Verify HTTP status code is 200 OK
4. Verify response contains the shared entry details
5. Verify only public information is included in the response

## Edge Cases and Security Testing

### Test Case 1: Role-Based Access Control

1. Authenticate with a tutor token
2. Attempt to create or update a diary entry
3. Verify 403 Forbidden responses
4. Authenticate with a student token
5. Attempt to review or confirm an entry
6. Verify 403 Forbidden responses

### Test Case 2: Privacy Settings

1. Create diary entries with different privacy settings
2. Verify private entries are only visible to the owner
3. Verify public entries are visible to other users
4. Test sharing private vs. public entries

### Test Case 3: Concurrent Operations

1. Simulate concurrent update requests to the same entry
2. Verify system handles race conditions correctly
3. Simulate concurrent review start requests from different tutors
4. Verify only one tutor can review an entry at a time

## Status Transition Testing

### Test Case 1: Valid Status Transitions

1. Test the valid status transitions:
   - NEW → SUBMIT: Student submits the entry for review
   - SUBMIT → REVIEWED: Tutor reviews the entry and adds corrections
   - REVIEWED → CONFIRM: Tutor confirms the review is complete
2. Verify each transition works correctly
3. Verify appropriate notifications are sent for each transition

### Test Case 2: Invalid Status Transitions

1. Test invalid status transitions:
   - NEW → REVIEWED: Attempt to skip submission
   - NEW → CONFIRM: Attempt to skip submission and review
   - SUBMIT → CONFIRM: Attempt to skip review
   - REVIEWED → SUBMIT: Attempt to revert to previous status
   - CONFIRM → REVIEWED: Attempt to revert to previous status
2. Verify appropriate error responses for each invalid transition
