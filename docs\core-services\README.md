# HEC Core Services & Utilities

This document provides an overview of the core services and utilities used throughout the HEC backend application. These services provide reusable functionality that can be leveraged by multiple modules.

## Available Core Services

### 1. Common Services

The common services provide fundamental functionality used across the application:

- **PaginationService**: Handles pagination for list endpoints
- **ConfigService**: Manages application configuration
- **LoggerService**: Provides structured logging capabilities
- **ValidationService**: Handles input validation

[Read more about Common Services](../api-documentation/6-common-services.md)

### 2. File Upload Service

The File Upload Service handles all file-related operations:

- **File storage**: Securely stores uploaded files
- **File metadata**: Tracks file metadata in the database
- **File serving**: Serves files with appropriate content types
- **File deletion**: Properly cleans up files when they are no longer needed

[Read more about File Upload Service](../conventions/FILE_UPLOAD_CONVENTION.md)

### 3. Audit Logging Service

The Audit Logging Service tracks entity creation and modification:

- **Automatic tracking**: Automatically logs who created and updated entities
- **Timestamp tracking**: Records when entities were created and updated
- **Audit trail**: Provides a complete history of changes

[Read more about Audit Logging Service](../conventions/AUDIT_LOGGING_CONVENTION.md)

### 4. Deeplink Service

The Deeplink Service manages the creation and handling of deep links:

- **Link generation**: Creates deep links for sharing content
- **QR code generation**: Generates QR codes for deep links
- **Link resolution**: Resolves deep links to the appropriate content

[Read more about Deeplink Service](../implementation/deeplink-service-implementation.md)

## Using Core Services

To use these core services in your modules:

1. **Import the service**: Import the service from its module
2. **Inject the service**: Use dependency injection to get an instance of the service
3. **Call the service methods**: Use the service's methods to perform the desired operations

Example:

```typescript
import { Injectable } from '@nestjs/common';
import { PaginationService } from '../../common/services/pagination.service';
import { PaginationDto } from '../../common/dtos/pagination.dto';

@Injectable()
export class YourService {
  constructor(private readonly paginationService: PaginationService) {}

  async findAll(paginationDto: PaginationDto) {
    // Get pagination parameters for the database query
    const { skip, take, page, limit } = this.paginationService.getPaginationParameters(paginationDto);

    // Execute the query with pagination
    const [items, total] = await this.repository.findAndCount({
      skip,
      take,
      order: { createdAt: 'DESC' }
    });

    // Create a paged list response with all pagination information
    return this.paginationService.createPagedList(items, total, paginationDto);
  }
}
```

## Best Practices

When using core services:

1. **Prefer core services over custom implementations**: Use the provided core services instead of creating your own implementations
2. **Follow the service conventions**: Adhere to the conventions defined for each service
3. **Contribute improvements**: If you find ways to improve the core services, contribute those improvements back to the core services
