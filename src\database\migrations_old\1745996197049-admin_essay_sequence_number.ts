import { MigrationInterface, QueryRunner } from "typeorm";

export class AdminEssaySequenceNumber1745996197049 implements MigrationInterface {
    name = 'AdminEssaySequenceNumber1745996197049'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "essay_mission" ADD "sequence_number" integer NOT NULL DEFAULT '1'`);
        await queryRunner.query(`ALTER TYPE "public"."plan_feature_type_enum" RENAME TO "plan_feature_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."plan_feature_type_enum" AS ENUM('hec_user_diary', 'hec_play', 'english_qa_writing', 'english_essay', 'english_novel', 'module')`);
        await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "type" TYPE "public"."plan_feature_type_enum" USING "type"::"text"::"public"."plan_feature_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."plan_feature_type_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."plan_feature_type_enum_old" AS ENUM('hec_user_diary', 'hec_play', 'english_qa_writing', 'english_essay', 'english_novel')`);
        await queryRunner.query(`ALTER TABLE "plan_feature" ALTER COLUMN "type" TYPE "public"."plan_feature_type_enum_old" USING "type"::"text"::"public"."plan_feature_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."plan_feature_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."plan_feature_type_enum_old" RENAME TO "plan_feature_type_enum"`);
        await queryRunner.query(`ALTER TABLE "essay_mission" DROP COLUMN "sequence_number"`);
    }

}
