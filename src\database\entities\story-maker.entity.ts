import { <PERSON>tity, Column, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { StoryMakerParticipation } from './story-maker-participation.entity';

@Entity()
export class StoryMaker extends AuditableBaseEntity {
  @Column()
  title: string;

  @Column({ type: 'text' })
  instruction: string;

  @Column()
  picture: string;

  @Column()
  score: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'word_limit', nullable: true })
  wordLimit: number;

  @Column({ name: 'deadline', nullable: true })
  deadline: number;

  // Relationships
  @OneToMany(() => StoryMakerParticipation, (participation) => participation.storyMaker)
  participations: StoryMakerParticipation[];
}
