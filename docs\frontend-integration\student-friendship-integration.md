# Student Friendship & Diary Follow System - Integration Flow

This document provides a clear integration flow for frontend developers to implement the Student Friendship and Diary Follow features with minimal verbal communication needed. It focuses exclusively on API endpoints and data flows without prescribing specific frontend implementation approaches.

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Integration Flow Diagrams](#integration-flow-diagrams)
   - [Friend Request Flow](#friend-request-flow)
   - [Diary Follow Request Flow](#diary-follow-request-flow)
   - [Search and Connection Flow](#search-and-connection-flow)
4. [API Endpoints](#api-endpoints)
   - [Student Search](#student-search)
   - [Friend Requests](#friend-requests)
   - [Diary Follow Requests](#diary-follow-requests)
   - [Friendship Management](#friendship-management)
5. [Notifications](#notifications)
6. [Chat Integration](#chat-integration)
7. [QR Code Sharing](#qr-code-sharing)
8. [Error <PERSON>ling](#error-handling)

## Overview

The Student Friendship and Diary Follow system allows students to:
- Find and connect with other students
- Send and respond to friend requests
- Request to follow friends' diaries
- Control who can view their diary entries
- Chat with friends
- Share diary entries via QR codes

## Authentication

All API requests require authentication using a JWT token. Include the token in the Authorization header:

```
Authorization: Bearer <token>
```

The token contains user information including the user's ID and role, which is used to enforce access control.

## Integration Flow Diagrams

### Friend Request Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│   Search    │      │ Send Friend │      │  Respond to │      │  Chat with  │
│  Students   │ ──▶  │   Request   │ ──▶  │   Request   │ ──▶  │   Friend    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /search │      │ POST /request│      │POST /respond│      │WebSocket    │
│ ?query=name │      │ requestedId  │      │ requestId   │      │Connection   │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**State Transitions:**
1. Initial state: No friendship
2. After sending request: Pending (for requester)
3. After accepting: Friends (both users)
4. After rejecting: None (both users)

### Diary Follow Request Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  View Friend│      │ Send Diary  │      │  Respond to │      │ View Friend's│
│   Profile   │ ──▶  │Follow Request│ ──▶  │   Request   │ ──▶  │    Diary    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│ GET /friends│      │ POST /diary- │      │POST /diary- │      │GET /diary/  │
│ /:friendId  │      │follow/request│      │follow/respond│     │entries?userId│
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**State Transitions:**
1. Initial state: Friends but no diary access
2. After sending request: Pending diary access
3. After accepting: Friends with diary access
4. After rejecting: Friends but no diary access

### Search and Connection Flow

```
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│  Search     │      │  Results    │      │ User Profile│      │ Connection  │
│  Request    │ ──▶  │   List      │ ──▶  │    View     │ ──▶  │  Actions    │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
       │                    │                    │                    │
       ▼                    ▼                    ▼                    ▼
┌─────────────┐      ┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│GET /student/│      │Process      │      │GET /student/│      │POST /request│
│friendship/  │      │Search       │      │profile/:id  │      │or other     │
│search?query │      │Results      │      │             │      │actions      │
└─────────────┘      └─────────────┘      └─────────────┘      └─────────────┘
```

**Connection States:**
1. Not connected: No friendship exists
2. Request sent: Pending request from current user
3. Request received: Pending request to current user
4. Connected: Friendship established
5. Diary access granted: Friend with diary access permission

## API Endpoints

### Student Search

#### Search for Students

```
GET /api/student/friendship/search
```

Query Parameters:
- `query` (string): Search term
- `type` (string, optional): Search type (id, name, email, phone)
- `page` (number, default: 1): Page number
- `limit` (number, default: 10): Items per page

Response:
```json
{
  "items": [
    {
      "id": "user-id",
      "userId": "USER123",
      "name": "Student Name",
      "profilePicture": "https://example.com/profile.jpg",
      "friendshipStatus": "none|pending|accepted|rejected",
      "isFriend": false,
      "canViewDiary": false
    }
  ],
  "totalCount": 100,
  "totalItems": 100,
  "itemsPerPage": 10,
  "currentPage": 1,
  "totalPages": 10
}
```

### Friend Requests

#### Send Friend Request

```
POST /api/student/friendship/request
```

Request Body:
```json
{
  "requestedId": "user-id",
  "requestMessage": "Hi, I'd like to be friends!"
}
```

Response:
```json
{
  "id": "friendship-id",
  "requesterId": "requester-id",
  "requesterName": "Requester Name",
  "requesterProfilePicture": "https://example.com/profile.jpg",
  "requestedId": "requested-id",
  "requestedName": "Requested Name",
  "requestedProfilePicture": "https://example.com/profile.jpg",
  "status": "pending",
  "requestMessage": "Hi, I'd like to be friends!",
  "canViewDiary": false,
  "createdAt": "2023-01-01T00:00:00Z"
}
```

#### Get Pending Friend Requests

```
GET /api/student/friendship/pending
```

Query Parameters:
- `type` (string, optional): Request type (sent, received)
- `page` (number, default: 1): Page number
- `limit` (number, default: 10): Items per page

Response:
```json
{
  "items": [
    {
      "id": "friendship-id",
      "requesterId": "requester-id",
      "requesterName": "Requester Name",
      "requesterProfilePicture": "https://example.com/profile.jpg",
      "requestedId": "requested-id",
      "requestedName": "Requested Name",
      "requestedProfilePicture": "https://example.com/profile.jpg",
      "status": "pending",
      "requestMessage": "Hi, I'd like to be friends!",
      "canViewDiary": false,
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ],
  "totalCount": 5,
  "totalItems": 5,
  "itemsPerPage": 10,
  "currentPage": 1,
  "totalPages": 1
}
```

#### Respond to Friend Request

```
POST /api/student/friendship/respond
```

Request Body:
```json
{
  "requestId": "friendship-id",
  "status": "accepted|rejected"
}
```

Response:
```json
{
  "id": "friendship-id",
  "requesterId": "requester-id",
  "requesterName": "Requester Name",
  "requesterProfilePicture": "https://example.com/profile.jpg",
  "requestedId": "requested-id",
  "requestedName": "Requested Name",
  "requestedProfilePicture": "https://example.com/profile.jpg",
  "status": "accepted|rejected",
  "requestMessage": "Hi, I'd like to be friends!",
  "canViewDiary": false,
  "createdAt": "2023-01-01T00:00:00Z"
}
```

### Diary Follow Requests

#### Send Diary Follow Request

```
POST /api/student/friendship/diary-follow/request
```

Request Body:
```json
{
  "diaryOwnerId": "user-id",
  "requestMessage": "I'd like to follow your diary!"
}
```

Response:
```json
{
  "id": "request-id",
  "requesterId": "requester-id",
  "requesterName": "Requester Name",
  "requesterProfilePicture": "https://example.com/profile.jpg",
  "diaryOwnerId": "diary-owner-id",
  "diaryOwnerName": "Diary Owner Name",
  "diaryOwnerProfilePicture": "https://example.com/profile.jpg",
  "status": "pending",
  "requestMessage": "I'd like to follow your diary!",
  "createdAt": "2023-01-01T00:00:00Z"
}
```

#### Get Pending Diary Follow Requests

```
GET /api/student/friendship/diary-follow/pending
```

Query Parameters:
- `type` (string, optional): Request type (sent, received)
- `page` (number, default: 1): Page number
- `limit` (number, default: 10): Items per page

Response:
```json
{
  "items": [
    {
      "id": "request-id",
      "requesterId": "requester-id",
      "requesterName": "Requester Name",
      "requesterProfilePicture": "https://example.com/profile.jpg",
      "diaryOwnerId": "diary-owner-id",
      "diaryOwnerName": "Diary Owner Name",
      "diaryOwnerProfilePicture": "https://example.com/profile.jpg",
      "status": "pending",
      "requestMessage": "I'd like to follow your diary!",
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ],
  "totalCount": 5,
  "totalItems": 5,
  "itemsPerPage": 10,
  "currentPage": 1,
  "totalPages": 1
}
```

#### Respond to Diary Follow Request

```
POST /api/student/friendship/diary-follow/respond
```

Request Body:
```json
{
  "requestId": "request-id",
  "status": "accepted|rejected"
}
```

Response:
```json
{
  "id": "request-id",
  "requesterId": "requester-id",
  "requesterName": "Requester Name",
  "requesterProfilePicture": "https://example.com/profile.jpg",
  "diaryOwnerId": "diary-owner-id",
  "diaryOwnerName": "Diary Owner Name",
  "diaryOwnerProfilePicture": "https://example.com/profile.jpg",
  "status": "accepted|rejected",
  "requestMessage": "I'd like to follow your diary!",
  "createdAt": "2023-01-01T00:00:00Z"
}
```

### Friendship Management

#### Get Friends List

```
GET /api/student/friendship/friends
```

Query Parameters:
- `query` (string, optional): Search term to filter friends
- `page` (number, default: 1): Page number
- `limit` (number, default: 10): Items per page

Response:
```json
{
  "items": [
    {
      "id": "user-id",
      "userId": "USER123",
      "name": "Friend Name",
      "profilePicture": "https://example.com/profile.jpg",
      "friendshipStatus": "accepted",
      "isFriend": true,
      "canViewDiary": true,
      "friendshipId": "friendship-id",
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ],
  "totalCount": 20,
  "totalItems": 20,
  "itemsPerPage": 10,
  "currentPage": 1,
  "totalPages": 2
}
```

#### Get Diary Followers

```
GET /api/student/friendship/diary-followers
```

Query Parameters:
- `query` (string, optional): Search term to filter followers
- `page` (number, default: 1): Page number
- `limit` (number, default: 10): Items per page

Response:
```json
{
  "items": [
    {
      "id": "user-id",
      "userId": "USER123",
      "name": "Follower Name",
      "profilePicture": "https://example.com/profile.jpg",
      "friendshipStatus": "accepted",
      "isFriend": true,
      "canViewDiary": true,
      "friendshipId": "friendship-id",
      "createdAt": "2023-01-01T00:00:00Z"
    }
  ],
  "totalCount": 15,
  "totalItems": 15,
  "itemsPerPage": 10,
  "currentPage": 1,
  "totalPages": 2
}
```

#### Update Diary Access

```
PATCH /api/student/friendship/:friendshipId/diary-access
```

Request Body:
```json
{
  "canViewDiary": true|false
}
```

Response:
```json
{
  "id": "friendship-id",
  "requesterId": "requester-id",
  "requesterName": "Requester Name",
  "requesterProfilePicture": "https://example.com/profile.jpg",
  "requestedId": "requested-id",
  "requestedName": "Requested Name",
  "requestedProfilePicture": "https://example.com/profile.jpg",
  "status": "accepted",
  "canViewDiary": true|false,
  "createdAt": "2023-01-01T00:00:00Z"
}
```

## Notifications

The system automatically sends notifications for the following events:

1. **Friend Request Received**: When a student receives a friend request
2. **Friend Request Accepted/Rejected**: When a friend request is accepted or rejected
3. **Diary Follow Request Received**: When a student receives a diary follow request
4. **Diary Follow Request Accepted/Rejected**: When a diary follow request is accepted or rejected

Notifications are delivered through:
- In-app notifications (visible in the notification center)
- Push notifications (if the user has enabled them)

## Chat Integration

When students become friends, a chat conversation is automatically created between them. To access the chat:

1. Use the Chat API to get the conversation between two users:
```
GET /api/chat/conversations/with/:userId
```

2. Use the WebSocket connection to send and receive messages:
```javascript
// Connect to the WebSocket server
const socket = io('http://**************:3010', {
  query: {
    token: 'your-jwt-token'
  }
});

// Join a conversation
socket.emit('joinConversation', { conversationId: 'conversation-id' });

// Send a message
socket.emit('sendMessage', {
  conversationId: 'conversation-id',
  content: 'Hello!',
  contentType: 'text'
});

// Receive messages
socket.on('receiveMessage', (message) => {
  console.log('New message:', message);
});
```

## QR Code Sharing

To generate a QR code for sharing a diary entry:

```
GET /api/diary/entries/:entryId/qr-code
```

Response:
```json
{
  "qrCodeUrl": "https://example.com/qr-code.png",
  "shareUrl": "https://example.com/shared/diary/abc123"
}
```

The QR code URL can be used to display the QR code image.


## Error Handling

| Error Code | Description | API Response |
|------------|-------------|--------------|
| 400 | Bad Request - Invalid input parameters | Returns validation errors in the response body |
| 401 | Unauthorized - Invalid or expired token | Returns authentication error |
| 403 | Forbidden - User doesn't have permission | Returns permission error |
| 404 | Not Found - Resource not found | Returns not found error with details |
| 409 | Conflict - Request already exists | Returns conflict error with details |
| 429 | Too Many Requests - Rate limit exceeded | Returns rate limit information |
| 500 | Server Error | Returns server error |

### Common Error Scenarios

1. **Sending a friend request to a user who already sent you a request**
   - Error: 409 Conflict
   - Response includes the existing request details

2. **Sending a diary follow request to a non-friend**
   - Error: 403 Forbidden
   - Response indicates friendship is required

3. **Accepting a request that was canceled or already processed**
   - Error: 404 Not Found
   - Response indicates the request no longer exists

4. **Rate limiting on search or requests**
   - Error: 429 Too Many Requests
   - Response includes retry-after information
