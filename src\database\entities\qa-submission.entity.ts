import { <PERSON><PERSON><PERSON>, Column, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { QAAssignment } from './qa-assignment.entity';
import { User } from './user.entity';
import { QAAssignmentSets } from './qa-assignment-sets.entity';

export enum QASubmissionStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  REVIEWED = 'reviewed'
}

@Entity()
export class QASubmission extends AuditableBaseEntity {
  @Column({ name: 'assignment_id', nullable: true })
  assignmentId: string;

  @OneToOne(() => QAAssignment)
  @JoinColumn({ name: 'assignment_id' })
  assignment: QAAssignment;

  @Column({ type: 'text' })
  answer: string;

  @Column({nullable: true})
  points: number;

  @Column({
    type: 'enum',
    enum: QASubmissionStatus,
    default: QASubmissionStatus.DRAFT
  })
  status: QASubmissionStatus;

  @Column({ name: 'submission_date', type: 'timestamp', nullable: true })
  submissionDate: Date;

  @Column({ type: 'text', nullable: true })
  feedback: string;

  @Column({ type: 'jsonb', nullable: true })
  corrections: any;

  @Column({ name: 'reviewed_at', type: 'timestamp', nullable: true })
  reviewedAt: Date;

  @Column({ name: 'reviewed_by', nullable: true })
  reviewedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'reviewed_by' })
  reviewer: User;

  @Column({ name: 'set_sequence' })
  setSequence: number;

  @ManyToOne(() => QAAssignmentSets, { eager: true })
  @JoinColumn({ name: 'set_sequence', referencedColumnName: 'setSequence' })
  assignmentSet: QAAssignmentSets;
  // @ManyToOne(() => User)
  // @JoinColumn({ name: 'student_id' })
  // student: User;

  // @Column()
  // studentId: string;
}
