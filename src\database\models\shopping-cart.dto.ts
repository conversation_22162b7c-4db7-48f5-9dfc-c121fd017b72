import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID, IsInt, IsOptional, IsEnum, Min, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { ShoppingCartStatus } from '../entities/shopping-cart.entity';
import { PaymentMethod } from '../entities/shop-item-purchase.entity';

/**
 * DTO for adding an item to the shopping cart
 */
export class AddToCartDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Shop item ID to add to cart'
  })
  @IsNotEmpty()
  @IsUUID()
  shopItemId: string;

  @ApiProperty({
    example: 1,
    description: 'Quantity to add',
    default: 1
  })
  @IsOptional()
  @IsInt()
  @Min(1, { message: 'Quantity must be at least 1' })
  @Type(() => Number)
  quantity?: number;
}

/**
 * DTO for updating a cart item
 */
export class UpdateCartItemDto {
  @ApiProperty({
    example: 2,
    description: 'New quantity for the item'
  })
  @IsNotEmpty()
  @IsInt()
  @Min(1, { message: 'Quantity must be at least 1' })
  @Type(() => Number)
  quantity: number;
}

/**
 * DTO for cart item response
 */
export class CartItemResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  shopItemId: string;

  @ApiProperty()
  title: string;

  @ApiProperty({
    description: 'Original price before any discounts'
  })
  originalPrice: number;

  @ApiProperty({
    description: 'Final price after any discounts'
  })
  price: number;

  @ApiProperty({
    description: 'Discount percentage (0 if not on sale)'
  })
  discountPercentage: number;

  @ApiProperty({
    description: 'Whether the item is on sale'
  })
  isOnSale: boolean;

  @ApiProperty({
    description: 'Promotion ID if applicable',
    required: false,
    nullable: true
  })
  promotionId: string | null;

  @ApiProperty()
  rewardPoints: number;

  @ApiProperty()
  quantity: number;

  @ApiProperty()
  totalPrice: number;

  @ApiProperty()
  totalRewardPoints: number;

  @ApiProperty({ required: false })
  imageUrl?: string;

  @ApiProperty({ required: false })
  categoryId?: string;

  @ApiProperty({ required: false })
  categoryName?: string;
}

/**
 * DTO for shopping cart response
 */
export class ShoppingCartResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty({ enum: ShoppingCartStatus })
  status: ShoppingCartStatus;

  @ApiProperty()
  lastActivity: Date;

  @ApiProperty({ type: [CartItemResponseDto] })
  items: CartItemResponseDto[];

  @ApiProperty()
  totalPrice: number;

  @ApiProperty()
  totalRewardPoints: number;

  @ApiProperty()
  itemCount: number;
}

/**
 * DTO for checkout
 */
export class CheckoutDto {
  @ApiProperty({
    example: PaymentMethod.REWARD_POINTS,
    description: 'Payment method',
    enum: PaymentMethod
  })
  @IsNotEmpty()
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({
    example: true,
    description: 'Whether to use reward points for the purchase',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  useRewardPoints?: boolean;

  @ApiProperty({
    example: { cardToken: 'tok_visa' },
    description: 'Payment details (required for credit card payments)',
    required: false
  })
  @IsOptional()
  paymentDetails?: any;

  @ApiProperty({
    example: 'https://example.com/payment/success',
    description: 'Return URL after successful payment',
    required: false
  })
  @IsOptional()
  @IsString()
  returnUrl?: string;

  @ApiProperty({
    example: 'https://example.com/payment/cancel',
    description: 'Return URL after cancelled payment',
    required: false
  })
  @IsOptional()
  @IsString()
  cancelUrl?: string;

  @ApiProperty({
    example: 'SUMMER20',
    description: 'Promotion code to apply to the checkout',
    required: false
  })
  @IsOptional()
  @IsString()
  promoCode?: string;
}

/**
 * DTO for reward point information in checkout response
 */
export class RewardPointInfoDto {
  @ApiProperty({
    description: 'The conversion rate used for reward points (points per unit of currency)'
  })
  conversionRate: number;

  @ApiProperty({
    description: 'Total reward points for the purchase'
  })
  totalRewardPoints: number;

  @ApiProperty({
    description: 'Informational message about reward points calculation'
  })
  message: string;
}

/**
 * DTO for checkout response
 */
export class CheckoutResponseDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  orderId: string;

  @ApiProperty()
  totalAmount: number;

  @ApiProperty()
  rewardPointsUsed: number;

  @ApiProperty()
  remainingRewardPoints: number;

  @ApiProperty({ enum: PaymentMethod })
  paymentMethod: PaymentMethod;

  @ApiProperty()
  purchaseDate: Date;

  @ApiProperty({ type: [CartItemResponseDto] })
  items: CartItemResponseDto[];

  @ApiProperty({
    type: RewardPointInfoDto,
    description: 'Information about reward points calculation'
  })
  rewardPointInfo: RewardPointInfoDto;

  @ApiProperty({
    required: false,
    description: 'Information about applied promo code'
  })
  promoCodeInfo?: {
    code: string;
    applied: boolean;
    message?: string;
  };

  @ApiProperty({
    required: false,
    description: 'Payment transaction ID if payment gateway was used'
  })
  paymentTransactionId?: string;

  @ApiProperty({
    required: false,
    description: 'Payment URL for gateway payments'
  })
  paymentUrl?: string;
}
