import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { ShopItem } from './shop-item.entity';
import { DiarySkin } from './diary-skin.entity';

/**
 * Entity to map shop items to diary skins
 * This creates a bridge between the shop system and the diary skin system
 */
@Entity()
export class ShopSkinMapping extends AuditableBaseEntity {
  @Column({ name: 'shop_item_id' })
  shopItemId: string;

  @ManyToOne(() => ShopItem, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'shop_item_id' })
  shopItem: ShopItem;

  @Column({ name: 'diary_skin_id' })
  diarySkinId: string;

  @ManyToOne(() => DiarySkin, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'diary_skin_id' })
  diarySkin: DiarySkin;
}
