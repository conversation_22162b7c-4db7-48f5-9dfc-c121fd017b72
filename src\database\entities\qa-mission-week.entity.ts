import { BaseEntity, Column, <PERSON>tity, PrimaryGeneratedColumn } from "typeorm";

@Entity()
export class QAMissionWeek extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column({ type: 'int' })
  sequence: number;

  @Column()
  month: string;

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date' })
  endDate: Date;

  @Column({ type: 'int' })
  year: number;
}
