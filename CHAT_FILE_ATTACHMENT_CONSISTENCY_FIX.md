# Chat File Attachment Consistency Fix

## Problem Analysis

The chat system's file attachment URLs are inconsistent with the FileRegistry system:

### Current Issues:
1. **Inconsistent URL patterns**:
   - Chat: `http://localhost:3000/chat/files/995b02a7-dc9f-4b42-9767-3ac4365c0e73`
   - FileRegistry: `http://localhost:3012/media/profile-pictures/user-id?v=timestamp`

2. **Missing FileEntityType support**:
   - `MESSAGE_ATTACHMENT` exists in enum but not implemented in FileRegistryService

3. **Manual URL generation**:
   - Chat service manually constructs URLs instead of using FileRegistryService

4. **Storage provider inconsistency**:
   - Chat doesn't leverage S3 support from FileRegistry system

## Solution: Integrate Chat with FileRegistry System

### 1. Update FileEntityType Enum

**File: `src/common/enums/file-entity-type.enum.ts`**

```typescript
/**
 * Enum representing different entity types for file registry
 */
export enum FileEntityType {
  PROFILE_PICTURE = 'profile_picture',
  SHOP_ITEM = 'shop_item',
  DIARY_SKIN = 'diary_skin',
  DIARY_QR = 'diary_qr',
  STORY_MAKER = 'story_maker',
  MESSAGE_ATTACHMENT = 'message_attachment'  // Add this
}
```

### 2. Extend FileRegistryService for Message Attachments

**File: `src/common/services/file-registry.service.ts`**

Add these methods to the FileRegistryService class:

```typescript
// Add to imports
import { MessageRegistry } from '../../database/entities/message-registry.entity';

// Add to constructor injection
@InjectRepository(MessageRegistry)
private readonly messageRegistryRepository: Repository<MessageRegistry>,

/**
 * Get message attachment file URL
 * @param attachmentId Message attachment ID (from MessageRegistry)
 * @returns File URL
 */
async getMessageAttachmentUrl(attachmentId: string): Promise<string> {
  try {
    // Check if we're using S3 storage
    if (this.storageConfigService.isS3Provider()) {
      return await this.getS3FileUrl(FileEntityType.MESSAGE_ATTACHMENT, attachmentId);
    }

    // Use local storage URL generation
    const baseUrl = this.fileUtilService.getBaseUrl();
    const timestamp = Date.now();
    return `${baseUrl}/media/message-attachments/${attachmentId}?v=${timestamp}`;
  } catch (error) {
    this.logger.error(`Error getting message attachment URL: ${error.message}`);
    throw error;
  }
}

/**
 * Get message attachment file by registry ID
 * @param registryId Registry ID
 * @returns Registry entry or null if not found
 */
private async getMessageAttachmentFile(registryId: string): Promise<MessageRegistry> {
  try {
    const registry = await this.messageRegistryRepository.findOne({
      where: { id: registryId }
    });

    if (registry) {
      return registry;
    }

    this.logger.warn(`No message attachment registry entry found for ID: ${registryId}`);
    return null;
  } catch (error) {
    this.logger.error(`Error getting message attachment file: ${error.message}`);
    return null;
  }
}

/**
 * Register message attachment with S3 metadata
 */
private async registerMessageAttachmentWithS3(
  registryId: string, 
  uploadResult: any, 
  file: any, 
  options?: any
): Promise<MessageRegistry> {
  try {
    let registry = await this.messageRegistryRepository.findOne({
      where: { id: registryId }
    });

    if (!registry) {
      registry = this.messageRegistryRepository.create({
        userId: options?.userId,
        messageId: options?.messageId,
        filePath: uploadResult.key,
        fileName: file.originalname,
        mimeType: file.mimetype,
        fileSize: file.size,
        isTemporary: options?.isTemporary || true,
        storageProvider: StorageProvider.S3,
        storageKey: uploadResult.key,
        cdnUrl: uploadResult.cdnUrl,
        storageMetadata: uploadResult.metadata
      });
    } else {
      // Update existing registry
      registry.filePath = uploadResult.key;
      registry.fileName = file.originalname;
      registry.mimeType = file.mimetype;
      registry.fileSize = file.size;
      registry.storageProvider = StorageProvider.S3;
      registry.storageKey = uploadResult.key;
      registry.cdnUrl = uploadResult.cdnUrl;
      registry.storageMetadata = uploadResult.metadata;
    }

    return await this.messageRegistryRepository.save(registry);
  } catch (error) {
    this.logger.error(`Error registering message attachment with S3: ${error.message}`);
    throw error;
  }
}
```

### 3. Update FileRegistryService Switch Statements

Add `MESSAGE_ATTACHMENT` case to all switch statements in FileRegistryService:

```typescript
// In getFileUrl method
case FileEntityType.MESSAGE_ATTACHMENT:
  return `${baseUrl}/media/message-attachments/${entityId}?v=${timestamp}`;

// In getLocalFileUrl method  
case FileEntityType.MESSAGE_ATTACHMENT:
  return `${baseUrl}/media/message-attachments/${entityId}?v=${timestamp}`;

// In getFile method
case FileEntityType.MESSAGE_ATTACHMENT:
  result = await this.getMessageAttachmentFile(entityId);
  break;

// In registerFile method
case FileEntityType.MESSAGE_ATTACHMENT:
  return await this.registerMessageAttachment(entityId, filePath, fileName, mimeType, fileSize, userId);

// In uploadFile method
case FileEntityType.MESSAGE_ATTACHMENT:
  return await this.uploadMessageAttachment(file, referenceId, entityId);

// In registerFileWithS3Metadata method
case FileEntityType.MESSAGE_ATTACHMENT:
  registry = await this.registerMessageAttachmentWithS3(entityId, uploadResult, file, options);
  break;
```

### 4. Update Chat Service to Use FileRegistryService

**File: `src/modules/chat/chat.service.ts`**

```typescript
// Add to imports
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';

// Add to constructor
constructor(
  // ... existing injections
  private readonly fileRegistryService: FileRegistryService,
) {}

/**
 * Map message entity to DTO with consistent file URLs
 * @param message Message entity
 * @returns Message DTO
 */
private async mapMessageToDto(message: Message): Promise<MessageDto> {
  const sender = message.sender;

  const attachments = await Promise.all(
    message.attachments?.map(async (attachment) => {
      try {
        // Use FileRegistryService for consistent URL generation
        const fileUrl = await this.fileRegistryService.getMessageAttachmentUrl(attachment.id);
        const thumbnailUrl = attachment.thumbnailPath
          ? await this.fileRegistryService.getMessageAttachmentUrl(attachment.id) // Could be separate thumbnail endpoint
          : undefined;

        return {
          id: attachment.id,
          filePath: attachment.filePath,
          fileName: attachment.fileName,
          mimeType: attachment.mimeType,
          fileSize: attachment.fileSize,
          thumbnailPath: attachment.thumbnailPath,
          fileUrl,
          thumbnailUrl
        };
      } catch (error) {
        this.logger.error(`Error generating URL for attachment ${attachment.id}: ${error.message}`);
        // Fallback to original URL generation
        const baseUrl = this.configService.get<string>('BASE_URL') || 'http://localhost:3000';
        return {
          id: attachment.id,
          filePath: attachment.filePath,
          fileName: attachment.fileName,
          mimeType: attachment.mimeType,
          fileSize: attachment.fileSize,
          thumbnailPath: attachment.thumbnailPath,
          fileUrl: `${baseUrl}/chat/files/${attachment.id}`,
          thumbnailUrl: attachment.thumbnailPath ? `${baseUrl}/chat/thumbnails/${attachment.id}` : undefined
        };
      }
    }) || []
  );

  return {
    id: message.id,
    conversationId: message.conversationId,
    senderId: message.senderId,
    senderName: sender?.name || 'Unknown',
    senderProfilePicture: sender?.profilePicture,
    recipientId: message.recipientId,
    type: message.type,
    content: message.content,
    status: message.status,
    readAt: message.readAt,
    deliveredAt: message.deliveredAt,
    metadata: message.metadata,
    attachments,
    createdAt: message.createdAt
  };
}

/**
 * Upload file using FileRegistryService for consistency
 * @param file File to upload
 * @param userId User ID
 * @returns Upload result with consistent URL
 */
async uploadFile(file: Express.Multer.File, userId: string): Promise<any> {
  try {
    // Use FileRegistryService for consistent upload handling
    const result = await this.fileRegistryService.uploadFile(
      FileEntityType.MESSAGE_ATTACHMENT,
      file,
      userId,
      { userId, isTemporary: true }
    );

    // Generate consistent URL
    const fileUrl = await this.fileRegistryService.getMessageAttachmentUrl(result.registry.id);

    return {
      id: result.registry.id,
      filePath: result.registry.filePath,
      fileName: result.registry.fileName,
      mimeType: result.registry.mimeType,
      fileSize: result.registry.fileSize,
      fileUrl
    };
  } catch (error) {
    this.logger.error(`Error uploading file: ${error.message}`, error.stack);
    throw error;
  }
}
```

### 5. Add Media Controller Route for Message Attachments

**File: `src/modules/media/media.controller.ts`** (or create if doesn't exist)

```typescript
@Get('message-attachments/:id')
@ApiOperation({ summary: 'Get message attachment file' })
@ApiParam({ name: 'id', description: 'Message attachment registry ID' })
async getMessageAttachment(
  @Param('id') id: string,
  @Res({ passthrough: true }) res: Response
): Promise<StreamableFile> {
  const { buffer, fileName, mimeType } = await this.fileRegistryService.getFileBuffer(
    FileEntityType.MESSAGE_ATTACHMENT, 
    id
  );

  res.set({
    'Content-Type': mimeType,
    'Content-Disposition': `attachment; filename="${encodeURIComponent(fileName)}"`,
  });

  return new StreamableFile(buffer);
}
```

## Benefits of This Fix

1. **Consistent URL patterns** across all file types
2. **Automatic S3 support** for message attachments
3. **Centralized file management** through FileRegistryService
4. **Cache-busting** with version parameters
5. **Proper error handling** and fallbacks
6. **Storage provider abstraction** (local/S3)

## Migration Strategy

1. **Phase 1**: Implement FileRegistryService extensions
2. **Phase 2**: Update chat service to use new methods
3. **Phase 3**: Add media controller routes
4. **Phase 4**: Test with both local and S3 storage
5. **Phase 5**: Update frontend to handle new URL patterns

This fix ensures that chat file attachments follow the same patterns and conventions as all other file types in the system.
