import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPromotionActiveStatusToShopItems1684500699132 implements MigrationInterface {    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shop_item" ADD "is_promotion_active" boolean NOT NULL DEFAULT true`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "shop_item" DROP COLUMN "is_promotion_active"`);
    }
}
