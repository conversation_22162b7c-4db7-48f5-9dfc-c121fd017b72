import { Injectable, Logger, NotFoundException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { PaymentTransaction, PaymentTransactionStatus, PurchaseType, KcpPaymentMethod } from '../../../database/entities/payment-transaction.entity';
import { PaymentWebhook, WebhookStatus, WebhookType } from '../../../database/entities/payment-webhook.entity';
import { ShopItemPurchase, PaymentMethod, PurchaseStatus } from '../../../database/entities/shop-item-purchase.entity';
import { UserPlan } from '../../../database/entities/user-plan.entity';
import { User } from '../../../database/entities/user.entity';
import { KcpService } from './kcp.service';
import { 
  InitiatePaymentDto, 
  PaymentInitiationResponseDto, 
  PaymentStatusResponseDto,
  ProcessPaymentDto,
  WebhookPayloadDto,
  RefundRequestDto,
  RefundResponseDto
} from '../dto/payment.dto';
import { PaymentInitiationRequest } from '../interfaces/kcp.interface';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(
    @InjectRepository(PaymentTransaction)
    private readonly paymentTransactionRepository: Repository<PaymentTransaction>,
    @InjectRepository(PaymentWebhook)
    private readonly paymentWebhookRepository: Repository<PaymentWebhook>,
    @InjectRepository(ShopItemPurchase)
    private readonly shopItemPurchaseRepository: Repository<ShopItemPurchase>,
    @InjectRepository(UserPlan)
    private readonly userPlanRepository: Repository<UserPlan>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly kcpService: KcpService,
    private readonly dataSource: DataSource
  ) {}

  /**
   * Initiate payment process
   */
  async initiatePayment(userId: string, initiatePaymentDto: InitiatePaymentDto): Promise<PaymentInitiationResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Initiating payment for user ${userId}, order ${initiatePaymentDto.orderId}`);

      // Validate user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Generate unique transaction ID
      const transactionId = this.generateTransactionId();

      // Create payment transaction record
      const paymentTransaction = this.paymentTransactionRepository.create({
        transactionId,
        orderId: initiatePaymentDto.orderId,
        amount: initiatePaymentDto.amount,
        currency: initiatePaymentDto.currency || 'KRW',
        paymentMethod: initiatePaymentDto.paymentMethod as KcpPaymentMethod,
        status: PaymentTransactionStatus.INITIATED,
        userId,
        purchaseType: initiatePaymentDto.purchaseType as PurchaseType,
        referenceId: initiatePaymentDto.referenceId,
        requestData: initiatePaymentDto,
        expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
      });

      const savedTransaction = await queryRunner.manager.save(paymentTransaction);

      // Prepare KCP payment request
      const kcpRequest: PaymentInitiationRequest = {
        orderId: initiatePaymentDto.orderId,
        amount: initiatePaymentDto.amount,
        currency: initiatePaymentDto.currency || 'KRW',
        productName: initiatePaymentDto.productName,
        buyerName: initiatePaymentDto.buyerName,
        buyerEmail: initiatePaymentDto.buyerEmail,
        buyerPhone: initiatePaymentDto.buyerPhone,
        paymentMethod: initiatePaymentDto.paymentMethod,
        returnUrl: initiatePaymentDto.returnUrl,
        cancelUrl: initiatePaymentDto.cancelUrl,
        userId,
        purchaseType: initiatePaymentDto.purchaseType,
        referenceId: initiatePaymentDto.referenceId,
        metadata: initiatePaymentDto.metadata
      };

      // Initiate payment with KCP
      const kcpResponse = await this.kcpService.initiatePayment(kcpRequest);

      if (kcpResponse.success) {
        // Update transaction with KCP response
        savedTransaction.kcpTransactionId = kcpResponse.transactionId;
        savedTransaction.status = PaymentTransactionStatus.PENDING;
        savedTransaction.responseData = kcpResponse;
        await queryRunner.manager.save(savedTransaction);

        await queryRunner.commitTransaction();

        this.logger.log(`Payment initiated successfully: ${transactionId}`);
        return {
          success: true,
          transactionId,
          paymentUrl: kcpResponse.paymentUrl,
          redirectUrl: kcpResponse.redirectUrl,
          message: 'Payment initiated successfully',
          expiresAt: kcpResponse.expiresAt
        };
      } else {
        // Update transaction with error
        savedTransaction.status = PaymentTransactionStatus.FAILED;
        savedTransaction.errorMessage = kcpResponse.message;
        savedTransaction.responseData = kcpResponse;
        await queryRunner.manager.save(savedTransaction);

        await queryRunner.commitTransaction();

        return {
          success: false,
          transactionId,
          paymentUrl: undefined,
          redirectUrl: undefined,
          message: kcpResponse.message || 'Payment initiation failed',
          errorCode: kcpResponse.errorCode
        };
      }

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Payment initiation failed: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to initiate payment');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Process payment completion
   */
  async processPayment(processPaymentDto: ProcessPaymentDto): Promise<PaymentStatusResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      this.logger.log(`Processing payment completion: ${processPaymentDto.transactionId}`);

      // Find payment transaction
      const transaction = await this.paymentTransactionRepository.findOne({
        where: { transactionId: processPaymentDto.transactionId }
      });

      if (!transaction) {
        throw new NotFoundException(`Payment transaction ${processPaymentDto.transactionId} not found`);
      }

      if (transaction.isFinalState()) {
        throw new BadRequestException(`Payment transaction ${processPaymentDto.transactionId} is already in final state`);
      }

      // Update transaction status
      transaction.status = PaymentTransactionStatus.PROCESSING;
      transaction.processedAt = new Date();
      await queryRunner.manager.save(transaction);

      // Verify payment with KCP (simulate for now)
      const isValid = await this.kcpService.verifyPayment(processPaymentDto.additionalData);

      if (isValid) {
        // Mark transaction as completed
        transaction.status = PaymentTransactionStatus.COMPLETED;
        transaction.kcpApprovalTime = new Date();
        transaction.responseData = { ...transaction.responseData, ...processPaymentDto.additionalData };
        await queryRunner.manager.save(transaction);

        // Complete the purchase based on type
        if (transaction.purchaseType === PurchaseType.SHOP_ITEM) {
          await this.completeShopItemPurchase(queryRunner, transaction);
        } else if (transaction.purchaseType === PurchaseType.PLAN) {
          await this.completePlanSubscription(queryRunner, transaction);
        }

        await queryRunner.commitTransaction();

        this.logger.log(`Payment completed successfully: ${transaction.transactionId}`);
        return this.mapToStatusResponse(transaction);
      } else {
        // Mark transaction as failed
        transaction.status = PaymentTransactionStatus.FAILED;
        transaction.errorMessage = 'Payment verification failed';
        await queryRunner.manager.save(transaction);

        await queryRunner.commitTransaction();

        return this.mapToStatusResponse(transaction);
      }

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Payment processing failed: ${error.message}`, error.stack);
      throw new InternalServerErrorException('Failed to process payment');
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(transactionId: string): Promise<PaymentStatusResponseDto> {
    const transaction = await this.paymentTransactionRepository.findOne({
      where: { transactionId }
    });

    if (!transaction) {
      throw new NotFoundException(`Payment transaction ${transactionId} not found`);
    }

    return this.mapToStatusResponse(transaction);
  }

  /**
   * Process webhook
   */
  async processWebhook(payload: WebhookPayloadDto, signature: string, sourceIp: string): Promise<void> {
    try {
      this.logger.log(`Processing webhook for order: ${payload.ordr_idxx}`);

      // Validate webhook signature
      const isValidSignature = this.kcpService.validateWebhookSignature(JSON.stringify(payload), signature);
      if (!isValidSignature) {
        this.logger.warn(`Invalid webhook signature for order: ${payload.ordr_idxx}`);
        return;
      }

      // Create webhook record
      const webhook = this.paymentWebhookRepository.create({
        transactionId: payload.tno,
        webhookType: this.determineWebhookType(payload),
        status: WebhookStatus.RECEIVED,
        payload,
        signature,
        sourceIp
      });

      await this.paymentWebhookRepository.save(webhook);

      // Process webhook based on type
      await this.handleWebhookPayload(webhook);

    } catch (error) {
      this.logger.error(`Webhook processing failed: ${error.message}`, error.stack);
    }
  }

  /**
   * Generate unique transaction ID
   */
  private generateTransactionId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `TXN-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Map transaction to status response
   */
  private mapToStatusResponse(transaction: PaymentTransaction): PaymentStatusResponseDto {
    return {
      transactionId: transaction.transactionId,
      status: transaction.status,
      amount: transaction.amount,
      currency: transaction.currency,
      paymentMethod: transaction.paymentMethod,
      completedAt: transaction.processedAt,
      errorMessage: transaction.errorMessage,
      kcpData: transaction.responseData
    };
  }

  /**
   * Determine webhook type from payload
   */
  private determineWebhookType(payload: WebhookPayloadDto): WebhookType {
    if (payload.res_cd === '0000') {
      return WebhookType.PAYMENT_COMPLETE;
    } else {
      return WebhookType.PAYMENT_FAILED;
    }
  }

  /**
   * Handle webhook payload processing
   */
  private async handleWebhookPayload(webhook: PaymentWebhook): Promise<void> {
    // Implementation for webhook processing
    // This would update payment transaction status based on webhook data
    webhook.markAsProcessed();
    await this.paymentWebhookRepository.save(webhook);
  }

  /**
   * Complete shop item purchase
   */
  private async completeShopItemPurchase(queryRunner: any, transaction: PaymentTransaction): Promise<void> {
    // Find and update shop item purchase
    const purchase = await queryRunner.manager.findOne(ShopItemPurchase, {
      where: { id: transaction.referenceId }
    });

    if (purchase) {
      purchase.status = PurchaseStatus.PAYMENT_CONFIRMED;
      purchase.paymentTransactionId = transaction.id;
      await queryRunner.manager.save(purchase);
    }
  }

  /**
   * Complete plan subscription
   */
  private async completePlanSubscription(queryRunner: any, transaction: PaymentTransaction): Promise<void> {
    // Find and update user plan
    const userPlan = await queryRunner.manager.findOne(UserPlan, {
      where: { id: transaction.referenceId }
    });

    if (userPlan) {
      userPlan.isPaid = true;
      userPlan.paymentTransactionId = transaction.id;
      await queryRunner.manager.save(userPlan);
    }
  }
}
