import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { ShopCategory } from '../../database/entities/shop-category.entity';
import { CreateShopCategoryDto, UpdateShopCategoryDto, ShopCategoryResponseDto } from '../../database/models/shop.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { SHOP_CONSTANTS } from './constants/shop-constants';

@Injectable()
export class ShopCategoryService {
  private readonly logger = new Logger(ShopCategoryService.name);

  constructor(
    @InjectRepository(ShopCategory)
    private shopCategoryRepository: Repository<ShopCategory>,
  ) {}

  /**
   * Create a new shop category
   * @param createShopCategoryDto Category data
   * @returns Created category
   */
  async createShopCategory(createShopCategoryDto: CreateShopCategoryDto): Promise<ShopCategoryResponseDto> {
    try {
      // Check if category with the same name already exists
      const existingCategory = await this.shopCategoryRepository.findOne({
        where: { name: createShopCategoryDto.name }
      });

      if (existingCategory) {
        throw new ConflictException(`Category with name '${createShopCategoryDto.name}' already exists`);
      }

      // Create and save the category
      const category = this.shopCategoryRepository.create({
        name: createShopCategoryDto.name,
        description: createShopCategoryDto.description,
        parentId: createShopCategoryDto.parentId,
        imageUrl: createShopCategoryDto.imageUrl,
        isActive: createShopCategoryDto.isActive !== undefined ? createShopCategoryDto.isActive : true,
        displayOrder: createShopCategoryDto.displayOrder || 0,
      });

      const savedCategory = await this.shopCategoryRepository.save(category);
      this.logger.log(`Created shop category with ID: ${savedCategory.id}`);

      return this.mapCategoryToDto(savedCategory);
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Error creating shop category: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create shop category: ${error.message}`);
    }
  }

  /**
   * Get all shop categories
   * @param includeInactive Whether to include inactive categories
   * @param parentId Optional parent ID to filter by
   * @param paginationDto Pagination parameters
   * @returns List of categories
   */
  async getAllShopCategories(
    includeInactive: boolean = false,
    parentId?: string,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<ShopCategoryResponseDto>> {
    try {
      const whereConditions: FindOptionsWhere<ShopCategory> = {};

      if (!includeInactive) {
        whereConditions.isActive = true;
      }

      if (parentId) {
        whereConditions.parentId = parentId;
      }

      // Apply pagination if provided
      const { page = 1, limit = 10 } = paginationDto || {};
      const skip = (page - 1) * limit;

      const [categories, totalCount] = await this.shopCategoryRepository.findAndCount({
        where: whereConditions,
        order: { displayOrder: 'ASC', name: 'ASC' },
        relations: ['parent'],
        skip: paginationDto ? skip : undefined,
        take: paginationDto ? limit : undefined
      });

      const dtos = categories.map(category => this.mapCategoryToDto(category));
      return new PagedListDto(dtos, totalCount);
    } catch (error) {
      this.logger.error(`Error getting shop categories: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get shop categories: ${error.message}`);
    }
  }

  /**
   * Get a shop category by ID
   * @param id Category ID
   * @returns Category details
   */
  async getShopCategoryById(id: string): Promise<ShopCategoryResponseDto> {
    try {
      const category = await this.shopCategoryRepository.findOne({
        where: { id },
        relations: ['parent']
      });

      if (!category) {
        throw new NotFoundException(`Shop category with ID ${id} not found`);
      }

      return this.mapCategoryToDto(category);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Error getting shop category: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get shop category: ${error.message}`);
    }
  }

  /**
   * Update a shop category
   * @param id Category ID
   * @param updateShopCategoryDto Update data
   * @returns Updated category
   */
  async updateShopCategory(id: string, updateShopCategoryDto: UpdateShopCategoryDto): Promise<ShopCategoryResponseDto> {
    try {
      const category = await this.shopCategoryRepository.findOne({
        where: { id }
      });

      if (!category) {
        throw new NotFoundException(`Shop category with ID ${id} not found`);
      }

      // Check if this is a protected category
      if (category.name === SHOP_CONSTANTS.PROTECTED_CATEGORIES.SKIN ||
          category.name === SHOP_CONSTANTS.PROTECTED_CATEGORIES.EMOTICON) {
        throw new ForbiddenException(`The '${category.name}' category is protected and cannot be modified`);
      }

      // Check if name is being changed and if it conflicts with an existing category
      if (updateShopCategoryDto.name && updateShopCategoryDto.name !== category.name) {
        // Check if trying to rename to a protected category name
        if (updateShopCategoryDto.name === SHOP_CONSTANTS.PROTECTED_CATEGORIES.SKIN ||
            updateShopCategoryDto.name === SHOP_CONSTANTS.PROTECTED_CATEGORIES.EMOTICON) {
          throw new ForbiddenException(`Cannot rename to '${updateShopCategoryDto.name}' as it is a protected category name`);
        }

        const existingCategory = await this.shopCategoryRepository.findOne({
          where: { name: updateShopCategoryDto.name }
        });

        if (existingCategory && existingCategory.id !== id) {
          throw new ConflictException(`Category with name '${updateShopCategoryDto.name}' already exists`);
        }
      }

      // Update fields
      if (updateShopCategoryDto.name !== undefined) {
        category.name = updateShopCategoryDto.name;
      }

      if (updateShopCategoryDto.description !== undefined) {
        category.description = updateShopCategoryDto.description;
      }

      if (updateShopCategoryDto.parentId !== undefined) {
        category.parentId = updateShopCategoryDto.parentId;
      }

      if (updateShopCategoryDto.imageUrl !== undefined) {
        category.imageUrl = updateShopCategoryDto.imageUrl;
      }

      if (updateShopCategoryDto.isActive !== undefined) {
        category.isActive = updateShopCategoryDto.isActive;
      }

      if (updateShopCategoryDto.displayOrder !== undefined) {
        category.displayOrder = updateShopCategoryDto.displayOrder;
      }

      const updatedCategory = await this.shopCategoryRepository.save(category);
      this.logger.log(`Updated shop category with ID: ${updatedCategory.id}`);

      // Fetch the updated category with relations
      const categoryWithRelations = await this.shopCategoryRepository.findOne({
        where: { id },
        relations: ['parent']
      });

      return this.mapCategoryToDto(categoryWithRelations);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof ConflictException || error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`Error updating shop category: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to update shop category: ${error.message}`);
    }
  }

  /**
   * Delete a shop category
   * @param id Category ID
   * @returns Success message
   */
  async deleteShopCategory(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const category = await this.shopCategoryRepository.findOne({
        where: { id },
        relations: ['shopItems']
      });

      if (!category) {
        throw new NotFoundException(`Shop category with ID ${id} not found`);
      }

      // Check if this is a protected category
      if (category.name === SHOP_CONSTANTS.PROTECTED_CATEGORIES.SKIN ||
          category.name === SHOP_CONSTANTS.PROTECTED_CATEGORIES.EMOTICON) {
        throw new ForbiddenException(`The '${category.name}' category is protected and cannot be deleted`);
      }

      // Check if category has items
      if (category.shopItems && category.shopItems.length > 0) {
        throw new BadRequestException(`Cannot delete category with ${category.shopItems.length} items. Remove or reassign items first.`);
      }

      await this.shopCategoryRepository.remove(category);
      this.logger.log(`Deleted shop category with ID: ${id}`);

      return { success: true, message: `Shop category with ID ${id} deleted successfully` };
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException || error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error(`Error deleting shop category: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to delete shop category: ${error.message}`);
    }
  }

  /**
   * Get or create the special "Skin" category
   * This category is protected and cannot be modified or deleted
   * @returns The skin category
   */
  async getOrCreateSkinCategory(): Promise<ShopCategoryResponseDto> {
    try {
      // Try to find the skin category
      const skinCategory = await this.shopCategoryRepository.findOne({
        where: { name: SHOP_CONSTANTS.PROTECTED_CATEGORIES.SKIN }
      });

      // If it exists, return it
      if (skinCategory) {
        return this.mapCategoryToDto(skinCategory);
      }

      // If it doesn't exist, create it
      const newSkinCategory = this.shopCategoryRepository.create({
        name: SHOP_CONSTANTS.PROTECTED_CATEGORIES.SKIN,
        description: 'Special category for diary skins. This category is protected and cannot be modified or deleted.',
        isActive: true,
        displayOrder: 0
      });

      const savedCategory = await this.shopCategoryRepository.save(newSkinCategory);
      this.logger.log(`Created protected skin category with ID: ${savedCategory.id}`);

      return this.mapCategoryToDto(savedCategory);
    } catch (error) {
      this.logger.error(`Error getting or creating skin category: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get or create skin category: ${error.message}`);
    }
  }

  /**
   * Get or create the special "Emoticon" category
   * This category is protected and cannot be modified or deleted
   * @returns The emoticon category
   */
  async getOrCreateEmoticonCategory(): Promise<ShopCategoryResponseDto> {
    try {
      // Try to find the emoticon category
      const emoticonCategory = await this.shopCategoryRepository.findOne({
        where: { name: SHOP_CONSTANTS.PROTECTED_CATEGORIES.EMOTICON }
      });

      // If it exists, return it
      if (emoticonCategory) {
        return this.mapCategoryToDto(emoticonCategory);
      }

      // If it doesn't exist, create it
      const newEmoticonCategory = this.shopCategoryRepository.create({
        name: SHOP_CONSTANTS.PROTECTED_CATEGORIES.EMOTICON,
        description: 'Special category for emoticons. This category is protected and cannot be modified or deleted.',
        isActive: true,
        displayOrder: 0
      });

      const savedCategory = await this.shopCategoryRepository.save(newEmoticonCategory);
      this.logger.log(`Created protected emoticon category with ID: ${savedCategory.id}`);

      return this.mapCategoryToDto(savedCategory);
    } catch (error) {
      this.logger.error(`Error getting or creating emoticon category: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get or create emoticon category: ${error.message}`);
    }
  }

  /**
   * Map category entity to DTO
   * @param category Category entity
   * @returns Category DTO
   */
  private mapCategoryToDto(category: ShopCategory): ShopCategoryResponseDto {
    return {
      id: category.id,
      name: category.name,
      description: category.description,
      parentId: category.parentId,
      parentName: category.parent?.name,
      imageUrl: category.imageUrl,
      isActive: category.isActive,
      displayOrder: category.displayOrder,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    };
  }
}
