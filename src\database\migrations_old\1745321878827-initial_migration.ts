import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialMigration1745321878827 implements MigrationInterface {
    name = 'InitialMigration1745321878827'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "role" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "name" character varying NOT NULL, CONSTRAINT "UQ_ae4578dcaed5adff96595e61660" UNIQUE ("name"), CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_role" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "user_id" uuid NOT NULL, "role_id" uuid NOT NULL, CONSTRAINT "PK_ed4ec68b176b2c2e86a0a72eab3" PRIMARY KEY ("id", "user_id", "role_id"))`);
        await queryRunner.query(`CREATE TYPE "public"."plan_feature_type_enum" AS ENUM('hec_user_diary', 'hec_play', 'english_qa_writing', 'english_essay', 'english_novel')`);
        await queryRunner.query(`CREATE TABLE "plan_feature" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "type" "public"."plan_feature_type_enum" NOT NULL, "name" character varying NOT NULL, "description" text NOT NULL, CONSTRAINT "UQ_4e8d1f7e8813997a8ad4a2f4cff" UNIQUE ("type"), CONSTRAINT "PK_c9f5c79b8a1c181c5d4db0e066e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."plan_type_enum" AS ENUM('starter', 'standard', 'pro', 'ultimate')`);
        await queryRunner.query(`CREATE TYPE "public"."plan_subscription_type_enum" AS ENUM('monthly', 'yearly')`);
        await queryRunner.query(`CREATE TABLE "plan" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "name" character varying NOT NULL, "type" "public"."plan_type_enum" NOT NULL DEFAULT 'starter', "subscription_type" "public"."plan_subscription_type_enum" NOT NULL DEFAULT 'monthly', "description" text NOT NULL, "price" numeric(10,2) NOT NULL, "duration_days" integer NOT NULL, "auto_renew" boolean NOT NULL DEFAULT false, "legacy_features" text, "is_active" boolean NOT NULL DEFAULT true, CONSTRAINT "UQ_8aa73af67fa634d33de9bf874ab" UNIQUE ("name"), CONSTRAINT "PK_54a2b686aed3b637654bf7ddbb3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_plan" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "user_id" uuid NOT NULL, "plan_id" uuid NOT NULL, "start_date" TIMESTAMP NOT NULL, "end_date" TIMESTAMP NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "payment_reference" character varying, "is_paid" boolean NOT NULL DEFAULT false, "auto_renew" boolean NOT NULL DEFAULT false, "last_renewal_date" TIMESTAMP, "next_renewal_date" TIMESTAMP, "cancellation_date" TIMESTAMP, "notes" text, CONSTRAINT "PK_aa22a94c276c9921fe6590c1557" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "profile_pictures" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" uuid, "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying NOT NULL, "file_size" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "REL_bcb95fc382bed71fb8d212b02f" UNIQUE ("user_id"), CONSTRAINT "PK_55851331ec0d252521dd1f7cde2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."user_type_enum" AS ENUM('admin', 'tutor', 'student')`);
        await queryRunner.query(`CREATE TABLE "user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "name" character varying NOT NULL, "user_id" character varying NOT NULL, "email" character varying NOT NULL, "password" character varying NOT NULL, "type" "public"."user_type_enum" NOT NULL DEFAULT 'student', "is_confirmed" boolean NOT NULL DEFAULT false, "is_active" boolean NOT NULL DEFAULT false, "last_login_at" TIMESTAMP, "profile_picture" character varying, "phone_number" character varying NOT NULL, "address" character varying, "city" character varying, "state" character varying, "country" character varying, "postal_code" character varying, "bio" character varying, "date_of_birth" date, "gender" character varying NOT NULL, "agreed_to_terms" boolean NOT NULL DEFAULT false, "refresh_token" character varying, "refresh_token_expiry" TIMESTAMP, "social_links" json, CONSTRAINT "UQ_758b8ce7c18b9d347461b30228d" UNIQUE ("user_id"), CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "student_diary_skin" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "name" character varying NOT NULL, "description" text NOT NULL, "template_content" text NOT NULL, "preview_image_path" character varying, "is_active" boolean NOT NULL DEFAULT true, "student_id" uuid NOT NULL, "is_global" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_347aa675f5f70fd0b77516608ed" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shop_category" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "name" character varying NOT NULL, "description" text, "is_active" boolean NOT NULL DEFAULT true, "parent_id" character varying, "display_order" integer NOT NULL DEFAULT '0', "image_url" character varying, CONSTRAINT "PK_53aefe72f30f467c4fbb16b9745" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."shop_item_type_enum" AS ENUM('free', 'in_app_purchase')`);
        await queryRunner.query(`CREATE TABLE "shop_item" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "item_number" character varying NOT NULL, "title" character varying NOT NULL, "description" text NOT NULL, "category_id" uuid NOT NULL, "type" "public"."shop_item_type_enum" NOT NULL DEFAULT 'in_app_purchase', "price" numeric(10,2) NOT NULL DEFAULT '0', "file_path" character varying, "is_active" boolean NOT NULL DEFAULT true, "is_featured" boolean NOT NULL DEFAULT false, "promotion_id" character varying, "discounted_price" numeric(10,2), "metadata" json, "purchase_count" integer NOT NULL DEFAULT '0', "view_count" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_45ef796043f3b27975c32d94d20" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_skin" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "name" character varying NOT NULL, "description" text NOT NULL, "template_content" text, "preview_image_path" character varying, "is_active" boolean NOT NULL DEFAULT true, "is_global" boolean NOT NULL DEFAULT true, "created_by_id" uuid, CONSTRAINT "PK_9ee781f84a05f481ec10b774d0d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shop_skin_mapping" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "shop_item_id" uuid NOT NULL, "diary_skin_id" uuid NOT NULL, CONSTRAINT "PK_b483549ec76b767e1f4a477dfdd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_otp" ("id" SERIAL NOT NULL, "user_id" character varying NOT NULL, "otp" character varying NOT NULL, "expiration_time" TIMESTAMP NOT NULL, "generated_time" TIMESTAMP NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_494c022ed33e6ee19a2bbb11b22" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."tutor_approval_status_enum" AS ENUM('pending', 'approved', 'rejected')`);
        await queryRunner.query(`CREATE TABLE "tutor_approval" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" character varying NOT NULL, "status" "public"."tutor_approval_status_enum" NOT NULL DEFAULT 'pending', "admin_id" character varying, "admin_notes" character varying, "rejection_reason" character varying, "approved_at" TIMESTAMP, "rejected_at" TIMESTAMP, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_30cb5a58add0f26fffc1ba82491" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shop_item_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "shop_item_id" uuid NOT NULL, "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, "user_id" character varying, CONSTRAINT "PK_be7a654134506d2379e0b948a3c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."promotion_discount_type_enum" AS ENUM('percentage', 'fixed_amount')`);
        await queryRunner.query(`CREATE TYPE "public"."promotion_applicable_type_enum" AS ENUM('plan', 'shop_item', 'all')`);
        await queryRunner.query(`CREATE TABLE "promotion" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "name" character varying NOT NULL, "description" text NOT NULL, "discount_type" "public"."promotion_discount_type_enum" NOT NULL, "discount_value" numeric(10,2) NOT NULL, "applicable_type" "public"."promotion_applicable_type_enum" NOT NULL, "applicable_category_ids" json, "applicable_plan_ids" json, "promotion_code" character varying, "start_date" TIMESTAMP NOT NULL, "end_date" TIMESTAMP NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "usage_limit" integer, "usage_count" integer NOT NULL DEFAULT '0', "minimum_purchase_amount" numeric(10,2), "maximum_discount_amount" numeric(10,2), CONSTRAINT "PK_fab3630e0789a2002f1cadb7d38" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."shop_item_purchase_payment_method_enum" AS ENUM('reward_points', 'credit_card', 'free')`);
        await queryRunner.query(`CREATE TYPE "public"."shop_item_purchase_status_enum" AS ENUM('completed', 'pending', 'failed', 'refunded')`);
        await queryRunner.query(`CREATE TABLE "shop_item_purchase" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "user_id" uuid NOT NULL, "shop_item_id" uuid NOT NULL, "original_price" numeric(10,2) NOT NULL, "final_price" numeric(10,2) NOT NULL, "promotion_id" character varying, "discount_amount" numeric(10,2), "payment_method" "public"."shop_item_purchase_payment_method_enum" NOT NULL, "status" "public"."shop_item_purchase_status_enum" NOT NULL DEFAULT 'completed', "payment_details" json, "notes" text, CONSTRAINT "PK_4ddf0a56be5c606861875e806b1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "profile_picture_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "profile_picture_id" uuid NOT NULL, "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, "user_id" character varying NOT NULL, CONSTRAINT "PK_e9ee0f051f579676af971f0a5db" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."reward_point_source_enum" AS ENUM('diary_award', 'play', 'qa', 'novel', 'essay', 'shop_purchase', 'admin_adjustment')`);
        await queryRunner.query(`CREATE TYPE "public"."reward_point_type_enum" AS ENUM('earned', 'spent', 'adjusted', 'expired')`);
        await queryRunner.query(`CREATE TABLE "reward_point" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "user_id" uuid NOT NULL, "source" "public"."reward_point_source_enum" NOT NULL, "type" "public"."reward_point_type_enum" NOT NULL, "points" integer NOT NULL, "reference_id" character varying, "description" text, "expiry_date" TIMESTAMP, CONSTRAINT "PK_9fa3dfc8d772d427aeb23fb1a56" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "email_verifications" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" character varying NOT NULL, "token" character varying NOT NULL, "expiration_time" TIMESTAMP NOT NULL, "is_used" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_c1ea2921e767f83cd44c0af203f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "password_resets" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" character varying NOT NULL, "token" character varying NOT NULL, "expiration_time" TIMESTAMP NOT NULL, "is_used" boolean NOT NULL DEFAULT false, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_4816377aa98211c1de34469e742" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_feedback" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "diary_entry_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "feedback" text NOT NULL, "rating" integer NOT NULL DEFAULT '0', "award" character varying, CONSTRAINT "PK_78f9b0c952fa6e185ef5fdbb935" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_share" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "diary_entry_id" uuid NOT NULL, "share_token" character varying NOT NULL, "expiry_date" TIMESTAMP, "is_active" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_63012cc4b797be19b36969cfd93" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_status_enum" AS ENUM('draft', 'submitted', 'under_review', 'reviewed')`);
        await queryRunner.query(`CREATE TABLE "diary_entry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "diary_id" uuid NOT NULL, "entry_date" date NOT NULL, "title" character varying NOT NULL, "content" text NOT NULL, "status" "public"."diary_entry_status_enum" NOT NULL DEFAULT 'draft', "skin_id" uuid, "background_color" character varying, "is_private" boolean NOT NULL DEFAULT false, "review_start_time" TIMESTAMP, "reviewing_tutor_id" uuid, "review_expiry_time" TIMESTAMP, "score" integer, "evaluated_at" TIMESTAMP, "evaluated_by" uuid, CONSTRAINT "PK_31547daef53774d068540599e40" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "user_id" uuid NOT NULL, "default_skin_id" uuid NOT NULL, "tutor_greeting" text, CONSTRAINT "REL_330f20310184a92a90225c36cb" UNIQUE ("user_id"), CONSTRAINT "PK_7422c55a0908c4271ff1918437d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_skin_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "diary_skin_id" uuid NOT NULL, "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, "user_id" character varying, CONSTRAINT "PK_3c5415afb86d9978aee8e7b0b32" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."diary_award_period_enum" AS ENUM('weekly', 'monthly')`);
        await queryRunner.query(`CREATE TABLE "diary_award" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "user_id" uuid NOT NULL, "period" "public"."diary_award_period_enum" NOT NULL, "period_start_date" date NOT NULL, "period_end_date" date NOT NULL, "total_score" integer NOT NULL, "award_title" character varying, "award_description" text, CONSTRAINT "PK_595dce60e6d3b854a4a8ad973e2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."award_module_enum" AS ENUM('diary', 'play', 'qa', 'novel', 'essay')`);
        await queryRunner.query(`CREATE TYPE "public"."award_criteria_enum" AS ENUM('diary_score', 'attendance', 'diary_decoration', 'play_performance', 'qa_performance', 'novel_performance', 'essay_performance', 'custom')`);
        await queryRunner.query(`CREATE TYPE "public"."award_frequency_enum" AS ENUM('daily', 'weekly', 'monthly', 'yearly', 'one_time')`);
        await queryRunner.query(`CREATE TABLE "award" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "name" character varying NOT NULL, "description" text NOT NULL, "module" "public"."award_module_enum" NOT NULL, "criteria" "public"."award_criteria_enum" array NOT NULL, "frequency" "public"."award_frequency_enum" NOT NULL, "reward_points" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "criteria_config" json, "start_date" TIMESTAMP, "end_date" TIMESTAMP, "image_url" character varying, CONSTRAINT "PK_e887e4e69663925ebb60d3a7775" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "audit_log" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" character varying, "action" character varying NOT NULL, "resource" character varying NOT NULL, "details" json, "ip_address" character varying NOT NULL, "user_agent" character varying NOT NULL, "timestamp" TIMESTAMP NOT NULL, "status" character varying, "error_message" character varying, CONSTRAINT "PK_07fefa57f7f5ab8fc3f52b3ed0b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "award_winner" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL, "updated_at" TIMESTAMP NOT NULL, "user_id" uuid NOT NULL, "award_id" uuid NOT NULL, "award_date" date NOT NULL, "award_reason" text, "metadata" json, CONSTRAINT "PK_331f04f00afef649d7a47c67abd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "plan_feature_map" ("plan_id" uuid NOT NULL, "feature_id" uuid NOT NULL, CONSTRAINT "PK_16cf1cd5fe6289f88912ae527bc" PRIMARY KEY ("plan_id", "feature_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_099af0f098a074e2a2842c6026" ON "plan_feature_map" ("plan_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_5947b4fb7b9c93694bbf9b9c24" ON "plan_feature_map" ("feature_id") `);
        await queryRunner.query(`ALTER TABLE "user_role" ADD CONSTRAINT "FK_d0e5815877f7395a198a4cb0a46" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_role" ADD CONSTRAINT "FK_32a6fc2fcb019d8e3a8ace0f55f" FOREIGN KEY ("role_id") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_plan" ADD CONSTRAINT "FK_5a8dd225812b1927bc8bc60632c" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_plan" ADD CONSTRAINT "FK_ab1f08d687398cd4762faad4690" FOREIGN KEY ("plan_id") REFERENCES "plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "profile_pictures" ADD CONSTRAINT "FK_bcb95fc382bed71fb8d212b02fd" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_diary_skin" ADD CONSTRAINT "FK_4e7bf414c99b770402b561a809c" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_item" ADD CONSTRAINT "FK_d0f2a32e6a14288280764413a8a" FOREIGN KEY ("category_id") REFERENCES "shop_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_skin" ADD CONSTRAINT "FK_1575bed4b69f934df585b59840a" FOREIGN KEY ("created_by_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ADD CONSTRAINT "FK_6b110a0c2b8283cfcde3ae5d9f9" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ADD CONSTRAINT "FK_14dd83805ed84fab64df6348a00" FOREIGN KEY ("diary_skin_id") REFERENCES "diary_skin"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_item_registry" ADD CONSTRAINT "FK_0d6e4cd3e8ca30faa5b98b14031" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD CONSTRAINT "FK_8347774fc4c52bd91d3c6b4376e" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD CONSTRAINT "FK_5d462a8a9fb780aa06a4dfccf08" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "profile_picture_registry" ADD CONSTRAINT "FK_1d0b2a8d6e2051c59d4f37dee9e" FOREIGN KEY ("profile_picture_id") REFERENCES "profile_pictures"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reward_point" ADD CONSTRAINT "FK_2b561c7b6ec97ff6365dd551fb4" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_feedback" ADD CONSTRAINT "FK_0a720eca6b5012a76ec03ea7fe4" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_feedback" ADD CONSTRAINT "FK_4d38de3a38b2ed13b42bf14cdad" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_share" ADD CONSTRAINT "FK_c7af4e791867d5bcba3bcf4cbbd" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ADD CONSTRAINT "FK_efb966e9fe0caef3e790f720fa5" FOREIGN KEY ("diary_id") REFERENCES "diary"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ADD CONSTRAINT "FK_ad12d4b9edf19455766d9457d6b" FOREIGN KEY ("skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ADD CONSTRAINT "FK_6c526b99b834d18cb49907a8c0e" FOREIGN KEY ("reviewing_tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ADD CONSTRAINT "FK_2643e09b05f859d6cdf34083cba" FOREIGN KEY ("evaluated_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary" ADD CONSTRAINT "FK_330f20310184a92a90225c36cbe" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary" ADD CONSTRAINT "FK_67ae196f4fadd5230b210a3917c" FOREIGN KEY ("default_skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_skin_registry" ADD CONSTRAINT "FK_217c6f3608dcb0376d4c5329f89" FOREIGN KEY ("diary_skin_id") REFERENCES "diary_skin"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_award" ADD CONSTRAINT "FK_e6d3eb40116bf219b6829b4f92b" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "award_winner" ADD CONSTRAINT "FK_90c460a2dd57cedc826c55edb2f" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "award_winner" ADD CONSTRAINT "FK_fdfbe81ebe306c4b5e524350a96" FOREIGN KEY ("award_id") REFERENCES "award"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "plan_feature_map" ADD CONSTRAINT "FK_099af0f098a074e2a2842c60260" FOREIGN KEY ("plan_id") REFERENCES "plan"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "plan_feature_map" ADD CONSTRAINT "FK_5947b4fb7b9c93694bbf9b9c244" FOREIGN KEY ("feature_id") REFERENCES "plan_feature"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "plan_feature_map" DROP CONSTRAINT "FK_5947b4fb7b9c93694bbf9b9c244"`);
        await queryRunner.query(`ALTER TABLE "plan_feature_map" DROP CONSTRAINT "FK_099af0f098a074e2a2842c60260"`);
        await queryRunner.query(`ALTER TABLE "award_winner" DROP CONSTRAINT "FK_fdfbe81ebe306c4b5e524350a96"`);
        await queryRunner.query(`ALTER TABLE "award_winner" DROP CONSTRAINT "FK_90c460a2dd57cedc826c55edb2f"`);
        await queryRunner.query(`ALTER TABLE "diary_award" DROP CONSTRAINT "FK_e6d3eb40116bf219b6829b4f92b"`);
        await queryRunner.query(`ALTER TABLE "diary_skin_registry" DROP CONSTRAINT "FK_217c6f3608dcb0376d4c5329f89"`);
        await queryRunner.query(`ALTER TABLE "diary" DROP CONSTRAINT "FK_67ae196f4fadd5230b210a3917c"`);
        await queryRunner.query(`ALTER TABLE "diary" DROP CONSTRAINT "FK_330f20310184a92a90225c36cbe"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT "FK_2643e09b05f859d6cdf34083cba"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT "FK_6c526b99b834d18cb49907a8c0e"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT "FK_ad12d4b9edf19455766d9457d6b"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT "FK_efb966e9fe0caef3e790f720fa5"`);
        await queryRunner.query(`ALTER TABLE "diary_share" DROP CONSTRAINT "FK_c7af4e791867d5bcba3bcf4cbbd"`);
        await queryRunner.query(`ALTER TABLE "diary_feedback" DROP CONSTRAINT "FK_4d38de3a38b2ed13b42bf14cdad"`);
        await queryRunner.query(`ALTER TABLE "diary_feedback" DROP CONSTRAINT "FK_0a720eca6b5012a76ec03ea7fe4"`);
        await queryRunner.query(`ALTER TABLE "reward_point" DROP CONSTRAINT "FK_2b561c7b6ec97ff6365dd551fb4"`);
        await queryRunner.query(`ALTER TABLE "profile_picture_registry" DROP CONSTRAINT "FK_1d0b2a8d6e2051c59d4f37dee9e"`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP CONSTRAINT "FK_5d462a8a9fb780aa06a4dfccf08"`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP CONSTRAINT "FK_8347774fc4c52bd91d3c6b4376e"`);
        await queryRunner.query(`ALTER TABLE "shop_item_registry" DROP CONSTRAINT "FK_0d6e4cd3e8ca30faa5b98b14031"`);
        await queryRunner.query(`ALTER TABLE "shop_skin_mapping" DROP CONSTRAINT "FK_14dd83805ed84fab64df6348a00"`);
        await queryRunner.query(`ALTER TABLE "shop_skin_mapping" DROP CONSTRAINT "FK_6b110a0c2b8283cfcde3ae5d9f9"`);
        await queryRunner.query(`ALTER TABLE "diary_skin" DROP CONSTRAINT "FK_1575bed4b69f934df585b59840a"`);
        await queryRunner.query(`ALTER TABLE "shop_item" DROP CONSTRAINT "FK_d0f2a32e6a14288280764413a8a"`);
        await queryRunner.query(`ALTER TABLE "student_diary_skin" DROP CONSTRAINT "FK_4e7bf414c99b770402b561a809c"`);
        await queryRunner.query(`ALTER TABLE "profile_pictures" DROP CONSTRAINT "FK_bcb95fc382bed71fb8d212b02fd"`);
        await queryRunner.query(`ALTER TABLE "user_plan" DROP CONSTRAINT "FK_ab1f08d687398cd4762faad4690"`);
        await queryRunner.query(`ALTER TABLE "user_plan" DROP CONSTRAINT "FK_5a8dd225812b1927bc8bc60632c"`);
        await queryRunner.query(`ALTER TABLE "user_role" DROP CONSTRAINT "FK_32a6fc2fcb019d8e3a8ace0f55f"`);
        await queryRunner.query(`ALTER TABLE "user_role" DROP CONSTRAINT "FK_d0e5815877f7395a198a4cb0a46"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5947b4fb7b9c93694bbf9b9c24"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_099af0f098a074e2a2842c6026"`);
        await queryRunner.query(`DROP TABLE "plan_feature_map"`);
        await queryRunner.query(`DROP TABLE "award_winner"`);
        await queryRunner.query(`DROP TABLE "audit_log"`);
        await queryRunner.query(`DROP TABLE "award"`);
        await queryRunner.query(`DROP TYPE "public"."award_frequency_enum"`);
        await queryRunner.query(`DROP TYPE "public"."award_criteria_enum"`);
        await queryRunner.query(`DROP TYPE "public"."award_module_enum"`);
        await queryRunner.query(`DROP TABLE "diary_award"`);
        await queryRunner.query(`DROP TYPE "public"."diary_award_period_enum"`);
        await queryRunner.query(`DROP TABLE "diary_skin_registry"`);
        await queryRunner.query(`DROP TABLE "diary"`);
        await queryRunner.query(`DROP TABLE "diary_entry"`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_status_enum"`);
        await queryRunner.query(`DROP TABLE "diary_share"`);
        await queryRunner.query(`DROP TABLE "diary_feedback"`);
        await queryRunner.query(`DROP TABLE "password_resets"`);
        await queryRunner.query(`DROP TABLE "email_verifications"`);
        await queryRunner.query(`DROP TABLE "reward_point"`);
        await queryRunner.query(`DROP TYPE "public"."reward_point_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."reward_point_source_enum"`);
        await queryRunner.query(`DROP TABLE "profile_picture_registry"`);
        await queryRunner.query(`DROP TABLE "shop_item_purchase"`);
        await queryRunner.query(`DROP TYPE "public"."shop_item_purchase_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."shop_item_purchase_payment_method_enum"`);
        await queryRunner.query(`DROP TABLE "promotion"`);
        await queryRunner.query(`DROP TYPE "public"."promotion_applicable_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."promotion_discount_type_enum"`);
        await queryRunner.query(`DROP TABLE "shop_item_registry"`);
        await queryRunner.query(`DROP TABLE "tutor_approval"`);
        await queryRunner.query(`DROP TYPE "public"."tutor_approval_status_enum"`);
        await queryRunner.query(`DROP TABLE "user_otp"`);
        await queryRunner.query(`DROP TABLE "shop_skin_mapping"`);
        await queryRunner.query(`DROP TABLE "diary_skin"`);
        await queryRunner.query(`DROP TABLE "shop_item"`);
        await queryRunner.query(`DROP TYPE "public"."shop_item_type_enum"`);
        await queryRunner.query(`DROP TABLE "shop_category"`);
        await queryRunner.query(`DROP TABLE "student_diary_skin"`);
        await queryRunner.query(`DROP TABLE "user"`);
        await queryRunner.query(`DROP TYPE "public"."user_type_enum"`);
        await queryRunner.query(`DROP TABLE "profile_pictures"`);
        await queryRunner.query(`DROP TABLE "user_plan"`);
        await queryRunner.query(`DROP TABLE "plan"`);
        await queryRunner.query(`DROP TYPE "public"."plan_subscription_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."plan_type_enum"`);
        await queryRunner.query(`DROP TABLE "plan_feature"`);
        await queryRunner.query(`DROP TYPE "public"."plan_feature_type_enum"`);
        await queryRunner.query(`DROP TABLE "user_role"`);
        await queryRunner.query(`DROP TABLE "role"`);
    }

}
