import { MigrationInterface, QueryRunner } from "typeorm";

export class QAAssignmentAddModel1746681782654 implements MigrationInterface {
    name = 'QAAssignmentAddModel1746681782654'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_assignment" DROP CONSTRAINT "FK_c595583aee2e12fb71f3e0776c2"`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" DROP COLUMN "tutor_id"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_assignment" ADD "tutor_id" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" ADD CONSTRAINT "FK_c595583aee2e12fb71f3e0776c2" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
