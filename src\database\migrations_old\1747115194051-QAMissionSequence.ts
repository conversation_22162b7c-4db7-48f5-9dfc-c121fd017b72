import { MigrationInterface, QueryRunner } from "typeorm";

export class QAMissionSequence1747115194051 implements MigrationInterface {
    name = 'QAMissionSequence1747115194051'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "qa_monthly_mission_tasks" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying(50) NOT NULL, "description" text NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "sequence" integer DEFAULT '1', "word_limit_minumum" integer NOT NULL, "word_limit_maximum" integer, "total_score" integer, "deadline" date, "instructions" text NOT NULL, "mission_id" uuid NOT NULL, CONSTRAINT "PK_69715723a4c804eada066f50cd3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."qa_mission_time_frequency_enum" AS ENUM('weekly', 'monthly')`);
        await queryRunner.query(`CREATE TABLE "qa_mission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "time_frequency" "public"."qa_mission_time_frequency_enum" NOT NULL DEFAULT 'weekly', "is_active" boolean NOT NULL DEFAULT true, "week_id" uuid, "month_id" uuid, CONSTRAINT "PK_6894f62006f7d34f2fae96039b5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "qa_weekly_mission_tasks" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying(50) NOT NULL, "description" text NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "sequence" integer DEFAULT '1', "word_limit_minumum" integer NOT NULL, "word_limit_maximum" integer, "total_score" integer, "deadline" date, "instructions" text NOT NULL, "mission_id" uuid NOT NULL, CONSTRAINT "PK_376f8c11101064802491a4cf1b9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "qa_monthly_mission_tasks" ADD CONSTRAINT "FK_eac8c67aa92a405d759584ceac7" FOREIGN KEY ("mission_id") REFERENCES "qa_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_mission" ADD CONSTRAINT "FK_3abefaedf983f685f2c25a4292e" FOREIGN KEY ("week_id") REFERENCES "qa_mission_week"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_mission" ADD CONSTRAINT "FK_bb13566db454bdf3b7c4a0d0c5d" FOREIGN KEY ("month_id") REFERENCES "qa_mission_month"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_weekly_mission_tasks" ADD CONSTRAINT "FK_973cdf02500625f250263ec4417" FOREIGN KEY ("mission_id") REFERENCES "qa_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "qa_weekly_mission_tasks" DROP CONSTRAINT "FK_973cdf02500625f250263ec4417"`);
        await queryRunner.query(`ALTER TABLE "qa_mission" DROP CONSTRAINT "FK_bb13566db454bdf3b7c4a0d0c5d"`);
        await queryRunner.query(`ALTER TABLE "qa_mission" DROP CONSTRAINT "FK_3abefaedf983f685f2c25a4292e"`);
        await queryRunner.query(`ALTER TABLE "qa_monthly_mission_tasks" DROP CONSTRAINT "FK_eac8c67aa92a405d759584ceac7"`);
        await queryRunner.query(`DROP TABLE "qa_weekly_mission_tasks"`);
        await queryRunner.query(`DROP TABLE "qa_mission"`);
        await queryRunner.query(`DROP TYPE "public"."qa_mission_time_frequency_enum"`);
        await queryRunner.query(`DROP TABLE "qa_monthly_mission_tasks"`);
    }
}
