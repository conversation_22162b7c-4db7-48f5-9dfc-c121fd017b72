import { MigrationInterface, QueryRunner } from "typeorm";

export class DiaryEntryLikeImplemeted1747770929785 implements MigrationInterface {
    name = 'DiaryEntryLikeImplemeted1747770929785'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT "FK_750c3fd7052bd0ed7a2e520af45"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_fac44bb71949efc3e4ce2eac6f7"`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_like_liker_type_enum" AS ENUM('student', 'tutor')`);
        await queryRunner.query(`CREATE TABLE "diary_entry_like" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "diary_entry_id" uuid NOT NULL, "liker_id" uuid NOT NULL, "liker_type" "public"."diary_entry_like_liker_type_enum" NOT NULL, CONSTRAINT "UQ_256412907c05a48b2a7f5c4e750" UNIQUE ("diary_entry_id", "liker_id"), CONSTRAINT "PK_c08131c537e95c7710c00c4d2b0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP COLUMN "first_submitted_at"`);
        await queryRunner.query(`ALTER TABLE "story_maker" DROP COLUMN "deadline"`);
        await queryRunner.query(`ALTER TABLE "story_maker" DROP COLUMN "word_limit"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP COLUMN "submission_skin_id"`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD "content" text`);
        await queryRunner.query(`UPDATE "story_maker_participation" SET "content" = '' WHERE "content" IS NULL`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" ALTER COLUMN "content" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD "feedback" text`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP COLUMN "evaluated_by"`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD "evaluated_by" character varying(36)`);
        await queryRunner.query(`ALTER TYPE "public"."diary_award_period_enum" RENAME TO "diary_award_period_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."diary_award_period_enum" AS ENUM('weekly', 'monthly', 'quarterly')`);
        await queryRunner.query(`ALTER TABLE "diary_award" ALTER COLUMN "period" TYPE "public"."diary_award_period_enum" USING "period"::"text"::"public"."diary_award_period_enum"`);
        await queryRunner.query(`DROP TYPE "public"."diary_award_period_enum_old"`);
        // First update any 'play' values to 'diary' as a fallback
        await queryRunner.query(`UPDATE "award" SET "module" = 'diary' WHERE "module" = 'play'`);
        await queryRunner.query(`UPDATE "award" SET "module" = 'diary' WHERE "module" = 'qa'`);
        
        // Now we can safely change the enum
        await queryRunner.query(`ALTER TYPE "public"."award_module_enum" RENAME TO "award_module_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."award_module_enum" AS ENUM('diary', 'novel', 'essay')`);
        await queryRunner.query(`ALTER TABLE "award" ALTER COLUMN "module" TYPE "public"."award_module_enum" USING "module"::"text"::"public"."award_module_enum"`);
        await queryRunner.query(`DROP TYPE "public"."award_module_enum_old"`);
        await queryRunner.query(`ALTER TYPE "public"."award_frequency_enum" RENAME TO "award_frequency_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."award_frequency_enum" AS ENUM('weekly', 'monthly', 'quarterly', 'yearly', 'one_time')`);
        await queryRunner.query(`ALTER TABLE "award" ALTER COLUMN "frequency" TYPE "public"."award_frequency_enum" USING "frequency"::"text"::"public"."award_frequency_enum"`);
        await queryRunner.query(`DROP TYPE "public"."award_frequency_enum_old"`);
        await queryRunner.query(`ALTER TABLE "award_schedule" ALTER COLUMN "updated_at" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TYPE "public"."award_schedule_module_enum" RENAME TO "award_schedule_module_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."award_schedule_module_enum" AS ENUM('diary', 'novel', 'essay')`);
        await queryRunner.query(`ALTER TABLE "award_schedule" ALTER COLUMN "module" TYPE "public"."award_schedule_module_enum" USING "module"::"text"::"public"."award_schedule_module_enum"`);
        await queryRunner.query(`DROP TYPE "public"."award_schedule_module_enum_old"`);
        await queryRunner.query(`ALTER TABLE "diary_entry_like" ADD CONSTRAINT "FK_932451a171cbea05988aadf6f6e" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry_like" ADD CONSTRAINT "FK_bd9a33da417ca4be7569811d2c7" FOREIGN KEY ("liker_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "diary_entry_like" DROP CONSTRAINT "FK_bd9a33da417ca4be7569811d2c7"`);
        await queryRunner.query(`ALTER TABLE "diary_entry_like" DROP CONSTRAINT "FK_932451a171cbea05988aadf6f6e"`);
        await queryRunner.query(`CREATE TYPE "public"."award_schedule_module_enum_old" AS ENUM('diary', 'play', 'qa', 'novel', 'essay')`);
        await queryRunner.query(`ALTER TABLE "award_schedule" ALTER COLUMN "module" TYPE "public"."award_schedule_module_enum_old" USING "module"::"text"::"public"."award_schedule_module_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."award_schedule_module_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."award_schedule_module_enum_old" RENAME TO "award_schedule_module_enum"`);
        await queryRunner.query(`ALTER TABLE "award_schedule" ALTER COLUMN "updated_at" DROP DEFAULT`);
        await queryRunner.query(`CREATE TYPE "public"."award_frequency_enum_old" AS ENUM('daily', 'weekly', 'monthly', 'yearly', 'one_time')`);
        await queryRunner.query(`ALTER TABLE "award" ALTER COLUMN "frequency" TYPE "public"."award_frequency_enum_old" USING "frequency"::"text"::"public"."award_frequency_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."award_frequency_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."award_frequency_enum_old" RENAME TO "award_frequency_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."award_module_enum_old" AS ENUM('diary', 'play', 'qa', 'novel', 'essay')`);
        await queryRunner.query(`ALTER TABLE "award" ALTER COLUMN "module" TYPE "public"."award_module_enum_old" USING "module"::"text"::"public"."award_module_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."award_module_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."award_module_enum_old" RENAME TO "award_module_enum"`);
        await queryRunner.query(`CREATE TYPE "public"."diary_award_period_enum_old" AS ENUM('weekly', 'monthly')`);
        await queryRunner.query(`ALTER TABLE "diary_award" ALTER COLUMN "period" TYPE "public"."diary_award_period_enum_old" USING "period"::"text"::"public"."diary_award_period_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."diary_award_period_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."diary_award_period_enum_old" RENAME TO "diary_award_period_enum"`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP COLUMN "evaluated_by"`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD "evaluated_by" uuid`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP COLUMN "feedback"`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP COLUMN "content"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD "submission_skin_id" uuid`);
        await queryRunner.query(`ALTER TABLE "story_maker" ADD "word_limit" integer`);
        await queryRunner.query(`ALTER TABLE "story_maker" ADD "deadline" integer`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD "first_submitted_at" TIMESTAMP`);
        await queryRunner.query(`DROP TABLE "diary_entry_like"`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_like_liker_type_enum"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_fac44bb71949efc3e4ce2eac6f7" FOREIGN KEY ("submission_skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_750c3fd7052bd0ed7a2e520af45" FOREIGN KEY ("evaluated_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
