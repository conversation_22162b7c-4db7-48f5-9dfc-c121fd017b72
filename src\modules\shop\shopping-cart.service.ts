import { Injectable, NotFoundException, BadRequestException, InternalServerErrorException, Logger, Inject, forwardRef } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { ShoppingCartStatus } from '../../database/entities/shopping-cart.entity';
import { PaymentMethod, PurchaseStatus } from '../../database/entities/shop-item-purchase.entity';
import { RewardPointSource, RewardPointType } from '../../database/entities/reward-point.entity';
import { AddToCartDto, UpdateCartItemDto, ShoppingCartResponseDto, CheckoutDto, CheckoutResponseDto } from '../../database/models/shopping-cart.dto';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';
import { StudentOwnedItemService } from './student-owned-item.service';
import { RewardPointSettingService } from './reward-point-setting.service';
import { PromotionsService } from '../promotions/promotions.service';
import { PromotionApplicableType } from '../../database/entities/promotion.entity';
import { PaymentService } from '../payment/services/payment.service';
import { InitiatePaymentDto } from '../payment/dto/payment.dto';
import { KcpPaymentMethod, PurchaseType } from '../../database/entities/payment-transaction.entity';

@Injectable()
export class ShoppingCartService {
  private readonly logger = new Logger(ShoppingCartService.name);

  constructor(
    private dataSource: DataSource,
    private fileRegistryService: FileRegistryService,
    @Inject(forwardRef(() => StudentOwnedItemService))
    private studentOwnedItemService: StudentOwnedItemService,
    private rewardPointSettingService: RewardPointSettingService,
    @Inject(forwardRef(() => PromotionsService))
    private promotionsService: PromotionsService,
    @Inject(forwardRef(() => PaymentService))
    private paymentService: PaymentService,
  ) {}

  /**
   * Get user's available reward points
   * @param userId User ID
   * @returns Available reward points
   */
  async getUserRewardPoints(userId: string): Promise<number> {
    try {
      // Get earned points (excluding expired ones)
      const earnedPointsQuery = await this.dataSource.query(
        `SELECT COALESCE(SUM(points), 0) as total
         FROM reward_point
         WHERE user_id = $1
         AND (type = $2 OR type = $3)
         AND (points > 0 AND (expiry_date IS NULL OR expiry_date > NOW())
              OR points <= 0)`,
        [userId, RewardPointType.EARNED, RewardPointType.ADJUSTED]
      );

      // Get spent points
      const spentPointsQuery = await this.dataSource.query(
        `SELECT COALESCE(SUM(points), 0) as total
         FROM reward_point
         WHERE user_id = $1 AND type = $2`,
        [userId, RewardPointType.SPENT]
      );

      const earnedPoints = Number(earnedPointsQuery[0].total) || 0;
      const spentPoints = Number(spentPointsQuery[0].total) || 0;
      const availablePoints = earnedPoints - spentPoints;

      this.logger.log(`User ${userId} has ${availablePoints} reward points (earned: ${earnedPoints}, spent: ${spentPoints})`);
      return availablePoints;
    } catch (error) {
      this.logger.error(`Error getting user reward points: ${error.message}`, error.stack);
      return 0;
    }
  }

  /**
   * Deduct reward points from user
   * @param userId User ID
   * @param points Points to deduct
   * @param description Description of the transaction
   */
  async deductRewardPoints(userId: string, points: number, description: string): Promise<void> {
    try {
      // Create a new reward point transaction using direct SQL
      const now = new Date().toISOString();
      await this.dataSource.query(
        `INSERT INTO reward_point (user_id, source, type, points, description, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [userId, RewardPointSource.SHOP_PURCHASE, RewardPointType.SPENT, points, description, now, now]
      );

      this.logger.log(`Deducted ${points} reward points from user ${userId} for ${description}`);
    } catch (error) {
      this.logger.error(`Error deducting reward points: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Failed to deduct reward points: ${error.message}`);
    }
  }

  /**
   * Get the active shopping cart for a user
   * @param userId User ID
   * @returns The active shopping cart
   */
  async getActiveCart(userId: string): Promise<any> {
    this.logger.log(`Getting active cart for user ${userId}`);

    try {
      // Find the active cart for the user with direct SQL
      const cartQuery = await this.dataSource.query(
        `SELECT id, status, last_activity as "lastActivity"
         FROM shopping_cart
         WHERE user_id = $1 AND status = $2`,
        [userId, ShoppingCartStatus.ACTIVE]
      );

      let cart: any;

      // If no active cart exists, create one
      if (!cartQuery || cartQuery.length === 0) {
        this.logger.log(`No active cart found for user ${userId}, creating a new one`);

        const now = new Date().toISOString();
        const insertResult = await this.dataSource.query(
          `INSERT INTO shopping_cart (user_id, status, last_activity, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5)
           RETURNING id, status, last_activity as "lastActivity"`,
          [userId, ShoppingCartStatus.ACTIVE, now, now, now]
        );

        if (!insertResult || insertResult.length === 0) {
          throw new Error('Failed to create shopping cart');
        }

        cart = insertResult[0];
        cart.items = [];

        this.logger.log(`Created new shopping cart with ID ${cart.id} for user ${userId}`);
      } else {
        cart = cartQuery[0];

        // Get cart items
        const itemsQuery = await this.dataSource.query(
          `SELECT ci.id, ci.shop_item_id as "shopItemId", ci.quantity, ci.price, ci.reward_points as "rewardPoints",
                  si.title, si.price as "originalPrice", si.promotion_id as "promotionId", si.file_path as "filePath",
                  si.type, si.is_purchasable_in_rewardpoint as "isPurchasableInRewardpoint", si.discounted_price as "discountedPrice",
                  si.is_promotion_active as "isPromotionActive"
           FROM shopping_cart_item ci
           JOIN shop_item si ON ci.shop_item_id = si.id
           WHERE ci.cart_id = $1`,
          [cart.id]
        );

        cart.items = itemsQuery;
        this.logger.log(`Found existing cart with ID ${cart.id} for user ${userId} with ${cart.items.length} items`);
      }

      return cart;
    } catch (error) {
      this.logger.error(`Error getting active cart: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Failed to get active cart: ${error.message}`);
    }
  }

  /**
   * Add an item to the shopping cart
   * @param userId User ID
   * @param addToCartDto DTO with item details
   * @returns The updated shopping cart
   */
  async addToCart(userId: string, addToCartDto: AddToCartDto): Promise<ShoppingCartResponseDto> {
    const { shopItemId, quantity = 1 } = addToCartDto;

    this.logger.log(`Adding item ${shopItemId} to cart for user ${userId} with quantity ${quantity}`);

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the shop item with direct query
      const shopItemQuery = await queryRunner.query(
        `SELECT id, title, price, type, is_purchasable_in_rewardpoint, discounted_price, promotion_id, is_promotion_active
         FROM shop_item
         WHERE id = $1 AND is_active = true`,
        [shopItemId]
      );

      if (!shopItemQuery || shopItemQuery.length === 0) {
        throw new NotFoundException(`Shop item with ID ${shopItemId} not found or is not active`);
      }

      const shopItem = shopItemQuery[0];
      this.logger.log(`Found shop item: ${shopItem.title} (${shopItem.id})`);

      // Get or create active cart for the user
      let cartQuery = await queryRunner.query(
        `SELECT id, status, last_activity
         FROM shopping_cart
         WHERE user_id = $1 AND status = $2`,
        [userId, ShoppingCartStatus.ACTIVE]
      );

      let cartId: string;
      if (!cartQuery || cartQuery.length === 0) {
        // Create a new cart
        const now = new Date().toISOString();
        const insertCartResult = await queryRunner.query(
          `INSERT INTO shopping_cart (user_id, status, last_activity, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5)
           RETURNING id`,
          [userId, ShoppingCartStatus.ACTIVE, now, now, now]
        );

        cartId = insertCartResult[0].id;
        this.logger.log(`Created new cart with ID ${cartId} for user ${userId}`);
      } else {
        cartId = cartQuery[0].id;
        this.logger.log(`Using existing cart with ID ${cartId} for user ${userId}`);
      }

      // Calculate the final price
      const originalPrice = Number(shopItem.price);
      const hasActivePromotion = shopItem.promotion_id && shopItem.is_promotion_active;
      const discountedPrice = hasActivePromotion && shopItem.discounted_price ? Number(shopItem.discounted_price) : null;
      const finalPrice = discountedPrice !== null && discountedPrice < originalPrice ? discountedPrice : originalPrice;

      // Check if the item is already in the cart
      const cartItemQuery = await queryRunner.query(
        `SELECT id, quantity
         FROM shopping_cart_item
         WHERE cart_id = $1 AND shop_item_id = $2`,
        [cartId, shopItemId]
      );

      if (cartItemQuery && cartItemQuery.length > 0) {
        // Update existing cart item
        const cartItem = cartItemQuery[0];
        const newQuantity = Number(cartItem.quantity) + Number(quantity);

        this.logger.log(`Updating existing cart item ${cartItem.id} from quantity ${cartItem.quantity} to ${newQuantity}`);

        await queryRunner.query(
          `UPDATE shopping_cart_item
           SET quantity = $1, updated_at = $2
           WHERE id = $3`,
          [newQuantity, new Date().toISOString(), cartItem.id]
        );
      } else {
        // Get the active reward point setting
        let conversionRate = 100; // Default to 100 if no setting is found
        try {
          const activeSetting = await this.rewardPointSettingService.getActiveRewardPointSetting();
          conversionRate = activeSetting.conversionRate;
        } catch (error) {
          this.logger.warn(`Could not get active reward point setting: ${error.message}. Using default conversion rate of 100.`);
        }

        // Calculate reward points based on price and conversion rate
        const rewardPoints = shopItem.type === 'FREE' ? 0 : (shopItem.is_purchasable_in_rewardpoint ? Math.round(finalPrice * conversionRate) : 0);
        const now = new Date().toISOString();

        this.logger.log(`Creating new cart item for shop item ${shopItemId} with price ${finalPrice} and reward points ${rewardPoints}`);

        await queryRunner.query(
          `INSERT INTO shopping_cart_item (cart_id, shop_item_id, quantity, price, reward_points, created_at, updated_at)
           VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [cartId, shopItemId, quantity, finalPrice, rewardPoints, now, now]
        );
      }

      // Update cart's last activity
      await queryRunner.query(
        `UPDATE shopping_cart
         SET last_activity = $1, updated_at = $2
         WHERE id = $3`,
        [new Date().toISOString(), new Date().toISOString(), cartId]
      );

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Return the updated cart
      return this.getCartResponse(userId);
    } catch (error) {
      // Rollback the transaction in case of error, but only if it's active
      try {
        if (queryRunner.isTransactionActive) {
          await queryRunner.rollbackTransaction();
        }
      } catch (rollbackError) {
        this.logger.error(`Error during transaction rollback: ${rollbackError.message}`, rollbackError.stack);
      }

      this.logger.error(`Error adding item to cart: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new InternalServerErrorException(`Failed to add item to cart: ${error.message}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Update the quantity of an item in the cart
   * @param userId User ID
   * @param cartItemId Cart item ID
   * @param updateCartItemDto DTO with updated quantity
   * @returns The updated shopping cart
   */
  async updateCartItem(userId: string, cartItemId: string, updateCartItemDto: UpdateCartItemDto): Promise<ShoppingCartResponseDto> {
    const { quantity } = updateCartItemDto;

    this.logger.log(`Updating cart item ${cartItemId} for user ${userId} to quantity ${quantity}`);

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // First, verify the cart item belongs to the user's active cart
      const cartItemCheck = await queryRunner.query(
        `SELECT ci.id, ci.quantity, c.id as cart_id
         FROM shopping_cart_item ci
         JOIN shopping_cart c ON ci.cart_id = c.id
         WHERE ci.id = $1 AND c.user_id = $2 AND c.status = $3`,
        [cartItemId, userId, ShoppingCartStatus.ACTIVE]
      );

      if (!cartItemCheck || cartItemCheck.length === 0) {
        throw new NotFoundException(`Cart item with ID ${cartItemId} not found in your active cart`);
      }

      const cartItem = cartItemCheck[0];
      this.logger.log(`Found cart item ${cartItemId} with current quantity ${cartItem.quantity}`);

      // Update the quantity
      await queryRunner.query(
        `UPDATE shopping_cart_item
         SET quantity = $1, updated_at = $2
         WHERE id = $3`,
        [quantity, new Date().toISOString(), cartItemId]
      );

      this.logger.log(`Updated cart item ${cartItemId} quantity to ${quantity}`);

      // Update the cart's last activity
      await queryRunner.query(
        `UPDATE shopping_cart
         SET last_activity = $1, updated_at = $2
         WHERE id = $3`,
        [new Date().toISOString(), new Date().toISOString(), cartItem.cart_id]
      );

      // Verify the update was successful
      const verifyUpdate = await queryRunner.query(
        `SELECT quantity FROM shopping_cart_item WHERE id = $1`,
        [cartItemId]
      );

      if (verifyUpdate && verifyUpdate.length > 0) {
        this.logger.log(`Verified cart item ${cartItemId} now has quantity ${verifyUpdate[0].quantity}`);
      } else {
        this.logger.warn(`Could not verify cart item ${cartItemId} update`);
      }

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Return the updated cart
      return this.getCartResponse(userId);
    } catch (error) {
      // Rollback the transaction in case of error, but only if it's active
      try {
        if (queryRunner.isTransactionActive) {
          await queryRunner.rollbackTransaction();
        }
      } catch (rollbackError) {
        this.logger.error(`Error during transaction rollback: ${rollbackError.message}`, rollbackError.stack);
      }

      this.logger.error(`Error updating cart item: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new InternalServerErrorException(`Failed to update cart item: ${error.message}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Remove an item from the cart
   * @param userId User ID
   * @param cartItemId Cart item ID
   * @returns The updated shopping cart
   */
  async removeCartItem(userId: string, cartItemId: string): Promise<ShoppingCartResponseDto> {
    this.logger.log(`Removing cart item ${cartItemId} for user ${userId}`);

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // First, verify the cart item belongs to the user's active cart
      const cartItemCheck = await queryRunner.query(
        `SELECT ci.id, ci.shop_item_id, c.id as cart_id
         FROM shopping_cart_item ci
         JOIN shopping_cart c ON ci.cart_id = c.id
         WHERE ci.id = $1 AND c.user_id = $2 AND c.status = $3`,
        [cartItemId, userId, ShoppingCartStatus.ACTIVE]
      );

      if (!cartItemCheck || cartItemCheck.length === 0) {
        throw new NotFoundException(`Cart item with ID ${cartItemId} not found in your active cart`);
      }

      const cartItem = cartItemCheck[0];
      this.logger.log(`Found cart item ${cartItemId} in cart ${cartItem.cart_id} for user ${userId}`);

      // Delete the cart item with direct SQL
      await queryRunner.query(
        `DELETE FROM shopping_cart_item WHERE id = $1`,
        [cartItemId]
      );

      this.logger.log(`Deleted cart item ${cartItemId}`);

      // Verify the item is actually gone
      const verifyDeleted = await queryRunner.query(
        `SELECT id FROM shopping_cart_item WHERE id = $1`,
        [cartItemId]
      );

      if (verifyDeleted && verifyDeleted.length > 0) {
        this.logger.warn(`Item ${cartItemId} still exists after deletion attempt!`);
        throw new Error(`Failed to delete cart item ${cartItemId}`);
      } else {
        this.logger.log(`Verified cart item ${cartItemId} no longer exists in database`);
      }

      // Update the cart's last activity
      await queryRunner.query(
        `UPDATE shopping_cart
         SET last_activity = $1, updated_at = $2
         WHERE id = $3`,
        [new Date().toISOString(), new Date().toISOString(), cartItem.cart_id]
      );

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Return the updated cart
      return this.getCartResponse(userId);
    } catch (error) {
      // Rollback the transaction in case of error, but only if it's active
      try {
        if (queryRunner.isTransactionActive) {
          await queryRunner.rollbackTransaction();
        }
      } catch (rollbackError) {
        this.logger.error(`Error during transaction rollback: ${rollbackError.message}`, rollbackError.stack);
      }

      this.logger.error(`Error removing cart item: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new InternalServerErrorException(`Failed to remove item from cart: ${error.message}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Clear the shopping cart
   * @param userId User ID
   * @returns The empty shopping cart
   */
  async clearCart(userId: string): Promise<ShoppingCartResponseDto> {
    this.logger.log(`Clearing cart for user ${userId}`);

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the active cart for the current user
      const cartQuery = await queryRunner.query(
        `SELECT id FROM shopping_cart
         WHERE user_id = $1 AND status = $2`,
        [userId, ShoppingCartStatus.ACTIVE]
      );

      if (!cartQuery || cartQuery.length === 0) {
        throw new NotFoundException(`Active cart not found for user ${userId}`);
      }

      const cartId = cartQuery[0].id;
      this.logger.log(`Found active cart with ID ${cartId} for user ${userId}`);

      // Count items before deletion for logging
      const countQuery = await queryRunner.query(
        `SELECT COUNT(*) as count FROM shopping_cart_item WHERE cart_id = $1`,
        [cartId]
      );

      const itemCount = countQuery[0].count;
      this.logger.log(`Found ${itemCount} items to delete from cart ${cartId}`);

      // Remove all items from the cart
      await queryRunner.query(
        `DELETE FROM shopping_cart_item WHERE cart_id = $1`,
        [cartId]
      );

      this.logger.log(`Deleted all items from cart ${cartId}`);

      // Verify items were deleted
      const verifyQuery = await queryRunner.query(
        `SELECT COUNT(*) as count FROM shopping_cart_item WHERE cart_id = $1`,
        [cartId]
      );

      if (verifyQuery[0].count > 0) {
        this.logger.warn(`Cart ${cartId} still has ${verifyQuery[0].count} items after deletion attempt`);
        throw new Error(`Failed to clear cart ${cartId}`);
      } else {
        this.logger.log(`Verified cart ${cartId} is now empty`);
      }

      // Update the cart's last activity
      await queryRunner.query(
        `UPDATE shopping_cart
         SET last_activity = $1, updated_at = $2
         WHERE id = $3`,
        [new Date().toISOString(), new Date().toISOString(), cartId]
      );

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Return the updated cart
      return this.getCartResponse(userId);
    } catch (error) {
      // Rollback the transaction in case of error
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error clearing cart: ${error.message}`, error.stack);

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new InternalServerErrorException(`Failed to clear cart: ${error.message}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Get the shopping cart response
   * @param userId User ID
   * @returns The shopping cart response
   */
  async getCartResponse(userId: string): Promise<ShoppingCartResponseDto> {
    this.logger.log(`Getting cart response for user ${userId}`);

    try {
      // Get the active cart with items using a direct query
      const cartQuery = await this.dataSource.query(
        `SELECT c.id, c.status, c.last_activity as "lastActivity"
         FROM shopping_cart c
         WHERE c.user_id = $1 AND c.status = $2`,
        [userId, ShoppingCartStatus.ACTIVE]
      );

      if (!cartQuery || cartQuery.length === 0) {
        // No active cart found
        this.logger.log(`No active cart found for user ${userId} in getCartResponse`);
        return {
          id: '',
          status: ShoppingCartStatus.ACTIVE,
          lastActivity: new Date(),
          items: [],
          totalPrice: 0,
          totalRewardPoints: 0,
          itemCount: 0,
        };
      }

      const cart = cartQuery[0];
      this.logger.log(`Found active cart with ID ${cart.id} for user ${userId}`);

      // Get cart items with a direct query, including category information
      const cartItemsQuery = await this.dataSource.query(
        `SELECT ci.id, ci.shop_item_id as "shopItemId", ci.quantity, ci.price, ci.reward_points as "rewardPoints",
                si.title, si.price as "originalPrice", si.promotion_id as "promotionId", si.file_path as "filePath",
                si.type, si.is_purchasable_in_rewardpoint as "isPurchasableInRewardpoint", si.discounted_price as "discountedPrice",
                si.is_promotion_active as "isPromotionActive", si.category_id as "categoryId", sc.name as "categoryName"
         FROM shopping_cart_item ci
         JOIN shop_item si ON ci.shop_item_id = si.id
         LEFT JOIN shop_category sc ON si.category_id = sc.id
         WHERE ci.cart_id = $1`,
        [cart.id]
      );

      this.logger.log(`Found ${cartItemsQuery.length} items in cart ${cart.id}`);

      // Map cart items to response DTOs
      const items = await Promise.all(cartItemsQuery.map(async (item: any) => {
        const imageUrl = item.filePath
          ? await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.SHOP_ITEM, item.shopItemId)
          : null;

        // Calculate discount information
        const originalPrice = Number(item.originalPrice);
        const finalPrice = Number(item.price);
        const hasActivePromotion = item.promotionId && item.isPromotionActive;
        const isOnSale = hasActivePromotion && item.discountedPrice !== null && Number(item.discountedPrice) < originalPrice;
        const discountPercentage = isOnSale
          ? Math.round(((originalPrice - Number(item.discountedPrice)) / originalPrice) * 100)
          : 0;

        return {
          id: item.id,
          shopItemId: item.shopItemId,
          title: item.title,
          originalPrice: originalPrice,
          price: finalPrice,
          discountPercentage: discountPercentage,
          isOnSale: isOnSale,
          promotionId: hasActivePromotion ? item.promotionId : null,
          rewardPoints: Number(item.rewardPoints),
          quantity: Number(item.quantity),
          totalPrice: Number(item.quantity) * finalPrice,
          totalRewardPoints: Number(item.quantity) * Number(item.rewardPoints),
          imageUrl,
          categoryId: item.categoryId || null,
          categoryName: item.categoryName || null,
        };
      }));

      // Calculate totals
      const totalPrice = items.reduce((sum, item) => sum + item.totalPrice, 0);
      const totalRewardPoints = items.reduce((sum, item) => sum + item.totalRewardPoints, 0);

      const response = {
        id: cart.id,
        status: cart.status,
        lastActivity: new Date(cart.lastActivity),
        items,
        totalPrice,
        totalRewardPoints,
        itemCount: items.length,
      };

      this.logger.log(`Returning cart response with ${items.length} items for user ${userId}`);
      return response;
    } catch (error) {
      this.logger.error(`Error getting cart response: ${error.message}`, error.stack);
      throw new InternalServerErrorException(`Failed to get shopping cart: ${error.message}`);
    }
  }

  /**
   * Process checkout
   * @param userId User ID
   * @param checkoutDto Checkout details
   * @returns Checkout response
   */
  async checkout(userId: string, checkoutDto: CheckoutDto): Promise<CheckoutResponseDto> {
    const { paymentMethod, useRewardPoints = false, promoCode } = checkoutDto;

    this.logger.log(`Processing checkout for user ${userId} with payment method ${paymentMethod}${promoCode ? ` and promo code ${promoCode}` : ''}`);

    // Start a transaction
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get the active cart
      const cart = await this.getActiveCart(userId);

      // Check if the cart is empty
      if (!cart.items || cart.items.length === 0) {
        throw new BadRequestException('Cannot checkout with an empty cart');
      }

      // Verify user exists
      const userQuery = await queryRunner.query(
        `SELECT id, email FROM "user" WHERE id = $1`,
        [userId]
      );

      if (!userQuery || userQuery.length === 0) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Get the current active reward point setting
      let conversionRate = 100; // Default to 100 if no setting is found
      try {
        const activeSetting = await this.rewardPointSettingService.getActiveRewardPointSetting();
        conversionRate = activeSetting.conversionRate;
        this.logger.log(`Using current active reward point setting with conversion rate: ${conversionRate}`);
      } catch (error) {
        this.logger.warn(`Could not get active reward point setting: ${error.message}. Using default conversion rate of 100.`);
      }

      // Apply promo code if provided
      let promoCodeApplied = false;
      let promoCodePromotionId = null;
      let promoCodeMessage = '';

      if (promoCode) {
        this.logger.log(`Applying promo code: ${promoCode}`);

        try {
          // First, get the promotion details to check for category restrictions
          let promotion: { id: string; applicable_category_ids: string[] } | null = null;
          try {
            // Find promotion by code
            const promotionResult = await queryRunner.query(
              `SELECT * FROM promotion WHERE promotion_code = $1`,
              [promoCode]
            );

            if (!promotionResult || promotionResult.length === 0) {
              this.logger.warn(`Promo code ${promoCode} not found`);
              promoCodeMessage = `Promo code ${promoCode} not found`;
              promotion = null;
            } else {
              promotion = promotionResult[0];
            }
          } catch (error) {
            this.logger.error(`Error fetching promotion by code: ${error.message}`, error.stack);
            promotion = null;
          }

          // Get shop items with their full details
          const shopItems = await Promise.all(cart.items.map(async (item) => {
            try {
              const shopItemResult = await queryRunner.query(
                `SELECT si.id, si.category_id, si.is_promotion_active, sc.name as category_name
                 FROM shop_item si
                 LEFT JOIN shop_category sc ON si.category_id = sc.id
                 WHERE si.id = $1`,
                [item.shopItemId]
              );
              if (shopItemResult && shopItemResult.length > 0) {
                return {
                  cartItem: item,
                  shopItem: shopItemResult[0]
                };
              }
            } catch (error) {
              this.logger.error(`Error fetching shop item details: ${error.message}`, error.stack);
            }
            return null;
          }));

          // Filter out null results and items with inactive promotions
          const shopItemsWithCategories = shopItems
            .filter(item => item !== null)
            .filter(item => {
              // Check if the item has a promotion and if it's active
              const hasActivePromotion = item.cartItem.promotionId && item.shopItem.is_promotion_active;
              return !item.cartItem.promotionId || hasActivePromotion;
            });

          // Check category restrictions if any
          const hasApplicableCategoryRestrictions = promotion?.applicable_category_ids && promotion.applicable_category_ids.length > 0;

          // Filter items that are in applicable categories
          const applicableItems = hasApplicableCategoryRestrictions
            ? shopItemsWithCategories.filter(item =>
                promotion.applicable_category_ids.includes(item.shopItem.category_id))
            : shopItemsWithCategories;

          if (hasApplicableCategoryRestrictions && applicableItems.length === 0) {
            this.logger.warn(`Promo code ${promoCode} is not applicable to any items in the cart`);
            promoCodeMessage = `Promo code ${promoCode} is not applicable to any items in the cart`;
          } else {
            // Calculate total original price for applicable items
            let totalOriginalPrice = 0;
            for (const item of applicableItems) {
              totalOriginalPrice += Number(item.cartItem.originalPrice) * Number(item.cartItem.quantity);
            }

            if (totalOriginalPrice > 0) {
              // Apply the promo code
              const promoResult = await this.promotionsService.applyPromotionCode({
                promotionCode: promoCode,
                originalPrice: totalOriginalPrice,
                itemType: PromotionApplicableType.SHOP_ITEM
              });

              if (promoResult.isApplied) {
                this.logger.log(`Promo code ${promoCode} applied successfully: ${promoResult.discountAmount} discount`);
                promoCodeApplied = true;
                promoCodePromotionId = promoResult.promotionId;
                promoCodeMessage = `Promo code ${promoCode} applied successfully`;

                // Adjust prices based on promo code discount
                const discountRatio = promoResult.discountAmount / totalOriginalPrice;

                // Only apply discount to applicable items
                for (const itemWithCategory of applicableItems) {
                  const item = itemWithCategory.cartItem;
                  // Calculate item's share of the discount
                  const itemDiscount = Number(item.originalPrice) * discountRatio;
                  const newPrice = Number(item.originalPrice) - itemDiscount;

                  // Update the item price in memory and database
                  item.price = newPrice;

                  await queryRunner.query(
                    `UPDATE shopping_cart_item
                     SET price = $1, updated_at = $2
                     WHERE id = $3`,
                    [newPrice, new Date().toISOString(), item.id]
                  );

                  this.logger.log(`Updated price for item ${item.shopItemId} from ${item.originalPrice} to ${newPrice} after promo code`);
                }
              } else {
                this.logger.warn(`Promo code ${promoCode} could not be applied: ${promoResult.message}`);
                promoCodeMessage = promoResult.message;
              }
            } else {
              this.logger.warn(`No applicable items with positive price found for promo code ${promoCode}`);
              promoCodeMessage = `No applicable items with positive price found for promo code ${promoCode}`;
            }
          }
        } catch (error) {
          this.logger.error(`Error applying promo code: ${error.message}`, error.stack);
          promoCodeMessage = `Error applying promo code: ${error.message}`;
          // Continue with checkout even if promo code application fails
        }
      }

      // Calculate totals and recalculate reward points using current conversion rate
      let totalPrice = 0;
      let totalRewardPoints = 0;

      for (const item of cart.items) {
        const itemPrice = Number(item.price) * Number(item.quantity);
        totalPrice += itemPrice;

        // Recalculate reward points using current conversion rate
        const isPurchasableWithRewardPoints = item.isPurchasableInRewardpoint === true;
        const isFreeItem = item.type === 'FREE';

        // Only recalculate if the item is purchasable with reward points and not free
        if (isPurchasableWithRewardPoints && !isFreeItem) {
          const recalculatedRewardPoints = Math.round(Number(item.price) * conversionRate) * Number(item.quantity);
          totalRewardPoints += recalculatedRewardPoints;

          // Update the item's reward points for consistency
          const updatedRewardPoints = Math.round(Number(item.price) * conversionRate);
          item.rewardPoints = updatedRewardPoints;

          // Update the reward points in the database
          await queryRunner.query(
            `UPDATE shopping_cart_item
             SET reward_points = $1, updated_at = $2
             WHERE id = $3`,
            [updatedRewardPoints, new Date().toISOString(), item.id]
          );

          this.logger.log(`Recalculated and updated reward points for item ${item.shopItemId}: ${item.rewardPoints} per unit, ${recalculatedRewardPoints} total`);
        } else {
          // Use the stored reward points for items that aren't purchasable with reward points
          totalRewardPoints += Number(item.rewardPoints) * Number(item.quantity);
        }
      }

      this.logger.log(`Cart totals: price=${totalPrice}, rewardPoints=${totalRewardPoints} (using conversion rate ${conversionRate})`);

      // Check if using reward points
      let rewardPointsUsed = 0;
      const userRewardPoints = await this.getUserRewardPoints(userId);

      // If payment method is reward points, user must have enough points for the entire purchase
      if (paymentMethod === PaymentMethod.REWARD_POINTS) {
        if (userRewardPoints < totalRewardPoints) {
          throw new BadRequestException(`Not enough reward points. Required: ${totalRewardPoints}, Available: ${userRewardPoints}`);
        }

        rewardPointsUsed = totalRewardPoints;
        this.logger.log(`Using ${rewardPointsUsed} reward points for the entire purchase`);
      }
      // If using reward points as partial payment with another payment method
      else if (useRewardPoints) {
        // Check if user has enough reward points
        if (userRewardPoints < totalRewardPoints) {
          throw new BadRequestException(`Not enough reward points. Required: ${totalRewardPoints}, Available: ${userRewardPoints}`);
        }

        rewardPointsUsed = totalRewardPoints;
        this.logger.log(`Using ${rewardPointsUsed} reward points for the purchase`);
        this.logger.log(`Payment gateway would process payment for amount: ${totalPrice}`);
      }
      // If using payment gateway only
      else {
        this.logger.log(`Payment gateway would process payment for amount: ${totalPrice}`);

        // Check if this is a KCP payment method
        const isKcpPayment = [PaymentMethod.KCP_CARD, PaymentMethod.KCP_BANK, PaymentMethod.KCP_MOBILE].includes(paymentMethod);

        if (isKcpPayment) {
          // For KCP payments, we need to initiate payment and return payment URL
          this.logger.log(`Initiating KCP payment for amount: ${totalPrice}`);

          // Create a temporary purchase record with PAYMENT_PENDING status
          const tempPurchaseId = await this.createTemporaryPurchase(queryRunner, userId, cart, totalPrice, paymentMethod, promoCodeApplied, promoCodePromotionId, promoCode, conversionRate);

          // Prepare payment initiation request
          const paymentRequest: InitiatePaymentDto = {
            orderId: `ORDER-${tempPurchaseId}-${Date.now()}`,
            amount: totalPrice,
            currency: 'KRW',
            productName: cart.items.length === 1 ? cart.items[0].title : `${cart.items.length} items`,
            buyerName: userQuery[0].email.split('@')[0], // Use email prefix as buyer name
            buyerEmail: userQuery[0].email,
            buyerPhone: '010-0000-0000', // Default phone number - should be collected from user
            paymentMethod: this.mapPaymentMethodToKcp(paymentMethod),
            purchaseType: PurchaseType.SHOP_ITEM,
            referenceId: tempPurchaseId,
            returnUrl: checkoutDto.returnUrl || `${process.env.FRONTEND_URL}/payment/success`,
            cancelUrl: checkoutDto.cancelUrl || `${process.env.FRONTEND_URL}/payment/cancel`,
            metadata: {
              cartId: cart.id,
              userId,
              promoCode: promoCodeApplied ? promoCode : null,
              rewardPointsUsed
            }
          };

          // Initiate payment with KCP
          const paymentResponse = await this.paymentService.initiatePayment(userId, paymentRequest);

          if (paymentResponse.success) {
            // Update the temporary purchase with payment transaction ID
            await queryRunner.query(
              `UPDATE shop_item_purchase
               SET payment_transaction_id = $1, updated_at = $2
               WHERE id = $3`,
              [paymentResponse.transactionId, new Date().toISOString(), tempPurchaseId]
            );

            // Commit the transaction
            await queryRunner.commitTransaction();

            // Return checkout response with payment URL
            const response: CheckoutResponseDto = {
              success: true,
              orderId: paymentRequest.orderId,
              totalAmount: totalPrice,
              rewardPointsUsed,
              remainingRewardPoints: await this.getUserRewardPoints(userId),
              paymentMethod,
              purchaseDate: new Date(),
              items: await this.mapCartItemsToResponse(cart.items),
              paymentTransactionId: paymentResponse.transactionId,
              paymentUrl: paymentResponse.paymentUrl,
              rewardPointInfo: {
                conversionRate,
                totalRewardPoints,
                message: `Reward points were calculated using the current conversion rate of ${conversionRate} points per unit of currency.`
              }
            };

            // Add promo code information if applicable
            if (promoCode) {
              response.promoCodeInfo = {
                code: promoCode,
                applied: promoCodeApplied,
                message: promoCodeMessage || (promoCodeApplied
                  ? `Promo code ${promoCode} was successfully applied to this order.`
                  : `Promo code ${promoCode} could not be applied to this order.`)
              };
            }

            return response;
          } else {
            throw new BadRequestException(`Payment initiation failed: ${paymentResponse.message}`);
          }
        } else {
          // For non-KCP payments, use existing logic
          if (checkoutDto.paymentDetails) {
            this.logger.log(`Payment details: ${JSON.stringify(checkoutDto.paymentDetails)}`);
          } else {
            this.logger.warn('No payment details provided for payment gateway');
          }
        }
      }

      // Create purchases for each item
      const purchases = [];
      for (const item of cart.items) {
        // Get the shop item details including category information
        const shopItemQuery = await queryRunner.query(
          `SELECT si.id, si.title, si.price, si.promotion_id, si.is_promotion_active, si.discounted_price,
                  si.category_id, sc.name as category_name
           FROM shop_item si
           LEFT JOIN shop_category sc ON si.category_id = sc.id
           WHERE si.id = $1`,
          [item.shopItemId]
        );

        if (!shopItemQuery || shopItemQuery.length === 0) {
          throw new NotFoundException(`Shop item with ID ${item.shopItemId} not found`);
        }

        const shopItem = shopItemQuery[0];

        // Calculate discount amount
        const originalPrice = Number(shopItem.price);
        const finalPrice = Number(item.price);
        const discountAmount = originalPrice !== finalPrice ? (originalPrice - finalPrice) * Number(item.quantity) : null;

        // Use the recalculated reward points from earlier
        const itemRewardPointsUsed = useRewardPoints || paymentMethod === PaymentMethod.REWARD_POINTS ?
          Number(item.rewardPoints) * Number(item.quantity) : 0;

        this.logger.log(`Using ${itemRewardPointsUsed} reward points for item ${item.shopItemId} (${shopItem.title})`);

        // Create purchase record with category information and conversion rate
        const now = new Date().toISOString();
        let purchaseNotes = `Purchase from category: ${shopItem.category_name || 'Unknown'}. Reward point conversion rate: ${conversionRate}.`;

        // Add promo code information if applicable
        if (promoCode && promoCodeApplied) {
          purchaseNotes += ` Promo code applied: ${promoCode}.`;
        }

        // Determine which promotion ID to use - promo code takes precedence over item promotion
        const promotionId = promoCodeApplied ? promoCodePromotionId : shopItem.promotion_id;

        const purchaseResult = await queryRunner.query(
          `INSERT INTO shop_item_purchase (
             user_id, shop_item_id, quantity, original_price, final_price,
             promotion_id, discount_amount, reward_points_used, payment_method, status,
             category_id, notes, created_at, updated_at, metadata
           )
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
           RETURNING id`,
          [
            userId, item.shopItemId, item.quantity, originalPrice, finalPrice,
            promotionId, discountAmount, itemRewardPointsUsed, paymentMethod, PurchaseStatus.COMPLETED,
            shopItem.category_id, purchaseNotes, now, now,
            JSON.stringify({
              rewardPointConversionRate: conversionRate,
              promoCodeApplied: promoCodeApplied,
              promoCode: promoCodeApplied ? promoCode : null
            })
          ]
        );

        const purchaseId = purchaseResult[0].id;
        purchases.push({ id: purchaseId, shopItemId: item.shopItemId });

        this.logger.log(`Created purchase record ${purchaseId} for shop item ${item.shopItemId}`);

        // Add the item to the student's owned items
        try {
          await this.studentOwnedItemService.addOwnedItem(userId, item.shopItemId, purchaseId);
          this.logger.log(`Added shop item ${item.shopItemId} to student ${userId}'s owned items`);
        } catch (error) {
          this.logger.error(`Error adding shop item to owned items: ${error.message}`, error.stack);
          // Don't throw the error, as the purchase was successful
        }
      }

      // Deduct reward points if used
      if (rewardPointsUsed > 0) {
        await this.deductRewardPoints(userId, rewardPointsUsed, 'Shop purchase');
      }

      // Mark the cart as checked out
      await queryRunner.query(
        `UPDATE shopping_cart
         SET status = $1, last_activity = $2, updated_at = $3
         WHERE id = $4`,
        [ShoppingCartStatus.CHECKED_OUT, new Date().toISOString(), new Date().toISOString(), cart.id]
      );

      this.logger.log(`Marked cart ${cart.id} as checked out`);

      // Commit the transaction
      await queryRunner.commitTransaction();

      // Map cart items to response DTOs
      const items = await Promise.all(cart.items.map(async (item: any) => {
        const imageUrl = item.filePath
          ? await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.SHOP_ITEM, item.shopItemId)
          : null;

        // Calculate discount information
        const originalPrice = Number(item.originalPrice);
        const finalPrice = Number(item.price);
        const hasActivePromotion = item.promotionId && item.isPromotionActive;
        const isOnSale = hasActivePromotion && item.discountedPrice !== null && Number(item.discountedPrice) < originalPrice;
        const discountPercentage = isOnSale
          ? Math.round(((originalPrice - Number(item.discountedPrice)) / originalPrice) * 100)
          : 0;

        // Prepare the response item
        return {
          id: item.id,
          shopItemId: item.shopItemId,
          title: item.title,
          originalPrice: originalPrice,
          price: finalPrice,
          discountPercentage: discountPercentage,
          isOnSale: isOnSale,
          promotionId: hasActivePromotion ? item.promotionId : null,
          rewardPoints: Number(item.rewardPoints),
          quantity: Number(item.quantity),
          totalPrice: Number(item.quantity) * finalPrice,
          totalRewardPoints: Number(item.quantity) * Number(item.rewardPoints),
          imageUrl,
          categoryId: item.categoryId || null,
          categoryName: item.categoryName || null,
        };
      }));

      // Get the updated reward points balance
      const remainingRewardPoints = await this.getUserRewardPoints(userId);

      // Prepare checkout response
      const response: CheckoutResponseDto = {
        success: true,
        orderId: purchases[0]?.id || '', // Use the first purchase ID as the order ID
        totalAmount: totalPrice,
        rewardPointsUsed,
        remainingRewardPoints,
        paymentMethod,
        purchaseDate: new Date(),
        items,
        rewardPointInfo: {
          conversionRate,
          totalRewardPoints,
          message: `Reward points were calculated using the current conversion rate of ${conversionRate} points per unit of currency.`
        }
      };

      // Add promo code information if applicable
      if (promoCode) {
        response.promoCodeInfo = {
          code: promoCode,
          applied: promoCodeApplied,
          message: promoCodeMessage || (promoCodeApplied
            ? `Promo code ${promoCode} was successfully applied to this order.`
            : `Promo code ${promoCode} could not be applied to this order.`)
        };
      }

      return response;
    } catch (error) {
      // Rollback the transaction in case of error, but only if it's active
      try {
        if (queryRunner.isTransactionActive) {
          await queryRunner.rollbackTransaction();
        }
      } catch (rollbackError) {
        this.logger.error(`Error during transaction rollback: ${rollbackError.message}`, rollbackError.stack);
      }

      this.logger.error(`Error processing checkout: ${error.message}`, error.stack);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException(`Failed to process checkout: ${error.message}`);
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Create a temporary purchase record for payment processing
   */
  private async createTemporaryPurchase(
    queryRunner: any,
    userId: string,
    cart: any,
    totalPrice: number,
    paymentMethod: PaymentMethod,
    promoCodeApplied: boolean,
    promoCodePromotionId: string | null,
    promoCode: string | null,
    conversionRate: number
  ): Promise<string> {
    const now = new Date().toISOString();
    let purchaseNotes = `Temporary purchase for payment processing. Reward point conversion rate: ${conversionRate}.`;

    if (promoCode && promoCodeApplied) {
      purchaseNotes += ` Promo code applied: ${promoCode}.`;
    }

    // Create a single purchase record representing the entire cart
    const firstItem = cart.items[0];
    const promotionId = promoCodeApplied ? promoCodePromotionId : firstItem.promotionId;

    const purchaseResult = await queryRunner.query(
      `INSERT INTO shop_item_purchase (
         user_id, shop_item_id, quantity, original_price, final_price,
         promotion_id, discount_amount, reward_points_used, payment_method, status,
         category_id, notes, created_at, updated_at, metadata
       )
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
       RETURNING id`,
      [
        userId, firstItem.shopItemId, 1, totalPrice, totalPrice,
        promotionId, null, 0, paymentMethod, PurchaseStatus.PAYMENT_PENDING,
        firstItem.categoryId, purchaseNotes, now, now,
        JSON.stringify({
          cartId: cart.id,
          itemCount: cart.items.length,
          rewardPointConversionRate: conversionRate,
          promoCodeApplied: promoCodeApplied,
          promoCode: promoCodeApplied ? promoCode : null,
          isTemporary: true
        })
      ]
    );

    return purchaseResult[0].id;
  }

  /**
   * Map payment method to KCP payment method
   */
  private mapPaymentMethodToKcp(paymentMethod: PaymentMethod): KcpPaymentMethod {
    switch (paymentMethod) {
      case PaymentMethod.KCP_CARD:
        return KcpPaymentMethod.CARD;
      case PaymentMethod.KCP_BANK:
        return KcpPaymentMethod.BANK;
      case PaymentMethod.KCP_MOBILE:
        return KcpPaymentMethod.MOBILE;
      default:
        return KcpPaymentMethod.CARD;
    }
  }

  /**
   * Map cart items to response format
   */
  private async mapCartItemsToResponse(cartItems: any[]): Promise<any[]> {
    return Promise.all(cartItems.map(async (item: any) => {
      const imageUrl = item.filePath
        ? await this.fileRegistryService.getFileUrlWithFallback(FileEntityType.SHOP_ITEM, item.shopItemId)
        : null;

      // Calculate discount information
      const originalPrice = Number(item.originalPrice);
      const finalPrice = Number(item.price);
      const hasActivePromotion = item.promotionId && item.isPromotionActive;
      const isOnSale = hasActivePromotion && item.discountedPrice !== null && Number(item.discountedPrice) < originalPrice;
      const discountPercentage = isOnSale
        ? Math.round(((originalPrice - Number(item.discountedPrice)) / originalPrice) * 100)
        : 0;

      return {
        id: item.id,
        shopItemId: item.shopItemId,
        title: item.title,
        originalPrice: originalPrice,
        price: finalPrice,
        discountPercentage: discountPercentage,
        isOnSale: isOnSale,
        promotionId: hasActivePromotion ? item.promotionId : null,
        rewardPoints: Number(item.rewardPoints),
        quantity: Number(item.quantity),
        totalPrice: Number(item.quantity) * finalPrice,
        totalRewardPoints: Number(item.quantity) * Number(item.rewardPoints),
        imageUrl,
        categoryId: item.categoryId || null,
        categoryName: item.categoryName || null,
      };
    }));
  }
}
