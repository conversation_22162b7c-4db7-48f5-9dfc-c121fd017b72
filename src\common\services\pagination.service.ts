import { Injectable } from '@nestjs/common';
import { PaginationDto } from '../models/pagination.dto';
import { PaginationMeta, PaginatedResponse } from '../models/api-response.model';
import { PagedListDto } from '../models/paged-list.dto';

@Injectable()
export class PaginationService {
  /**
   * Create a paginated response from an array of items
   * @param items The array of items to paginate
   * @param paginationDto The pagination parameters
   * @param totalItems The total number of items (if known)
   * @param message The success message
   * @returns A paginated response
   */
  createPaginatedResponse<T>(
    items: T[],
    paginationDto: PaginationDto,
    totalItems: number,
    message = 'Items retrieved successfully'
  ): PaginatedResponse<T> {
    const { page = 1, limit = 10 } = paginationDto;

    const totalPages = Math.ceil(totalItems / limit);

    const paginationMeta: PaginationMeta = {
      currentPage: page,
      itemsPerPage: limit,
      totalItems,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    };

    return {
      success: true,
      message,
      data: items,
      pagination: paginationMeta,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Calculate pagination parameters for database queries
   * @param paginationDto The pagination parameters
   * @returns An object with skip, take, page, and limit properties
   */
  getPaginationParameters(paginationDto: PaginationDto): {
    skip: number;
    take: number;
    page: number;
    limit: number;
  } {
    const { page = 1, limit = 10 } = paginationDto;

    return {
      skip: (page - 1) * limit,
      take: limit,
      page,
      limit
    };
  }

  /**
   * Create a paged list response from an array of items
   * @param items The array of items
   * @param totalCount The total number of items
   * @param paginationDto The pagination parameters
   * @returns A paged list response
   */
  createPagedList<T>(
    items: T[],
    totalCount: number,
    paginationDto: PaginationDto
  ): PagedListDto<T> {
    const { page = 1, limit = 10 } = paginationDto;
    return new PagedListDto<T>(items, totalCount, page, limit);
  }
}
