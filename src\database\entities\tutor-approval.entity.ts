import { Entity, Column } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';

export enum TutorApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

@Entity()
export class TutorApproval extends AuditableBaseEntity {
    @Column({ name: 'user_id' })
    userId: string;

    @Column({
      name: 'status',
      type: 'enum',
      enum: TutorApprovalStatus,
      default: TutorApprovalStatus.PENDING
    })
    status: TutorApprovalStatus;

    @Column({ name: 'admin_id', nullable: true })
    adminId: string;

    @Column({ name: 'admin_notes', nullable: true })
    adminNotes: string;

    @Column({ name: 'rejection_reason', nullable: true })
    rejectionReason: string;

    @Column({ name: 'approved_at', type: 'timestamp', nullable: true })
    approvedAt: Date;

    @Column({ name: 'rejected_at', type: 'timestamp', nullable: true })
    rejectedAt: Date;

    // CreatedAt and UpdatedAt are inherited from AuditableBaseEntity
}
