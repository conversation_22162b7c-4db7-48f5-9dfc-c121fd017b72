import { MigrationInterface, QueryRunner } from "typeorm";

export class AddTutorPermissionEntity1720000000001 implements MigrationInterface {
    name = 'AddTutorPermissionEntity1720000000001'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "tutor_permission" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "created_by" uuid,
                "updated_by" uuid,
                "tutor_id" uuid NOT NULL,
                "plan_feature_id" uuid NOT NULL,
                "granted_by" uuid NOT NULL,
                "is_active" boolean NOT NULL DEFAULT true,
                "notes" text,
                CONSTRAINT "PK_tutor_permission" PRIMARY KEY ("id")
            )
        `);
        
        await queryRunner.query(`
            ALTER TABLE "tutor_permission" 
            ADD CONSTRAINT "FK_tutor_permission_tutor" 
            FOREIGN KEY ("tutor_id") REFERENCES "user"("id") 
            ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        
        await queryRunner.query(`
            ALTER TABLE "tutor_permission" 
            ADD CONSTRAINT "FK_tutor_permission_plan_feature" 
            FOREIGN KEY ("plan_feature_id") REFERENCES "plan_feature"("id") 
            ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        
        await queryRunner.query(`
            ALTER TABLE "tutor_permission" 
            ADD CONSTRAINT "FK_tutor_permission_admin" 
            FOREIGN KEY ("granted_by") REFERENCES "user"("id") 
            ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "tutor_permission" DROP CONSTRAINT "FK_tutor_permission_admin"
        `);
        
        await queryRunner.query(`
            ALTER TABLE "tutor_permission" DROP CONSTRAINT "FK_tutor_permission_plan_feature"
        `);
        
        await queryRunner.query(`
            ALTER TABLE "tutor_permission" DROP CONSTRAINT "FK_tutor_permission_tutor"
        `);
        
        await queryRunner.query(`
            DROP TABLE "tutor_permission"
        `);
    }
}
