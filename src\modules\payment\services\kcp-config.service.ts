import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KcpConfig } from '../interfaces/kcp.interface';

@Injectable()
export class KcpConfigService {
  private readonly logger = new Logger(KcpConfigService.name);
  private readonly config: KcpConfig;

  constructor(private configService: ConfigService) {
    this.config = this.loadConfig();
    this.validateConfig();
  }

  private loadConfig(): KcpConfig {
    return {
      siteCd: this.configService.get<string>('KCP_SITE_CD'),
      siteKey: this.configService.get<string>('KCP_SITE_KEY'),
      apiUrl: this.configService.get<string>('KCP_API_URL', 'https://stg-spl.kcp.co.kr'),
      tradeRegUrl: this.configService.get<string>('KCP_TRADE_REG_URL', '/std/tradeReg/register'),
      paymentUrl: this.configService.get<string>('KCP_PAYMENT_URL', '/gw/enc/v1/payment'),
      webhookSecret: this.configService.get<string>('KCP_WEBHOOK_SECRET'),
      timeout: this.configService.get<number>('KCP_TIMEOUT', 30000),
      retryAttempts: this.configService.get<number>('KCP_RETRY_ATTEMPTS', 3),
      environment: this.configService.get<'development' | 'staging' | 'production'>('NODE_ENV', 'development')
    };
  }

  private validateConfig(): void {
    const requiredFields = ['siteCd', 'siteKey', 'webhookSecret'];
    const missingFields = requiredFields.filter(field => !this.config[field]);

    if (missingFields.length > 0) {
      const errorMessage = `Missing required KCP configuration: ${missingFields.join(', ')}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    this.logger.log('KCP configuration loaded successfully');
    this.logger.log(`Environment: ${this.config.environment}`);
    this.logger.log(`API URL: ${this.config.apiUrl}`);
    this.logger.log(`Site CD: ${this.config.siteCd}`);
  }

  getConfig(): KcpConfig {
    return { ...this.config };
  }

  getSiteCd(): string {
    return this.config.siteCd;
  }

  getSiteKey(): string {
    return this.config.siteKey;
  }

  getApiUrl(): string {
    return this.config.apiUrl;
  }

  getTradeRegUrl(): string {
    return `${this.config.apiUrl}${this.config.tradeRegUrl}`;
  }

  getPaymentUrl(): string {
    return `${this.config.apiUrl}${this.config.paymentUrl}`;
  }

  getWebhookSecret(): string {
    return this.config.webhookSecret;
  }

  getTimeout(): number {
    return this.config.timeout;
  }

  getRetryAttempts(): number {
    return this.config.retryAttempts;
  }

  isProduction(): boolean {
    return this.config.environment === 'production';
  }

  isStaging(): boolean {
    return this.config.environment === 'staging';
  }

  isDevelopment(): boolean {
    return this.config.environment === 'development';
  }

  /**
   * Get KCP certificate info (combination of site_cd and site_key)
   */
  getKcpCertInfo(): string {
    return `${this.config.siteCd}:${this.config.siteKey}`;
  }

  /**
   * Get request timeout for HTTP calls
   */
  getRequestTimeout(): number {
    return this.config.timeout;
  }

  /**
   * Get maximum retry attempts for failed requests
   */
  getMaxRetryAttempts(): number {
    return this.config.retryAttempts;
  }

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig(): {
    apiUrl: string;
    environment: string;
    timeout: number;
    retryAttempts: number;
  } {
    return {
      apiUrl: this.config.apiUrl,
      environment: this.config.environment,
      timeout: this.config.timeout,
      retryAttempts: this.config.retryAttempts
    };
  }

  /**
   * Validate webhook signature
   */
  validateWebhookSignature(payload: string, signature: string): boolean {
    try {
      // Implement signature validation logic here
      // This is a placeholder - actual implementation would use HMAC
      const crypto = require('crypto');
      const expectedSignature = crypto
        .createHmac('sha256', this.config.webhookSecret)
        .update(payload)
        .digest('hex');
      
      return signature === expectedSignature;
    } catch (error) {
      this.logger.error(`Error validating webhook signature: ${error.message}`);
      return false;
    }
  }

  /**
   * Generate order check value for KCP
   */
  generateOrderCheck(orderId: string, amount: number): string {
    try {
      const crypto = require('crypto');
      const data = `${orderId}${amount}${this.config.siteKey}`;
      return crypto.createHash('sha256').update(data).digest('hex');
    } catch (error) {
      this.logger.error(`Error generating order check: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get payment method configuration
   */
  getPaymentMethodConfig(paymentMethod: string): any {
    const baseConfig = {
      site_cd: this.config.siteCd,
      kcp_cert_info: this.getKcpCertInfo(),
      currency: 'KRW'
    };

    switch (paymentMethod) {
      case 'card':
        return {
          ...baseConfig,
          pay_method: '************',
          escw_used: 'N'
        };
      case 'bank':
        return {
          ...baseConfig,
          pay_method: '************',
          escw_used: 'N'
        };
      case 'mobile':
        return {
          ...baseConfig,
          pay_method: '************',
          escw_used: 'N'
        };
      case 'vacct':
        return {
          ...baseConfig,
          pay_method: '************',
          escw_used: 'N'
        };
      default:
        throw new Error(`Unsupported payment method: ${paymentMethod}`);
    }
  }
}
