import { <PERSON><PERSON><PERSON>, Column, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { UserRole } from "./user-role.entity";

@Entity()
export class Role extends AuditableBaseEntity {
  @Column({ name: 'name', unique: true })
  name: string;  // Example: "admin", "tutor", "student"

  @OneToMany(() => UserRole, userRole => userRole.role)
  userRoles: UserRole[];
}
