import { applyDecorators } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';
import { ApiResponse } from '../dto/api-response.dto';
import { UserPlanResponseDto } from '../../database/models/plans.dto';
import { SimplifiedPlanDto, SimplifiedFeatureDto } from '../../database/models/simplified-plan.dto';

/**
 * Custom decorator for documenting user plan history responses
 * @param description Description of the response
 * @returns Decorator
 */
export const ApiUserPlanHistoryResponse = (
  description = 'Returns the subscription history'
) => {
  return applyDecorators(
    ApiExtraModels(ApiResponse, UserPlanResponseDto, SimplifiedPlanDto, SimplifiedFeatureDto),
    ApiOkResponse({
      description,
      schema: {
        properties: {
          success: { type: 'boolean', example: true },
          message: { type: 'string', example: 'Subscription history retrieved successfully' },
          statusCode: { type: 'number', example: 200 },
          data: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
                userId: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
                planId: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
                planName: { type: 'string', example: 'Premium Plan' },
                startDate: { type: 'string', format: 'date-time', example: '2023-01-01T00:00:00.000Z' },
                endDate: { type: 'string', format: 'date-time', example: '2023-02-01T00:00:00.000Z' },
                isActive: { type: 'boolean', example: true },
                isPaid: { type: 'boolean', example: true },
                autoRenew: { type: 'boolean', example: true },
                plan: {
                  type: 'object',
                  properties: {
                    id: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
                    name: { type: 'string', example: 'Premium Plan' },
                    type: { type: 'string', example: 'pro' },
                    subscriptionType: { type: 'string', example: 'monthly' },
                    description: { type: 'string', example: 'Premium features' },
                    price: { type: 'number', example: 29.99 },
                    features: {
                      type: 'array',
                      items: {
                        type: 'object',
                        properties: {
                          id: { type: 'string', example: '123e4567-e89b-12d3-a456-426614174000' },
                          type: { type: 'string', example: 'hec_user_diary' },
                          name: { type: 'string', example: 'HEC User Diary' },
                          description: { type: 'string', example: 'Access to the HEC User Diary platform' }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        required: ['success', 'message', 'statusCode', 'data']
      },
    }),
  );
};
