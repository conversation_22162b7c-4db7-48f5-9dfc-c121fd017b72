import {
  <PERSON>,
  Get,
  Post,
  Put,
  Body,
  Param,
  UseGuards,
  Req,
  Parse<PERSON><PERSON><PERSON><PERSON><PERSON>,
  BadRequestException
} from '@nestjs/common';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiBody
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../../common/guards/jwt.guard';
import { TutorGuard } from '../../../common/guards/tutor.guard';
import { ApiResponse } from '../../../common/dto/api-response.dto';
import {
  ApiOkResponseWithType,
  ApiOkResponseWithArrayType,
  ApiErrorResponse
} from '../../../common/decorators/api-response.decorator';
import { NovelReviewService } from '../services/novel-review.service';
import {
  NovelEntryResponseDto,
  CreateNovelFeedbackDto,
  CreateNovelCorrectionDto,
  UpdateNovelCorrectionDto,
  CreateNovelReviewDto,
  NovelFeedbackResponseDto,
  NovelCorrectionResponseDto
} from '../../../database/models/novel.dto';

@ApiTags('tutor-novel')
@ApiBearerAuth('JWT-auth')
@Controller('tutor/novel')
@UseGuards(JwtAuthGuard, TutorGuard)
export class TutorNovelController {
  constructor(
    private readonly novelReviewService: NovelReviewService
  ) {}

  @Get('entries')
  @ApiOperation({ summary: 'Get novel entries assigned to tutor for review' })
  @ApiOkResponseWithArrayType(NovelEntryResponseDto, 'Retrieved novel entries for review')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required')
  async getTutorEntries(
    @Req() req: any
  ): Promise<ApiResponse<NovelEntryResponseDto[]>> {
    const result = await this.novelReviewService.getTutorEntries(req.user.id);
    return ApiResponse.success(result, 'Retrieved novel entries for review');
  }

  @Get('entries/:id')
  @ApiOperation({ summary: 'Get a specific novel entry for review' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiOkResponseWithType(NovelEntryResponseDto, 'Retrieved novel entry for review')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry not found')
  async getEntryForReview(
    @Req() req: any,
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponse<NovelEntryResponseDto>> {
    const result = await this.novelReviewService.getEntryForReview(req.user.id, id);
    return ApiResponse.success(result, 'Retrieved novel entry for review');
  }

  @Post('entries/:id/feedback')
  @ApiOperation({ summary: 'Submit feedback for a novel entry' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiBody({
    type: CreateNovelFeedbackDto,
    description: 'Feedback data',
    examples: {
      example1: {
        value: {
          feedback: 'Great character development! The dialogue feels natural and engaging. Consider adding more descriptive details about the setting.'
        }
      }
    }
  })
  @ApiOkResponseWithType(NovelFeedbackResponseDto, 'Feedback submitted successfully')
  @ApiErrorResponse(400, 'Invalid input data or entry not submitted')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry not found')
  async submitFeedback(
    @Req() req: any,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() createFeedbackDto: CreateNovelFeedbackDto
  ): Promise<ApiResponse<NovelFeedbackResponseDto>> {
    const result = await this.novelReviewService.submitFeedback(req.user.id, id, createFeedbackDto);
    return ApiResponse.success(result, 'Feedback submitted successfully', 201);
  }

  @Post('entries/:id/correction')
  @ApiOperation({ summary: 'Submit correction text for a novel entry (without score)' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiBody({
    type: CreateNovelCorrectionDto,
    description: 'Correction text data',
    examples: {
      example1: {
        value: {
          correction: 'Overall excellent work. The plot structure is well-developed and the characters are compelling. Minor grammar corrections needed in paragraphs 3 and 7.'
        }
      }
    }
  })
  @ApiOkResponseWithType(NovelCorrectionResponseDto, 'Correction submitted successfully')
  @ApiErrorResponse(400, 'Invalid input data, entry not submitted, or correction already exists')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry not found')
  async submitCorrection(
    @Req() req: any,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() createCorrectionDto: CreateNovelCorrectionDto
  ): Promise<ApiResponse<NovelCorrectionResponseDto>> {
    const result = await this.novelReviewService.submitCorrection(req.user.id, id, createCorrectionDto);
    return ApiResponse.success(result, 'Correction submitted successfully', 201);
  }

  @Put('entries/:id/correction')
  @ApiOperation({ summary: 'Update correction text for a novel entry (score cannot be modified)' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiBody({
    type: UpdateNovelCorrectionDto,
    description: 'Updated correction text data',
    examples: {
      example1: {
        value: {
          correction: 'Updated: Overall excellent work with improved character development. The plot structure is well-developed and engaging.'
        }
      }
    }
  })
  @ApiOkResponseWithType(NovelCorrectionResponseDto, 'Correction updated successfully')
  @ApiErrorResponse(400, 'Invalid input data, no access to correction, or score already set')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry or correction not found')
  async updateCorrection(
    @Req() req: any,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCorrectionDto: UpdateNovelCorrectionDto
  ): Promise<ApiResponse<NovelCorrectionResponseDto>> {
    const result = await this.novelReviewService.updateCorrection(req.user.id, id, updateCorrectionDto);
    return ApiResponse.success(result, 'Correction updated successfully');
  }

  @Post('entries/:id/review')
  @ApiOperation({ summary: 'Submit final review with correction and score for a novel entry' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiBody({
    type: CreateNovelReviewDto,
    description: 'Final review data with correction and score',
    examples: {
      example1: {
        value: {
          correction: 'Overall excellent work. The plot structure is well-developed and the characters are compelling. Minor grammar corrections needed in paragraphs 3 and 7.',
          score: 85
        }
      }
    }
  })
  @ApiOkResponseWithType(NovelCorrectionResponseDto, 'Review submitted successfully')
  @ApiErrorResponse(400, 'Invalid input data, entry not submitted, or already reviewed')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry not found')
  async submitReview(
    @Req() req: any,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() createReviewDto: CreateNovelReviewDto
  ): Promise<ApiResponse<NovelCorrectionResponseDto>> {
    if (!id || id === 'undefined' || id.trim() === '') {
      throw new BadRequestException('Entry ID is required and cannot be undefined');
    }
    const result = await this.novelReviewService.submitReview(req.user.id, id, createReviewDto);
    return ApiResponse.success(result, 'Review submitted successfully', 201);
  }

  @Post('entries/:id/confirm')
  @ApiOperation({ summary: 'Confirm a novel entry review' })
  @ApiParam({ name: 'id', description: 'Entry ID', type: String })
  @ApiOkResponseWithType(NovelEntryResponseDto, 'Entry confirmed successfully')
  @ApiErrorResponse(400, 'Bad request - Entry must be reviewed and have a score')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Tutor access required or no access to student')
  @ApiErrorResponse(404, 'Entry not found')
  async confirmEntry(
    @Req() req: any,
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponse<NovelEntryResponseDto>> {
    const result = await this.novelReviewService.confirmEntry(req.user.id, id);
    return ApiResponse.success(result, 'Entry confirmed successfully');
  }
}
