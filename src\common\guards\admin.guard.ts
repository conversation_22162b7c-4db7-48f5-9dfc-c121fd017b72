import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { UserType } from '../../database/entities/user.entity';
import { Messages } from '../../constants/messages';
import { JwtPayload } from '../../modules/auth/interfaces/jwt-payload.interface';

@Injectable()
export class AdminGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user as JwtPayload;

    if (!user) {
      throw new ForbiddenException(Messages.UNAUTHORIZED);
    }

    // Check if user is an admin
    if (user.type === UserType.ADMIN || (user.roles && Array.isArray(user.roles) && user.roles.includes('admin'))) {
      return true;
    }

    throw new ForbiddenException(Messages.FORBIDDEN);
  }
}
