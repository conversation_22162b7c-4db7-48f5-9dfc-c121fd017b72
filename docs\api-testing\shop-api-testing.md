# Shop API Testing Flow

This document outlines the testing flow for the Shop API endpoints.

## Prerequisites

Before testing the Shop API:

1. Ensure the HEC backend is running
2. Have valid authentication tokens for different user roles (admin, student)
3. Set up your API testing tool (<PERSON><PERSON> recommended)

## Shop Category Testing Flow

### Test Case 1: Get All Categories

1. Authenticate with any valid token
2. Send a GET request to `/api/shop/categories` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of categories
5. Verify pagination metadata (totalItems, currentPage, etc.)

### Test Case 2: Get Category by ID

1. Authenticate with any valid token
2. Send a GET request to `/api/shop/categories/{categoryId}`
3. Verify HTTP status code is 200 OK
4. Verify response contains the category details
5. Test with non-existent category ID and verify 404 Not Found response

### Test Case 3: Admin Category Management

1. Authenticate with an admin token
2. Test creating a new category (POST to `/api/admin/shop/categories`)
3. Test updating a category (PUT to `/api/admin/shop/categories/{categoryId}`)
4. Test deactivating a category (PUT with isActive=false)
5. Verify changes are persisted in the database
6. Test with non-admin token and verify 403 Forbidden responses

## Shop Item Testing Flow

### Test Case 1: Get All Shop Items

1. Authenticate with any valid token
2. Send a GET request to `/api/shop/items` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of shop items
5. Verify each item contains required fields (id, title, price, etc.)
6. Verify only active items are returned by default

### Test Case 2: Filter and Sort Shop Items

1. Test filtering by category
2. Test filtering by item type (FREE, IN_APP_PURCHASE)
3. Test filtering by price range
4. Test filtering by featured status
5. Test sorting by different fields (price, title, createdAt, etc.)
6. Verify filtered and sorted results match the criteria

### Test Case 3: Get Shop Item by ID

1. Authenticate with any valid token
2. Send a GET request to `/api/shop/items/{itemId}`
3. Verify HTTP status code is 200 OK
4. Verify response contains the complete item details
5. Test with non-existent item ID and verify 404 Not Found response

### Test Case 4: Admin Item Management

1. Authenticate with an admin token
2. Test creating a new shop item (POST to `/api/admin/shop/items`)
3. Test updating a shop item (PUT to `/api/admin/shop/items/{itemId}`)
4. Test deactivating a shop item (PUT with isActive=false)
5. Verify changes are persisted in the database
6. Test with non-admin token and verify 403 Forbidden responses

## Shopping Cart Testing Flow

### Test Case 1: Get Shopping Cart

1. Authenticate with a student token
2. Send a GET request to `/api/shop/cart`
3. Verify HTTP status code is 200 OK
4. Verify response contains cart details
5. Verify cart is empty for a new user
6. Verify cart persists between sessions

### Test Case 2: Add Item to Cart

1. Authenticate with a student token
2. Send a POST request to `/api/shop/cart/add` with valid item data
3. Verify HTTP status code is 200 OK
4. Verify item is added to the cart
5. Verify cart totals are updated correctly
6. Verify promotion information is included (originalPrice, price, discountPercentage, isOnSale, promotionId)
7. Verify the final price reflects any active promotions
8. Test adding the same item again and verify quantity is updated

### Test Case 3: Update Cart Item

1. Authenticate with a student token
2. Add an item to the cart
3. Send a PUT request to `/api/shop/cart/items/{cartItemId}` with updated quantity
4. Verify HTTP status code is 200 OK
5. Verify item quantity is updated
6. Verify cart totals are updated correctly
7. Test with quantity=0 and verify item is removed from cart

### Test Case 4: Remove Item from Cart

1. Authenticate with a student token
2. Add an item to the cart
3. Send a DELETE request to `/api/shop/cart/items/{cartItemId}`
4. Verify HTTP status code is 200 OK
5. Verify item is removed from the cart
6. Verify cart totals are updated correctly

### Test Case 5: Clear Cart

1. Authenticate with a student token
2. Add multiple items to the cart
3. Send a DELETE request to `/api/shop/cart`
4. Verify HTTP status code is 200 OK
5. Verify all items are removed from the cart
6. Verify cart totals are reset to zero

## Checkout Testing Flow

### Test Case 1: Checkout with Credit Card

1. Authenticate with a student token
2. Add items to the cart (including items with active promotions)
3. Send a POST request to `/api/shop/cart/checkout` with credit card payment method
4. Verify HTTP status code is 200 OK
5. Verify payment is processed correctly
6. Verify purchased items are added to the user's owned items
7. Verify cart is cleared after checkout
8. Verify purchase history is updated
9. Verify promotion information is preserved in purchase records (originalPrice, finalPrice, promotionId, discountAmount)

### Test Case 2: Checkout with Reward Points

1. Authenticate with a student token
2. Add items to the cart (including items with active promotions)
3. Send a POST request to `/api/shop/cart/checkout` with reward points payment method
4. Verify HTTP status code is 200 OK
5. Verify reward points are deducted from the user's balance
6. Verify purchased items are added to the user's owned items
7. Verify cart is cleared after checkout
8. Verify purchase history is updated
9. Verify promotion information is preserved in purchase records (originalPrice, finalPrice, promotionId, discountAmount)

### Test Case 3: Checkout with Promotions

1. Authenticate with a student token
2. Create a shop item with an active promotion (as admin)
3. Add the item to the cart
4. Verify the cart shows the discounted price and promotion details
5. Complete checkout
6. Verify the purchase record includes promotion details
7. Verify the owned item reflects the discounted price paid

### Test Case 4: Checkout Validation

1. Test checkout with empty cart
2. Test checkout with insufficient reward points
3. Test checkout with invalid payment details
4. Test checkout with items that are no longer available
5. Verify appropriate error responses for each case

## Reward Points Testing Flow

### Test Case 1: Get Reward Points Balance

1. Authenticate with a student token
2. Send a GET request to `/api/reward-points/balance`
3. Verify HTTP status code is 200 OK
4. Verify response contains the current reward points balance
5. Verify balance matches the user's earned minus spent points

### Test Case 2: Get Reward Points History

1. Authenticate with a student token
2. Send a GET request to `/api/reward-points/history` with pagination parameters
3. Verify HTTP status code is 200 OK
4. Verify response contains paginated list of reward point transactions
5. Verify both earned and spent transactions are included
6. Verify transaction details match the database records

### Test Case 3: Admin Reward Points Management

1. Authenticate with an admin token
2. Test adding reward points to a user (POST to `/api/admin/reward-points/add`)
3. Test deducting reward points from a user (POST to `/api/admin/reward-points/deduct`)
4. Verify changes are reflected in the user's balance
5. Verify transactions are recorded in the history
6. Test with non-admin token and verify 403 Forbidden responses

## Student Owned Items Testing Flow

### Test Case 1: Get Owned Items

1. Authenticate with a student token
2. Purchase shop items
3. Send a GET request to `/api/student/owned-items` with pagination parameters
4. Verify HTTP status code is 200 OK
5. Verify response contains paginated list of owned items
6. Verify purchased items appear in the list

### Test Case 2: Filter and Sort Owned Items

1. Test filtering by item category (skin, emoticon)
2. Test filtering by purchase date range
3. Test sorting by different fields (purchaseDate, title, etc.)
4. Verify filtered and sorted results match the criteria

### Test Case 3: Use Owned Item

1. Authenticate with a student token
2. Purchase a shop item
3. Send a POST request to `/api/student/owned-items/{itemId}/use`
4. Verify HTTP status code is 200 OK
5. Verify item is marked as in use
6. Verify appropriate settings are updated (e.g., default skin)

## Edge Cases and Security Testing

### Test Case 1: Role-Based Access Control

1. Authenticate with a student token
2. Attempt to access admin shop management endpoints
3. Verify 403 Forbidden responses
4. Authenticate with a tutor token
5. Attempt to access student-specific endpoints
6. Verify 403 Forbidden responses

### Test Case 2: Concurrent Operations

1. Simulate concurrent cart operations from the same user
2. Verify system handles race conditions correctly
3. Simulate concurrent checkout operations
4. Verify only one checkout process completes successfully

### Test Case 3: Item Availability

1. Test purchasing an item that becomes unavailable during the checkout process
2. Test purchasing an item that is deactivated
3. Test purchasing a limited quantity item when stock runs out
4. Verify appropriate error handling for each case
