# HEC Essay Module - Award Calculations

## Overview

The essay module has 4 awards across 2 categories, each with monthly and annual variants.

## Awards

### 1. Best Writer Award
### 2. Best Perfect Award

---

## 1. Best Writer Award

Recognizes students with exceptional writing quality and consistency in essay submissions.

### Criteria
- `ESSAY_PERFORMANCE` (essay quality based on tutor evaluations)

### Calculation Formula

```typescript
// Quality Score (70% weight) - based on average essay points
const qualityScore = Math.min(averageScore * 10, 100);

// Quantity Score (30% weight) - based on number of completed essays
const quantityRatio = Math.min(submissions.length / minEssays, 2);
const quantityScore = Math.min(quantityRatio * 50, 100);

// Final Writer Score
const writerScore = (qualityScore * 0.7) + (quantityScore * 0.3);
```

### Configuration
- **Monthly**: Min score 7, 3+ essays, 150 points
- **Annual**: Min score 8, 30+ essays, 500 points

### Example Calculation

**Student Profile (Monthly)**:
- 5 essay submissions
- Average score: 8.2 points
- Required minimum: 3 essays, 7+ average

**Calculation**:
1. **Quality Score**: `min(8.2 * 10, 100) = 82`
2. **Quantity Score**: `min((5/3) * 50, 100) = 83.3`
3. **Final Score**: `(82 * 0.7) + (83.3 * 0.3) = 82.4`

**Result**: 82.4/100 - Strong writer performance

---

## 2. Best Perfect Award

Recognizes students who excel in all aspects of essay writing - quality, completion, and consistency.

### Criteria
- `ESSAY_PERFORMANCE` (comprehensive essay performance)

### Calculation Formula

```typescript
// Essay Quality (60% weight) - based on average essay scores
const qualityScore = Math.min(averageScore * 10, 100);

// Completion Rate (40% weight) - percentage of assigned tasks completed
const completionScore = Math.min(completionRate, 100);

// Final Perfect Score
const perfectScore = (qualityScore * 0.6) + (completionScore * 0.4);
```

### Configuration
- **Monthly**: Min score 7, 5+ essays, 90%+ completion, 200 points
- **Annual**: Min score 8, 40+ essays, 85%+ completion, 750 points

### Example Calculation

**Student Profile (Monthly)**:
- 6 essay submissions
- Average score: 8.5 points
- Completion rate: 95%
- Available tasks: 6, completed: 6

**Calculation**:
1. **Quality Score**: `min(8.5 * 10, 100) = 85`
2. **Completion Score**: `min(95, 100) = 95`
3. **Final Score**: `(85 * 0.6) + (95 * 0.4) = 89`

**Result**: 89/100 - Excellent all-around performance

---

## Award Filtering

### Client Requirements Only
The system only processes awards that match the exact client requirements:
- **Best Writer Award**
- **Best Perfect Award**

Any additional awards created outside the seeder are automatically filtered out during calculation to ensure only client-specified awards are considered.

## Scheduler

### Automated Generation
- **Monthly Awards**: 1st of each month at 02:00 UTC
- **Annual Awards**: January 1st at 03:00 UTC

### Implementation
```typescript
@Injectable()
export class SimplifiedAwardScheduler {
  // Monthly awards: 1st of each month at 2:00 AM UTC
  @Cron('0 2 1 * *')
  async generateMonthlyAwards() {
    const lastMonth = addMonthsUTC(getCurrentUTCDate(), -1);
    const startDate = getStartOfMonthUTC(lastMonth);
    const endDate = getEndOfMonthUTC(lastMonth);
    await Promise.all([
      this.diaryAwardService.generateAwardsForRange(startDate, endDate),
      this.essayAwardService.generateAwardsForRange(startDate, endDate),
      this.novelAwardService.generateAwardsForRange(startDate, endDate)
    ]);
  }

  // Annual awards: January 1st at 3:00 AM UTC
  @Cron('0 3 1 1 *')
  async generateAnnualAwards() {
    const lastYear = getCurrentUTCDate().getUTCFullYear() - 1;
    const startDate = new Date(Date.UTC(lastYear, 0, 1));
    const endDate = new Date(Date.UTC(lastYear, 11, 31, 23, 59, 59, 999));
    await Promise.all([
      this.diaryAwardService.generateAwardsForRange(startDate, endDate),
      this.essayAwardService.generateAwardsForRange(startDate, endDate),
      this.novelAwardService.generateAwardsForRange(startDate, endDate)
    ]);
  }
}
```

### Manual Triggers
```bash
# Check status
GET /api/award-scheduler/status

# Generate monthly awards for December 2023
POST /api/award-scheduler/trigger/monthly?year=2023&month=12

# Generate annual awards for 2023
POST /api/award-scheduler/trigger/annual?year=2023
```
