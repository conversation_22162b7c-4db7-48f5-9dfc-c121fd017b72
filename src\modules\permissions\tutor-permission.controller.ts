import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete,
  Param, 
  Body, 
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiBody, ApiQuery, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { AdminGuard } from '../../common/guards/admin.guard';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from '../../common/decorators/api-response.decorator';
import { 
  CreateTutorPermissionDto,
  UpdateTutorPermissionDto,
  TutorPermissionResponseDto,
  TutorPermissionPaginationDto
} from '../../database/models/tutor-permission.dto';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { TutorPermissionService } from './tutor-permission.service';
import { ApiOkResponseWithEmptyData } from 'src/common/decorators/api-empty-response.decorator';

@Controller('admin/permissions')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth('JWT-auth')
@ApiTags('Admin Permissions')
export class TutorPermissionController {
  constructor(private readonly tutorPermissionService: TutorPermissionService) {}

  @Post()
  @ApiOperation({ summary: 'Grant feature management permission to a tutor' })
  @ApiBody({
    type: CreateTutorPermissionDto,
    description: 'Tutor permission creation data',
    examples: {
      example1: {
        value: {
          tutorId: '123e4567-e89b-12d3-a456-426614174000',
          planFeatureId: '123e4567-e89b-12d3-a456-426614174001',
          notes: 'Assigned to handle English grammar Q&A'
        }
      }
    }
  })
  @ApiOkResponseWithType(TutorPermissionResponseDto, 'Permission granted successfully')
  @ApiErrorResponse(400, 'Invalid input data or user is not a tutor')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Tutor or plan feature not found')
  @ApiErrorResponse(409, 'Tutor already has permission for this plan feature')
  async create(
    @Body() createTutorPermissionDto: CreateTutorPermissionDto,
    @Request() req
  ): Promise<ApiResponse<TutorPermissionResponseDto>> {
    const result = await this.tutorPermissionService.create(createTutorPermissionDto, req.user.id);
    return ApiResponse.success(
      result,
      'Permission granted successfully',
      201
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get all tutor permissions' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction',
  })
  @ApiQuery({
    name: 'isActive',
    required: false,
    type: Boolean,
    description: 'Filter by active status',
  })
  @ApiQuery({
    name: 'planFeatureId',
    required: false,
    type: String,
    description: 'Filter by plan feature ID',
  })
  @ApiQuery({
    name: 'searchTerm',
    required: false,
    type: String,
    description: 'Search term for tutor name or email',
  })
  @ApiOkResponseWithPagedListType(TutorPermissionResponseDto, 'Permissions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  async findAll(
    @Query() paginationDto: TutorPermissionPaginationDto
  ): Promise<ApiResponse<PagedListDto<TutorPermissionResponseDto>>> {
    const result = await this.tutorPermissionService.findAll(paginationDto);
    return ApiResponse.success(result, 'Permissions retrieved successfully');
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific tutor permission by ID' })
  @ApiParam({
    name: 'id',
    description: 'ID of the permission to retrieve',
    type: String
  })
  @ApiOkResponseWithType(TutorPermissionResponseDto, 'Permission retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Permission not found')
  async findOne(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponse<TutorPermissionResponseDto>> {
    const result = await this.tutorPermissionService.findOne(id);
    return ApiResponse.success(result, 'Permission retrieved successfully');
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a tutor permission' })
  @ApiParam({
    name: 'id',
    description: 'ID of the permission to update',
    type: String
  })
  @ApiBody({
    type: UpdateTutorPermissionDto,
    description: 'Permission update data',
    examples: {
      example1: {
        value: {
          isActive: true,
          notes: 'Updated to handle all Q&A categories'
        }
      }
    }
  })
  @ApiOkResponseWithType(TutorPermissionResponseDto, 'Permission updated successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Permission not found')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTutorPermissionDto: UpdateTutorPermissionDto
  ): Promise<ApiResponse<TutorPermissionResponseDto>> {
    const result = await this.tutorPermissionService.update(id, updateTutorPermissionDto);
    return ApiResponse.success(result, 'Permission updated successfully');
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a tutor permission' })
  @ApiParam({
    name: 'id',
    description: 'ID of the permission to delete',
    type: String
  })
  @ApiOkResponseWithEmptyData('Permission deleted successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'Permission not found')
  async remove(
    @Param('id', ParseUUIDPipe) id: string
  ): Promise<ApiResponse<null>> {
    await this.tutorPermissionService.remove(id);
    return ApiResponse.success(null, 'Permission deleted successfully');
  }
}
