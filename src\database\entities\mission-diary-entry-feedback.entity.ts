import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { MissionDiaryEntry } from './mission-diary-entry.entity';
import { User } from './user.entity';

@Entity()
export class MissionDiaryEntryFeedback extends AuditableBaseEntity {
  @Column({ name: 'mission_entry_id', type: 'uuid' })
  missionEntryId: string;

  @ManyToOne(() => MissionDiaryEntry, entry => entry.feedbacks)
  @JoinColumn({ name: 'mission_entry_id' })
  missionEntry: MissionDiaryEntry;

  @Column({ name: 'tutor_id', type: 'uuid' })
  tutorId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'tutor_id' })
  tutor: User;

  @Column({ name: 'feedback', type: 'text' })
  feedback: string;

  @Column({ name: 'rating', type: 'integer', nullable: true })
  rating: number;
}
