import { ApiProperty } from '@nestjs/swagger';
import { PlanType, SubscriptionType } from '../entities/plan.entity';
import { FeatureType } from '../entities/plan-feature.entity';

/**
 * Simplified feature object for use in plan responses
 */
export class SimplifiedFeatureDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'The ID of the feature'
  })
  id: string;

  @ApiProperty({
    example: 'hec_user_diary',
    description: 'The type of the feature',
    enum: FeatureType
  })
  type: FeatureType;

  @ApiProperty({
    example: 'HEC User Diary',
    description: 'The name of the feature'
  })
  name: string;

  @ApiProperty({
    example: 'Access to the HEC User Diary platform',
    description: 'Description of the feature'
  })
  description: string;
}

/**
 * Simplified plan object for use in user plan responses
 */
export class SimplifiedPlanDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'The ID of the plan'
  })
  id: string;

  @ApiProperty({
    example: 'Premium Plan',
    description: 'The name of the plan'
  })
  name: string;

  @ApiProperty({
    example: 'pro',
    description: 'The type of the plan',
    enum: PlanType
  })
  type: PlanType;

  @ApiProperty({
    example: 'monthly',
    description: 'The subscription type (monthly or yearly)',
    enum: SubscriptionType
  })
  subscriptionType: SubscriptionType;

  @ApiProperty({
    example: 'Access to all premium features',
    description: 'Description of the plan'
  })
  description: string;

  @ApiProperty({
    example: 99.99,
    description: 'The price of the plan'
  })
  price: number;

  @ApiProperty({
    type: [SimplifiedFeatureDto],
    description: 'Features included in the plan'
  })
  features: SimplifiedFeatureDto[];

  @ApiProperty({
    example: true,
    description: 'Whether the plan is applicable for promotion',
    required: false
  })
  isApplicableForPromotion?: boolean;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'The ID of the promotion applied to this plan',
    required: false
  })
  promotionId?: string;
}
