import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Brackets } from 'typeorm';
import { StudentOwnedItem, OwnedItemStatus } from '../../database/entities/student-owned-item.entity';
import { ShopItem } from '../../database/entities/shop-item.entity';
import { User } from '../../database/entities/user.entity';
import { StudentOwnedItemResponseDto, UpdateStudentOwnedItemDto, StudentOwnedItemsResponseDto } from '../../database/models/student-owned-item.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import { FileRegistryService } from '../../common/services/file-registry.service';
import { FileEntityType } from '../../common/enums/file-entity-type.enum';

@Injectable()
export class StudentOwnedItemService {
  private readonly logger = new Logger(StudentOwnedItemService.name);

  constructor(
    @InjectRepository(StudentOwnedItem)
    private studentOwnedItemRepository: Repository<StudentOwnedItem>,
    @InjectRepository(ShopItem)
    private shopItemRepository: Repository<ShopItem>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private fileRegistryService: FileRegistryService,
  ) {}

  /**
   * Add a purchased item to a student's owned items
   * @param studentId Student ID
   * @param shopItemId Shop item ID
   * @param purchaseId Purchase ID
   * @returns The created owned item
   */  async addOwnedItem(studentId: string, shopItemId: string, purchaseId: string): Promise<StudentOwnedItem> {
    try {
      // Check if student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId }
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Check if shop item exists with its relations
      const shopItem = await this.shopItemRepository.findOne({
        where: { id: shopItemId },
        relations: ['category']
      });

      if (!shopItem) {
        throw new NotFoundException(`Shop item with ID ${shopItemId} not found`);
      }

      // Check if student already owns this item
      const existingItem = await this.studentOwnedItemRepository.findOne({
        where: { studentId, shopItemId },
        relations: ['shopItem', 'shopItem.category']
      });

      if (existingItem) {
        // If the item already exists, just update the status and return it
        existingItem.status = OwnedItemStatus.AVAILABLE;
        existingItem.acquiredDate = new Date();
        existingItem.purchaseId = purchaseId;
        return this.studentOwnedItemRepository.save(existingItem);
      }

      // Create a new owned item
      const ownedItem = this.studentOwnedItemRepository.create({
        studentId,
        shopItemId,
        purchaseId,
        status: OwnedItemStatus.AVAILABLE,
        acquiredDate: new Date(),
      });

      return this.studentOwnedItemRepository.save(ownedItem);
    } catch (error) {
      this.logger.error(`Error adding owned item: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get all items owned by a student
   * @param studentId Student ID
   * @param category Optional category to filter by (can be category ID or name)
   * @param status Optional status to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of owned items
   */
  async getStudentOwnedItems(
    studentId: string,
    category?: string,
    status?: OwnedItemStatus,
    paginationDto?: PaginationDto
  ): Promise<StudentOwnedItemsResponseDto> {
    try {
      // Check if student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId }
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Build query
      const query = this.studentOwnedItemRepository.createQueryBuilder('ownedItem')
        .leftJoinAndSelect('ownedItem.shopItem', 'shopItem')
        .leftJoinAndSelect('shopItem.category', 'category')
        .where('ownedItem.studentId = :studentId', { studentId });

      // Apply filters
      if (category) {
        // Try filtering by both ID and name
        query.andWhere(new Brackets(qb => {
          qb.where('category.id = :categoryId', { categoryId: category })
            .orWhere('LOWER(category.name) = LOWER(:categoryName)', { categoryName: category });
        }));
      }

      if (status) {
        query.andWhere('ownedItem.status = :status', { status });
      }

      // Get total count
      const totalItems = await query.getCount();

      // Apply pagination
      const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto || {};
      const skip = (page - 1) * limit;

      query.skip(skip).take(limit);

      // Apply sorting
      if (sortBy && sortDirection) {
        if (sortBy.startsWith('shopItem.')) {
          query.orderBy(sortBy, sortDirection as 'ASC' | 'DESC');
        } else {
          query.orderBy(`ownedItem.${sortBy}`, sortDirection as 'ASC' | 'DESC');
        }
      } else {
        query.orderBy('ownedItem.acquiredDate', 'DESC');
      }

      // Get results
      const ownedItems = await query.getMany();

      // Map to DTOs
      const itemPromises = ownedItems.map(async (item) => {
        const dto: StudentOwnedItemResponseDto = {
          id: item.id,
          shopItemId: item.shopItemId,
          title: item.shopItem.title,
          description: item.shopItem.description,
          categoryName: item.shopItem.category?.name || 'Unknown',
          status: item.status,
          acquiredDate: item.acquiredDate,
          expiryDate: item.expiryDate,
          lastUsedDate: item.lastUsedDate,
          isFavorite: item.isFavorite,
          notes: item.notes,
          filePath: null
        };

        // Get file URL if available
        if (item.shopItem.filePath) {
          try {
            dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(
              FileEntityType.SHOP_ITEM,
              item.shopItemId
            );
          } catch (error) {
            this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
          }
        }

        return dto;
      });

      const items = await Promise.all(itemPromises);

      // Calculate pagination info
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        totalItems,
        itemsPerPage: limit,
        currentPage: page,
        totalPages
      };
    } catch (error) {
      this.logger.error(`Error getting student owned items: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific owned item
   * @param id Owned item ID
   * @param studentId Student ID
   * @returns The owned item
   */
  async getOwnedItemById(id: string, studentId: string): Promise<StudentOwnedItemResponseDto> {
    try {
      const ownedItem = await this.studentOwnedItemRepository.findOne({
        where: { id, studentId },
        relations: ['shopItem']
      });

      if (!ownedItem) {
        throw new NotFoundException(`Owned item with ID ${id} not found for student ${studentId}`);
      }

      const dto: StudentOwnedItemResponseDto = {
        id: ownedItem.id,
        shopItemId: ownedItem.shopItemId,
        title: ownedItem.shopItem.title,
        description: ownedItem.shopItem.description,
        categoryName: ownedItem.shopItem.category?.name || 'Unknown',
        status: ownedItem.status,
        acquiredDate: ownedItem.acquiredDate,
        expiryDate: ownedItem.expiryDate,
        lastUsedDate: ownedItem.lastUsedDate,
        isFavorite: ownedItem.isFavorite,
        notes: ownedItem.notes,
        filePath: null
      };

      // Get file URL if available
      if (ownedItem.shopItem.filePath) {
        try {
          dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(
            FileEntityType.SHOP_ITEM,
            ownedItem.shopItemId
          );
        } catch (error) {
          this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
        }
      }

      return dto;
    } catch (error) {
      this.logger.error(`Error getting owned item: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update an owned item
   * @param id Owned item ID
   * @param studentId Student ID
   * @param updateDto Update data
   * @returns The updated owned item
   */
  async updateOwnedItem(
    id: string,
    studentId: string,
    updateDto: UpdateStudentOwnedItemDto
  ): Promise<StudentOwnedItemResponseDto> {
    try {
      const ownedItem = await this.studentOwnedItemRepository.findOne({
        where: { id, studentId },
        relations: ['shopItem']
      });

      if (!ownedItem) {
        throw new NotFoundException(`Owned item with ID ${id} not found for student ${studentId}`);
      }

      // Update fields
      if (updateDto.status !== undefined) {
        ownedItem.status = updateDto.status;

        // If status is changed to IN_USE, update lastUsedDate
        if (updateDto.status === OwnedItemStatus.IN_USE) {
          ownedItem.lastUsedDate = new Date();
        }
      }

      if (updateDto.isFavorite !== undefined) {
        ownedItem.isFavorite = updateDto.isFavorite;
      }

      if (updateDto.notes !== undefined) {
        ownedItem.notes = updateDto.notes;
      }

      // Save changes
      await this.studentOwnedItemRepository.save(ownedItem);

      // Return updated item
      return this.getOwnedItemById(id, studentId);
    } catch (error) {
      this.logger.error(`Error updating owned item: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a student owns a specific item
   * @param studentId Student ID
   * @param shopItemId Shop item ID
   * @returns True if the student owns the item
   */
  async doesStudentOwnItem(studentId: string, shopItemId: string): Promise<boolean> {
    try {
      const ownedItem = await this.studentOwnedItemRepository.findOne({
        where: { studentId, shopItemId, status: OwnedItemStatus.AVAILABLE }
      });

      return !!ownedItem;
    } catch (error) {
      this.logger.error(`Error checking if student owns item: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get student owned items grouped by category
   * @param studentId Student ID
   * @param status Optional status to filter by
   * @returns Owned items grouped by category
   */
  async getStudentOwnedItemsGroupedByCategory(
    studentId: string,
    status?: OwnedItemStatus
  ): Promise<{ [category: string]: StudentOwnedItemResponseDto[] }> {
    try {
      // Check if student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId }
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Build query
      const query = this.studentOwnedItemRepository.createQueryBuilder('ownedItem')
        .leftJoinAndSelect('ownedItem.shopItem', 'shopItem')
        .leftJoinAndSelect('shopItem.category', 'category')
        .where('ownedItem.studentId = :studentId', { studentId });

      // Apply status filter if provided
      if (status) {
        query.andWhere('ownedItem.status = :status', { status });
      }

      // Order by category name and then by acquisition date
      query.orderBy('category.name', 'ASC')
        .addOrderBy('ownedItem.acquiredDate', 'DESC');

      // Get results
      const ownedItems = await query.getMany();

      // Map to DTOs
      const itemPromises = ownedItems.map(async (item) => {
        const dto: StudentOwnedItemResponseDto = {
          id: item.id,
          shopItemId: item.shopItemId,
          title: item.shopItem.title,
          description: item.shopItem.description,
          categoryId: item.shopItem.categoryId,
          categoryName: item.shopItem.category?.name || 'Unknown',
          shopItemCategory: item.shopItem.category?.name ,
          status: item.status,
          acquiredDate: item.acquiredDate,
          expiryDate: item.expiryDate,
          lastUsedDate: item.lastUsedDate,
          isFavorite: item.isFavorite,
          notes: item.notes,
          filePath: null
        };

        // Get file URL if available
        if (item.shopItem.filePath) {
          try {
            dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(
              FileEntityType.SHOP_ITEM,
              item.shopItemId
            );
          } catch (error) {
            this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
          }
        }

        return dto;
      });

      const items = await Promise.all(itemPromises);

      // Group by category
      const groupedItems = items.reduce((groups, item) => {
        const category = item.categoryName || 'Unknown';
        if (!groups[category]) {
          groups[category] = [];
        }
        groups[category].push(item);
        return groups;
      }, {});

      return groupedItems;
    } catch (error) {
      this.logger.error(`Error getting student owned items grouped by category: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get student owned items by category
   * @param studentId Student ID
   * @param categoryId Category ID
   * @param status Optional status to filter by
   * @param paginationDto Optional pagination parameters
   * @returns Paged list of owned items in the specified category
   */
  async getStudentOwnedItemsByCategory(
    studentId: string,
    categoryId: string,
    status?: OwnedItemStatus,
    paginationDto?: PaginationDto
  ): Promise<StudentOwnedItemsResponseDto> {
    try {
      // Check if student exists
      const student = await this.userRepository.findOne({
        where: { id: studentId }
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Build query
      const query = this.studentOwnedItemRepository.createQueryBuilder('ownedItem')
        .leftJoinAndSelect('ownedItem.shopItem', 'shopItem')
        .where('ownedItem.studentId = :studentId', { studentId })
        .andWhere('shopItem.categoryId = :categoryId', { categoryId });

      // Apply status filter if provided
      if (status) {
        query.andWhere('ownedItem.status = :status', { status });
      }

      // Get total count
      const totalItems = await query.getCount();

      // Apply pagination
      const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto || {};
      const skip = (page - 1) * limit;

      query.skip(skip).take(limit);

      // Apply sorting
      if (sortBy && sortDirection) {
        if (sortBy.startsWith('shopItem.')) {
          query.orderBy(sortBy, sortDirection as 'ASC' | 'DESC');
        } else {
          query.orderBy(`ownedItem.${sortBy}`, sortDirection as 'ASC' | 'DESC');
        }
      } else {
        query.orderBy('ownedItem.acquiredDate', 'DESC');
      }

      // Get results
      const ownedItems = await query.getMany();

      // Map to DTOs
      const itemPromises = ownedItems.map(async (item) => {
        const dto: StudentOwnedItemResponseDto = {
          id: item.id,
          shopItemId: item.shopItemId,
          title: item.shopItem.title,
          description: item.shopItem.description,
          categoryId: item.shopItem.categoryId,
          categoryName: item.shopItem.category?.name || 'Unknown',
          shopItemCategory: item.shopItem.category?.name ,
          status: item.status,
          acquiredDate: item.acquiredDate,
          expiryDate: item.expiryDate,
          lastUsedDate: item.lastUsedDate,
          isFavorite: item.isFavorite,
          notes: item.notes,
          filePath: null
        };

        // Get file URL if available
        if (item.shopItem.filePath) {
          try {
            dto.filePath = await this.fileRegistryService.getFileUrlWithFallback(
              FileEntityType.SHOP_ITEM,
              item.shopItemId
            );
          } catch (error) {
            this.logger.error(`Error getting file URL: ${error.message}`, error.stack);
          }
        }

        return dto;
      });

      const items = await Promise.all(itemPromises);

      // Calculate pagination info
      const totalPages = Math.ceil(totalItems / limit);

      return {
        items,
        totalItems,
        itemsPerPage: limit,
        currentPage: page,
        totalPages
      };
    } catch (error) {
      this.logger.error(`Error getting student owned items by category: ${error.message}`, error.stack);
      throw error;
    }
  }
}
