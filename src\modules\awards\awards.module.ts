import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AwardsService } from './awards.service';
import { AwardsController } from './awards.controller';
import { Award } from '../../database/entities/award.entity';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { RewardPoint } from '../../database/entities/reward-point.entity';
import { AwardSchedule } from '../../database/entities/award-schedule.entity';
import { User } from '../../database/entities/user.entity';
import { CommonModule } from '../../common/common.module';
import { JwtService } from '@nestjs/jwt';
import { SimplifiedAwardScheduler } from './simplified-award.scheduler';
import { SimplifiedAwardSchedulerController } from './simplified-award-scheduler.controller';
import { DiaryModule } from '../diary/diary.module';
import { EssayModule } from '../essay/essay.module';
import { NovelModule } from '../novel/novel.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Award, AwardWinner, RewardPoint, User, AwardSchedule]),
    CommonModule,
    forwardRef(() => DiaryModule),
    forwardRef(() => EssayModule),
    forwardRef(() => NovelModule),
  ],
  controllers: [AwardsController, SimplifiedAwardSchedulerController],
  providers: [
    {
      provide: AwardsService,
      useClass: AwardsService,
    },
    {
      provide: SimplifiedAwardScheduler,
      useClass: SimplifiedAwardScheduler,
    },
    JwtService,
  ],
  exports: [AwardsService, SimplifiedAwardScheduler],
})
export class AwardsModule {}
