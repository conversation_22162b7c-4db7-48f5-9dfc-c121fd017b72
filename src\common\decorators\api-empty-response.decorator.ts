import { applyDecorators } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';
import { ApiResponse } from '../dto/api-response.dto';

/**
 * Decorator for documenting successful API responses with empty data (null)
 * @param description Description of the response
 * @returns Decorator
 */
export const ApiOkResponseWithEmptyData = (description = 'Successful operation') => {
  return applyDecorators(
    ApiExtraModels(ApiResponse),
    ApiOkResponse({
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(ApiResponse) },
          {
            properties: {
              data: { 
                type: 'null',
                example: null
              },
              success: { example: true },
              statusCode: { example: 200 },
              message: { example: description }
            },
          },
        ],
      },
    }),
  );
};
