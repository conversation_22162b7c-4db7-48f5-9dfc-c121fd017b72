import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsOptional, IsBoolean, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for creating a reward point setting
 */
export class CreateRewardPointSettingDto {
  /**
   * Name of the reward point setting
   * @example "Standard Conversion Rate"
   */
  @ApiProperty({
    example: 'Standard Conversion Rate',
    description: 'Name of the reward point setting'
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  /**
   * Description of the reward point setting
   * @example "Standard conversion rate for reward points"
   */
  @ApiProperty({
    example: 'Standard conversion rate for reward points',
    description: 'Description of the reward point setting',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * Conversion rate for reward points (how many points equal 1 unit of currency)
   * @example 100
   */
  @ApiProperty({
    example: 100,
    description: 'Conversion rate for reward points (how many points equal 1 unit of currency)'
  })
  @IsNotEmpty({ message: 'Conversion rate is required' })
  @Type(() => Number)
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Conversion rate must be a valid number' })
  @Min(1, { message: 'Conversion rate must be at least 1' })
  conversionRate: number;

  /**
   * Whether this setting is active
   * @example true
   */
  @ApiProperty({
    example: true,
    description: 'Whether this setting is active',
    default: false
  })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean({ message: 'isActive must be a boolean value' })
  isActive?: boolean;
}

/**
 * DTO for updating a reward point setting
 */
export class UpdateRewardPointSettingDto {
  /**
   * Name of the reward point setting
   * @example "Standard Conversion Rate"
   */
  @ApiProperty({
    example: 'Standard Conversion Rate',
    description: 'Name of the reward point setting',
    required: false
  })
  @IsOptional()
  @IsString()
  name?: string;

  /**
   * Description of the reward point setting
   * @example "Standard conversion rate for reward points"
   */
  @ApiProperty({
    example: 'Standard conversion rate for reward points',
    description: 'Description of the reward point setting',
    required: false
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * Conversion rate for reward points (how many points equal 1 unit of currency)
   * @example 100
   */
  @ApiProperty({
    example: 100,
    description: 'Conversion rate for reward points (how many points equal 1 unit of currency)',
    required: false
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber({ allowNaN: false, allowInfinity: false }, { message: 'Conversion rate must be a valid number' })
  @Min(1, { message: 'Conversion rate must be at least 1' })
  conversionRate?: number;
}

/**
 * DTO for reward point setting response
 */
export class RewardPointSettingResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty()
  conversionRate: number;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
