import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { AppModule } from '../app.module';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserType } from '../database/entities/user.entity';
import { TutorApproval, TutorApprovalStatus } from '../database/entities/tutor-approval.entity';

async function bootstrap() {
  const logger = new Logger('SeedTutorApproval');
  logger.log('Starting tutor approval seeding...');

  const app = await NestFactory.createApplicationContext(AppModule);

  try {
    // Get repositories
    const userRepository = app.get<Repository<User>>(getRepositoryToken(User));
    const tutorApprovalRepository = app.get<Repository<TutorApproval>>(getRepositoryToken(TutorApproval));

    // Find the tutor user by userId
    const tutorUser = await userRepository.findOne({
      where: { userId: 'tutor', type: UserType.TUTOR }
    });

    if (!tutorUser) {
      logger.error('Tutor user with userId "tutor" not found');
      return;
    }

    logger.log(`Found tutor user: ${tutorUser.name} (${tutorUser.id})`);

    // Check if approval already exists
    const existingApproval = await tutorApprovalRepository.findOne({
      where: { userId: tutorUser.id }
    });

    if (existingApproval) {
      logger.log(`Tutor approval already exists with status: ${existingApproval.status}`);
      
      // Update the status to approved if it's not already
      if (existingApproval.status !== TutorApprovalStatus.APPROVED) {
        existingApproval.status = TutorApprovalStatus.APPROVED;
        existingApproval.adminNotes = 'Approved via seeding script';
        existingApproval.approvedAt = new Date();
        
        await tutorApprovalRepository.save(existingApproval);
        
        // Also update the user's active status
        tutorUser.isActive = true;
        await userRepository.save(tutorUser);
        
        logger.log(`Updated existing approval to APPROVED status`);
      }
      
      return;
    }

    // Create a new tutor approval
    const tutorApproval = new TutorApproval();
    tutorApproval.userId = tutorUser.id;
    tutorApproval.status = TutorApprovalStatus.APPROVED;
    tutorApproval.adminNotes = 'Approved via seeding script';
    tutorApproval.approvedAt = new Date();

    await tutorApprovalRepository.save(tutorApproval);

    // Update the user's active status
    tutorUser.isActive = true;
    await userRepository.save(tutorUser);

    logger.log(`Created and approved tutor approval for user: ${tutorUser.name}`);
    logger.log('Tutor approval seeding completed successfully');
  } catch (error) {
    logger.error(`Error during tutor approval seeding: ${error.message}`, error.stack);
  } finally {
    await app.close();
  }
}

bootstrap();
