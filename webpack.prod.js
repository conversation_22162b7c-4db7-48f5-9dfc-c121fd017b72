const path = require('path');
const webpack = require('webpack');

// Handle the case where webpack-node-externals might not be installed yet
let nodeExternals;
try {
  nodeExternals = require('webpack-node-externals');
} catch (e) {
  console.warn('webpack-node-externals not found, continuing without it');
  nodeExternals = () => ({});
}

// Handle the case where ignore-loader might not be installed yet
let ignoreLoader;
try {
  ignoreLoader = require.resolve('ignore-loader');
} catch (e) {
  console.warn('ignore-loader not found, using fallback');
  ignoreLoader = path.resolve(__dirname, 'ignore-loader.js');
}

module.exports = {
  entry: './src/main.ts',
  target: 'node',
  mode: 'production',
  externals: [nodeExternals()],
  // Add this to ensure TypeORM metadata is properly handled
  optimization: {
    minimize: false
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: {
          loader: 'ts-loader',
          options: {
            configFile: 'tsconfig.webpack.json',
            transpileOnly: true
          }
        },
        exclude: /node_modules/,
      },
      // Add a rule to handle HTML files (for node-pre-gyp)
      {
        test: /\.html$/,
        use: [
          {
            loader: ignoreLoader,
            options: {}
          }
        ]
      }
    ],
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      // This is the key fix - map 'src' to the absolute path of the src directory
      'src': path.resolve(__dirname, 'src'),
    },
    // Add fallbacks for missing modules
    fallback: {
      'nock': false,
      'pg-native': false,
      '@nestjs/microservices': false,
      '@nestjs/websockets': false,
      '@nestjs/microservices/microservices-module': false,
      '@nestjs/websockets/socket-module': false,
    }
  },
  output: {
    path: path.join(__dirname, 'dist'),
    filename: 'main.js',
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env.NODE_ENV': JSON.stringify('production'),
    }),
    // Ignore specific modules that are causing issues
    new webpack.IgnorePlugin({
      resourceRegExp: /^(pg-native|nock|@nestjs\/microservices|@nestjs\/websockets)$/,
    }),
  ],
  // Increase the timeout for webpack
  watchOptions: {
    aggregateTimeout: 300,
    poll: 1000,
  },
  // Increase the memory limit
  performance: {
    hints: false,
  },
};
