import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialMigration1748145879762 implements MigrationInterface {
    name = 'InitialMigration1748145879762'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "waterfall_question" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "question_text" text NOT NULL, "question_text_plain" text NOT NULL, "correct_answers" text array NOT NULL, "options" text array NOT NULL, "set_id" uuid NOT NULL, CONSTRAINT "PK_c5437c19874506a69a4d46b997f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "waterfall_set" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying NOT NULL, "total_score" integer NOT NULL, "total_questions" integer NOT NULL, CONSTRAINT "PK_c38239fc4cd4d8e11dcdb5d7478" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."plan_feature_type_enum" AS ENUM('hec_user_diary', 'hec_play', 'english_qa_writing', 'english_essay', 'english_novel', 'module')`);
        await queryRunner.query(`CREATE TABLE "plan_feature" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "type" "public"."plan_feature_type_enum" NOT NULL, "name" character varying NOT NULL, "description" text NOT NULL, CONSTRAINT "UQ_4e8d1f7e8813997a8ad4a2f4cff" UNIQUE ("type"), CONSTRAINT "PK_c9f5c79b8a1c181c5d4db0e066e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."plan_type_enum" AS ENUM('starter', 'standard', 'pro', 'ultimate')`);
        await queryRunner.query(`CREATE TYPE "public"."plan_subscription_type_enum" AS ENUM('monthly', 'yearly')`);
        await queryRunner.query(`CREATE TABLE "plan" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "name" character varying NOT NULL, "type" "public"."plan_type_enum" NOT NULL DEFAULT 'starter', "subscription_type" "public"."plan_subscription_type_enum" NOT NULL DEFAULT 'monthly', "description" text NOT NULL, "price" numeric(10,2) NOT NULL, "duration_days" integer NOT NULL, "auto_renew" boolean NOT NULL DEFAULT false, "legacy_features" text, "is_active" boolean NOT NULL DEFAULT true, "is_applicable_for_promotion" boolean NOT NULL DEFAULT false, "promotion_id" character varying, CONSTRAINT "UQ_8aa73af67fa634d33de9bf874ab" UNIQUE ("name"), CONSTRAINT "PK_54a2b686aed3b637654bf7ddbb3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_plan" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "plan_id" uuid NOT NULL, "start_date" TIMESTAMP NOT NULL, "end_date" TIMESTAMP NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "payment_reference" character varying, "is_paid" boolean NOT NULL DEFAULT false, "auto_renew" boolean NOT NULL DEFAULT false, "last_renewal_date" TIMESTAMP, "next_renewal_date" TIMESTAMP, "cancellation_date" TIMESTAMP, "notes" text, CONSTRAINT "PK_aa22a94c276c9921fe6590c1557" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "profile_pictures" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid, "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying NOT NULL, "file_size" integer NOT NULL, CONSTRAINT "REL_bcb95fc382bed71fb8d212b02f" UNIQUE ("user_id"), CONSTRAINT "PK_55851331ec0d252521dd1f7cde2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."user_type_enum" AS ENUM('admin', 'tutor', 'student')`);
        await queryRunner.query(`CREATE TABLE "user" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "name" character varying NOT NULL, "user_id" character varying NOT NULL, "email" character varying NOT NULL, "password" character varying NOT NULL, "type" "public"."user_type_enum" NOT NULL DEFAULT 'student', "is_confirmed" boolean NOT NULL DEFAULT false, "is_active" boolean NOT NULL DEFAULT false, "last_login_at" TIMESTAMP, "profile_picture" character varying, "phone_number" character varying NOT NULL, "address" character varying, "city" character varying, "state" character varying, "country" character varying, "postal_code" character varying, "bio" character varying, "date_of_birth" date, "gender" character varying NOT NULL, "agreed_to_terms" boolean NOT NULL DEFAULT false, "refresh_token" character varying, "refresh_token_expiry" TIMESTAMP, "social_links" json, CONSTRAINT "UQ_e12875dfb3b1d92d7d7c5377e22" UNIQUE ("email"), CONSTRAINT "PK_cace4a159ff9f2512dd42373760" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "role" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "name" character varying NOT NULL, CONSTRAINT "UQ_ae4578dcaed5adff96595e61660" UNIQUE ("name"), CONSTRAINT "PK_b36bcfe02fc8de3c57a8b2391c2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_role" ("user_id" uuid NOT NULL, "role_id" uuid NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_f634684acb47c1a158b83af5150" PRIMARY KEY ("user_id", "role_id"))`);
        await queryRunner.query(`CREATE TABLE "waterfall_participation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "student_id" uuid NOT NULL, "set_id" uuid NOT NULL, "total_correct_answers" integer NOT NULL, "total_questions" integer NOT NULL, "score" double precision NOT NULL, CONSTRAINT "PK_546b15de0e5f72d1d178d74ca37" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "waterfall_answer" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "participation_id" uuid NOT NULL, "question_id" uuid NOT NULL, "submitted_answers" text NOT NULL, "is_correct" boolean NOT NULL, CONSTRAINT "PK_945109b50cc3e61f0a893f7689e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "tutor_education" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "tutor_id" uuid NOT NULL, "degree" character varying(100) NOT NULL, "institution" character varying(255) NOT NULL, "field_of_study" character varying(255) NOT NULL, "start_date" date NOT NULL, "end_date" date, "is_current" boolean NOT NULL DEFAULT false, "description" text, "location" character varying(255), "grade" character varying(50), "activities" text, CONSTRAINT "PK_5989490567adc3dc31480f3b9c3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."notification_type_enum" AS ENUM('diary_submission', 'diary_update', 'diary_review', 'diary_feedback', 'mission_created', 'mission_submission', 'mission_submission_updated', 'mission_feedback', 'mission_correction', 'mission_review_complete', 'tutor_greeting', 'tutor_assignment', 'tutor_verification', 'chat_message', 'qa_submission', 'qa_review', 'qa_feedback', 'essay_submission', 'essay_review', 'essay_feedback', 'story_submission', 'story_review', 'system')`);
        await queryRunner.query(`CREATE TABLE "notification" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "type" "public"."notification_type_enum" NOT NULL, "title" character varying NOT NULL, "message" text NOT NULL, "related_entity_id" character varying, "related_entity_type" character varying, "is_read" boolean NOT NULL DEFAULT false, "read_at" TIMESTAMP, CONSTRAINT "PK_705b6c7cdf9b2c2ff7ac7872cb7" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."notification_delivery_channel_enum" AS ENUM('in_app', 'email', 'push', 'mobile', 'sms', 'realtime_message')`);
        await queryRunner.query(`CREATE TYPE "public"."notification_delivery_status_enum" AS ENUM('pending', 'sent', 'failed', 'failed_permanent', 'retry_scheduled', 'read')`);
        await queryRunner.query(`CREATE TABLE "notification_delivery" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "notification_id" uuid NOT NULL, "channel" "public"."notification_delivery_channel_enum" NOT NULL, "status" "public"."notification_delivery_status_enum" NOT NULL DEFAULT 'pending', "sent_at" TIMESTAMP, "error_message" text, "error_code" character varying, "error_details" text, "retry_count" integer NOT NULL DEFAULT '0', "max_retries" integer NOT NULL DEFAULT '3', "next_retry_at" TIMESTAMP, "last_retry_at" TIMESTAMP, "retry_strategy" character varying DEFAULT 'exponential', "priority" integer NOT NULL DEFAULT '1', "payload" text, CONSTRAINT "PK_109dea88c25343da49d86de6bfd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."user_notification_preference_notification_type_enum" AS ENUM('diary_submission', 'diary_update', 'diary_review', 'diary_feedback', 'mission_created', 'mission_submission', 'mission_submission_updated', 'mission_feedback', 'mission_correction', 'mission_review_complete', 'tutor_greeting', 'tutor_assignment', 'tutor_verification', 'chat_message', 'qa_submission', 'qa_review', 'qa_feedback', 'essay_submission', 'essay_review', 'essay_feedback', 'story_submission', 'story_review', 'system')`);
        await queryRunner.query(`CREATE TYPE "public"."user_notification_preference_channel_enum" AS ENUM('in_app', 'email', 'push', 'mobile', 'sms', 'realtime_message')`);
        await queryRunner.query(`CREATE TABLE "user_notification_preference" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "notification_type" "public"."user_notification_preference_notification_type_enum" NOT NULL, "channel" "public"."user_notification_preference_channel_enum" NOT NULL, "is_enabled" boolean NOT NULL DEFAULT true, "time_window" character varying, "days_of_week" text, CONSTRAINT "UQ_9b29d976a2aa706bc1e1e628cc2" UNIQUE ("user_id", "notification_type", "channel"), CONSTRAINT "PK_98bedc3257235969f6ff2ec6682" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "tutor_permission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "tutor_id" uuid NOT NULL, "plan_feature_id" uuid NOT NULL, "granted_by" uuid NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "notes" text, CONSTRAINT "PK_4f20137d99a11ba9c6c98c25dea" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."student_tutor_mapping_status_enum" AS ENUM('active', 'inactive')`);
        await queryRunner.query(`CREATE TABLE "student_tutor_mapping" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "student_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "plan_feature_id" uuid NOT NULL, "status" "public"."student_tutor_mapping_status_enum" NOT NULL DEFAULT 'active', "assigned_date" TIMESTAMP NOT NULL, "last_activity_date" TIMESTAMP, "notes" text, CONSTRAINT "UQ_923a8a79073b6f291fafe813288" UNIQUE ("student_id", "plan_feature_id"), CONSTRAINT "PK_c7fd7f7a2f25d9230bce48147ab" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."tutor_approval_status_enum" AS ENUM('pending', 'approved', 'rejected')`);
        await queryRunner.query(`CREATE TABLE "tutor_approval" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" character varying NOT NULL, "status" "public"."tutor_approval_status_enum" NOT NULL DEFAULT 'pending', "admin_id" character varying, "admin_notes" character varying, "rejection_reason" character varying, "approved_at" TIMESTAMP, "rejected_at" TIMESTAMP, CONSTRAINT "PK_30cb5a58add0f26fffc1ba82491" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shop_category" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "name" character varying NOT NULL, "description" text, "is_active" boolean NOT NULL DEFAULT true, "parent_id" uuid, "display_order" integer NOT NULL DEFAULT '0', "image_url" character varying, CONSTRAINT "PK_53aefe72f30f467c4fbb16b9745" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."shop_item_type_enum" AS ENUM('free', 'in_app_purchase')`);
        await queryRunner.query(`CREATE TABLE "shop_item" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "item_number" character varying NOT NULL, "title" character varying NOT NULL, "description" text NOT NULL, "category_id" uuid NOT NULL, "type" "public"."shop_item_type_enum" NOT NULL DEFAULT 'in_app_purchase', "price" numeric(10,2) NOT NULL DEFAULT '0', "is_purchasable_in_rewardpoint" boolean NOT NULL DEFAULT false, "file_path" character varying, "is_active" boolean NOT NULL DEFAULT true, "is_featured" boolean NOT NULL DEFAULT false, "view_count" integer NOT NULL DEFAULT '0', "purchase_count" integer NOT NULL DEFAULT '0', "promotion_id" character varying, "is_promotion_active" boolean NOT NULL DEFAULT true, "discounted_price" numeric(10,2), "metadata" text, CONSTRAINT "PK_45ef796043f3b27975c32d94d20" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."shop_item_purchase_payment_method_enum" AS ENUM('reward_points', 'credit_card', 'free')`);
        await queryRunner.query(`CREATE TYPE "public"."shop_item_purchase_status_enum" AS ENUM('completed', 'pending', 'failed', 'refunded')`);
        await queryRunner.query(`CREATE TABLE "shop_item_purchase" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "shop_item_id" uuid NOT NULL, "original_price" numeric(10,2) NOT NULL, "final_price" numeric(10,2) NOT NULL, "promotion_id" character varying, "discount_amount" numeric(10,2), "reward_points_used" numeric(10,2) NOT NULL DEFAULT '0', "quantity" integer NOT NULL DEFAULT '1', "payment_method" "public"."shop_item_purchase_payment_method_enum" NOT NULL, "status" "public"."shop_item_purchase_status_enum" NOT NULL DEFAULT 'completed', "payment_details" json, "notes" text, "category_id" uuid, "metadata" text, CONSTRAINT "PK_4ddf0a56be5c606861875e806b1" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."student_owned_item_status_enum" AS ENUM('available', 'in_use', 'expired')`);
        await queryRunner.query(`CREATE TABLE "student_owned_item" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "student_id" uuid NOT NULL, "shop_item_id" uuid NOT NULL, "purchase_id" uuid, "status" "public"."student_owned_item_status_enum" NOT NULL DEFAULT 'available', "acquired_date" TIMESTAMP NOT NULL DEFAULT now(), "expiry_date" TIMESTAMP, "last_used_date" TIMESTAMP, "is_favorite" boolean NOT NULL DEFAULT false, "notes" character varying, CONSTRAINT "PK_2b1cd8dd62aa6b826e11d80c6d8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_a3e9a158d886e27427535a1f0c" ON "student_owned_item" ("student_id", "shop_item_id") `);
        await queryRunner.query(`CREATE TABLE "story_maker_evaluation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "submission_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "corrections" text, "feedback" text, "evaluated_at" TIMESTAMP NOT NULL, CONSTRAINT "PK_57c5df88c32d6d49d6184fc0b0a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "story_maker_submission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "participation_id" uuid NOT NULL, "content" text NOT NULL, "submitted_at" TIMESTAMP NOT NULL, "is_evaluated" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_e3465d5bb19f3b5d456e079d213" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "story_maker_participation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "student_id" uuid NOT NULL, "story_maker_id" uuid NOT NULL, "first_submitted_at" TIMESTAMP, "is_evaluated" boolean NOT NULL DEFAULT false, "score" integer, "evaluated_at" TIMESTAMP, "evaluated_by" uuid, CONSTRAINT "CHK_0ab49d83154ff371d4c37ab161" CHECK (score > 0), CONSTRAINT "PK_1b757b3be4796670123a4d2e44c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "story_maker" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying NOT NULL, "instruction" text NOT NULL, "picture" character varying NOT NULL, "score" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "word_limit" integer, "deadline" integer, CONSTRAINT "PK_eed86ea930591c6a2d20c45532c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."student_friendship_status_enum" AS ENUM('pending', 'accepted', 'rejected')`);
        await queryRunner.query(`CREATE TABLE "student_friendship" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "requester_id" uuid NOT NULL, "requested_id" uuid NOT NULL, "status" "public"."student_friendship_status_enum" NOT NULL DEFAULT 'pending', "request_message" character varying, "can_view_diary" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_8168e3debf4815a3c91eb495a79" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "student_diary_skin" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "name" character varying NOT NULL, "description" text NOT NULL, "template_content" text NOT NULL, "preview_image_path" character varying, "is_active" boolean NOT NULL DEFAULT true, "student_id" uuid NOT NULL, "is_global" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_347aa675f5f70fd0b77516608ed" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."story_maker_registry_storage_provider_enum" AS ENUM('local', 's3')`);
        await queryRunner.query(`CREATE TABLE "story_maker_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, "storage_provider" "public"."story_maker_registry_storage_provider_enum" NOT NULL DEFAULT 'local', "storage_key" character varying(500), "s3_bucket" character varying(100), "s3_region" character varying(50), "s3_etag" character varying(100), "s3_version_id" character varying(100), "s3_storage_class" character varying(50), "s3_server_side_encryption" character varying(50), "cdn_url" character varying(500), "presigned_url_expires_at" TIMESTAMP, "storage_metadata" jsonb, "is_migrated" boolean NOT NULL DEFAULT false, "migration_status" character varying(20), "migration_error" text, "migrated_at" TIMESTAMP, "story_maker_id" uuid NOT NULL, CONSTRAINT "PK_36424f4fdc8c8901305d8e02ffe" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "user_otp" ("id" SERIAL NOT NULL, "user_id" character varying NOT NULL, "otp" character varying NOT NULL, "expiration_time" TIMESTAMP NOT NULL, "generated_time" TIMESTAMP NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_494c022ed33e6ee19a2bbb11b22" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_skin" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "name" character varying NOT NULL, "description" text NOT NULL, "template_content" text, "preview_image_path" character varying, "is_active" boolean NOT NULL DEFAULT true, "is_global" boolean NOT NULL DEFAULT true, "created_by_id" uuid, CONSTRAINT "PK_9ee781f84a05f481ec10b774d0d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shop_skin_mapping" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "shop_item_id" uuid NOT NULL, "diary_skin_id" uuid NOT NULL, CONSTRAINT "PK_b483549ec76b767e1f4a477dfdd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "shopping_cart_item" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "cart_id" uuid NOT NULL, "shop_item_id" uuid NOT NULL, "quantity" integer NOT NULL DEFAULT '1', "price" numeric(10,2) NOT NULL, "reward_points" numeric(10,2) NOT NULL, CONSTRAINT "PK_15909d00f68f8f022e5545745aa" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."shopping_cart_status_enum" AS ENUM('active', 'checked_out', 'abandoned')`);
        await queryRunner.query(`CREATE TABLE "shopping_cart" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "status" "public"."shopping_cart_status_enum" NOT NULL DEFAULT 'active', "last_activity" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_40f9358cdf55d73d8a2ad226592" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."shop_item_registry_storage_provider_enum" AS ENUM('local', 's3')`);
        await queryRunner.query(`CREATE TABLE "shop_item_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, "storage_provider" "public"."shop_item_registry_storage_provider_enum" NOT NULL DEFAULT 'local', "storage_key" character varying(500), "s3_bucket" character varying(100), "s3_region" character varying(50), "s3_etag" character varying(100), "s3_version_id" character varying(100), "s3_storage_class" character varying(50), "s3_server_side_encryption" character varying(50), "cdn_url" character varying(500), "presigned_url_expires_at" TIMESTAMP, "storage_metadata" jsonb, "is_migrated" boolean NOT NULL DEFAULT false, "migration_status" character varying(20), "migration_error" text, "migrated_at" TIMESTAMP, "shop_item_id" uuid NOT NULL, "user_id" character varying, CONSTRAINT "PK_be7a654134506d2379e0b948a3c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."reward_point_source_enum" AS ENUM('diary_award', 'play', 'qa', 'novel', 'essay', 'shop_purchase', 'admin_adjustment')`);
        await queryRunner.query(`CREATE TYPE "public"."reward_point_type_enum" AS ENUM('earned', 'spent', 'adjusted', 'expired')`);
        await queryRunner.query(`CREATE TABLE "reward_point" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "source" "public"."reward_point_source_enum" NOT NULL, "type" "public"."reward_point_type_enum" NOT NULL, "points" integer NOT NULL, "reference_id" character varying, "description" text, "expiry_date" TIMESTAMP, CONSTRAINT "PK_9fa3dfc8d772d427aeb23fb1a56" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "qa_task_submission_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "submission_id" character varying NOT NULL, "content" text NOT NULL, "word_count" integer NOT NULL, "submission_date" TIMESTAMP NOT NULL, "sequence_number" integer NOT NULL, "meta_data" json, "submission" uuid, CONSTRAINT "PK_0e129316b58f562367a9846563f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_d24d8afb23a70831c0a86e4595" ON "qa_task_submission_history" ("submission_date") `);
        await queryRunner.query(`CREATE INDEX "IDX_be57aa932722d380a1f9399138" ON "qa_task_submission_history" ("submission", "sequence_number") `);
        await queryRunner.query(`CREATE TABLE "qa_task_submission_marking" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "points" double precision NOT NULL, "submission_feedback" text, "task_remarks" text, "submission_id" uuid NOT NULL, "submission_history_id" uuid NOT NULL, "submission" uuid, "submission_mark_id" uuid, CONSTRAINT "REL_5769bb5bb9b640e0d811df4c4f" UNIQUE ("submission_mark_id"), CONSTRAINT "PK_9164c9fb16da83d479b45bedf81" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_b172bc233df0e2aaf1872c497f" ON "qa_task_submission_marking" ("submission_id", "submission_history_id") `);
        await queryRunner.query(`CREATE TABLE "qa_weekly_mission_tasks" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying(50) NOT NULL, "description" text NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "sequence" integer DEFAULT '1', "word_limit_minumum" integer NOT NULL, "word_limit_maximum" integer, "total_score" integer, "deadline" integer, "instructions" text NOT NULL, "mission_id" uuid NOT NULL, CONSTRAINT "PK_376f8c11101064802491a4cf1b9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "qa_monthly_mission_tasks" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying(50) NOT NULL, "description" text NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "sequence" integer DEFAULT '1', "word_limit_minumum" integer NOT NULL, "word_limit_maximum" integer, "total_score" integer, "deadline" integer, "instructions" text NOT NULL, "mission_id" uuid NOT NULL, CONSTRAINT "PK_69715723a4c804eada066f50cd3" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "qa_mission_month" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying NOT NULL, "display" character varying NOT NULL, "sequence" integer NOT NULL, "year" integer NOT NULL, CONSTRAINT "PK_bb13566db454bdf3b7c4a0d0c5d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "qa_mission_week" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "title" character varying NOT NULL, "sequence" integer NOT NULL, "month" character varying NOT NULL, "start_date" date NOT NULL, "end_date" date NOT NULL, "year" integer NOT NULL, CONSTRAINT "PK_3abefaedf983f685f2c25a4292e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."qa_mission_time_frequency_enum" AS ENUM('weekly', 'monthly')`);
        await queryRunner.query(`CREATE TABLE "qa_mission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "time_frequency" "public"."qa_mission_time_frequency_enum" NOT NULL DEFAULT 'weekly', "is_active" boolean NOT NULL DEFAULT true, "week_id" uuid, "month_id" uuid, "sequence_number" integer, CONSTRAINT "PK_6894f62006f7d34f2fae96039b5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "qa_mission_tasks" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying(50) NOT NULL, "description" text NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "sequence" integer DEFAULT '1', "word_limit_minumum" integer NOT NULL, "word_limit_maximum" integer, "total_score" integer, "deadline" integer, "instructions" text NOT NULL, "mission_id" uuid NOT NULL, CONSTRAINT "PK_8d3da4ef23b295ce9bcfddc588c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."qa_task_submissions_status_enum" AS ENUM('draft', 'submitted', 'reviewed', 'discarded')`);
        await queryRunner.query(`CREATE TABLE "qa_task_submissions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "status" "public"."qa_task_submissions_status_enum" NOT NULL DEFAULT 'draft', "current_revision" integer NOT NULL DEFAULT '1', "task_id" uuid NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "latest_submission_id" uuid, "total_revisions" integer NOT NULL DEFAULT '1', "first_submitted_at" TIMESTAMP, "last_submitted_at" TIMESTAMP, "first_revision_progress" double precision NOT NULL DEFAULT '0', "is_first_revision" boolean NOT NULL DEFAULT true, "submission_mark_id" uuid, CONSTRAINT "REL_f8c9a915a6f0809f96073936ea" UNIQUE ("submission_mark_id"), CONSTRAINT "PK_86c7651ecffd3489db0989bb107" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "reward_point_setting" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "name" character varying(100) NOT NULL, "description" text, "conversion_rate" numeric(10,2) NOT NULL, "is_active" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_9df64b97592a5083c28b86ce286" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."qa_mission_goal_time_frequency_enum" AS ENUM('weekly', 'monthly')`);
        await queryRunner.query(`CREATE TABLE "qa_mission_goal" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "time_frequency" "public"."qa_mission_goal_time_frequency_enum" NOT NULL DEFAULT 'weekly', "is_active" boolean NOT NULL DEFAULT true, "sequence_number" integer NOT NULL DEFAULT '1', CONSTRAINT "PK_cd70982a1224cc072b198e82699" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "qa_task_missions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying(50) NOT NULL, "description" text NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "time_period_unit" integer DEFAULT '1', "word_limit_minumum" integer NOT NULL, "word_limit_maximum" integer, "deadline" integer, "instructions" text NOT NULL, "mission_id" uuid NOT NULL, "meta_data" json, "mission" uuid, CONSTRAINT "PK_04c6e81802d294d6b5dddc2908c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."qa_subscription_status_enum" AS ENUM('active', 'inactive', 'suspended')`);
        await queryRunner.query(`CREATE TABLE "qa_subscription" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "student_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "status" "public"."qa_subscription_status_enum" NOT NULL DEFAULT 'active', "subscription_dates" jsonb, CONSTRAINT "PK_6274da3cf67dfa20c6957698b5b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "qa_question" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "question" text NOT NULL, "points" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "minimum_words" integer NOT NULL, CONSTRAINT "PK_955828f714972215ef53c682ecb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."qa_assignment_status_enum" AS ENUM('assigned', 'draft', 'in_progress', 'completed', 'expired')`);
        await queryRunner.query(`CREATE TABLE "qa_assignment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "question_id" uuid NOT NULL, "student_id" uuid NOT NULL, "points" integer NOT NULL, "deadline" TIMESTAMP NOT NULL, "instructions" text, "status" "public"."qa_assignment_status_enum" NOT NULL DEFAULT 'assigned', "assigned_date" TIMESTAMP NOT NULL, CONSTRAINT "PK_f923d3914ff31c8a061da629d9c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."qa_submission_status_enum" AS ENUM('draft', 'submitted', 'reviewed')`);
        await queryRunner.query(`CREATE TABLE "qa_submission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "assignment_id" uuid NOT NULL, "answer" text NOT NULL, "points" integer, "status" "public"."qa_submission_status_enum" NOT NULL DEFAULT 'draft', "submission_date" TIMESTAMP, "feedback" text, "corrections" jsonb, "reviewed_at" TIMESTAMP, "reviewed_by" uuid, CONSTRAINT "REL_064b0390e4c62a2d7797079070" UNIQUE ("assignment_id"), CONSTRAINT "PK_a9eed2229650a83c5458b3de298" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."profile_picture_registry_storage_provider_enum" AS ENUM('local', 's3')`);
        await queryRunner.query(`CREATE TABLE "profile_picture_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, "storage_provider" "public"."profile_picture_registry_storage_provider_enum" NOT NULL DEFAULT 'local', "storage_key" character varying(500), "s3_bucket" character varying(100), "s3_region" character varying(50), "s3_etag" character varying(100), "s3_version_id" character varying(100), "s3_storage_class" character varying(50), "s3_server_side_encryption" character varying(50), "cdn_url" character varying(500), "presigned_url_expires_at" TIMESTAMP, "storage_metadata" jsonb, "is_migrated" boolean NOT NULL DEFAULT false, "migration_status" character varying(20), "migration_error" text, "migrated_at" TIMESTAMP, "profile_picture_id" uuid NOT NULL, "user_id" character varying NOT NULL, CONSTRAINT "PK_e9ee0f051f579676af971f0a5db" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."promotion_promotion_type_enum" AS ENUM('percentage', 'fixed_amount')`);
        await queryRunner.query(`CREATE TYPE "public"."promotion_discount_type_enum" AS ENUM('percentage', 'fixed_amount')`);
        await queryRunner.query(`CREATE TYPE "public"."promotion_applicable_type_enum" AS ENUM('plan', 'shop_item', 'all')`);
        await queryRunner.query(`CREATE TABLE "promotion" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "name" character varying NOT NULL, "description" text NOT NULL, "promotion_type" "public"."promotion_promotion_type_enum" NOT NULL DEFAULT 'percentage', "discount_type" "public"."promotion_discount_type_enum" NOT NULL, "discount_value" numeric(10,2) NOT NULL, "applicable_type" "public"."promotion_applicable_type_enum" NOT NULL, "applicable_category_ids" json, "applicable_plan_ids" json, "applied_to_plan" boolean DEFAULT false, "promotion_code" character varying, "start_date" TIMESTAMP, "end_date" TIMESTAMP, "is_active" boolean NOT NULL DEFAULT true, "usage_limit" integer, "usage_count" integer NOT NULL DEFAULT '0', "minimum_purchase_amount" numeric(10,2), "maximum_purchase_amount" numeric(10,2), "maximum_discount_amount" numeric(10,2), CONSTRAINT "PK_fab3630e0789a2002f1cadb7d38" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "password_resets" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" character varying NOT NULL, "token" character varying NOT NULL, "expiration_time" TIMESTAMP NOT NULL, "is_used" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_4816377aa98211c1de34469e742" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_mission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying NOT NULL, "description" text NOT NULL, "target_word_count" integer NOT NULL, "target_max_word_count" integer, "publish_date" TIMESTAMP NOT NULL, "expiry_date" TIMESTAMP, "admin_id" uuid NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "score" integer NOT NULL, CONSTRAINT "PK_c04698dc3a0fa7f33193684fd5e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."mission_diary_entry_status_enum" AS ENUM('NEW', 'SUBMITTED', 'REVIEWED', 'CONFIRMED', 'new', 'submit', 'reviewed', 'confirm')`);
        await queryRunner.query(`CREATE TABLE "mission_diary_entry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "mission_id" uuid NOT NULL, "student_id" uuid NOT NULL, "content" text NOT NULL, "word_count" integer NOT NULL, "progress" double precision NOT NULL, "status" "public"."mission_diary_entry_status_enum" NOT NULL DEFAULT 'NEW', "gained_score" integer, "reviewed_by" uuid, "reviewed_at" TIMESTAMP, "correction" text, "correction_provided_at" TIMESTAMP, "correction_provided_by" uuid, CONSTRAINT "PK_f3eba44dee5ab5ad5ba5c4ee5cc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "mission_diary_entry_feedback" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "mission_entry_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "feedback" text NOT NULL, "rating" integer, CONSTRAINT "PK_e496f231dbe9ee4e06489a16070" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."conversation_type_enum" AS ENUM('direct', 'group')`);
        await queryRunner.query(`CREATE TYPE "public"."conversation_status_enum" AS ENUM('active', 'archived', 'blocked')`);
        await queryRunner.query(`CREATE TABLE "conversation" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "type" "public"."conversation_type_enum" NOT NULL DEFAULT 'direct', "status" "public"."conversation_status_enum" NOT NULL DEFAULT 'active', "participant1_id" uuid NOT NULL, "participant2_id" uuid NOT NULL, "last_message_at" TIMESTAMP, "last_message_text" text, "last_message_sender_id" character varying, "participant1_unread_count" integer NOT NULL DEFAULT '0', "participant2_unread_count" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_864528ec4274360a40f66c29845" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "message_attachment" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "message_id" uuid NOT NULL, "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying NOT NULL, "file_size" integer NOT NULL, "thumbnail_path" character varying, CONSTRAINT "PK_d5bc54379802d99c07cd7ec00e4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."message_type_enum" AS ENUM('text', 'image', 'file', 'quiz', 'system')`);
        await queryRunner.query(`CREATE TYPE "public"."message_status_enum" AS ENUM('sent', 'delivered', 'read', 'deleted')`);
        await queryRunner.query(`CREATE TABLE "message" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "conversation_id" uuid NOT NULL, "sender_id" uuid NOT NULL, "recipient_id" uuid NOT NULL, "type" "public"."message_type_enum" NOT NULL DEFAULT 'text', "content" text NOT NULL, "status" "public"."message_status_enum" NOT NULL DEFAULT 'sent', "read_at" TIMESTAMP, "delivered_at" TIMESTAMP, "metadata" json, CONSTRAINT "PK_ba01f0a3e0123651915008bc578" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."message_registry_storage_provider_enum" AS ENUM('local', 's3')`);
        await queryRunner.query(`CREATE TABLE "message_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, "storage_provider" "public"."message_registry_storage_provider_enum" NOT NULL DEFAULT 'local', "storage_key" character varying(500), "s3_bucket" character varying(100), "s3_region" character varying(50), "s3_etag" character varying(100), "s3_version_id" character varying(100), "s3_storage_class" character varying(50), "s3_server_side_encryption" character varying(50), "cdn_url" character varying(500), "presigned_url_expires_at" TIMESTAMP, "storage_metadata" jsonb, "is_migrated" boolean NOT NULL DEFAULT false, "migration_status" character varying(20), "migration_error" text, "migrated_at" TIMESTAMP, "message_id" character varying, "user_id" uuid, "thumbnail_path" character varying, "is_temporary" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_9c93135ed4f4df5fcc68853d92c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."essay_mission_time_frequency_enum" AS ENUM('weekly', 'monthly')`);
        await queryRunner.query(`CREATE TABLE "essay_mission" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "time_frequency" "public"."essay_mission_time_frequency_enum" NOT NULL DEFAULT 'weekly', "is_active" boolean NOT NULL DEFAULT true, "sequence_number" integer NOT NULL DEFAULT '1', CONSTRAINT "PK_d884c1d984da37d0842f6e0e11e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "essay_mission_tasks" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying(50) NOT NULL, "description" text NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "time_period_unit" integer DEFAULT '1', "word_limit_minumum" integer NOT NULL, "word_limit_maximum" integer, "deadline" integer, "instructions" text NOT NULL, "mission_id" uuid NOT NULL, "meta_data" json, "mission" uuid, CONSTRAINT "PK_89ec22c90fb5407e279761c7dcf" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "essay_task_submission_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "content" text NOT NULL, "word_count" integer NOT NULL, "submission_date" TIMESTAMP NOT NULL, "sequence_number" integer NOT NULL, "meta_data" json, "submission_id" uuid NOT NULL, "previous_revision_id" uuid, "submission" uuid, "submission_mark_id" uuid, CONSTRAINT "REL_f06f909a5ae83cb331a1651496" UNIQUE ("submission_mark_id"), CONSTRAINT "PK_b080a0661e3ecf1ef572f1ce9fe" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_c4308ae58c4d15d45caccc9893" ON "essay_task_submission_history" ("submission_date") `);
        await queryRunner.query(`CREATE INDEX "IDX_af613ccf087d159f7484190092" ON "essay_task_submission_history" ("submission", "sequence_number") `);
        await queryRunner.query(`CREATE TYPE "public"."essay_task_submissions_status_enum" AS ENUM('draft', 'submitted', 'reviewed', 'discarded')`);
        await queryRunner.query(`CREATE TABLE "essay_task_submissions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" text, "status" "public"."essay_task_submissions_status_enum" NOT NULL DEFAULT 'draft', "current_revision" integer NOT NULL DEFAULT '1', "task_id" uuid NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "latest_submission_id" uuid, "total_revisions" integer NOT NULL DEFAULT '0', "first_submitted_at" TIMESTAMP, "last_submitted_at" TIMESTAMP, "first_revision_progress" double precision NOT NULL DEFAULT '0', "is_first_revision" boolean NOT NULL DEFAULT false, "submission_skin_id" uuid, "task" uuid, "submission_mark_id" uuid, "submission_skin" uuid, CONSTRAINT "REL_fc522fa06e151db1d68346f83c" UNIQUE ("submission_mark_id"), CONSTRAINT "PK_931bc4f39bbfb1578d3ecf40379" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_f7d75d2781798b22d628fd65a2" ON "essay_task_submissions" ("task", "created_by") `);
        await queryRunner.query(`CREATE TABLE "essay_task_submission_marking" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "points" double precision NOT NULL, "submission_feedback" text, "task_remarks" text, "submission_id" uuid NOT NULL, "submission_history_id" uuid NOT NULL, "submission" uuid, "submission_history" uuid, CONSTRAINT "REL_84039f2dd37abc5e5ea72261b1" UNIQUE ("submission"), CONSTRAINT "REL_0276834b14c1f78c5a81eb61c9" UNIQUE ("submission_history"), CONSTRAINT "PK_16cbd98bc507e01955d80bfd15c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_090783a2cd92909ecf3f68fb2e" ON "essay_task_submission_marking" ("submission_id", "submission_history_id") `);
        await queryRunner.query(`CREATE TYPE "public"."essay_module_skin_preference_scope_type_enum" AS ENUM('module_default', 'task_specific')`);
        await queryRunner.query(`CREATE TABLE "essay_module_skin_preference" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "skin_id" uuid NOT NULL, "scope_type" "public"."essay_module_skin_preference_scope_type_enum" NOT NULL, "task_id" uuid, "is_active" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_d6ad28a43aca68421c50cfd2f96" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_b459468a889b4e58df2199560f" ON "essay_module_skin_preference" ("created_by", "task_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_12c59662b2bdfe38b1222aa7ee" ON "essay_module_skin_preference" ("created_by", "scope_type") `);
        await queryRunner.query(`CREATE TABLE "email_verifications" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" character varying NOT NULL, "token" character varying NOT NULL, "expiration_time" TIMESTAMP NOT NULL, "is_used" boolean NOT NULL DEFAULT false, CONSTRAINT "PK_c1ea2921e767f83cd44c0af203f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_feedback" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "diary_entry_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "feedback" text NOT NULL, "rating" integer NOT NULL DEFAULT '0', "award" character varying, CONSTRAINT "PK_78f9b0c952fa6e185ef5fdbb935" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_share" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "diary_entry_id" uuid NOT NULL, "share_token" character varying NOT NULL, "expiry_date" TIMESTAMP, "is_active" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_63012cc4b797be19b36969cfd93" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_correction" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "diary_entry_id" uuid NOT NULL, "tutor_id" uuid NOT NULL, "correction_text" text NOT NULL, "score" integer NOT NULL, "comments" text, CONSTRAINT "REL_4e34f738eafe1ca1709ab1d2f0" UNIQUE ("diary_entry_id"), CONSTRAINT "PK_eba4d3f4490c81d2f04ff024518" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_settings_template" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "title" character varying NOT NULL, "level" integer NOT NULL, "word_limit" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "description" text, CONSTRAINT "PK_c5568ec2fdd7b2c2229ac684a28" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary_entry_settings" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "diary_entry_id" uuid NOT NULL, "settings_template_id" uuid NOT NULL, "title" character varying NOT NULL, "level" integer NOT NULL, "word_limit" integer NOT NULL, CONSTRAINT "REL_71e908d2df1da6e93365d9a531" UNIQUE ("diary_entry_id"), CONSTRAINT "PK_bfebdc41074afd95bfce0eaa65f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_like_liker_type_enum" AS ENUM('student', 'tutor')`);
        await queryRunner.query(`CREATE TABLE "diary_entry_like" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "diary_entry_id" uuid NOT NULL, "liker_id" uuid NOT NULL, "liker_type" "public"."diary_entry_like_liker_type_enum" NOT NULL, CONSTRAINT "UQ_256412907c05a48b2a7f5c4e750" UNIQUE ("diary_entry_id", "liker_id"), CONSTRAINT "PK_c08131c537e95c7710c00c4d2b0" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_status_enum" AS ENUM('new', 'submit', 'reviewed', 'confirm')`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_visibility_enum" AS ENUM('private', 'friends_only', 'public')`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_view_access_enum" AS ENUM('full', 'partial')`);
        await queryRunner.query(`CREATE TABLE "diary_entry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "diary_id" uuid NOT NULL, "entry_date" date NOT NULL, "title" character varying NOT NULL, "content" text NOT NULL, "status" "public"."diary_entry_status_enum" NOT NULL DEFAULT 'new', "skin_id" uuid, "background_color" character varying, "is_private" boolean NOT NULL DEFAULT false, "visibility" "public"."diary_entry_visibility_enum" NOT NULL DEFAULT 'private', "view_access" "public"."diary_entry_view_access_enum" NOT NULL DEFAULT 'full', "review_start_time" TIMESTAMP, "reviewing_tutor_id" uuid, "review_expiry_time" TIMESTAMP, "score" integer, "evaluated_at" TIMESTAMP, "evaluated_by" uuid, "thanks_message" text, CONSTRAINT "PK_31547daef53774d068540599e40" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "diary" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "default_skin_id" uuid NOT NULL, "tutor_greeting" text, CONSTRAINT "REL_330f20310184a92a90225c36cb" UNIQUE ("user_id"), CONSTRAINT "PK_7422c55a0908c4271ff1918437d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."diary_skin_registry_storage_provider_enum" AS ENUM('local', 's3')`);
        await queryRunner.query(`CREATE TABLE "diary_skin_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, "storage_provider" "public"."diary_skin_registry_storage_provider_enum" NOT NULL DEFAULT 'local', "storage_key" character varying(500), "s3_bucket" character varying(100), "s3_region" character varying(50), "s3_etag" character varying(100), "s3_version_id" character varying(100), "s3_storage_class" character varying(50), "s3_server_side_encryption" character varying(50), "cdn_url" character varying(500), "presigned_url_expires_at" TIMESTAMP, "storage_metadata" jsonb, "is_migrated" boolean NOT NULL DEFAULT false, "migration_status" character varying(20), "migration_error" text, "migrated_at" TIMESTAMP, "diary_skin_id" uuid NOT NULL, "user_id" character varying, CONSTRAINT "PK_3c5415afb86d9978aee8e7b0b32" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."diary_follow_request_status_enum" AS ENUM('pending', 'accepted', 'rejected')`);
        await queryRunner.query(`CREATE TABLE "diary_follow_request" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "requester_id" uuid NOT NULL, "diary_owner_id" uuid NOT NULL, "friendship_id" uuid, "status" "public"."diary_follow_request_status_enum" NOT NULL DEFAULT 'pending', "request_message" character varying, CONSTRAINT "PK_93cc07308159a035772a88790cd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."diary_qr_registry_storage_provider_enum" AS ENUM('local', 's3')`);
        await queryRunner.query(`CREATE TABLE "diary_qr_registry" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "file_path" character varying NOT NULL, "file_name" character varying NOT NULL, "mime_type" character varying, "file_size" integer, "storage_provider" "public"."diary_qr_registry_storage_provider_enum" NOT NULL DEFAULT 'local', "storage_key" character varying(500), "s3_bucket" character varying(100), "s3_region" character varying(50), "s3_etag" character varying(100), "s3_version_id" character varying(100), "s3_storage_class" character varying(50), "s3_server_side_encryption" character varying(50), "cdn_url" character varying(500), "presigned_url_expires_at" TIMESTAMP, "storage_metadata" jsonb, "is_migrated" boolean NOT NULL DEFAULT false, "migration_status" character varying(20), "migration_error" text, "migrated_at" TIMESTAMP, "diary_entry_id" uuid NOT NULL, "share_url" character varying NOT NULL, CONSTRAINT "PK_3f83ce9c73b24efe0ac0bc4dcbb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."diary_entry_attendance_status_enum" AS ENUM('present', 'absent')`);
        await queryRunner.query(`CREATE TABLE "diary_entry_attendance" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "diary_entry_id" uuid NOT NULL, "student_id" uuid NOT NULL, "entry_date" date NOT NULL, "status" "public"."diary_entry_attendance_status_enum" NOT NULL, "word_count" integer, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_869bd8123a9db9ded610171cc65" UNIQUE ("diary_entry_id"), CONSTRAINT "PK_cbdcdff13f3054421bca81f2cad" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_ddc7d254be54de06f5ad66ca4e" ON "diary_entry_attendance" ("student_id", "entry_date") `);
        await queryRunner.query(`CREATE TYPE "public"."diary_award_period_enum" AS ENUM('weekly', 'monthly', 'quarterly')`);
        await queryRunner.query(`CREATE TABLE "diary_award" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "period" "public"."diary_award_period_enum" NOT NULL, "period_start_date" date NOT NULL, "period_end_date" date NOT NULL, "total_score" integer NOT NULL, "award_title" character varying, "award_description" text, CONSTRAINT "PK_595dce60e6d3b854a4a8ad973e2" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."award_module_enum" AS ENUM('diary', 'novel', 'essay')`);
        await queryRunner.query(`CREATE TYPE "public"."award_criteria_enum" AS ENUM('diary_score', 'attendance', 'diary_decoration', 'play_performance', 'qa_performance', 'novel_performance', 'essay_performance', 'custom')`);
        await queryRunner.query(`CREATE TYPE "public"."award_frequency_enum" AS ENUM('weekly', 'monthly', 'quarterly', 'yearly', 'one_time')`);
        await queryRunner.query(`CREATE TABLE "award" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "name" character varying NOT NULL, "description" text NOT NULL, "module" "public"."award_module_enum" NOT NULL, "criteria" "public"."award_criteria_enum" array NOT NULL, "frequency" "public"."award_frequency_enum" NOT NULL, "reward_points" integer NOT NULL, "is_active" boolean NOT NULL DEFAULT true, "criteria_config" json, "start_date" TIMESTAMP, "end_date" TIMESTAMP, "image_url" character varying, CONSTRAINT "PK_e887e4e69663925ebb60d3a7775" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."award_schedule_module_enum" AS ENUM('diary', 'novel', 'essay')`);
        await queryRunner.query(`CREATE TYPE "public"."award_schedule_status_enum" AS ENUM('pending', 'completed', 'failed')`);
        await queryRunner.query(`CREATE TABLE "award_schedule" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "module" "public"."award_schedule_module_enum" NOT NULL, "schedule_date" date NOT NULL, "period_start_date" date NOT NULL, "period_end_date" date NOT NULL, "status" "public"."award_schedule_status_enum" NOT NULL DEFAULT 'pending', "error_message" text, "retry_count" integer NOT NULL DEFAULT '0', "last_retry_date" TIMESTAMP, "is_active" boolean NOT NULL DEFAULT true, "processing_started_at" TIMESTAMP, "processing_completed_at" TIMESTAMP, CONSTRAINT "PK_f4a26b9565a0d5af50daffd0a1b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "audit_log" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "user_id" character varying, "action" character varying NOT NULL, "resource" character varying NOT NULL, "details" json, "ip_address" character varying NOT NULL, "user_agent" character varying NOT NULL, "timestamp" TIMESTAMP NOT NULL, "status" character varying, "error_message" character varying, CONSTRAINT "PK_07fefa57f7f5ab8fc3f52b3ed0b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "award_winner" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP DEFAULT now(), "created_by" character varying(36), "updated_by" character varying(36), "user_id" uuid NOT NULL, "award_id" uuid NOT NULL, "award_date" date NOT NULL, "award_reason" text, "metadata" json, CONSTRAINT "PK_331f04f00afef649d7a47c67abd" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "plan_feature_map" ("plan_id" uuid NOT NULL, "feature_id" uuid NOT NULL, CONSTRAINT "PK_16cf1cd5fe6289f88912ae527bc" PRIMARY KEY ("plan_id", "feature_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_099af0f098a074e2a2842c6026" ON "plan_feature_map" ("plan_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_5947b4fb7b9c93694bbf9b9c24" ON "plan_feature_map" ("feature_id") `);
        await queryRunner.query(`ALTER TABLE "waterfall_question" ADD CONSTRAINT "FK_c19ed78ab6eb5bbd64702f2a456" FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_plan" ADD CONSTRAINT "FK_5a8dd225812b1927bc8bc60632c" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_plan" ADD CONSTRAINT "FK_ab1f08d687398cd4762faad4690" FOREIGN KEY ("plan_id") REFERENCES "plan"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "profile_pictures" ADD CONSTRAINT "FK_bcb95fc382bed71fb8d212b02fd" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_role" ADD CONSTRAINT "FK_d0e5815877f7395a198a4cb0a46" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_role" ADD CONSTRAINT "FK_32a6fc2fcb019d8e3a8ace0f55f" FOREIGN KEY ("role_id") REFERENCES "role"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" ADD CONSTRAINT "FK_15f9a5110613d2fa261348d7e0b" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" ADD CONSTRAINT "FK_b54f4c72fadee4feb2ca98da56d" FOREIGN KEY ("set_id") REFERENCES "waterfall_set"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" ADD CONSTRAINT "FK_aaec21ced4bc6bbf331eeb8bb6f" FOREIGN KEY ("participation_id") REFERENCES "waterfall_participation"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" ADD CONSTRAINT "FK_d9104da9519ec2a769ba473f498" FOREIGN KEY ("question_id") REFERENCES "waterfall_question"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tutor_education" ADD CONSTRAINT "FK_111e99a139edbc15490fec3032d" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "notification" ADD CONSTRAINT "FK_928b7aa1754e08e1ed7052cb9d8" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "notification_delivery" ADD CONSTRAINT "FK_2e70a64359ce7416b06a4d7a432" FOREIGN KEY ("notification_id") REFERENCES "notification"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" ADD CONSTRAINT "FK_8ef85bc8bf572f525a116f50eac" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tutor_permission" ADD CONSTRAINT "FK_940a2635c71fd6fc29f98720df0" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tutor_permission" ADD CONSTRAINT "FK_5b501eb2f5c81382b57bcb6debb" FOREIGN KEY ("plan_feature_id") REFERENCES "plan_feature"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tutor_permission" ADD CONSTRAINT "FK_233bcd1293b92a99007c35c951d" FOREIGN KEY ("granted_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" ADD CONSTRAINT "FK_2c4b0b8b5a845fa92703bd5f9c9" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" ADD CONSTRAINT "FK_e244b2500b4cd575dde3aca20d6" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" ADD CONSTRAINT "FK_77c7b2d7e9b869ab6c22d7461a9" FOREIGN KEY ("plan_feature_id") REFERENCES "plan_feature"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_category" ADD CONSTRAINT "FK_a2995f26fcc34c7a1f4513760fd" FOREIGN KEY ("parent_id") REFERENCES "shop_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_item" ADD CONSTRAINT "FK_d0f2a32e6a14288280764413a8a" FOREIGN KEY ("category_id") REFERENCES "shop_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD CONSTRAINT "FK_8347774fc4c52bd91d3c6b4376e" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD CONSTRAINT "FK_5d462a8a9fb780aa06a4dfccf08" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" ADD CONSTRAINT "FK_5e620f3877939956645a39860f7" FOREIGN KEY ("category_id") REFERENCES "shop_category"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ADD CONSTRAINT "FK_5ec730ca7ae3840095734dd8dae" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ADD CONSTRAINT "FK_afd6408684da50921602e404057" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" ADD CONSTRAINT "FK_503b746c1e584b2ef199093e62b" FOREIGN KEY ("purchase_id") REFERENCES "shop_item_purchase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "story_maker_evaluation" ADD CONSTRAINT "FK_6d8de60704ae69ea2f72b263312" FOREIGN KEY ("submission_id") REFERENCES "story_maker_submission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "story_maker_evaluation" ADD CONSTRAINT "FK_a2dc43a0b4818a2b3b179793546" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "story_maker_submission" ADD CONSTRAINT "FK_a424e6e7f9d1dc1058373fb6ca0" FOREIGN KEY ("participation_id") REFERENCES "story_maker_participation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_b3f833ff339c99ff633ec723d2f" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_331c686de7c16855ee6d3749781" FOREIGN KEY ("story_maker_id") REFERENCES "story_maker"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" ADD CONSTRAINT "FK_750c3fd7052bd0ed7a2e520af45" FOREIGN KEY ("evaluated_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_friendship" ADD CONSTRAINT "FK_ef52ab02e59161e08011b4dd02d" FOREIGN KEY ("requester_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_friendship" ADD CONSTRAINT "FK_016c1bc57046de066da7c314c51" FOREIGN KEY ("requested_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "student_diary_skin" ADD CONSTRAINT "FK_4e7bf414c99b770402b561a809c" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "story_maker_registry" ADD CONSTRAINT "FK_26306eeea7d42eb0d22ebc160d3" FOREIGN KEY ("story_maker_id") REFERENCES "story_maker"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_skin" ADD CONSTRAINT "FK_1575bed4b69f934df585b59840a" FOREIGN KEY ("created_by_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ADD CONSTRAINT "FK_6b110a0c2b8283cfcde3ae5d9f9" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_skin_mapping" ADD CONSTRAINT "FK_14dd83805ed84fab64df6348a00" FOREIGN KEY ("diary_skin_id") REFERENCES "diary_skin"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" ADD CONSTRAINT "FK_33e6e53bd90f5f35dabf8c509a6" FOREIGN KEY ("cart_id") REFERENCES "shopping_cart"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" ADD CONSTRAINT "FK_f06d406b33e3bacdc465d5baeaa" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shopping_cart" ADD CONSTRAINT "FK_2486032b4fc81da82629c53f955" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "shop_item_registry" ADD CONSTRAINT "FK_0d6e4cd3e8ca30faa5b98b14031" FOREIGN KEY ("shop_item_id") REFERENCES "shop_item"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "reward_point" ADD CONSTRAINT "FK_2b561c7b6ec97ff6365dd551fb4" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_history" ADD CONSTRAINT "FK_823255a58f1ab55467fb8a4e511" FOREIGN KEY ("submission") REFERENCES "qa_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "FK_c642437d858571413d52240de5f" FOREIGN KEY ("submission") REFERENCES "qa_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" ADD CONSTRAINT "FK_5769bb5bb9b640e0d811df4c4f0" FOREIGN KEY ("submission_mark_id") REFERENCES "qa_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_weekly_mission_tasks" ADD CONSTRAINT "FK_973cdf02500625f250263ec4417" FOREIGN KEY ("mission_id") REFERENCES "qa_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_monthly_mission_tasks" ADD CONSTRAINT "FK_eac8c67aa92a405d759584ceac7" FOREIGN KEY ("mission_id") REFERENCES "qa_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_mission" ADD CONSTRAINT "FK_3abefaedf983f685f2c25a4292e" FOREIGN KEY ("week_id") REFERENCES "qa_mission_week"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_mission" ADD CONSTRAINT "FK_bb13566db454bdf3b7c4a0d0c5d" FOREIGN KEY ("month_id") REFERENCES "qa_mission_month"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_mission_tasks" ADD CONSTRAINT "FK_802c484cd3199676dc0caee3527" FOREIGN KEY ("mission_id") REFERENCES "qa_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ADD CONSTRAINT "FK_dcc87476add377484ba58e60c90" FOREIGN KEY ("task_id") REFERENCES "qa_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" ADD CONSTRAINT "FK_f8c9a915a6f0809f96073936ea1" FOREIGN KEY ("submission_mark_id") REFERENCES "qa_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_task_missions" ADD CONSTRAINT "FK_4b179e99b9d286e8eb715182721" FOREIGN KEY ("mission") REFERENCES "qa_mission_goal"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_subscription" ADD CONSTRAINT "FK_9491aafd46c7008202b13db1f6f" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_subscription" ADD CONSTRAINT "FK_fe22a4ef6cc1bc0e7863a700c6d" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" ADD CONSTRAINT "FK_28d145b5d6658507977ee621a9d" FOREIGN KEY ("question_id") REFERENCES "qa_question"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" ADD CONSTRAINT "FK_7f6a5ac2305799686bf435eb50b" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_submission" ADD CONSTRAINT "FK_064b0390e4c62a2d77970790700" FOREIGN KEY ("assignment_id") REFERENCES "qa_assignment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "qa_submission" ADD CONSTRAINT "FK_71dd55c96e20f90148012dd8714" FOREIGN KEY ("reviewed_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "profile_picture_registry" ADD CONSTRAINT "FK_1d0b2a8d6e2051c59d4f37dee9e" FOREIGN KEY ("profile_picture_id") REFERENCES "profile_pictures"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_mission" ADD CONSTRAINT "FK_5eec50cf6462f3d617899b040c5" FOREIGN KEY ("admin_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ADD CONSTRAINT "FK_a3b7d2760ae08ffe65191d711f4" FOREIGN KEY ("mission_id") REFERENCES "diary_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ADD CONSTRAINT "FK_438585b4eb3e9eb83307641aee0" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" ADD CONSTRAINT "FK_f3970b9186ce11f3a8a97691329" FOREIGN KEY ("reviewed_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry_feedback" ADD CONSTRAINT "FK_5cb69c9da1e75c8e8ca70f2fa0e" FOREIGN KEY ("mission_entry_id") REFERENCES "mission_diary_entry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry_feedback" ADD CONSTRAINT "FK_5cb791d97eacc59d2110a9d795e" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversation" ADD CONSTRAINT "FK_58c31fcf8372f2b9da76f1771bc" FOREIGN KEY ("participant1_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "conversation" ADD CONSTRAINT "FK_452dcf5452f8aa5e2e117810051" FOREIGN KEY ("participant2_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message_attachment" ADD CONSTRAINT "FK_9db9a64915214dde2ca1e8db9a7" FOREIGN KEY ("message_id") REFERENCES "message"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message" ADD CONSTRAINT "FK_7fe3e887d78498d9c9813375ce2" FOREIGN KEY ("conversation_id") REFERENCES "conversation"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message" ADD CONSTRAINT "FK_c0ab99d9dfc61172871277b52f6" FOREIGN KEY ("sender_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message" ADD CONSTRAINT "FK_3318a3c87e7795d769d5b96e564" FOREIGN KEY ("recipient_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "message_registry" ADD CONSTRAINT "FK_db32ff04c2fc2c38f77d298efa0" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_mission_tasks" ADD CONSTRAINT "FK_75de5fc9088435658ed90ce0a9a" FOREIGN KEY ("mission") REFERENCES "essay_mission"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "FK_113affd435faf528a6eab27c545" FOREIGN KEY ("submission") REFERENCES "essay_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" ADD CONSTRAINT "FK_f06f909a5ae83cb331a1651496b" FOREIGN KEY ("submission_mark_id") REFERENCES "essay_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_9500bf12d147c9a0cde67f37318" FOREIGN KEY ("task") REFERENCES "essay_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_fc522fa06e151db1d68346f83cd" FOREIGN KEY ("submission_mark_id") REFERENCES "essay_task_submission_marking"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" ADD CONSTRAINT "FK_94a4ca8a8ec4b6919b370024c02" FOREIGN KEY ("submission_skin") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" ADD CONSTRAINT "FK_84039f2dd37abc5e5ea72261b1b" FOREIGN KEY ("submission") REFERENCES "essay_task_submissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" ADD CONSTRAINT "FK_0276834b14c1f78c5a81eb61c9b" FOREIGN KEY ("submission_history") REFERENCES "essay_task_submission_history"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" ADD CONSTRAINT "FK_3a54381f8049ae135e0bf604a92" FOREIGN KEY ("skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" ADD CONSTRAINT "FK_ac235b23221d9af3ce1fbceef96" FOREIGN KEY ("task_id") REFERENCES "essay_mission_tasks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_feedback" ADD CONSTRAINT "FK_0a720eca6b5012a76ec03ea7fe4" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_feedback" ADD CONSTRAINT "FK_4d38de3a38b2ed13b42bf14cdad" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_share" ADD CONSTRAINT "FK_c7af4e791867d5bcba3bcf4cbbd" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_correction" ADD CONSTRAINT "FK_4e34f738eafe1ca1709ab1d2f0e" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_correction" ADD CONSTRAINT "FK_66ebd72441e2688c9c5700bcaeb" FOREIGN KEY ("tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry_settings" ADD CONSTRAINT "FK_71e908d2df1da6e93365d9a531d" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry_settings" ADD CONSTRAINT "FK_f5ab83e89e7538fecbb2f30c209" FOREIGN KEY ("settings_template_id") REFERENCES "diary_settings_template"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry_like" ADD CONSTRAINT "FK_932451a171cbea05988aadf6f6e" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry_like" ADD CONSTRAINT "FK_bd9a33da417ca4be7569811d2c7" FOREIGN KEY ("liker_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ADD CONSTRAINT "FK_efb966e9fe0caef3e790f720fa5" FOREIGN KEY ("diary_id") REFERENCES "diary"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ADD CONSTRAINT "FK_ad12d4b9edf19455766d9457d6b" FOREIGN KEY ("skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ADD CONSTRAINT "FK_6c526b99b834d18cb49907a8c0e" FOREIGN KEY ("reviewing_tutor_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry" ADD CONSTRAINT "FK_2643e09b05f859d6cdf34083cba" FOREIGN KEY ("evaluated_by") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary" ADD CONSTRAINT "FK_330f20310184a92a90225c36cbe" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary" ADD CONSTRAINT "FK_67ae196f4fadd5230b210a3917c" FOREIGN KEY ("default_skin_id") REFERENCES "diary_skin"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_skin_registry" ADD CONSTRAINT "FK_217c6f3608dcb0376d4c5329f89" FOREIGN KEY ("diary_skin_id") REFERENCES "diary_skin"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_follow_request" ADD CONSTRAINT "FK_35520e53515c3f1cf09f1075829" FOREIGN KEY ("requester_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_follow_request" ADD CONSTRAINT "FK_14c9a7804964a36063197f72bca" FOREIGN KEY ("diary_owner_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_follow_request" ADD CONSTRAINT "FK_1f037564abc4dc109fec61786c8" FOREIGN KEY ("friendship_id") REFERENCES "student_friendship"("id") ON DELETE SET NULL ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_qr_registry" ADD CONSTRAINT "FK_a29acfaa42930505c29ff2918f8" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry_attendance" ADD CONSTRAINT "FK_869bd8123a9db9ded610171cc65" FOREIGN KEY ("diary_entry_id") REFERENCES "diary_entry"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_entry_attendance" ADD CONSTRAINT "FK_07a5b32e05c38a848b51f314b31" FOREIGN KEY ("student_id") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "diary_award" ADD CONSTRAINT "FK_e6d3eb40116bf219b6829b4f92b" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "award_winner" ADD CONSTRAINT "FK_90c460a2dd57cedc826c55edb2f" FOREIGN KEY ("user_id") REFERENCES "user"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "award_winner" ADD CONSTRAINT "FK_fdfbe81ebe306c4b5e524350a96" FOREIGN KEY ("award_id") REFERENCES "award"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "plan_feature_map" ADD CONSTRAINT "FK_099af0f098a074e2a2842c60260" FOREIGN KEY ("plan_id") REFERENCES "plan"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "plan_feature_map" ADD CONSTRAINT "FK_5947b4fb7b9c93694bbf9b9c244" FOREIGN KEY ("feature_id") REFERENCES "plan_feature"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "plan_feature_map" DROP CONSTRAINT "FK_5947b4fb7b9c93694bbf9b9c244"`);
        await queryRunner.query(`ALTER TABLE "plan_feature_map" DROP CONSTRAINT "FK_099af0f098a074e2a2842c60260"`);
        await queryRunner.query(`ALTER TABLE "award_winner" DROP CONSTRAINT "FK_fdfbe81ebe306c4b5e524350a96"`);
        await queryRunner.query(`ALTER TABLE "award_winner" DROP CONSTRAINT "FK_90c460a2dd57cedc826c55edb2f"`);
        await queryRunner.query(`ALTER TABLE "diary_award" DROP CONSTRAINT "FK_e6d3eb40116bf219b6829b4f92b"`);
        await queryRunner.query(`ALTER TABLE "diary_entry_attendance" DROP CONSTRAINT "FK_07a5b32e05c38a848b51f314b31"`);
        await queryRunner.query(`ALTER TABLE "diary_entry_attendance" DROP CONSTRAINT "FK_869bd8123a9db9ded610171cc65"`);
        await queryRunner.query(`ALTER TABLE "diary_qr_registry" DROP CONSTRAINT "FK_a29acfaa42930505c29ff2918f8"`);
        await queryRunner.query(`ALTER TABLE "diary_follow_request" DROP CONSTRAINT "FK_1f037564abc4dc109fec61786c8"`);
        await queryRunner.query(`ALTER TABLE "diary_follow_request" DROP CONSTRAINT "FK_14c9a7804964a36063197f72bca"`);
        await queryRunner.query(`ALTER TABLE "diary_follow_request" DROP CONSTRAINT "FK_35520e53515c3f1cf09f1075829"`);
        await queryRunner.query(`ALTER TABLE "diary_skin_registry" DROP CONSTRAINT "FK_217c6f3608dcb0376d4c5329f89"`);
        await queryRunner.query(`ALTER TABLE "diary" DROP CONSTRAINT "FK_67ae196f4fadd5230b210a3917c"`);
        await queryRunner.query(`ALTER TABLE "diary" DROP CONSTRAINT "FK_330f20310184a92a90225c36cbe"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT "FK_2643e09b05f859d6cdf34083cba"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT "FK_6c526b99b834d18cb49907a8c0e"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT "FK_ad12d4b9edf19455766d9457d6b"`);
        await queryRunner.query(`ALTER TABLE "diary_entry" DROP CONSTRAINT "FK_efb966e9fe0caef3e790f720fa5"`);
        await queryRunner.query(`ALTER TABLE "diary_entry_like" DROP CONSTRAINT "FK_bd9a33da417ca4be7569811d2c7"`);
        await queryRunner.query(`ALTER TABLE "diary_entry_like" DROP CONSTRAINT "FK_932451a171cbea05988aadf6f6e"`);
        await queryRunner.query(`ALTER TABLE "diary_entry_settings" DROP CONSTRAINT "FK_f5ab83e89e7538fecbb2f30c209"`);
        await queryRunner.query(`ALTER TABLE "diary_entry_settings" DROP CONSTRAINT "FK_71e908d2df1da6e93365d9a531d"`);
        await queryRunner.query(`ALTER TABLE "diary_correction" DROP CONSTRAINT "FK_66ebd72441e2688c9c5700bcaeb"`);
        await queryRunner.query(`ALTER TABLE "diary_correction" DROP CONSTRAINT "FK_4e34f738eafe1ca1709ab1d2f0e"`);
        await queryRunner.query(`ALTER TABLE "diary_share" DROP CONSTRAINT "FK_c7af4e791867d5bcba3bcf4cbbd"`);
        await queryRunner.query(`ALTER TABLE "diary_feedback" DROP CONSTRAINT "FK_4d38de3a38b2ed13b42bf14cdad"`);
        await queryRunner.query(`ALTER TABLE "diary_feedback" DROP CONSTRAINT "FK_0a720eca6b5012a76ec03ea7fe4"`);
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" DROP CONSTRAINT "FK_ac235b23221d9af3ce1fbceef96"`);
        await queryRunner.query(`ALTER TABLE "essay_module_skin_preference" DROP CONSTRAINT "FK_3a54381f8049ae135e0bf604a92"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" DROP CONSTRAINT "FK_0276834b14c1f78c5a81eb61c9b"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_marking" DROP CONSTRAINT "FK_84039f2dd37abc5e5ea72261b1b"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_94a4ca8a8ec4b6919b370024c02"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_fc522fa06e151db1d68346f83cd"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submissions" DROP CONSTRAINT "FK_9500bf12d147c9a0cde67f37318"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "FK_f06f909a5ae83cb331a1651496b"`);
        await queryRunner.query(`ALTER TABLE "essay_task_submission_history" DROP CONSTRAINT "FK_113affd435faf528a6eab27c545"`);
        await queryRunner.query(`ALTER TABLE "essay_mission_tasks" DROP CONSTRAINT "FK_75de5fc9088435658ed90ce0a9a"`);
        await queryRunner.query(`ALTER TABLE "message_registry" DROP CONSTRAINT "FK_db32ff04c2fc2c38f77d298efa0"`);
        await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_3318a3c87e7795d769d5b96e564"`);
        await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_c0ab99d9dfc61172871277b52f6"`);
        await queryRunner.query(`ALTER TABLE "message" DROP CONSTRAINT "FK_7fe3e887d78498d9c9813375ce2"`);
        await queryRunner.query(`ALTER TABLE "message_attachment" DROP CONSTRAINT "FK_9db9a64915214dde2ca1e8db9a7"`);
        await queryRunner.query(`ALTER TABLE "conversation" DROP CONSTRAINT "FK_452dcf5452f8aa5e2e117810051"`);
        await queryRunner.query(`ALTER TABLE "conversation" DROP CONSTRAINT "FK_58c31fcf8372f2b9da76f1771bc"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry_feedback" DROP CONSTRAINT "FK_5cb791d97eacc59d2110a9d795e"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry_feedback" DROP CONSTRAINT "FK_5cb69c9da1e75c8e8ca70f2fa0e"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" DROP CONSTRAINT "FK_f3970b9186ce11f3a8a97691329"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" DROP CONSTRAINT "FK_438585b4eb3e9eb83307641aee0"`);
        await queryRunner.query(`ALTER TABLE "mission_diary_entry" DROP CONSTRAINT "FK_a3b7d2760ae08ffe65191d711f4"`);
        await queryRunner.query(`ALTER TABLE "diary_mission" DROP CONSTRAINT "FK_5eec50cf6462f3d617899b040c5"`);
        await queryRunner.query(`ALTER TABLE "profile_picture_registry" DROP CONSTRAINT "FK_1d0b2a8d6e2051c59d4f37dee9e"`);
        await queryRunner.query(`ALTER TABLE "qa_submission" DROP CONSTRAINT "FK_71dd55c96e20f90148012dd8714"`);
        await queryRunner.query(`ALTER TABLE "qa_submission" DROP CONSTRAINT "FK_064b0390e4c62a2d77970790700"`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" DROP CONSTRAINT "FK_7f6a5ac2305799686bf435eb50b"`);
        await queryRunner.query(`ALTER TABLE "qa_assignment" DROP CONSTRAINT "FK_28d145b5d6658507977ee621a9d"`);
        await queryRunner.query(`ALTER TABLE "qa_subscription" DROP CONSTRAINT "FK_fe22a4ef6cc1bc0e7863a700c6d"`);
        await queryRunner.query(`ALTER TABLE "qa_subscription" DROP CONSTRAINT "FK_9491aafd46c7008202b13db1f6f"`);
        await queryRunner.query(`ALTER TABLE "qa_task_missions" DROP CONSTRAINT "FK_4b179e99b9d286e8eb715182721"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP CONSTRAINT "FK_f8c9a915a6f0809f96073936ea1"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submissions" DROP CONSTRAINT "FK_dcc87476add377484ba58e60c90"`);
        await queryRunner.query(`ALTER TABLE "qa_mission_tasks" DROP CONSTRAINT "FK_802c484cd3199676dc0caee3527"`);
        await queryRunner.query(`ALTER TABLE "qa_mission" DROP CONSTRAINT "FK_bb13566db454bdf3b7c4a0d0c5d"`);
        await queryRunner.query(`ALTER TABLE "qa_mission" DROP CONSTRAINT "FK_3abefaedf983f685f2c25a4292e"`);
        await queryRunner.query(`ALTER TABLE "qa_monthly_mission_tasks" DROP CONSTRAINT "FK_eac8c67aa92a405d759584ceac7"`);
        await queryRunner.query(`ALTER TABLE "qa_weekly_mission_tasks" DROP CONSTRAINT "FK_973cdf02500625f250263ec4417"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "FK_5769bb5bb9b640e0d811df4c4f0"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_marking" DROP CONSTRAINT "FK_c642437d858571413d52240de5f"`);
        await queryRunner.query(`ALTER TABLE "qa_task_submission_history" DROP CONSTRAINT "FK_823255a58f1ab55467fb8a4e511"`);
        await queryRunner.query(`ALTER TABLE "reward_point" DROP CONSTRAINT "FK_2b561c7b6ec97ff6365dd551fb4"`);
        await queryRunner.query(`ALTER TABLE "shop_item_registry" DROP CONSTRAINT "FK_0d6e4cd3e8ca30faa5b98b14031"`);
        await queryRunner.query(`ALTER TABLE "shopping_cart" DROP CONSTRAINT "FK_2486032b4fc81da82629c53f955"`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" DROP CONSTRAINT "FK_f06d406b33e3bacdc465d5baeaa"`);
        await queryRunner.query(`ALTER TABLE "shopping_cart_item" DROP CONSTRAINT "FK_33e6e53bd90f5f35dabf8c509a6"`);
        await queryRunner.query(`ALTER TABLE "shop_skin_mapping" DROP CONSTRAINT "FK_14dd83805ed84fab64df6348a00"`);
        await queryRunner.query(`ALTER TABLE "shop_skin_mapping" DROP CONSTRAINT "FK_6b110a0c2b8283cfcde3ae5d9f9"`);
        await queryRunner.query(`ALTER TABLE "diary_skin" DROP CONSTRAINT "FK_1575bed4b69f934df585b59840a"`);
        await queryRunner.query(`ALTER TABLE "story_maker_registry" DROP CONSTRAINT "FK_26306eeea7d42eb0d22ebc160d3"`);
        await queryRunner.query(`ALTER TABLE "student_diary_skin" DROP CONSTRAINT "FK_4e7bf414c99b770402b561a809c"`);
        await queryRunner.query(`ALTER TABLE "student_friendship" DROP CONSTRAINT "FK_016c1bc57046de066da7c314c51"`);
        await queryRunner.query(`ALTER TABLE "student_friendship" DROP CONSTRAINT "FK_ef52ab02e59161e08011b4dd02d"`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT "FK_750c3fd7052bd0ed7a2e520af45"`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT "FK_331c686de7c16855ee6d3749781"`);
        await queryRunner.query(`ALTER TABLE "story_maker_participation" DROP CONSTRAINT "FK_b3f833ff339c99ff633ec723d2f"`);
        await queryRunner.query(`ALTER TABLE "story_maker_submission" DROP CONSTRAINT "FK_a424e6e7f9d1dc1058373fb6ca0"`);
        await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP CONSTRAINT "FK_a2dc43a0b4818a2b3b179793546"`);
        await queryRunner.query(`ALTER TABLE "story_maker_evaluation" DROP CONSTRAINT "FK_6d8de60704ae69ea2f72b263312"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_503b746c1e584b2ef199093e62b"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_afd6408684da50921602e404057"`);
        await queryRunner.query(`ALTER TABLE "student_owned_item" DROP CONSTRAINT "FK_5ec730ca7ae3840095734dd8dae"`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP CONSTRAINT "FK_5e620f3877939956645a39860f7"`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP CONSTRAINT "FK_5d462a8a9fb780aa06a4dfccf08"`);
        await queryRunner.query(`ALTER TABLE "shop_item_purchase" DROP CONSTRAINT "FK_8347774fc4c52bd91d3c6b4376e"`);
        await queryRunner.query(`ALTER TABLE "shop_item" DROP CONSTRAINT "FK_d0f2a32e6a14288280764413a8a"`);
        await queryRunner.query(`ALTER TABLE "shop_category" DROP CONSTRAINT "FK_a2995f26fcc34c7a1f4513760fd"`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" DROP CONSTRAINT "FK_77c7b2d7e9b869ab6c22d7461a9"`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" DROP CONSTRAINT "FK_e244b2500b4cd575dde3aca20d6"`);
        await queryRunner.query(`ALTER TABLE "student_tutor_mapping" DROP CONSTRAINT "FK_2c4b0b8b5a845fa92703bd5f9c9"`);
        await queryRunner.query(`ALTER TABLE "tutor_permission" DROP CONSTRAINT "FK_233bcd1293b92a99007c35c951d"`);
        await queryRunner.query(`ALTER TABLE "tutor_permission" DROP CONSTRAINT "FK_5b501eb2f5c81382b57bcb6debb"`);
        await queryRunner.query(`ALTER TABLE "tutor_permission" DROP CONSTRAINT "FK_940a2635c71fd6fc29f98720df0"`);
        await queryRunner.query(`ALTER TABLE "user_notification_preference" DROP CONSTRAINT "FK_8ef85bc8bf572f525a116f50eac"`);
        await queryRunner.query(`ALTER TABLE "notification_delivery" DROP CONSTRAINT "FK_2e70a64359ce7416b06a4d7a432"`);
        await queryRunner.query(`ALTER TABLE "notification" DROP CONSTRAINT "FK_928b7aa1754e08e1ed7052cb9d8"`);
        await queryRunner.query(`ALTER TABLE "tutor_education" DROP CONSTRAINT "FK_111e99a139edbc15490fec3032d"`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" DROP CONSTRAINT "FK_d9104da9519ec2a769ba473f498"`);
        await queryRunner.query(`ALTER TABLE "waterfall_answer" DROP CONSTRAINT "FK_aaec21ced4bc6bbf331eeb8bb6f"`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" DROP CONSTRAINT "FK_b54f4c72fadee4feb2ca98da56d"`);
        await queryRunner.query(`ALTER TABLE "waterfall_participation" DROP CONSTRAINT "FK_15f9a5110613d2fa261348d7e0b"`);
        await queryRunner.query(`ALTER TABLE "user_role" DROP CONSTRAINT "FK_32a6fc2fcb019d8e3a8ace0f55f"`);
        await queryRunner.query(`ALTER TABLE "user_role" DROP CONSTRAINT "FK_d0e5815877f7395a198a4cb0a46"`);
        await queryRunner.query(`ALTER TABLE "profile_pictures" DROP CONSTRAINT "FK_bcb95fc382bed71fb8d212b02fd"`);
        await queryRunner.query(`ALTER TABLE "user_plan" DROP CONSTRAINT "FK_ab1f08d687398cd4762faad4690"`);
        await queryRunner.query(`ALTER TABLE "user_plan" DROP CONSTRAINT "FK_5a8dd225812b1927bc8bc60632c"`);
        await queryRunner.query(`ALTER TABLE "waterfall_question" DROP CONSTRAINT "FK_c19ed78ab6eb5bbd64702f2a456"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5947b4fb7b9c93694bbf9b9c24"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_099af0f098a074e2a2842c6026"`);
        await queryRunner.query(`DROP TABLE "plan_feature_map"`);
        await queryRunner.query(`DROP TABLE "award_winner"`);
        await queryRunner.query(`DROP TABLE "audit_log"`);
        await queryRunner.query(`DROP TABLE "award_schedule"`);
        await queryRunner.query(`DROP TYPE "public"."award_schedule_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."award_schedule_module_enum"`);
        await queryRunner.query(`DROP TABLE "award"`);
        await queryRunner.query(`DROP TYPE "public"."award_frequency_enum"`);
        await queryRunner.query(`DROP TYPE "public"."award_criteria_enum"`);
        await queryRunner.query(`DROP TYPE "public"."award_module_enum"`);
        await queryRunner.query(`DROP TABLE "diary_award"`);
        await queryRunner.query(`DROP TYPE "public"."diary_award_period_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ddc7d254be54de06f5ad66ca4e"`);
        await queryRunner.query(`DROP TABLE "diary_entry_attendance"`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_attendance_status_enum"`);
        await queryRunner.query(`DROP TABLE "diary_qr_registry"`);
        await queryRunner.query(`DROP TYPE "public"."diary_qr_registry_storage_provider_enum"`);
        await queryRunner.query(`DROP TABLE "diary_follow_request"`);
        await queryRunner.query(`DROP TYPE "public"."diary_follow_request_status_enum"`);
        await queryRunner.query(`DROP TABLE "diary_skin_registry"`);
        await queryRunner.query(`DROP TYPE "public"."diary_skin_registry_storage_provider_enum"`);
        await queryRunner.query(`DROP TABLE "diary"`);
        await queryRunner.query(`DROP TABLE "diary_entry"`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_view_access_enum"`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_visibility_enum"`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_status_enum"`);
        await queryRunner.query(`DROP TABLE "diary_entry_like"`);
        await queryRunner.query(`DROP TYPE "public"."diary_entry_like_liker_type_enum"`);
        await queryRunner.query(`DROP TABLE "diary_entry_settings"`);
        await queryRunner.query(`DROP TABLE "diary_settings_template"`);
        await queryRunner.query(`DROP TABLE "diary_correction"`);
        await queryRunner.query(`DROP TABLE "diary_share"`);
        await queryRunner.query(`DROP TABLE "diary_feedback"`);
        await queryRunner.query(`DROP TABLE "email_verifications"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_12c59662b2bdfe38b1222aa7ee"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b459468a889b4e58df2199560f"`);
        await queryRunner.query(`DROP TABLE "essay_module_skin_preference"`);
        await queryRunner.query(`DROP TYPE "public"."essay_module_skin_preference_scope_type_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_090783a2cd92909ecf3f68fb2e"`);
        await queryRunner.query(`DROP TABLE "essay_task_submission_marking"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f7d75d2781798b22d628fd65a2"`);
        await queryRunner.query(`DROP TABLE "essay_task_submissions"`);
        await queryRunner.query(`DROP TYPE "public"."essay_task_submissions_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_af613ccf087d159f7484190092"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_c4308ae58c4d15d45caccc9893"`);
        await queryRunner.query(`DROP TABLE "essay_task_submission_history"`);
        await queryRunner.query(`DROP TABLE "essay_mission_tasks"`);
        await queryRunner.query(`DROP TABLE "essay_mission"`);
        await queryRunner.query(`DROP TYPE "public"."essay_mission_time_frequency_enum"`);
        await queryRunner.query(`DROP TABLE "message_registry"`);
        await queryRunner.query(`DROP TYPE "public"."message_registry_storage_provider_enum"`);
        await queryRunner.query(`DROP TABLE "message"`);
        await queryRunner.query(`DROP TYPE "public"."message_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."message_type_enum"`);
        await queryRunner.query(`DROP TABLE "message_attachment"`);
        await queryRunner.query(`DROP TABLE "conversation"`);
        await queryRunner.query(`DROP TYPE "public"."conversation_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."conversation_type_enum"`);
        await queryRunner.query(`DROP TABLE "mission_diary_entry_feedback"`);
        await queryRunner.query(`DROP TABLE "mission_diary_entry"`);
        await queryRunner.query(`DROP TYPE "public"."mission_diary_entry_status_enum"`);
        await queryRunner.query(`DROP TABLE "diary_mission"`);
        await queryRunner.query(`DROP TABLE "password_resets"`);
        await queryRunner.query(`DROP TABLE "promotion"`);
        await queryRunner.query(`DROP TYPE "public"."promotion_applicable_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."promotion_discount_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."promotion_promotion_type_enum"`);
        await queryRunner.query(`DROP TABLE "profile_picture_registry"`);
        await queryRunner.query(`DROP TYPE "public"."profile_picture_registry_storage_provider_enum"`);
        await queryRunner.query(`DROP TABLE "qa_submission"`);
        await queryRunner.query(`DROP TYPE "public"."qa_submission_status_enum"`);
        await queryRunner.query(`DROP TABLE "qa_assignment"`);
        await queryRunner.query(`DROP TYPE "public"."qa_assignment_status_enum"`);
        await queryRunner.query(`DROP TABLE "qa_question"`);
        await queryRunner.query(`DROP TABLE "qa_subscription"`);
        await queryRunner.query(`DROP TYPE "public"."qa_subscription_status_enum"`);
        await queryRunner.query(`DROP TABLE "qa_task_missions"`);
        await queryRunner.query(`DROP TABLE "qa_mission_goal"`);
        await queryRunner.query(`DROP TYPE "public"."qa_mission_goal_time_frequency_enum"`);
        await queryRunner.query(`DROP TABLE "reward_point_setting"`);
        await queryRunner.query(`DROP TABLE "qa_task_submissions"`);
        await queryRunner.query(`DROP TYPE "public"."qa_task_submissions_status_enum"`);
        await queryRunner.query(`DROP TABLE "qa_mission_tasks"`);
        await queryRunner.query(`DROP TABLE "qa_mission"`);
        await queryRunner.query(`DROP TYPE "public"."qa_mission_time_frequency_enum"`);
        await queryRunner.query(`DROP TABLE "qa_mission_week"`);
        await queryRunner.query(`DROP TABLE "qa_mission_month"`);
        await queryRunner.query(`DROP TABLE "qa_monthly_mission_tasks"`);
        await queryRunner.query(`DROP TABLE "qa_weekly_mission_tasks"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b172bc233df0e2aaf1872c497f"`);
        await queryRunner.query(`DROP TABLE "qa_task_submission_marking"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_be57aa932722d380a1f9399138"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d24d8afb23a70831c0a86e4595"`);
        await queryRunner.query(`DROP TABLE "qa_task_submission_history"`);
        await queryRunner.query(`DROP TABLE "reward_point"`);
        await queryRunner.query(`DROP TYPE "public"."reward_point_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."reward_point_source_enum"`);
        await queryRunner.query(`DROP TABLE "shop_item_registry"`);
        await queryRunner.query(`DROP TYPE "public"."shop_item_registry_storage_provider_enum"`);
        await queryRunner.query(`DROP TABLE "shopping_cart"`);
        await queryRunner.query(`DROP TYPE "public"."shopping_cart_status_enum"`);
        await queryRunner.query(`DROP TABLE "shopping_cart_item"`);
        await queryRunner.query(`DROP TABLE "shop_skin_mapping"`);
        await queryRunner.query(`DROP TABLE "diary_skin"`);
        await queryRunner.query(`DROP TABLE "user_otp"`);
        await queryRunner.query(`DROP TABLE "story_maker_registry"`);
        await queryRunner.query(`DROP TYPE "public"."story_maker_registry_storage_provider_enum"`);
        await queryRunner.query(`DROP TABLE "student_diary_skin"`);
        await queryRunner.query(`DROP TABLE "student_friendship"`);
        await queryRunner.query(`DROP TYPE "public"."student_friendship_status_enum"`);
        await queryRunner.query(`DROP TABLE "story_maker"`);
        await queryRunner.query(`DROP TABLE "story_maker_participation"`);
        await queryRunner.query(`DROP TABLE "story_maker_submission"`);
        await queryRunner.query(`DROP TABLE "story_maker_evaluation"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a3e9a158d886e27427535a1f0c"`);
        await queryRunner.query(`DROP TABLE "student_owned_item"`);
        await queryRunner.query(`DROP TYPE "public"."student_owned_item_status_enum"`);
        await queryRunner.query(`DROP TABLE "shop_item_purchase"`);
        await queryRunner.query(`DROP TYPE "public"."shop_item_purchase_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."shop_item_purchase_payment_method_enum"`);
        await queryRunner.query(`DROP TABLE "shop_item"`);
        await queryRunner.query(`DROP TYPE "public"."shop_item_type_enum"`);
        await queryRunner.query(`DROP TABLE "shop_category"`);
        await queryRunner.query(`DROP TABLE "tutor_approval"`);
        await queryRunner.query(`DROP TYPE "public"."tutor_approval_status_enum"`);
        await queryRunner.query(`DROP TABLE "student_tutor_mapping"`);
        await queryRunner.query(`DROP TYPE "public"."student_tutor_mapping_status_enum"`);
        await queryRunner.query(`DROP TABLE "tutor_permission"`);
        await queryRunner.query(`DROP TABLE "user_notification_preference"`);
        await queryRunner.query(`DROP TYPE "public"."user_notification_preference_channel_enum"`);
        await queryRunner.query(`DROP TYPE "public"."user_notification_preference_notification_type_enum"`);
        await queryRunner.query(`DROP TABLE "notification_delivery"`);
        await queryRunner.query(`DROP TYPE "public"."notification_delivery_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."notification_delivery_channel_enum"`);
        await queryRunner.query(`DROP TABLE "notification"`);
        await queryRunner.query(`DROP TYPE "public"."notification_type_enum"`);
        await queryRunner.query(`DROP TABLE "tutor_education"`);
        await queryRunner.query(`DROP TABLE "waterfall_answer"`);
        await queryRunner.query(`DROP TABLE "waterfall_participation"`);
        await queryRunner.query(`DROP TABLE "user_role"`);
        await queryRunner.query(`DROP TABLE "role"`);
        await queryRunner.query(`DROP TABLE "user"`);
        await queryRunner.query(`DROP TYPE "public"."user_type_enum"`);
        await queryRunner.query(`DROP TABLE "profile_pictures"`);
        await queryRunner.query(`DROP TABLE "user_plan"`);
        await queryRunner.query(`DROP TABLE "plan"`);
        await queryRunner.query(`DROP TYPE "public"."plan_subscription_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."plan_type_enum"`);
        await queryRunner.query(`DROP TABLE "plan_feature"`);
        await queryRunner.query(`DROP TYPE "public"."plan_feature_type_enum"`);
        await queryRunner.query(`DROP TABLE "waterfall_set"`);
        await queryRunner.query(`DROP TABLE "waterfall_question"`);
    }

}
