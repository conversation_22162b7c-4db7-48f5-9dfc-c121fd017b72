import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, <PERSON><PERSON><PERSON>al, IsUUI<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsIn, IsBoolean } from 'class-validator';
import { NovelEntryStatus } from '../entities/novel-entry.entity';
import { NovelSuggestionStatus } from '../entities/novel-suggestion.entity';
import { NOVEL_TOPIC_CATEGORY_LIST } from '../../modules/novel/constants/novel.constants';
import { WordLimit } from '../../common/decorators/word-limit.decorator';
import { DiarySkinResponseDto } from './diary.dto';

// Topic DTOs
export class CreateNovelTopicDto {
  @ApiProperty({ example: 'Fantasy Adventure', description: 'Title for the topic' })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ example: 'Topic 1', description: 'Unique sequence title for the topic' })
  @IsNotEmpty()
  @IsString()
  sequenceTitle: string;

  @ApiProperty({ enum: NOVEL_TOPIC_CATEGORY_LIST, example: 'monthly' })
  @IsNotEmpty()
  @IsIn(NOVEL_TOPIC_CATEGORY_LIST)
  category: string;

  @ApiProperty({ example: 'Write a creative story about...', description: 'Instructions for the topic' })
  @IsNotEmpty()
  @IsString()
  instruction: string;

  @ApiProperty({ example: true, description: 'Whether the topic is active' })
  @IsNotEmpty()
  @IsBoolean()
  isActive: boolean;
}

export class UpdateNovelTopicDto {
  @ApiProperty({ example: 'Fantasy Adventure', description: 'Title for the topic', required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ example: 'Topic 1', description: 'Unique sequence title for the topic', required: false })
  @IsOptional()
  @IsString()
  sequenceTitle?: string;

  @ApiProperty({ enum: NOVEL_TOPIC_CATEGORY_LIST, example: 'monthly', required: false })
  @IsOptional()
  @IsIn(NOVEL_TOPIC_CATEGORY_LIST)
  category?: string;

  @ApiProperty({ example: 'Write a creative story about...', description: 'Instructions for the topic', required: false })
  @IsOptional()
  @IsString()
  instruction?: string;

  @ApiProperty({ example: true, description: 'Whether the topic is active', required: false })
  @IsOptional()
  isActive?: boolean;
}

export class NovelTopicResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  title: string;

  @ApiProperty()
  sequenceTitle: string;

  @ApiProperty({ enum: NOVEL_TOPIC_CATEGORY_LIST })
  category: string;

  @ApiProperty()
  instruction: string;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

// Entry DTOs
export class CreateNovelEntryDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Topic ID' })
  @IsNotEmpty()
  @IsUUID()
  topicId: string;

  @ApiProperty({ example: 'Once upon a time...', description: 'Novel content' })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Skin ID', required: false })
  @IsOptional()
  @IsUUID()
  skinId?: string;
}

export class UpdateNovelEntryDto {
  @ApiProperty({ example: 'Once upon a time...', description: 'Novel content', required: false })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Skin ID', required: false })
  @IsOptional()
  @IsUUID()
  skinId?: string;

  @ApiProperty({ example: '#f5f5f5', description: 'Background color for the novel entry', required: false })
  @IsOptional()
  @IsString()
  backgroundColor?: string;
}

export class SubmitNovelEntryDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Entry ID' })
  @IsNotEmpty()
  @IsUUID()
  entryId: string;

  @ApiProperty({ example: 'Once upon a time, in a magical kingdom...', description: 'Novel content', required: false })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Skin ID', required: false })
  @IsOptional()
  @IsUUID()
  skinId?: string;

  @ApiProperty({ example: '#f5f5f5', description: 'Background color for the novel entry', required: false })
  @IsOptional()
  @IsString()
  backgroundColor?: string;
}

// Feedback DTOs
export class CreateNovelFeedbackDto {
  @ApiProperty({ example: 'Great work on character development!', description: 'Feedback text' })
  @IsNotEmpty()
  @IsString()
  feedback: string;
}

export class NovelFeedbackResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  entryId: string;

  @ApiProperty()
  tutorId: string;

  @ApiProperty()
  feedback: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

// Correction DTOs
export class CreateNovelCorrectionDto {
  @ApiProperty({ example: 'Consider improving the plot structure...', description: 'Correction text' })
  @IsNotEmpty()
  @IsString()
  correction: string;
}

export class UpdateNovelCorrectionDto {
  @ApiProperty({ example: 'Consider improving the plot structure...', description: 'Correction text' })
  @IsNotEmpty()
  @IsString()
  correction: string;
}

export class CreateNovelReviewDto {
  @ApiProperty({ example: 'Consider improving the plot structure...', description: 'Correction text' })
  @IsNotEmpty()
  @IsString()
  correction: string;

  @ApiProperty({ example: 85, description: 'Score out of 100' })
  @IsNotEmpty()
  @IsInt()
  @Min(0)
  @Max(100)
  score: number;
}

export class NovelCorrectionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  entryId: string;

  @ApiProperty()
  tutorId: string;

  @ApiProperty()
  correction: string;

  @ApiProperty({ required: false })
  score?: number;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class NovelEntryResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  topicId: string;

  @ApiProperty()
  studentId: string;

  @ApiProperty({ required: false })
  studentName?: string;

  @ApiProperty()
  content: string;

  @ApiProperty()
  wordCount: number;

  @ApiProperty({ enum: NovelEntryStatus })
  status: NovelEntryStatus;

  @ApiProperty({ required: false })
  skinId?: string;

  @ApiProperty({ required: false })
  backgroundColor?: string;

  @ApiProperty({ required: false })
  submittedAt?: Date;

  @ApiProperty({ required: false })
  reviewedAt?: Date;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ required: false })
  topic?: NovelTopicResponseDto;

  @ApiProperty({ required: false })
  feedbacks?: NovelFeedbackResponseDto[];

  @ApiProperty({ required: false })
  correction?: NovelCorrectionResponseDto;

  @ApiProperty({ required: false })
  skin?: DiarySkinResponseDto;

  @ApiProperty({
    required: false,
    example: 'Hello tutor! I\'m excited to share my novel entries with you.',
    description: 'Student\'s greeting message to the tutor for the Novel module'
  })
  tutorGreeting?: string;
}

// Suggestion DTOs
export class CreateNovelSuggestionDto {
  @ApiProperty({ example: 'A story about magical creatures and their adventures in an enchanted forest', description: 'Suggested topic description (max 15 words)' })
  @IsNotEmpty()
  @IsString()
  @WordLimit(15, { message: 'Description cannot exceed 15 words' })
  description: string;
}

export class NovelSuggestionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  studentId: string;

  @ApiProperty({ enum: NovelSuggestionStatus })
  status: NovelSuggestionStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty({ required: false })
  studentName?: string;
}

// Skin Preference DTOs
export class SetNovelSkinPreferenceDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Default skin ID' })
  @IsNotEmpty()
  @IsUUID()
  defaultSkinId: string;
}

// Tutor Greeting DTOs
export class UpdateNovelTutorGreetingDto {
  @ApiProperty({
    example: 'Hello tutor! I\'m excited to share my novel entries with you.',
    description: 'Greeting message for the tutor'
  })
  @IsNotEmpty({ message: 'Greeting message is required' })
  @IsString({ message: 'Greeting message must be a string' })
  greeting: string;
}

export class NovelSkinPreferenceResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  studentId: string;

  @ApiProperty()
  defaultSkinId: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

// Category DTOs
export class NovelCategoryResponseDto {
  @ApiProperty({ example: 'monthly', description: 'Category value' })
  value: string;

  @ApiProperty({ example: 'Monthly novel topics', description: 'Category description' })
  description: string;
}
