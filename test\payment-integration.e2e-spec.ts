import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { Repository, DataSource } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import request from 'supertest';
import { PaymentController } from '../src/modules/payment/payment.controller';
import { PaymentService } from '../src/modules/payment/services/payment.service';
import { KcpService } from '../src/modules/payment/services/kcp.service';
import { KcpConfigService } from '../src/modules/payment/services/kcp-config.service';
import { PaymentTransaction, PaymentTransactionStatus } from '../src/database/entities/payment-transaction.entity';
import { KcpPaymentMethod, PurchaseType } from '../src/modules/payment/interfaces/kcp.interface';
import { PaymentWebhook } from '../src/database/entities/payment-webhook.entity';
import { User, UserType } from '../src/database/entities/user.entity';
import { ShopItemPurchase } from '../src/database/entities/shop-item-purchase.entity';
import { UserPlan } from '../src/database/entities/user-plan.entity';
import LoggerService from '../src/common/services/logger.service';
import { getTestDatabaseConfig } from './utils/test-database.config';
import {
  createTestUser,
  generateTestToken,
  cleanupTestDatabase,
  seedTestDatabase
} from './utils/test-helpers';

describe('Payment Integration (e2e)', () => {
  let app: INestApplication;
  let paymentService: PaymentService;
  let kcpService: KcpService;
  let jwtService: JwtService;
  let dataSource: DataSource;
  let userRepository: Repository<User>;
  let paymentTransactionRepository: Repository<PaymentTransaction>;
  let paymentWebhookRepository: Repository<PaymentWebhook>;
  let shopItemPurchaseRepository: Repository<ShopItemPurchase>;
  let userPlanRepository: Repository<UserPlan>;
  let testUser: User;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRootAsync({
          useFactory: () => getTestDatabaseConfig(),
        }),
        TypeOrmModule.forFeature([
          PaymentTransaction,
          PaymentWebhook,
          ShopItemPurchase,
          UserPlan,
          User,
        ]),
        JwtModule.register({
          secret: 'test_jwt_secret_key_for_testing_only',
          signOptions: { expiresIn: '1h' },
        }),
      ],
      controllers: [PaymentController],
      providers: [
        PaymentService,
        KcpService,
        KcpConfigService,
        LoggerService,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    paymentService = moduleFixture.get<PaymentService>(PaymentService);
    kcpService = moduleFixture.get<KcpService>(KcpService);
    jwtService = moduleFixture.get<JwtService>(JwtService);
    dataSource = moduleFixture.get<DataSource>(DataSource);
    userRepository = moduleFixture.get<Repository<User>>(getRepositoryToken(User));
    paymentTransactionRepository = moduleFixture.get<Repository<PaymentTransaction>>(getRepositoryToken(PaymentTransaction));
    paymentWebhookRepository = moduleFixture.get<Repository<PaymentWebhook>>(getRepositoryToken(PaymentWebhook));
    shopItemPurchaseRepository = moduleFixture.get<Repository<ShopItemPurchase>>(getRepositoryToken(ShopItemPurchase));
    userPlanRepository = moduleFixture.get<Repository<UserPlan>>(getRepositoryToken(UserPlan));

    await app.init();

    // Seed test database
    await seedTestDatabase(dataSource);

    // Skip user cleanup to preserve existing data
    // Use try-catch for user creation in case user already exists

    // Create test user (or find existing one)
    try {
      testUser = await createTestUser(userRepository, {
        type: UserType.STUDENT,
        name: 'Test Student',
        email: '<EMAIL>',
      });
    } catch (error) {
      // If user already exists, find and use it
      const existingUser = await userRepository.findOne({
        where: { email: '<EMAIL>' }
      });
      if (!existingUser) {
        throw new Error('Could not create or find test user');
      }
      testUser = existingUser;
    }

    // Generate auth token
    authToken = generateTestToken(testUser, jwtService);
  });

  afterAll(async () => {
    // Skip database cleanup to preserve existing data
    await app.close();
  });

  afterEach(async () => {
    // Skip cleanup to preserve all existing data
    // Tests will create new data with unique IDs each time
  });

  describe('Payment Initiation API', () => {
    it('should initiate shop item payment successfully', async () => {
      const shopItemPaymentRequest = {
        orderId: `TEST-SHOP-ORDER-${Date.now()}`,
        amount: 5000,
        currency: 'KRW',
        productName: 'Test Shop Item - Premium Skin',
        buyerName: 'Test Student',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.CARD,
        purchaseType: PurchaseType.SHOP_ITEM,
        referenceId: `shop-item-${Date.now()}`,
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const response = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(shopItemPaymentRequest)
        .expect(201); // Payment creation returns 201 Created

      expect(response.body.success).toBe(true);
      expect(response.body.data.transactionId).toBeDefined();
      expect(response.body.data.paymentUrl).toBeDefined();

      // Verify the payment transaction was created with correct data
      const transaction = await paymentTransactionRepository.findOne({
        where: { transactionId: response.body.data.transactionId }
      });
      expect(transaction).toBeDefined();
      expect(transaction!.purchaseType).toBe(PurchaseType.SHOP_ITEM);
      expect(Number(transaction!.amount)).toBe(5000);
    });

    it('should initiate plan subscription payment successfully', async () => {
      const planPaymentRequest = {
        orderId: `TEST-PLAN-ORDER-${Date.now()}`,
        amount: 29900,
        currency: 'KRW',
        productName: 'Premium Plan - Monthly Subscription',
        buyerName: 'Test Student',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.CARD,
        purchaseType: PurchaseType.PLAN,
        referenceId: `plan-${Date.now()}`,
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const response = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(planPaymentRequest)
        .expect(201); // Payment creation returns 201 Created

      expect(response.body.success).toBe(true);
      expect(response.body.data.transactionId).toBeDefined();
      expect(response.body.data.paymentUrl).toBeDefined();

      // Verify the payment transaction was created with correct data
      const transaction = await paymentTransactionRepository.findOne({
        where: { transactionId: response.body.data.transactionId }
      });
      expect(transaction).toBeDefined();
      expect(transaction!.purchaseType).toBe(PurchaseType.PLAN);
      expect(Number(transaction!.amount)).toBe(29900);
    });

    it('should validate required fields', async () => {
      const invalidRequest = {
        orderId: '',
        amount: -100,
        // Missing required fields
      };

      await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidRequest)
        .expect(500); // Service-level validation returns 500
    });
  });

  describe('Payment Status', () => {
    it('should get payment status', async () => {
      // First create a payment transaction
      const paymentRequest = {
        orderId: `TEST-ORDER-${Date.now()}`,
        amount: 10000,
        currency: 'KRW',
        productName: 'Test Product',
        buyerName: 'Test User',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.CARD,
        purchaseType: PurchaseType.SHOP_ITEM,
        referenceId: 'test-reference-id',
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const initResponse = await paymentService.initiatePayment(testUser.id, paymentRequest);
      const transactionId = initResponse.transactionId;

      const response = await request(app.getHttpServer())
        .get(`/payment/status/${transactionId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.transactionId).toBe(transactionId);
      expect(response.body.data.status).toBeDefined();
    });

    it('should return 404 for non-existent transaction', async () => {
      await request(app.getHttpServer())
        .get('/payment/status/non-existent-transaction')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('KCP Service', () => {
    it('should validate KCP configuration', () => {
      expect(kcpService).toBeDefined();
      // Add more KCP-specific tests here
    });

    it('should generate payment URL', async () => {
      const paymentRequest = {
        orderId: `TEST-ORDER-${Date.now()}`,
        amount: 10000,
        currency: 'KRW',
        productName: 'Test Product',
        buyerName: 'Test User',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.CARD,
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
        userId: testUser.id,
        purchaseType: PurchaseType.SHOP_ITEM,
        referenceId: 'test-reference-id',
      };

      const result = await kcpService.initiatePayment(paymentRequest);
      expect(result.success).toBe(true);
      expect(result.transactionId).toBeDefined();
    });
  });

  describe('Shop Item Purchase Integration', () => {
    it('should create shop item purchase with different payment methods', async () => {
      // Test with KCP Card payment
      const cardPaymentRequest = {
        orderId: `TEST-SHOP-CARD-${Date.now()}`,
        amount: 3500,
        currency: 'KRW',
        productName: 'Diary Skin - Ocean Theme',
        buyerName: 'Test Student',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.CARD,
        purchaseType: PurchaseType.SHOP_ITEM,
        referenceId: `diary-skin-${Date.now()}`,
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const cardResponse = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(cardPaymentRequest)
        .expect(201);

      expect(cardResponse.body.success).toBe(true);
      expect(cardResponse.body.data.transactionId).toBeDefined();

      // Test with KCP Bank payment
      const bankPaymentRequest = {
        orderId: `TEST-SHOP-BANK-${Date.now()}`,
        amount: 7500,
        currency: 'KRW',
        productName: 'Novel Skin - Fantasy Theme',
        buyerName: 'Test Student',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.BANK,
        purchaseType: PurchaseType.SHOP_ITEM,
        referenceId: `novel-skin-${Date.now()}`,
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const bankResponse = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(bankPaymentRequest)
        .expect(201);

      expect(bankResponse.body.success).toBe(true);
      expect(bankResponse.body.data.transactionId).toBeDefined();
    });

    it('should handle different shop item price ranges', async () => {
      // Test low-price item (under 1000 KRW)
      const lowPriceRequest = {
        orderId: `TEST-SHOP-LOW-${Date.now()}`,
        amount: 500,
        currency: 'KRW',
        productName: 'Basic Sticker Pack',
        buyerName: 'Test Student',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.CARD,
        purchaseType: PurchaseType.SHOP_ITEM,
        referenceId: `sticker-pack-${Date.now()}`,
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const lowPriceResponse = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(lowPriceRequest)
        .expect(201);

      expect(lowPriceResponse.body.success).toBe(true);

      // Test high-price item (over 50000 KRW)
      const highPriceRequest = {
        orderId: `TEST-SHOP-HIGH-${Date.now()}`,
        amount: 75000,
        currency: 'KRW',
        productName: 'Premium Bundle - All Skins',
        buyerName: 'Test Student',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.CARD,
        purchaseType: PurchaseType.SHOP_ITEM,
        referenceId: `premium-bundle-${Date.now()}`,
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const highPriceResponse = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(highPriceRequest)
        .expect(201);

      expect(highPriceResponse.body.success).toBe(true);
    });
  });

  describe('Plan Subscription Integration', () => {
    it('should create plan subscriptions with different durations', async () => {
      // Test monthly plan
      const monthlyPlanRequest = {
        orderId: `TEST-PLAN-MONTHLY-${Date.now()}`,
        amount: 19900,
        currency: 'KRW',
        productName: 'Standard Plan - Monthly',
        buyerName: 'Test Student',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.CARD,
        purchaseType: PurchaseType.PLAN,
        referenceId: `monthly-plan-${Date.now()}`,
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const monthlyResponse = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(monthlyPlanRequest)
        .expect(201);

      expect(monthlyResponse.body.success).toBe(true);

      // Test yearly plan
      const yearlyPlanRequest = {
        orderId: `TEST-PLAN-YEARLY-${Date.now()}`,
        amount: 199000,
        currency: 'KRW',
        productName: 'Premium Plan - Yearly',
        buyerName: 'Test Student',
        buyerEmail: '<EMAIL>',
        buyerPhone: '010-1234-5678',
        paymentMethod: KcpPaymentMethod.BANK,
        purchaseType: PurchaseType.PLAN,
        referenceId: `yearly-plan-${Date.now()}`,
        returnUrl: 'http://localhost:3011/payment/success',
        cancelUrl: 'http://localhost:3011/payment/cancel',
      };

      const yearlyResponse = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(yearlyPlanRequest)
        .expect(201);

      expect(yearlyResponse.body.success).toBe(true);
    });
  });

  describe('Webhook Processing', () => {
    it('should process shop item webhook successfully', async () => {
      const shopWebhookPayload = {
        site_cd: 'TEST_SITE',
        ordr_idxx: `TEST-SHOP-ORDER-${Date.now()}`,
        tno: `TXN-${Date.now()}`,
        res_cd: '0000',
        res_msg: 'SUCCESS',
        amount: '5000',
        good_name: 'Test Shop Item',
        buyr_name: 'Test Student',
        buyr_mail: '<EMAIL>',
        pay_method: '************',
      };

      const response = await request(app.getHttpServer())
        .post('/payment/webhook/kcp')
        .set('x-kcp-signature', 'test-signature')
        .send(shopWebhookPayload)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should process plan subscription webhook successfully', async () => {
      const planWebhookPayload = {
        site_cd: 'TEST_SITE',
        ordr_idxx: `TEST-PLAN-ORDER-${Date.now()}`,
        tno: `TXN-${Date.now()}`,
        res_cd: '0000',
        res_msg: 'SUCCESS',
        amount: '29900',
        good_name: 'Premium Plan Subscription',
        buyr_name: 'Test Student',
        buyr_mail: '<EMAIL>',
        pay_method: '************',
      };

      const response = await request(app.getHttpServer())
        .post('/payment/webhook/kcp')
        .set('x-kcp-signature', 'test-signature')
        .send(planWebhookPayload)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle payment initiation errors gracefully', async () => {
      const invalidPaymentRequest = {
        orderId: '',
        amount: 0,
        currency: 'INVALID',
        productName: '',
        buyerName: '',
        buyerEmail: 'invalid-email',
        buyerPhone: '',
        paymentMethod: 'invalid',
        purchaseType: 'invalid',
        referenceId: '',
        returnUrl: '',
        cancelUrl: '',
      };

      const response = await request(app.getHttpServer())
        .post('/payment/initiate')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidPaymentRequest)
        .expect(500); // Service-level validation returns 500

      // The error response structure may vary, just check that we got a 500 error
      expect(response.status).toBe(500);
    });

    it('should handle unauthorized requests', async () => {
      const paymentRequest = {
        orderId: `TEST-ORDER-${Date.now()}`,
        amount: 10000,
        // ... other fields
      };

      await request(app.getHttpServer())
        .post('/payment/initiate')
        .send(paymentRequest)
        .expect(401);
    });
  });

});

describe('Payment Integration Unit Tests', () => {
  let paymentService: PaymentService;
  let kcpService: KcpService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
      ],
      providers: [
        PaymentService,
        KcpService,
        KcpConfigService,
        LoggerService,
        // Mock dependencies
        {
          provide: getRepositoryToken(PaymentTransaction),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(PaymentWebhook),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ShopItemPurchase),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(UserPlan),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: DataSource,
          useValue: {
            createQueryRunner: jest.fn().mockReturnValue({
              connect: jest.fn(),
              startTransaction: jest.fn(),
              commitTransaction: jest.fn(),
              rollbackTransaction: jest.fn(),
              release: jest.fn(),
            }),
          },
        },
      ],
    }).compile();

    paymentService = module.get<PaymentService>(PaymentService);
    kcpService = module.get<KcpService>(KcpService);
  });

  describe('PaymentService', () => {
    it('should be defined', () => {
      expect(paymentService).toBeDefined();
    });

    // Add more unit tests for PaymentService methods
  });

  describe('KcpService', () => {
    it('should be defined', () => {
      expect(kcpService).toBeDefined();
    });

    // Add more unit tests for KcpService methods
  });
});
