import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON><PERSON> } from "typeorm";
import { AuditableBaseEntity } from "./base-entity";
import { QATaskMissions } from "./qa-task-missions.entity";

export enum QAMissionFrequency {
  WEEKLY = "weekly",
  MONTHLY = "monthly",
}

@Entity()
export class QAMissionGoal extends AuditableBaseEntity {
  @Column({
    name: "time_frequency",
    type: "enum",
    enum: QAMissionFrequency,
    default: QAMissionFrequency.WEEKLY
  })
  timeFrequency: QAMissionFrequency;

  @Column({
    name: "is_active",
    default: true,
    type: "boolean"
  })
  isActive: boolean;

  @Column({
    name: "sequence_number",
    default: 1,
    type: "int"
  })
  sequenceNumber: number;

  @OneToMany(() => QATaskMissions, task => task.mission)
  tasks: QATaskMissions[];
}
